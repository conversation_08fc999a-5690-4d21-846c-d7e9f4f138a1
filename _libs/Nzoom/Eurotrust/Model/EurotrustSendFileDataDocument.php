<?php

namespace Nzoom\Eurotrust\Model;

use JsonSerializable;

class EurotrustSendFileDataDocument implements JsonSerializable
{
    const CERTIFICATE_TYPE_QUALIFIED = 1;
    const CERTIFICATE_TYPE_ADVANCED = 2;

    //max chars 4000
    private string $description;

    //to be converted to timestamp
    private \DateTime $dateExpire;

    //0, 500, 2000, 20000, 100000, 250000.
    // The amount is in euro. 0 stands for unlimited.
    private int $coverage;

    // 1 - Е-Sign Qualified;
    // 2 - E-Sign Advanced;
    // If certificateType is 2, only coverage 500 is accepted.
    private ?int $certificateType = null;

    //0/1 whether the document requires previewing before signing
    private int $preview;

    //SHA-512 of the file
    private ?string $checksumDocument = null;

    private ?array $annotationParameters;

    public function __construct(string $description, \DateTime $dateExpire, int $coverage, int $preview = 0)
    {
        $this->description = mb_substr($description, 0, 4000);
        $this->dateExpire = $dateExpire;
        $this->coverage = $coverage;
        if (!in_array($preview, [0,1])) {
            throw new \InvalidArgumentException('Invalid preview!');
        }
        $this->preview = $preview;
    }

    public function jsonSerialize()
    {
        $data = [
            'description' => $this->description,
            'dateExpire' => $this->dateExpire->getTimestamp(),
            'coverage' => $this->coverage,
            'preview' => $this->preview,
        ];

        if (isset($this->certificateType)) {
            $data['certificateType'] = $this->certificateType;
        }

        if (isset($this->checksumDocument)) {
            $data['checksumDocument'] = $this->checksumDocument;
        }

        if (isset($this->annotationParameters)) {
            $data['annotationParameters'] = $this->annotationParameters;
        }

        return $data;
    }

    /**
     * @return string
     */
    public function getChecksumDocument(): string
    {
        return $this->checksumDocument;
    }

    /**
     * @param string $checksumDocument
     */
    public function setChecksumDocument(string $checksumDocument): void
    {
        $this->checksumDocument = $checksumDocument;
    }

    /**
     * @param int|null $certificateType
     * @throws \Exception
     */
    public function setCertificateType(int $certificateType): void
    {
        if (!in_array($certificateType, [self::CERTIFICATE_TYPE_QUALIFIED, self::CERTIFICATE_TYPE_ADVANCED])) {
            throw new \InvalidArgumentException('Invalid certificate type!');
        }
        $this->certificateType = $certificateType;
    }

    /**
     * @param EurotrustAnnotation $annotation
     */
    public function setAnnotationParameters(array $annotation): void
    {
        $this->annotationParameters = $annotation;
    }

    /**
     * @return EurotrustAnnotation|null
     */
    public function getAnnotationParameters(): ?EurotrustAnnotation
    {
        return $this->annotationParameters;
    }

    /**
     * @return \DateTime
     */
    public function getDateExpire(): \DateTime
    {
        return $this->dateExpire;
    }
}
