a:6:{s:9:"classesIn";a:1:{s:23:"Nzoom\Db\DbCapabilities";a:6:{s:4:"name";s:14:"DbCapabilities";s:14:"namespacedName";s:23:"Nzoom\Db\DbCapabilities";s:9:"namespace";s:8:"Nzoom\Db";s:9:"startLine";i:5;s:7:"endLine";i:70;s:7:"methods";a:3:{s:8:"getDbVer";a:6:{s:10:"methodName";s:8:"getDbVer";s:9:"signature";s:18:"getDbVer(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:36;s:7:"endLine";i:44;s:3:"ccn";i:2;}s:15:"isIcuCompatible";a:6:{s:10:"methodName";s:15:"isIcuCompatible";s:9:"signature";s:23:"isIcuCompatible(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:52;s:7:"endLine";i:57;s:3:"ccn";i:2;}s:19:"mySqlWordRegExpWrap";a:6:{s:10:"methodName";s:19:"mySqlWordRegExpWrap";s:9:"signature";s:34:"mySqlWordRegExpWrap($word): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:65;s:7:"endLine";i:68;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:71;s:18:"commentLinesOfCode";i:27;s:21:"nonCommentLinesOfCode";i:44;}s:15:"ignoredLinesFor";a:1:{i:0;i:5;}s:17:"executableLinesIn";a:9:{i:37;i:4;i:38;i:5;i:40;i:6;i:43;i:7;i:53;i:8;i:54;i:9;i:56;i:10;i:66;i:11;i:67;i:12;}}