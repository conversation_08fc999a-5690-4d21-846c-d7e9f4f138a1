a:6:{s:9:"classesIn";a:1:{s:41:"Nzoom\Export\Streamer\PointerFileStreamer";a:6:{s:4:"name";s:19:"PointerFileStreamer";s:14:"namespacedName";s:41:"Nzoom\Export\Streamer\PointerFileStreamer";s:9:"namespace";s:21:"Nzoom\Export\Streamer";s:9:"startLine";i:11;s:7:"endLine";i:140;s:7:"methods";a:6:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:77:"__construct($filePointer, string $filename, string $mimeType, int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:36;s:7:"endLine";i:66;s:3:"ccn";i:6;}s:16:"performStreaming";a:6:{s:10:"methodName";s:16:"performStreaming";s:9:"signature";s:24:"performStreaming(): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:71;s:7:"endLine";i:88;s:3:"ccn";i:6;}s:7:"cleanup";a:6:{s:10:"methodName";s:7:"cleanup";s:9:"signature";s:15:"cleanup(): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:93;s:7:"endLine";i:103;s:3:"ccn";i:2;}s:12:"getChunkSize";a:6:{s:10:"methodName";s:12:"getChunkSize";s:9:"signature";s:19:"getChunkSize(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:110;s:7:"endLine";i:113;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:34:"setChunkSize(int $chunkSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:121;s:7:"endLine";i:129;s:3:"ccn";i:2;}s:12:"getTotalSize";a:6:{s:10:"methodName";s:12:"getTotalSize";s:9:"signature";s:20:"getTotalSize(): ?int";s:10:"visibility";s:6:"public";s:9:"startLine";i:136;s:7:"endLine";i:139;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:141;s:18:"commentLinesOfCode";i:51;s:21:"nonCommentLinesOfCode";i:90;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:33:{i:38;i:4;i:39;i:5;i:42;i:6;i:44;i:7;i:45;i:8;i:48;i:9;i:49;i:10;i:50;i:11;i:51;i:12;i:52;i:13;i:56;i:14;i:57;i:15;i:60;i:16;i:61;i:17;i:62;i:18;i:73;i:19;i:74;i:20;i:75;i:21;i:77;i:22;i:78;i:23;i:81;i:24;i:84;i:25;i:85;i:26;i:96;i:27;i:97;i:28;i:98;i:29;i:102;i:30;i:112;i:31;i:123;i:32;i:124;i:33;i:127;i:34;i:128;i:35;i:138;i:36;}}