{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=comments&amp;comment=list&amp;model={$model_name}&amp;model_id={$model_id}&amp;page={/capture}
<table cellpadding="3" cellspacing="3" border="0">
  <tr>
    <td valign="top" style="padding-left: 0; margin-left: 0; border-collapse: collapse;">
      <table border="0" cellpadding="0" cellspacing="0">
    {if $pagination.pages > 1}
        <tr>
          <td class="pagemenu">
      {assign var=sort value=$comments_sort}
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        pagination=$pagination
        sort=$comments_sort
        session_param=$comments_session_param
        use_ajax=$comments_use_ajax
        hide_stats=1
      }
          </td>
        </tr>
    {/if}
        <tr>
          <td id="comments_container">
            <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border" style="width: 100%;">
              <tr>
                <td class="t_caption t_border" nowrap="nowrap" style="width: 20px"><div class="t_caption_title">{#num#|escape}</div></td>
                <td class="t_caption t_border {$comments_sort.added_by.class}" nowrap="nowrap" style="width: 140px"><div class="t_caption_title" onclick="{$comments_sort.added_by.link}">{#added_by#|escape}</div></td>
                <td class="t_caption t_border {$comments_sort.subject.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$comments_sort.subject.link}">{#subject#|escape}</div></td>
                <td class="t_caption t_border {$comments_sort.content.class}"><div class="t_caption_title" onclick="{$comments_sort.content.link}" style="width: 300px">{#comment#|escape}</div></td>
                <td class="t_caption t_border {$comments_sort.added.class}" nowrap="nowrap" style="width: 150px"><div class="t_caption_title" onclick="{$comments_sort.added.link}">{#date#|escape}</div></td>
                <td class="t_caption" style="width: 14px">&nbsp;</td>
              </tr>
            {counter start=$pagination.start name='item_counter' print=false}
            {foreach name='i' from=$comments item='comment'}
              <tr class="{cycle values='t_odd,t_even'} vtop">
                <td class="t_border hright">{counter name='item_counter' print=true}</td>
                <td class="t_border {$comments_sort.added_by.isSorted}" nowrap="nowrap">{$comment->get('added_by_name')|escape|default:"&nbsp;"}</td>
                <td class="t_border {$comments_sort.subject.isSorted}">{$comment->get('subject')|mb_truncate:80|escape|default:"&nbsp;"}</td>
                {strip}
                {capture assign='comment_full'}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_comment#|escape}" title="{#full_comment#|escape}" onclick="toggleContent('comment', {$smarty.foreach.i.iteration});" />
                {/capture}
                {capture assign='comment_part'}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_comment#|escape}" title="{#part_comment#|escape}" onclick="toggleContent('comment', {$smarty.foreach.i.iteration});" />
                {/capture}
                {/strip}
                <td class="t_border {$comments_sort.content.isSorted}" id="comment_{$comment->get('id')}">
                  <div id="comment_part_{$smarty.foreach.i.iteration}">
                    {$comment->get('content')|escape|mb_truncate:130:$comment_full|nl2br}
                  </div>
                  <div id="comment_full_{$smarty.foreach.i.iteration}" style="display: none;">
                    {$comment->get('content')|nl2br}{$comment_part}
                  </div>
                </td>
                <td class="t_border {$comments_sort.added.isSorted}">{$comment->get('added')|date_format:#date_mid#|escape}</td>
                <td class="hcenter" nowrap="nowrap">
                {capture assign='module_name'}{$model_name|mb_lower}s{/capture}
                <img src="{$theme->imagesUrl}small/reply.png" width="12" height="12" border="0" alt="{#reply#|escape}" {if $currentUser->checkRights($module_name, 'comments_add')}title="{#reply#|escape}" class="page_menu_link" onclick="window.location.href='#comments_add_form'; $('subject').value='Re: {$comment->get('subject')|escape:'quotes'|escape}'"{else}class="dimmed pointer" onclick="alert('{#error_add_notallowed#|escape}')"{/if} />
                {math assign=last_date equation=x-60*y x=$smarty.now y=$smarty.const.PH_COMMENTS_EDIT_INTERVAL}
                {capture assign='last_date_formated'}{$last_date|date_format:'%Y-%m-%d %H:%M:%S'}{/capture}
                {if $currentUser->checkRights($module_name, 'comments_add') && $comment->get('added_by') eq $currentUser->get('id') && $last_date_formated lt $comment->get('modified')}
                  <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" alt="{#edit#|escape}" title="{#edit#|escape}" class="page_menu_link" onclick="editComment({$comment->get('id')},'comment_container');" />
                {else}
                  <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" alt="{#edit#|escape}" class="dimmed pointer" onclick="alert('{#error_edit_notallowed#|escape}')" />
                {/if}
                </td>
              </tr>
            {foreachelse}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="error" colspan="6">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
              <tr>
                <td class="t_footer" colspan="6"></td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td class="pagemenu">
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        pagination=$pagination
        sort=$comments_sort
        session_param=$comments_session_param
        use_ajax=$comments_use_ajax
        hide_selection_stats=true
      }
          </td>
        </tr>
      </table>

    </td>
  </tr>
</table>
{capture assign='module_name'}{$model_name|mb_lower}s{/capture}
{if $currentUser->checkRights($module_name, 'comments_add')}
  {if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
  {if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
  {if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
    <a name="comments_add_form"></a>
    <form name="comments_add" action="" method="post" onsubmit="saveComment(this,'{$comments_session_param}'); return false;">
      <input type="hidden" name="model_id" id="model_id" value="{$model_id}"  />
      <input type="hidden" name="model" id="model" value="{$model_name}"  />
      {if $event_date}
        <input type="hidden" name="event_date" id="event_date" value="{$event_date}"  />
      {/if}
      <div id="comment_container">
      <table border="0" cellpadding="3" cellspacing="0" class="t_table t_table_border" style="width:55%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="3"><div class="t_caption_title">{#add_comment#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><label for="subject">{#subject#|escape}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="subject" id="subject" value="" title="" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="content">{#comment#|escape}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <textarea class="areabox doubled higher" name="content" id="content" title="" onfocus="highlight(this)" onblur="unhighlight(this)"></textarea>
          </td>
        </tr>
    {if $include_portal_users_option && !$currentUser->get('is_portal')}
      <tr>
        <td class="labelbox" nowrap="nowrap"><a name="error_is_portal"><label for="is_portal"{if $messages->getErrors('is_portal')} class="error"{/if}>{#is_portal#|escape}:</label></a></td>
        <td>&nbsp;</td>
        <td nowrap="nowrap">
          {capture assign='is_portal'}{strip}
            {if $is_portal eq 1}
              1
            {else}
              0
            {/if}
          {/strip}{/capture}
          {capture assign="is_portal_suffix"}_{uniqid}{/capture}
          <input type="radio" name="is_portal" id="is_portal1{$is_portal_suffix}" value="1" title="{#is_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" {if $is_portal} checked="checked"{/if} /><label for="is_portal1{$is_portal_suffix}">{#is_portal#|escape}</label>
          <input type="radio" name="is_portal" id="is_portal2{$is_portal_suffix}" value="0" title="{#is_not_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !$is_portal} checked="checked"{/if} /><label for="is_portal2{$is_portal_suffix}">{#is_not_portal#|escape}</label>
        </td>
      </tr>
    {/if}
        <tr>
        <td colspan="3">
          <button type="submit" class="button" name="addComment" id="addComment">{#add#|escape}</button></td>
        </tr>
        <tr>
          <td class="t_footer" colspan="3"></td>
        </tr>
      </table>
      </div>
    </form>
{/if}
