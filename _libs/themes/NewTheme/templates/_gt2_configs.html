<div id="gt2_config_pattern">
<table id="table_config" width="600" border="0">
  <tr>
    <td>
      <fieldset>
      <legend>{#configurator_title#|escape}</legend>
        <table id="table_config_container" align="left" border="0">
          <tr>
            <td><label for="config"{if $messages->getErrors('config')} class="error"{/if}>{help label_content=#configurator_load_save#|escape}</label></td>
            <td width="180">
              {include file='input_combobox.html'
                       name=config
                       options=$configs
                       standalone=true
                       label=#configurator_title#
               }
            </td>
            <td>
              <img src="{$theme->imagesUrl}small/view.png" alt="{#configurator_reload#|escape}" title="{#configurator_reload#|escape}" onclick="return confirmAction('load_config', function(el) {ldelim} manageGT2Config('{$module}','{$controller}', 'loadGT2config'); {rdelim}, this);" class="pointer" />
              <img src="{$theme->imagesUrl}small/download.png" alt="{#configurator_save#|escape}" title="{#configurator_save#|escape}" onclick="return confirmAction('save_config', function(el) {ldelim} manageGT2Config('{$module}','{$controller}', 'saveGT2config'); {rdelim}, this);" class="pointer" />
              <img src="{$theme->imagesUrl}small/delete.png" alt="{#configurator_delete#|escape}" title="{#configurator_delete#|escape}" onclick="return confirmAction('delete_config', function(el) {ldelim} manageGT2Config('{$module}', '{$controller}', 'deleteGT2config'); {rdelim}, this);" class="pointer" />
            </td>
          </tr>
        </table>
      </fieldset>
    </td>
  </tr>
</table>
</div>
