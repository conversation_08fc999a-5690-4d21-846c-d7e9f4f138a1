  <div class="gt2_audit_legend">
    <h1>{#legend#|escape}</h1>
    <table border="0" cellpadding="0" cellspacing="5">
      <tr class="finance_audit_added">
        <td class="legend_color">&nbsp;</td>
        <td class="legend_text">{#legend_added_row#}</td>
      </tr>
      <tr class="finance_audit_deleted">
        <td class="legend_color">&nbsp;</td>
        <td class="legend_text">{#legend_deleted_row#}</td>
      </tr>
      <tr class="finance_audit_updated">
        <td class="legend_color old_value">&nbsp;</td>
        <td class="legend_text">{#legend_old_value#}</td>
      </tr>
      <tr class="finance_audit_updated">
        <td class="legend_color new_value">&nbsp;</td>
        <td class="legend_text">{#legend_new_value#}</td>
      </tr>
    </table>
  </div>

<div class="clear"></div>
<br />
<span class="red">{#gt2_audit_only_changed_rows#}</span>
<br /><br />
{if !empty($gt2_audit.new_values) || !empty($gt2_audit.old_values)}
  {capture assign='colspan'}{$gt2_audit.labels|@count}{/capture}
  {assign var='colspan' value=$colspan*2}
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_header t_table_border">
    <tr>
      <td colspan="{$colspan}" class="t_caption3 strong legend">{#data#|escape}</td>
    </tr>
    <tr>
      {foreach name='i' from=$gt2_audit.labels item='label'}
        <td class="t_caption{if !$smarty.foreach.i.last} t_border{/if}" colspan="2" nowrap="nowrap"><div class="t_caption_title">{$label|escape}</div></td>
      {/foreach}
    </tr>
    {foreach name='i' from=$gt2_audit.new_values item='values' key='row'}
    {if !empty($values.field_name) && $values.field_name == 'bb_delimiter'}
    <tr>
      <td colspan="{$colspan}" class="t_caption3 strong legend">{if empty($values.deleted)}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$values.model_id}{if $smarty.request.archive}&amp;archive=1{/if}#bb_row_{$values.bb_id}">{$values.label|escape|default:'&nbsp;'}</a>{else}{$values.label|escape|default:'&nbsp;'}{/if}</td>
    </tr>
    {elseif $values.article_id || $values.price || $values.average_weighted_delivery_price || $values.last_delivery_price || $values.quantity || $values.subtotal || $values.action eq 'deleted'}
    <tr class="finance_audit_{if $values.action eq 'deleted' || empty($values)}deleted{elseif $values.action eq 'added' || empty($gt2_audit.old_values.$row)}added{else}updated{/if}">
      {foreach name='j' from=$gt2_audit.labels key='var' item='bla'}
        {if $values.action eq 'deleted' || empty($values)}
          <td class="{if !$smarty.foreach.j.last}t_border{/if}{if !$smarty.foreach.i.last} t_v_border{/if}" colspan="2">{$gt2_audit.old_values.$row.$var|nl2br|default:'&nbsp;'}</td>
        {elseif $values.action eq 'added' || empty($gt2_audit.old_values.$row)}
          <td class="{if !$smarty.foreach.j.last}t_border{/if}{if !$smarty.foreach.i.last} t_v_border{/if}" colspan="2">{$values.$var|nl2br|default:'&nbsp;'}</td>
        {elseif $gt2_audit.old_values.$row.$var eq $values.$var}
          <td class="{if !$smarty.foreach.j.last}t_border{/if}{if !$smarty.foreach.i.last} t_v_border{/if}" colspan="2">{$values.$var|nl2br|default:'&nbsp;'}</td>
        {else}
          <td class="t_border{if !$smarty.foreach.i.last} t_v_border{/if} old_value">{$gt2_audit.old_values.$row.$var|nl2br|default:'&nbsp;'}</td>
          <td class="{if !$smarty.foreach.j.last}t_border{/if}{if !$smarty.foreach.i.last} t_v_border{/if} new_value">{$values.$var|nl2br|default:'&nbsp;'}</td>
        {/if}
      {/foreach}
    </tr>
    {/if}
    {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="{$colspan}">{#no_changes_made#|escape}</td>
    </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="{$colspan}"></td>
    </tr>
  </table>
{/if}