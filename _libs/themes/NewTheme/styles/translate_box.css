/* Translate Options Styles */
#translate_container {
    border: 1px solid #CCCCCC;
    border-bottom: 0;
    background: #F1F1F1;
}
#translate_container #translate_flags {
    padding: 0 5px;
}
#translate_container #translate_flags div {
    color: #999999;
    float: left;
}
#translate_container #translate_flags img {
    opacity: 0.3;
    filter: alpha(opacity=30);
    border: 1px solid #F1F1F1;
}
#translate_container #translate_flags img:hover {
    opacity: 1;
    filter: alpha(opacity=100);
    border: 1px solid #999999;
}
#translate_container #translate_switch {
    height: 7px;
    cursor: pointer;
    font-size: 1px;
    line-height: 0;
}
#translate_container #translate_switch div {
    height: 7px;
    font-size: 1px;
    line-height: 0;
}
#translate_container #translate_flags img.selected,
#translate_container #translate_flags img.selected:hover {
    opacity: 1;
    filter: alpha(opacity=100);
    border: 1px solid #999999;
}
.translations img {
    margin: 0 3px;
    border: 1px solid #F1F1F1;
}
.translations img.selected {
    border: 1px solid #999999;
}
.make_translation_div {
    margin: 0px 0px 0px 10px;
}
