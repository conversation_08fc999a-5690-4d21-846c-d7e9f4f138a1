<?php

use Nzoom\I18n\I18n;
use Nzoom\Theme\Theme;
use Nzoom\Navigation;

class NavBarFactory
{
    private Registry $registry;
    private Theme $theme;
    private ?Model $model;

    private string $moduleParam;
    private string $controllerParam;
    private string $module;
    private string $controller;
    private string $action;
    private I18n $translater;
    private bool $validLogin;
    private ?User $user;

    /**
     * @param $model
     * @param Registry $registry
     */
    public function __construct(Registry $registry, $model=null)
    {
        $this->registry = $registry;
        $this->translater = $this->registry['translater'];
        $this->theme = $registry['theme'];
        $this->lang = $this->registry['lang'];
        $this->module = $this->registry['module'];
        $this->controllerParam = $this->registry['controller_param'];
        $this->controller = $this->registry['controller'];
        $this->moduleParam = $this->registry['module_param'];
        $this->action = $this->registry['action'];

        $this->validLogin = (bool) $this->registry['validLogin'];

        $this->model = $model ?: null;
    }

    public function createNavMenu(): Menu
    {
        $navMenu = new Menu();
        $navMenu->addItem($this->createHomeMenuItem());


        if (!empty($this->module) && $this->module != 'index') {
            $navMenu->addItem($this->createModuleMenuItem());
        }

        if (!empty($this->controller)
                && $this->controller != $this->module
                && (!empty($this->model) || !empty($_GET['type']))) {
            $navMenu->addItem($this->createControllerMenuItem());
        }

        if (!empty($this->model) && is_object($this->model) && $this->model->get('id') && $this->model->modelName !== 'Report') {
            if ($this->model->get('type')) {
                $navMenu->addItem($this->createTypeMenuItem());
            }

            $fullLabel = $this->getModelItemLabel();
            if ($fullLabel) {
                $navMenu->addItem($this->createModelMenuItem($fullLabel));
            }
        }

        $label = $this->getActionItemLabel();
        if (!empty($label)) {
            $navMenu->addItem($this->createActionMenuItem($label));
        }

        return $navMenu;
    }

    /**
     * @param string $param
     * @param array $placeholders
     * @return string
     */
    function i18n(string $param, array $placeholders = array()): string
    {
        $translated = $this->translater->translate($param);
        if (empty($translated)) {
            $translated = $this->translater->translate('menu_'.$param);
        }

        //replace placeholders
        if (count($placeholders) > 0) {
            if (preg_match('#%#', $translated)) {
                $translated = vsprintf($translated, $placeholders);
            } else {
                foreach ($placeholders as $find => $replace) {
                    $translated = str_replace('[' . $find . ']', $replace, $translated);
                }
            }
        }

        return (string) $translated;
    }

    /**
     * @return string|null
     */
    private function getModuleUrl(): ?string
    {
        try {
            $rights = $this->getUser()->getRights();
        } catch (\Exception $e) {
            return null;
        }

        if ($this->module === 'finance' || $this->module === 'nomenclatures') {
            $hasAccessToModuleIndex = 'all' === ($rights[$this->module]['index'] ?? 'none');
        } else {
            $hasAccessToModuleIndex = 'none' !== ($rights[$this->module]['list'] ?? 'none');
        }

        if (! $hasAccessToModuleIndex) {
            return null;
        }

        return Navigation::buildUrl([
            $this->moduleParam => $this->module,
            'type' => '',
            'type_section' => '',
        ]);
    }

    /**
     * @return string|null
     */
    private function getControllerUrl(): ?string
    {
        try {
            $rights = $this->getUser()->getRights();
        } catch (\Exception $e) {
            return null;
        }

        $controllerRightsKey = "{$this->module}_{$this->controller}";
        $hasAccessToControllerList =  'none' !== ($rights[$controllerRightsKey]['list'] ?? 'none')
            && ('none' !== ($rights[$this->module] ?? 'none') || !array_key_exists($this->module, $rights));

        if (! $hasAccessToControllerList) {
            return null;
        }

        return Navigation::buildUrl([
            $this->moduleParam => $this->module,
            $this->controllerParam => $this->controller,
            'type' => '',
            'type_section' => '',
        ]);
    }

    /**
     * @return string|null
     */
    private function getTypeUrlWithModel(): ?string
    {
        try {
            $rights = $this->getUser()->getRights();
        } catch (\Exception $e) {
            return null;
        }

        $modelType = $this->model->get('type');

        $typeRightsKey = "{$this->module}" . ($this->controller !== $this->module ? "_{$this->controller}" : '') . "{$modelType}";
        if ('none' === ($rights[$typeRightsKey]['list'] ?? 'none')) {
            return null;
        }
        $urlParams = [
            $this->moduleParam => $this->module,
        ];

        if (!empty($this->controller) && $this->controller !== $this->module) {
            if ('none' === ($rights["{$this->module}_{$this->controller}"]['list'] ?? 'none')) {
                return null;
            }
            $urlParams[$this->controllerParam] = $this->controller;
        }

        $urlParams['type'] = $modelType;

        return Navigation::buildUrl($urlParams);
    }

    /**
     * @param string $legend
     * @param MenuItem $menuItem
     * @param int $limit
     * @param int $treshhold
     * @return void
     */
    private function setLimitedI18n(string $legend, MenuItem $menuItem, $limit=30, $treshhold=2): void
    {
        if (mb_strlen($legend) > $limit + $treshhold) {
            $menuItem->setI18n(General::mb_truncate($legend, $limit));
            $menuItem->setLegend($legend);
            return;
        }

        $menuItem->setI18n($legend);
    }

    /**
     * @return User
     * @throws \Exception
     */
    private function getUser(): User
    {
        if (isset($this->user)) {
            return $this->user;
        }

        if ($this->validLogin) {
            return $this->user = $this->registry['currentUser'];
        }

        throw new \Exception('No logged in user!');
    }

    /**
     * @return MenuItem
     */
    private function createHomeMenuItem(): MenuItem
    {
        if ($this->validLogin) {
            $itemHome = new MenuItem('home', Navigation::buildUrl([$this->moduleParam => 'index', 'index' => 'frontend']));
            $itemHome->setIcon('home');
            $itemHome->setLegend($this->i18n('menu_index_frontend'));
        } else {
            $itemHome = new MenuItem('home', Navigation::buildUrl([$this->moduleParam => 'auth', 'auth' => 'login']));
            $itemHome->setI18n($this->i18n('menu_auth_login'));
        }
        return $itemHome;
    }

    /**
     * @return MenuItem
     */
    private function createModuleMenuItem(): MenuItem
    {
        $itemModule = new MenuItem('module', $this->getModuleUrl());
        $this->setLimitedI18n($this->i18n($this->module) ?? '', $itemModule);
        $modulSubIcon = in_array($this->module, ['finance', 'nomenclatures']) ? 'account_tree' : 'list';
        $itemModule->setIcon($this->theme->getIconForRecord("{$this->module}")
            . ($itemModule->getUrl() ? "<span class=\"nz-glyph-sub\">{$modulSubIcon}</span>" : ''));
        return $itemModule;
    }

    /**
     * @return MenuItem
     */
    private function createControllerMenuItem(): MenuItem
    {
        $itemController = new MenuItem('controller', $this->getControllerUrl());
        $this->setLimitedI18n($this->i18n($this->module . '_' . $this->controller) ?? '', $itemController);
        $itemController->setIcon($this->theme->getIconForRecord("{$this->module}_{$this->controller}")
            . "<span class=\"nz-glyph-sub\">{$this->theme->getIconForAction("list")}</span>");
        return $itemController;
    }

    /**
     * @return MenuItem
     */
    private function createTypeMenuItem(): MenuItem
    {
        /** @var Model $model */
        $model = $this->model;
        $type = $model->getModelType();

        $url = $this->getTypeUrlWithModel();
        $itemType = new MenuItem('type_list', $url);
        $this->setLimitedI18n($type->get('name_plural'), $itemType);

        $itemType->setIcon($this->theme->getIconForRecord("{$this->module}_{$this->controller}")
            . ($url ? "<span class=\"nz-glyph-sub\">{$this->theme->getIconForAction("list")}</span>" : ''));
        return $itemType;
    }

    /**
     * @param string $fullLabel
     * @return MenuItem
     */
    private function createModelMenuItem(string $fullLabel): MenuItem
    {
        $modelName = General::singular2plural(strtolower($this->model->modelName));
        $model = explode('_', $modelName, 2);

        $urlParams = [
            $this->moduleParam => $model[0],
        ];
        if (count($model) > 1) {
            $actionParam = $model[1];
            $urlParams[$this->controllerParam] = $model[1];
        } else {
            $actionParam = $model[0];

        }
        $urlParams[$actionParam] = 'view';
        $urlParams['view'] = $this->model->get('id');

        $url = Navigation::buildUrl($urlParams);

        $itemModel = new MenuItem($this->model->modelName, $url);
        $this->setLimitedI18n($fullLabel, $itemModel);

        $iconKey = "{$this->model->module}";
        if (isset($model[1]) && $model[1] !== $this->model->module) {
            $iconKey .= "_{$model[1]}";
        }

        $itemModel->setIcon($this->theme->getIconForRecord($iconKey));

        return $itemModel;
    }

    /**
     * @return string
     */
    private function getModelItemLabel(): string
    {
        $ident = $this->model->getIdentifierStr();
        $descr = $this->model->getRecordDescriptionStr();

        if (! $descr) {
            return $ident;
        }

        if ($ident) {
            return "{$ident} - {$descr}";
        }

        return $descr;
    }

    /**
     * @return string
     */
    private function getActionItemLabel(): string
    {
        if ($this->action === 'branches' && $this->model) {
            $label = $this->model->getBranchLabels('branches');
        } elseif ($this->module === 'finance') {
            if ($this->controller === 'payments' && $this->action === 'multiadd') {
                $label = $this->i18n($this->module . '_' . $this->controller . '_' . $this->action);
            } elseif ($this->action === 'list') {
                if (!empty($_GET['type'])) {
                    $modelClass = General::plural2singular($this->module . '_' . $this->controller);
                    $modelTmp = new $modelClass($this->registry, ['type' => $_GET['type']]);
                    $label = $modelTmp->getModelType()->get('name_plural');
                } else {
                    $label = $this->i18n($this->module . '_' . $this->controller);
                }
            }
        }
        return $label??$this->i18n($this->action);
    }

    /**
     * @param string $label
     * @return MenuItem
     */
    private function createActionMenuItem(string $label): MenuItem
    {
        $itemAction = new MenuItem($this->action, '');
        $itemAction->setI18n($label);
        $itemAction->setIcon($this->theme->getIconForAction($this->action));
        return $itemAction;
    }
}
