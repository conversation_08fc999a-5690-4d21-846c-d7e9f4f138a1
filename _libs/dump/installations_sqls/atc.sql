########################################################################################
### SQL nZoom Specific Updates - Еър Трейд Център ООД (http://atc.n-zoom.com/) ###
########################################################################################

######################################################################################
# 2015-03-19 - Added new automation 'changeRelatedStatusDocument' for ATC installation

# Added new automation 'changeRelatedStatusDocument' for ATC installation
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Смяна на статус на свързаните документи', 0, NULL, 1, 'documents', NULL, 'action', '3', 'document_var_case := dosie_engine_id\r\nstatus_sold := 11\r\nstatus_returned := 12', 'condition := ((''[action]'' == ''add'') || (''[action]'' == ''edit'')) && [request_is_post]', 'plugin := atc\r\nmethod := changeRelatedStatusDocument', NULL, 0, 0, 1);

######################################################################################
# 2015-04-15 - Added javascript for barcode scanner

# Added javascript for barcode scanner
UPDATE `_fields_meta` SET `source`=CONCAT(source, '\r\n#javascript for barcode scanner\r\njavascript := function barcodeRead(e){e.which>=48&&e.which<=57?(0==barcode_chars.length&&(barcode_initial_element=document.activeElement,barcode_initial_value=document.activeElement.value),barcode_chars.push(String.fromCharCode(e.which))):barcode_chars.length>=10&&9==e.which&&Event.stop(e),0==barcode_pressed&&setTimeout(function(){if(barcode_chars.length>=10){var e=barcode_chars.join(""),a=!1,i=0;if($$(\'input[id^="air_serial_num_"]\').each(function(r){i++;var c=r.getValue();if(c==e||barcode_initial_value==e)throw c==e&&r.focus(),a=!0,barcode_initial_element.value=barcode_initial_value,$break;if(""==c)throw r.setValue(e),r.focus(),a=!0,barcode_initial_element.value=barcode_initial_value,$break}),!a){addField("var_group_1","","","1");var i=0;$$(\'input[id^="air_serial_num_"]\').each(function(r){if(i++,""==r.getValue())throw r.setValue(e),r.focus(),a=!0,barcode_initial_element.value=barcode_initial_value,$break})}}barcode_chars=[],barcode_pressed=!1},500),barcode_pressed=!0}var barcode_pressed=!1,barcode_chars=[],barcode_initial_value=barcode_initial_element=null;document.observe("dom:loaded",function(){Event.observe(window,"keydown",barcodeRead)});') WHERE  `name`='air_group' AND model_type=1 AND model='Document' AND source NOT LIKE '%barcodeRead%';

######################################################################################
# 2015-06-19 - Added new dashlet plugin: "atc_upcoming_prophylactics"

# Added new dashlet plugin: "atc_upcoming_prophylactics"
INSERT INTO `dashlets_plugins` (`type`, `settings`) VALUES
  ('atc_upcoming_prophylactics', 'doc_type_warranty_card := 3\r\ndoc_warranty_card_substatus := 8\r\ndate_visit_disallow_date_before_days := -13\r\ndate_visit_disallow_date_after_days := +0\r\n\r\nfield_doc_prop_prophylaxis_one := prophylaxis_one\r\nfield_doc_prop_prophylaxis_one__dateplan := prophylaxis_one__dateplan\r\nfield_doc_prop_prophylaxis_one__datereal := prophylaxis_one__datereal\r\nfield_doc_prop_prophylaxis_one__cust := prophylaxis_one__cust\r\nfield_doc_prop_prophylaxis_two := prophylaxis_two\r\nfield_doc_prop_prophylaxis_two__dateplan := prophylaxis_two__dateplan\r\nfield_doc_prop_prophylaxis_two__datereal := prophylaxis_two__datereal\r\nfield_doc_prop_prophylaxis_two__cust := prophylaxis_two__cust\r\nfield_doc_prop_prophylaxis_three := prophylaxis_three\r\nfield_doc_prop_prophylaxis_three__dateplan := prophylaxis_three__dateplan\r\nfield_doc_prop_prophylaxis_three__datereal := prophylaxis_three__datereal\r\nfield_doc_prop_prophylaxis_three__cust := prophylaxis_three__cust\r\nfield_doc_prop_prophylaxis_four := prophylaxis_four\r\nfield_doc_prop_prophylaxis_four__dateplan := prophylaxis_four__dateplan\r\nfield_doc_prop_prophylaxis_four__datereal := prophylaxis_four__datereal\r\nfield_doc_prop_prophylaxis_four__cust := prophylaxis_four__cust\r\nfield_doc_prop_prophylaxis_five := prophylaxis_five\r\nfield_doc_prop_prophylaxis_five__dateplan := prophylaxis_five__dateplan\r\nfield_doc_prop_prophylaxis_five__datereal := prophylaxis_five__datereal\r\nfield_doc_prop_prophylaxis_five__cust := prophylaxis_five__cust\r\nfield_doc_prop_client_name := client_name\r\nfield_doc_prop_client_address := client_address\r\nfield_doc_prop_client_telephon := client_telephon\r\nfield_doc_prop_mash_kind_engine := mash_kind_engine\r\nfield_doc_prop_mash_type_engine := mash_type_engine\r\nfield_doc_prop_mash_air_model := mash_air_model\r\nfield_doc_prop_mash_air_serial_num := mash_air_serial_num\r\nfield_doc_prop_real_montaj_date := real_montaj_date');
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Предстоящи профилактики', 'Инфо панел за по-лесно проследяване на предстоящите профилактики на машини.',  'bg'),
  (LAST_INSERT_ID(), 'Upcoming preventions', 'Dashlet for easier tracking of machines upcoming preventions.', 'en');

######################################################################################
# 2015-07-02 - Added new dashlet plugin: "atc_unmounted_machines" and 2 automations related to it and deactivated one automation
#            - Added new report: "atc_machine_report"
#            - Adding roles permissions for the report "atc_machine_report"
#            - Add new setting for dashlet plugin: "atc_unmounted_machines"
#            - Add new settings for dashlet plugin: "atc_unmounted_machines"

# PRE-DEPLOYED # Added new dashlet plugin: "atc_unmounted_machines"
# INSERT INTO `dashlets_plugins` (`type`, `settings`) VALUES
#   ('atc_unmounted_machines', 'doc_type_machine_file := 2\r\ndoc_machine_file_substatus_unmounted := 4\r\ndoc_machine_file_substatus_released := 5\r\nfield_doc_machine_file_type_engine := type_engine\r\nfield_doc_machine_file_type_engine_id := type_engine_id\r\nfield_doc_machine_file_kind_engine := kind_engine\r\nfield_doc_machine_file_kind_engine_id := kind_engine_id\r\nfield_doc_machine_file_model_engine := model_engine\r\nfield_doc_machine_file_air_serial_num := air_serial_num\r\nfield_doc_machine_file_engine_desc := engine_desc\r\nfield_doc_machine_file_warranty_min := warranty_min\r\nfield_doc_machine_file_warranty_max := warranty_max\r\n\r\ndoc_type_warranty_card := 3\r\nfield_doc_warranty_card_dealer_name := dealer_name\r\nfield_doc_warranty_card_dealer_name_id := dealer_name_id\r\nfield_doc_warranty_card_dosie_engine := dosie_engine\r\nfield_doc_warranty_card_dosie_engine_id := dosie_engine_id\r\nfield_doc_warranty_card_mash_type_engine := mash_type_engine\r\nfield_doc_warranty_card_mash_type_engine_id := mash_type_engine_id\r\nfield_doc_warranty_card_mash_kind_engine := mash_kind_engine\r\nfield_doc_warranty_card_mash_kind_engine_id := mash_kind_engine_id\r\nfield_doc_warranty_card_mash_air_model := mash_air_model\r\nfield_doc_warranty_card_mash_air_serial_num := mash_air_serial_num\r\nfield_doc_warranty_card_warranty_min := warranty_min\r\nfield_doc_warranty_card_warranty_max := warranty_max\r\nfield_doc_warranty_card_mash_sell_date := mash_sell_date\r\n\r\ndoc_type_machine_release := 4\r\nfield_doc_machine_release_dosie_engine := dosie_engine\r\nfield_doc_machine_release_dosie_engine_id := dosie_engine_id\r\nfield_doc_machine_release_mash_type_engine := mash_type_engine\r\nfield_doc_machine_release_mash_type_engine_id := mash_type_engine_id\r\nfield_doc_machine_release_mash_kind_engine := mash_kind_engine\r\nfield_doc_machine_release_mash_kind_engine_id := mash_kind_engine_id\r\nfield_doc_machine_release_mash_air_model := mash_air_model\r\nfield_doc_machine_release_mash_air_serial_num := mash_air_serial_num\r\nfield_doc_machine_release_warranty_min := warranty_min\r\nfield_doc_machine_release_warranty_max := warranty_max\r\nfield_doc_machine_release_mash_sell_date := mash_sell_date\r\n\r\ncus_type_dealer := 2\r\nfield_cus_dealer_type_contragent := type_contragent\r\nfield_cus_dealer_type_contragent_option_value_dealer := 1');
# INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Немонтирани машини', 'Инфо панел за по-лесно проследяване на немонтираните машини.',  'bg'),
#   (LAST_INSERT_ID(), 'Unmounted machines', 'Dashlet for easier tracking of unmounted machines.', 'en');
# INSERT INTO automations (name, module, automation_type, start_model_type, settings, conditions, method, nums) VALUES
#   ('Актуализиране досие на машина след успешно добавяне на документ за издаване на машина.', 'documents', 'action', '4', 'machine_file_status := opened\r\nmachine_file_substatus := 5\r\nmachine_files_var := dosie_engine_id', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\' == \'1\'', 'plugin := atc\r\nmethod := updateMachineFileAfterMachineReleise', 0),
#   ('Актуализиране досие на машина след успешно добавяне на гаранционна карта.', 'documents', 'action', '3', 'machine_file_status := closed\r\nmachine_file_substatus := 11\r\nmachine_files_var := dosie_engine_id', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\' == \'1\'', 'plugin := atc\r\nmethod := updateMachineFileAfterWarrantyCard', 0);
# UPDATE automations
#   SET active = 0
#   WHERE method LIKE '%changeRelatedStatusDocument%';

# PRE-DEPLOYED # Added new report: "atc_machine_report"
# INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
# (321, 'atc_machine_report', 'report_filters_required := \r\nreport_meta_models := Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document\r\nreport_meta_model_types := 2,2,2,2,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3\r\nreport_meta_names := kind_engine,type_engine,model_engine,air_serial_num,dosie_engine_id,dosie_engine_id,real_montaj_date,client_name,client_address,client_telephon,prophylaxis_five__dateplan,prophylaxis_one__datereal,prophylaxis_two__datereal,prophylaxis_three__datereal,prophylaxis_four__datereal,prophylaxis_five__datereal,prophylaxis_one__cust,prophylaxis_two__cust,prophylaxis_three__cust,prophylaxis_four__cust,prophylaxis_five__cust\r\nreport_filters_multi := \r\namr_filters_dealer_type_id := 2\r\namr_filters_editor_type_id := 2\r\namr_filters_document_status_types := 2,3\r\namr_filters_document_type := 2\r\namr_filters_dealer_type_contragent := 1\r\namr_query_add_kind_engine := kind_engine_2\r\namr_query_add_type_engine := type_engine_2\r\namr_query_add_model_engine := model_engine_2\r\namr_query_add_air_serial_num := air_serial_num_2\r\namr_query_add_dosie_engine_issued_by := dosie_engine_id_4\r\namr_query_add_dosie_engine_warranty := dosie_engine_id_3\r\namr_query_add_real_montaj_date := real_montaj_date_3\r\namr_query_add_client_name := client_name_3\r\namr_query_add_client_address := client_address_3\r\namr_query_add_client_telephon := client_telephon_3\r\namr_query_add_prophylaxis_five__dateplan := prophylaxis_five__dateplan_3\r\namr_filters_document_install_type := 3\r\namr_query_add_prophylaxis_date_1 := prophylaxis_one__datereal_3\r\namr_query_add_prophylaxis_date_2 := prophylaxis_two__datereal_3\r\namr_query_add_prophylaxis_date_3 := prophylaxis_three__datereal_3\r\namr_query_add_prophylaxis_date_4 := prophylaxis_four__datereal_3\r\namr_query_add_prophylaxis_date_5 := prophylaxis_five__datereal_3\r\namr_query_add_prophylaxis_cust_1 := prophylaxis_one__cust_3\r\namr_query_add_prophylaxis_cust_2 := prophylaxis_two__cust_3\r\namr_query_add_prophylaxis_cust_3 := prophylaxis_three__cust_3\r\namr_query_add_prophylaxis_cust_4 := prophylaxis_four__cust_3\r\namr_query_add_prophylaxis_cust_5 := prophylaxis_five__cust_3\r\n', 0, 0, 1);
# INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
# (321, 'Machine Report', NULL, NULL, 'en'),
# (321, 'Досие на машина', NULL, NULL, 'bg');

# PRE-DEPLOYED # Adding roles permissions for the report "atc_machine_report"
# INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#   ('reports', 'generate_report', '321', '0', '1'),
#   ('reports', 'export', '321', '0', '2');

# PRE-DEPLOYED # INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#   SELECT '1', `id`, 'all'
#   FROM `roles_definitions`
#   WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '321';

# PRE-DEPLOYED # Add new setting for dashlet plugin: "atc_unmounted_machines"
# UPDATE dashlets_plugins
#   SET settings = REPLACE(settings, 'field_doc_warranty_card_mash_sell_date := mash_sell_date', 'field_doc_warranty_card_mash_sell_date := mash_sell_date\r\nfield_doc_warranty_card_warranty_all := warranty_all')
#   WHERE type = 'atc_unmounted_machines'
#     AND settings NOT LIKE '%field_doc_warranty_card_warranty_all := warranty_all%';

# PRE-DEPLOYED # Add new settings for dashlet plugin: "atc_unmounted_machines"
# UPDATE dashlets_plugins
#   SET settings = REPLACE(settings, 'field_doc_machine_release_mash_sell_date := mash_sell_date', 'field_doc_machine_release_mash_sell_date := mash_sell_date\r\nfield_doc_machine_release_released_by := released_by\r\nfield_doc_machine_release_released_by_id := released_by_id')
#   WHERE type = 'atc_unmounted_machines'
#     AND settings NOT LIKE '%field_doc_machine_release_released_by := released_by%';

######################################################################################
# 2015-07-10 - Add new setting for dashlet plugin: "atc_unmounted_machines"
#            - Changed automations for updating documents of type "Machine file"

# Add new setting for dashlet plugin: "atc_unmounted_machines"
UPDATE dashlets_plugins
  SET settings = REPLACE(settings, 'field_doc_warranty_card_dealer_name_id := dealer_name_id', 'field_doc_warranty_card_dealer_name_id := dealer_name_id\r\nfield_doc_warranty_card_dealer_name_address := dealer_name_address')
  WHERE type = 'atc_unmounted_machines'
    AND settings NOT LIKE '%field_doc_warranty_card_dealer_name_address := dealer_name_address%';

# Changed automations for updating documents of type "Machine file"
UPDATE automations
  SET name = 'Актуализиране досие на машина след успешно добавяне на документ за издаване на машина или гаранционна карта.',
    method = 'plugin := atc\r\nmethod := updateMachineFileAfterMachineReleaseAndWarrantyCard',
    settings = CONCAT(settings, '\r\ndoc_type_machine_release := 4\r\ndoc_type_warranty_card := 3')
  WHERE method LIKE '%method := updateMachineFileAfterMachineReleise%'
    OR method LIKE '%method := updateMachineFileAfterWarrantyCard%';

######################################################################################
# 2015-07-23 - Removed settings settings from dashlet "atc_upcoming_prophylactics"

# Removed settings settings from dashlet "atc_upcoming_prophylactics"
UPDATE dashlets_plugins
  SET settings = REPLACE(settings, '\r\nfield_doc_prop_prophylaxis_five := prophylaxis_five\r\nfield_doc_prop_prophylaxis_five__dateplan := prophylaxis_five__dateplan\r\nfield_doc_prop_prophylaxis_five__datereal := prophylaxis_five__datereal\r\nfield_doc_prop_prophylaxis_five__cust := prophylaxis_five__cust', '')
  WHERE `type` = 'atc_upcoming_prophylactics'
    AND settings LIKE '%field_doc_prop_prophylaxis_five := prophylaxis_five%';

######################################################################################
# 2015-07-28 - Update settings for report 'atc_machine_report'

# Update settings for report 'atc_machine_report'
UPDATE `reports`
  SET `settings` = 'report_filters_required := \r\nreport_meta_models := Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document,Document\r\nreport_meta_model_types := 2,2,2,2,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,1,1\r\nreport_meta_names := kind_engine,type_engine,model_engine,air_serial_num,dosie_engine_id,dosie_engine_id,real_montaj_date,client_name,client_address,client_telephon,prophylaxis_five__dateplan,prophylaxis_one__datereal,prophylaxis_two__datereal,prophylaxis_three__datereal,prophylaxis_four__datereal,prophylaxis_five__datereal,prophylaxis_one__cust,prophylaxis_two__cust,prophylaxis_three__cust,prophylaxis_four__cust,prophylaxis_five__cust,dilur_name,dilur_name_id\r\nreport_filters_multi := \r\namr_filters_dealer_type_id := 2\r\namr_filters_editor_type_id := 2\r\namr_filters_document_status_types := 2,3\r\namr_filters_document_type := 2\r\namr_filters_dealer_type_contragent := 1\r\namr_query_add_kind_engine := kind_engine_2\r\namr_query_add_type_engine := type_engine_2\r\namr_query_add_model_engine := model_engine_2\r\namr_query_add_air_serial_num := air_serial_num_2\r\namr_query_add_dosie_engine_issued_by := dosie_engine_id_4\r\namr_query_add_dosie_engine_warranty := dosie_engine_id_3\r\namr_query_add_real_montaj_date := real_montaj_date_3\r\namr_query_add_client_name := client_name_3\r\namr_query_add_client_address := client_address_3\r\namr_query_add_client_telephon := client_telephon_3\r\namr_query_add_prophylaxis_five__dateplan := prophylaxis_five__dateplan_3\r\namr_filters_document_install_type := 3\r\namr_query_add_prophylaxis_date_1 := prophylaxis_one__datereal_3\r\namr_query_add_prophylaxis_date_2 := prophylaxis_two__datereal_3\r\namr_query_add_prophylaxis_date_3 := prophylaxis_three__datereal_3\r\namr_query_add_prophylaxis_date_4 := prophylaxis_four__datereal_3\r\namr_query_add_prophylaxis_date_5 := prophylaxis_five__datereal_3\r\namr_query_add_prophylaxis_cust_1 := prophylaxis_one__cust_3\r\namr_query_add_prophylaxis_cust_2 := prophylaxis_two__cust_3\r\namr_query_add_prophylaxis_cust_3 := prophylaxis_three__cust_3\r\namr_query_add_prophylaxis_cust_4 := prophylaxis_four__cust_3\r\namr_query_add_prophylaxis_cust_5 := prophylaxis_five__cust_3\r\namr_query_add_dilur_name_id := dilur_name_id_1\r\namr_query_add_dilur_name := dilur_name_1\r\nreport_pagination_items_per_page := 20\r\namr_filters_document_issued_type := 4\r\namr_filters_document_sold_from_storage_type := 1\r\n'
  WHERE `reports`.`id` = 321;

######################################################################################
# 2015-08-27 - Fixed the barcode scanner javascript

# Fixed the barcode scanner javascript
UPDATE  _fields_meta
    SET `source` = REPLACE(source, 't={field:"air_serial_num"}', 't={field:"air_serial_num",scope:""};')
    WHERE source LIKE '%t={field:"air_serial_num"}%';

######################################################################################
# 2016-05-13 - Fixed date placeholders in emails templates

# Fixed date placeholders in emails templates
UPDATE `emails_i18n` SET `body` = REPLACE(`body`, '[a_prophylaxis_one__dateplan<span style="background-color:rgb(255, 255, 255)">|date_format:%d.%m.%Y</span>]', '[a_prophylaxis_one__dateplan|date_format:%d.%m.%Y]') WHERE `parent_id` BETWEEN 1001 AND 1004 AND `lang` = 'bg';
UPDATE `emails_i18n` SET `body` = REPLACE(`body`, '[a_prophylaxis_two__dateplan<span style="color:rgb(0, 0, 0)"><span style="background-color:rgb(255, 255, 255)">|date_format:%d.%m.%Y</span></span>]', '[a_prophylaxis_two__dateplan|date_format:%d.%m.%Y]') WHERE `parent_id` BETWEEN 1001 AND 1004 AND `lang` = 'bg';
UPDATE `emails_i18n` SET `body` = REPLACE(`body`, '[a_prophylaxis_three__dateplan<span style="color:rgb(0, 0, 0)"><span style="background-color:rgb(255, 255, 255)">|date_format:%d.%m.%Y</span></span>]', '[a_prophylaxis_three__dateplan|date_format:%d.%m.%Y]') WHERE `parent_id` BETWEEN 1001 AND 1004 AND `lang` = 'bg';
UPDATE `emails_i18n` SET `body` = REPLACE(`body`, '[a_prophylaxis_four__dateplan<span style="color:rgb(0, 0, 0)"><span style="background-color:rgb(255, 255, 255)">|date_format:%d.%m.%Y</span></span>]', '[a_prophylaxis_four__dateplan|date_format:%d.%m.%Y]') WHERE `parent_id` BETWEEN 1001 AND 1004 AND `lang` = 'bg';

######################################################################################
# 2015-08-29 - Added new automation 'duplicateProtocols' for ATC installation

# Added new automation 'duplicateProtocols' for ATC installation
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  ('Дублиране на протоколи', 0, NULL, 1, 'documents', NULL, 'action', '1', '', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\'', 'plugin := atc\r\nmethod := duplicateProtocols', NULL, 0, 0, 1);

UPDATE `automations` SET `conditions` = "condition := ('[action]' == 'add' || '[action]' == 'edit') && '[request_is_post]' && '[b_custom_num]' != ''" WHERE `method` LIKE 'plugin := atc%method := duplicateProtocols';

######################################################################################
# 2016-09-10 - Added new automation `changeStatusIfDeleted` for ATC installation
#            - Added new automation `changeStatusWhenDeactivate` for ATC installation

# Added new automations `changeStatusIfDeleted` and `changeStatusWhenDeactivate`
INSERT IGNORE INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `nums`, `active`) VALUES
  ('Досиета към статус немонтирана след премахване от Гаранционна карта', 'documents', 'action', '3', '', 'condition := \'[request_is_post]\' && \'[action]\' == \'edit\'', 'plugin := atc\r\nmethod := changeStatusIfDeleted', 0, 1),
  ('Досиета към статус немонтирана след деактивиране на Гаранционна карта', 'documents', 'crontab', '3', 'minutes_interval := 30', 'condition := 1', 'plugin := atc\r\nmethod := changeStatusWhenDeactivate', 0, 1);

######################################################################################
# 2016-10-06 - Added the report `bgservice_requests` to ATC installation

# Added the report `bgservice_requests` to ATC installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (211, 'bgservice_requests', 'type_request_client := 5\r\ntype_request :=\r\nrequest_client_description := work_done_desc\r\nrequest_description :=\r\nworking_time_starts := 09:00\r\nworking_time_ends := 18:00\r\nbugzilla_base_link :=\r\n\r\nusers_departments := 1\r\nhide_deadline_column := 1', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (211, 'Заявки', NULL, NULL, 'bg'),
  (211, 'Requests', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '211', '0', '1'),
  ('reports', 'export', '211', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '211';

######################################################################################
# 2016-11-14 - Added new report `atc_service_orders_analysis` to ATC installation

# Added new report `atc_service_orders_analysis` to ATC installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (357, 'atc_service_orders_analysis', 'document_service_order := 5\r\nnom_type_machine_type_id := 5\r\nnom_kind_machine_type_id := 6\r\nnom_diagnostic_result_type_id := 9\r\nnom_problem_type_id := 8\r\nnom_part_type_id := 10\r\n\r\ndoc_add_var_datetime_add := doc_datetime_add\r\ndoc_add_var_type_machine := mash_type_engine_id\r\ndoc_add_var_kind_machine := mash_kind_engine_id\r\ndoc_add_var_diagnosis_result := diagnosis_result_id\r\ndoc_add_var_problem_type := problem_type_id\r\ndoc_add_var_part := part_id\r\ndoc_add_var_installation_date := real_montaj_date\r\ndoc_add_var_warranty_period := warranty_all\r\ndoc_add_var_warranty_status := warranty_status', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (357, 'Анализ на сервизни поръчки', NULL, NULL, 'bg'),
  (357, 'Service orders analysis', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '357', '0', '1'),
  ('reports', 'export', '357', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '357';

######################################################################################
# 2016-11-16 - Added settings (custom_javascript and execute_after) to toggle readonly some of the variables depending on whether the engine_serial_name (ID: 503) has value or not

# Added settings (custom_javascript and execute_after) to toggle readonly some of the variables depending on whether the engine_serial_name (ID: 503) has value or not
UPDATE `_fields_meta` SET source = 'autocomplete := autocompleters\r\nautocomplete_plugin_search := customQuery\r\nautocomplete_plugin_param_sql := SELECT dc.value as ID , dc2.value as SERNUM, dc3.value as MASHTYPE, dc4.value as MASHTYPEID, dc5.value as MASHKIND, dc6.value as MASHKINDID, dc7.value as MODEL, d.customer as MONTAJID, ci.name as MONTAJ, dc8.value as DEALERID, dc9.value as DEALER,  dc10.value as REALDATE, dc11.value as ENDCUST, dc12.value as ENDCUSTADR, dc13.value as ENDCUSTPHONE, d.full_num as WarrantyNum, min(dc14.value) as PROFILAKPLAN, max(dc15.value) as PROFILAKREAL, dc16.value as WarrantyAll, dc17.value as SERIALNUMBER, ds.name as STATUSNAME from documents_cstm dc join documents d on dc.model_id = d.id AND d.type = 3 AND d.active = 1 AND d.deleted_by = 0 AND dc.var_id = 315 join documents_cstm dc2 on dc.model_id = dc2.model_id AND dc2.var_id = 316 AND dc.num = dc2.num AND dc2.value LIKE "%<search_string_parts>%" join documents_cstm dc3 on dc.model_id = dc3.model_id AND dc3.var_id = 317 AND dc.num = dc3.num join documents_cstm dc4 on dc.model_id = dc4.model_id AND dc4.var_id = 355 AND dc.num = dc4.num join documents_cstm dc5 on dc.model_id = dc5.model_id AND dc5.var_id = 318 AND dc.num = dc5.num join documents_cstm dc6 on dc.model_id = dc6.model_id AND dc6.var_id = 356 AND dc.num = dc6.num join documents_cstm dc7 on dc.model_id = dc7.model_id AND dc7.var_id = 319 AND dc.num = dc7.num join customers_i18n ci on d.customer = ci.parent_id left join documents_cstm dc8 on dc.model_id = dc8.model_id AND dc8.var_id = 353 left join documents_cstm dc9 on dc.model_id = dc9.model_id AND dc9.var_id = 354 join documents_cstm dc10 on dc.model_id = dc10.model_id AND dc10.var_id = 312 left join documents_cstm dc11 on dc.model_id = dc11.model_id AND dc11.var_id = 307 left join documents_cstm dc12 on dc.model_id = dc12.model_id AND dc12.var_id = 308 left join documents_cstm dc13 on dc.model_id = dc13.model_id AND dc13.var_id = 309 left join documents_cstm dc14 on dc.model_id = dc14.model_id AND dc14.var_id IN (326, 330, 334, 338, 342) AND dc14.value >= DATE_SUB(NOW(),INTERVAL 1 YEAR) left join documents_cstm dc15 on dc.model_id = dc15.model_id AND dc15.var_id IN (327, 331, 335, 339, 343)  join documents_cstm dc16 on dc.model_id = dc16.model_id AND dc16.var_id = 313 join documents_cstm dc17 on dc.model_id = dc17.model_id AND dc.num = dc17.num AND dc17.var_id = 320 join documents_statuses ds on d.substatus = ds.id AND ds.lang = \'bg\'\r\nautocomplete_fill_options := $engine_serial_id => <id>\r\nautocomplete_fill_options := $engine_serial_name => <SERNUM>\r\nautocomplete_fill_options := $mash_type_engine => <MASHTYPE>\r\nautocomplete_fill_options := $mash_type_engine_id => <MASHTYPEID>\r\nautocomplete_fill_options := $mash_kind_engine => <MASHKIND>\r\nautocomplete_fill_options := $mash_kind_engine_id => <MASHKINDID>\r\nautocomplete_fill_options := $mash_air_model => <MODEL>\r\nautocomplete_fill_options := $mash_air_serial_num => <SERIALNUMBER>\r\nautocomplete_fill_options := $dealer_name_id => <DEALERID>\r\nautocomplete_fill_options := $dealer_name => <DEALER>\r\nautocomplete_fill_options := $mounted_by_id => <MONTAJID>\r\nautocomplete_fill_options := $mounted_by_name => <MONTAJ>\r\nautocomplete_fill_options := $real_montaj_date => <REALDATE>\r\nautocomplete_fill_options := $client_name => <ENDCUST>\r\nautocomplete_fill_options := $client_address => <ENDCUSTADR>\r\nautocomplete_fill_options := $client_telephon => <ENDCUSTPHONE>\r\nautocomplete_fill_options := $prophylaxis_dateplan => <PROFILAKPLAN>\r\nautocomplete_fill_options := $prophylaxis_datereal => <PROFILAKREAL>\r\nautocomplete_fill_options := $warranty_number => <WarrantyNum>\r\nautocomplete_fill_options := $warranty_all => <WarrantyAll>\r\nautocomplete_fill_options := $warranty_status => <STATUSNAME>\r\nautocomplete_suggestions := <SERNUM> (<MASHTYPE>, <MASHKIND>, <MODEL>)\r\nautocomplete_min_chars := 11\r\nautocomplete_clear := 1\r\n#autocomplete_check_user_permissions := 1\r\n\r\nautocomplete_execute_after := toggleReadonly([\'mash_type_engine\', \'mash_kind_engine\', \'mash_air_model\', \'mash_air_serial_num\', \'dealer_name\', \'mounted_by_name\', \'real_montaj_date\', \'client_name\', \'client_address\', \'client_telephon\', \'warranty_number\', \'warranty_all\', \'prophylaxis_dateplan\', \'prophylaxis_datereal\', \'warranty_status\'], $(\'engine_serial_name\').value != \'\');\r\nautocomplete_custom_javascript := document.observe(\'dom:loaded\', function() {toggleReadonly([\'mash_type_engine\', \'mash_kind_engine\', \'mash_air_model\', \'mash_air_serial_num\', \'dealer_name\', \'mounted_by_name\', \'real_montaj_date\', \'client_name\', \'client_address\', \'client_telephon\', \'warranty_number\', \'warranty_all\', \'prophylaxis_dateplan\', \'prophylaxis_datereal\', \'warranty_status\'], $(\'engine_serial_name\').value != \'\');});\r\n' WHERE id=503;

######################################################################################
# 2017-02-08 - Added automation that manage relations between documents with type 5 and documents with type 2

# PRE-DEPLOYED # Added automation that manage relations between documents with type 5 and documents with type 2
#INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#  ('Добавяне на връзки за Досие на машина', 'documents', 'action', '5', 'model_type := 2', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[prev_a_mash_air_serial_num]\' != \'[a_mash_air_serial_num]\'', 'plugin := atc\r\nmethod := addRelationServiceOrders', NULL, 0, 0, 1);

######################################################################################
# 2017-03-07 - Added automation for validating documents of type "Warranty card"

# Added automation for validating documents of type "Warranty card"
DELETE FROM automations WHERE module = 'documents' AND start_model_type = '3' AND method LIKE '%validateWarrantyCard%';
INSERT INTO automations
  SET name = 'Валидация на гаранционни карти',
    module = 'documents',
    start_model_type = '3',
    automation_type = 'before_action',
    settings = '# ДП в гаранционните карти, в която се съхранява серийния номер на машините\r\nfield_mash_air_serial_num := mash_air_serial_num',
    conditions = 'condition := \'[request_is_post]\' && (\'[action]\' == \'add\' || \'[action]\' == \'edit\')',
    method = 'plugin := atc\r\nmethod := validateWarrantyCard',
    after_action = 'cancel_action_on_fail := 1',
    nums = 0,
    active = 0;

######################################################################################
# 2017-08-10 - Add automation for hiding the "Add" tab for documents of type 3

# Add automation for hiding the "Add" tab for documents of type 3
DELETE FROM automations
  WHERE method LIKE '%changePermissions%';
INSERT INTO automations
  SET name = 'Скриване на таб „Добавяне“.',
    module = 'documents',
    start_model_type = '3',
    automation_type = 'before_action',
    settings = '',
    conditions = 'condition := 1',
    method = 'plugin := atc\r\nmethod := changePermissions',
    nums = 0;

######################################################################################
# 2017-08-22 - Rename automation changePermissions to beforeWarrantyCard
#            - Add automation beforeWarrantyCard as before_action to hide the "Add" tab
#            - Readd automations for preventing from manual adding of warranty cards

# Add automation for hiding the "Add" tab for documents of type 3
UPDATE automations
  SET method = REPLACE(method, 'changePermissions', 'beforeWarrantyCard'),
    name = 'Защита от ръчно добавяне на гаранционни карти'
  WHERE method LIKE '%changePermissions%';
# Add automation beforeWarrantyCard as before_action to hide the "Add" tab
DELETE FROM automations
  WHERE method LIKE '%beforeWarrantyCard%'
    AND automation_type = 'before_action';
INSERT INTO automations
  SET name = 'Защита от ръчно добавяне на гаранционни карти',
    module = 'documents',
    start_model_type = '3',
    automation_type = 'before_action',
    settings = '',
    conditions = 'condition := 1',
    method = 'plugin := atc\r\nmethod := beforeWarrantyCard',
    nums = 0;

# Readd automations for preventing from manual adding of warranty cards
DELETE FROM automations
  WHERE method LIKE '%beforeWarrantyCard%';
INSERT INTO automations (name, module, start_model_type, automation_type, settings, conditions, method, nums) VALUES
  ('Защита от ръчно добавяне на гаранционни карти.', 'documents', '3', 'before_viewer', '', 'condition := 1', 'plugin := atc\r\nmethod := beforeWarrantyCard', 0),
  ('Скриване на таб „Добавяне“.', 'documents', '3', 'before_action', '', 'condition := 1', 'plugin := atc\r\nmethod := beforeWarrantyCard', 0);

######################################################################################
# 2019-05-09 - Update settings of automation addRelationServiceOrders

# Update settings of automation addRelationServiceOrders
UPDATE automations
  SET settings = ''
  WHERE method LIKE '%addRelationServiceOrders%';

######################################################################################
# 2019-07-08 - Add automation shouldWarrantyGenerate as before_action to check whether warranty fits the requirements for selected pattern generation

 INSERT INTO automations (name, module, start_model_type, automation_type, settings, conditions, method, nums) VALUES
  ('Проверка дали даден шаблон може да се генерира.', 'documents', '3', 'before_action', '# Кой тип гаранции да се позволяват\r\nwarranty_max := 2\r\n# При кой шаблон\r\npattern := 2', 'condition := \'[action]\' == \'generate\'', 'plugin := atc\r\nmethod := shouldWarrantyGenerate', 0);

# Add action=>print as additional condition
UPDATE automations SET conditions = 'condition := \'[action]\' == \'generate\' || \'[action]\' == \'print\'' WHERE method LIKE '%shouldWarrantyGenerate%';

######################################################################################
# 2021-06-02 - Added new setting for auto export for certain number of results and additional settings values for required fields for "atc_machine_report"

# Added new setting for auto export for certain number of results and additional settings values for required fields for "atc_machine_report"
UPDATE reports
  SET settings = REPLACE(settings, '\r\nreport_meta_models :=', 'period_from, period_to, status\r\nreport_meta_models :=')
  WHERE `type` = 'atc_machine_report' AND settings NOT LIKE '%period_from%';
UPDATE reports
  SET settings = CONCAT('export_over := 100\r\n\r\n', settings)
  WHERE `type` = 'atc_machine_report' AND settings NOT LIKE '%export_over%';

######################################################################################
# 2021-11-22 - Change conditions for automation: updateMachineFileAfterMachineReleaseAndWarrantyCard

# Change conditions for automation: updateMachineFileAfterMachineReleaseAndWarrantyCard
UPDATE `automations`
  SET `conditions` = "condition := '[request_is_post]'\r\ncondition := preg_match('/add/', '[action]')"
  WHERE `method` LIKE '%updateMachineFileAfterMachineReleaseAndWarrantyCard%';

######################################################################################
# 2021-11-30 - Replaced chackbox_group users_to_assign variable (115, 304) to text
#            - Added automations to set variables as they should be set by a javascript (commented in autocompleters customer/dilur_name)
#            - Commented javascript that works with checkbox_group in customer/dilur_name

# Replaced chackbox_group users_to_assign variable (115, 304) to text
INSERT INTO documents_cstm
SELECT model_id, 115000, 1, GROUP_CONCAT(value), added, added_by, modified, modified_by, lang FROM documents_cstm WHERE var_id=115 GROUP BY model_id;
DELETE FROM documents_cstm WHERE var_id=115;
UPDATE documents_cstm SET var_id=115 WHERE var_id=115000;
UPDATE`_fields_meta` SET `source`='', `type`='text', `table` = 0 WHERE  `id`=115;

INSERT INTO documents_cstm
SELECT model_id, 304000, 1, GROUP_CONCAT(value), added, added_by, modified, modified_by, lang FROM documents_cstm WHERE var_id=304 GROUP BY model_id;
DELETE FROM documents_cstm WHERE var_id=304;
UPDATE documents_cstm SET var_id=304 WHERE var_id=304000;
UPDATE`_fields_meta` SET `source`='', `type`='text', `table` = 0 WHERE  `id`=304;

# Added automations to set variables as they should be set by a javascript (commented in autocompleters customer/dilur_name)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Установяване на потребители към дилър за продажба от склад', 0, NULL, 1, 'documents', NULL, 'action', '1', '', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\' && (\'[a_dilur_name_id]\' != \'[prev_a_dilur_name_id]\' ||  \'[b_customer]\' != \'[prev_b_customer]\')', 'method := setAdditionalVar\r\nvar_name := users_to_assign\r\nvar_value := php(Calculator::calc_sql($registry, \'SELECT GROUP_CONCAT(id) FROM users where default_customer!=0 AND (default_customer="[b_customer]" OR default_customer=(SELECT value FROM documents_cstm WHERE var_id=116 AND model_id=[b_id] AND num=1 AND lang="")) AND deleted=0 AND active=1\'))\r\n', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%setAdditionalVar%' AND start_model_type=1 AND method LIKE '%users_to_assign%');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Установяване на потребители към монтажист за гаранционна карта', 0, NULL, 1, 'documents', NULL, 'action', '3', '', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\' && \'[b_customer]\' != \'[prev_b_customer]\'', 'method := setAdditionalVar\r\nvar_name := users_to_assign\r\nvar_value := php(Calculator::calc_sql($registry, \'SELECT GROUP_CONCAT(id) FROM users where default_customer!=0 AND default_customer="[b_customer]" AND deleted=0 AND active=1\'))', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%setAdditionalVar%' AND start_model_type=3 AND method LIKE '%users_to_assign%');

# Commented javascript that works with checkbox_group in customer/dilur_name
UPDATE _fields_meta SET source=replace(source, 'autocomplete_execute_after', '#autocomplete_execute_after') WHERE name IN ('customer', 'dilur_name') AND model_type IN (1,3,4) AND source NOT LIKE '%#autocomplete_execute_after%';

######################################################################################
# 2022-01-13 - Added automation for nzoom2SAPCustomerSync

# Added automation for nzoom2SAPCustomerSync
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на дилъри/монтажни фирми към SAP', 0, NULL, 1, 'customers', NULL, 'before_action', '2', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\'', 'plugin := atc\r\nmethod := nzoom2SAPCustomerSync\r\n', 'cancel_action_on_fail := 1', 0, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2SAPCustomerSync%' AND start_model_type=2);

# Create custom table for logs
CREATE TABLE IF NOT EXISTS `sap_debmas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `customer_id` INT NOT NULL DEFAULT '0',
  `filename` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `path` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `xml` LONGTEXT NOT NULL COLLATE 'utf8_unicode_ci',
  `added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COLLATE='utf8_unicode_ci' ENGINE=InnoDB;

######################################################################################
# 2022-01-18 - Changed nzoom2SAPCustomerSync to be action automation instead of before_action

# Changed nzoom2SAPCustomerSync to be action automation instead of before_action
UPDATE `automations` SET `after_action`='', automation_type='action' WHERE   method LIKE '%nzoom2SAPCustomerSync%';

######################################################################################
# 2022-01-27 - Added direction to the sap_debmas DB table

# Added direction to the sap_debmas DB table
ALTER TABLE `sap_debmas`
    ADD COLUMN `direction` ENUM('outgoing','incoming') NOT NULL DEFAULT 'outgoing' AFTER `id`;

######################################################################################
# 2022-02-09 - Added automation to sync customers from SAP to nZoom

# Added automation to sync customers from SAP to nZoom
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на дилъри/монтажни фирми от SAP', 0, NULL, 1, 'customers', NULL, 'crontab', '2', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n\r\nlog_stats := 1\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := atc\r\nmethod := sap2NzoomCustomerSync\r\n', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%sap2NzoomCustomerSync%' AND start_model_type=2);

######################################################################################
# 2022-03-09 - Added automation to sync service orders from nZoom to SAP
#            - Added automation to sync order confirmations from nZoom to SAP

# Added automation to sync service orders from nZoom to SAP
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на сервизни поръчки към SAP', 0, NULL, 1, 'documents', NULL, 'action', '7', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n', 'condition := \'[request_is_post]\' == \'1\' && in_array(\'[action]\', array(\'setstatus\'))\r\ncondition := $request->get(\'substatus\') == \'closed_24\'', 'plugin := atc\r\nmethod := nzoom2SAPOrderSync\r\n', NULL, 0, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2SAPOrderSync%' AND start_model_type=7);

# Create custom table for logs of sap orders
CREATE TABLE IF NOT EXISTS `sap_iorder` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `direction` ENUM('outgoing','incoming') NOT NULL DEFAULT 'outgoing',
  `order_id` INT NOT NULL DEFAULT '0',
  `filename` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `path` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `xml` LONGTEXT NOT NULL COLLATE 'utf8_unicode_ci',
  `added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COLLATE='utf8_unicode_ci' ENGINE=InnoDB;

# Added automation to sync order confirmations from nZoom to SAP
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на потвърждения към SAP', 0, NULL, 1, 'documents', NULL, 'action', '8', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n', 'condition := \'[request_is_post]\' == \'1\' && in_array(\'[action]\', array(\'setstatus\'))\r\ncondition := $request->get(\'substatus\') == \'closed_24\'', 'plugin := atc\r\nmethod := nzoom2SAPConfirmationSync\r\n', NULL, 0, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2SAPConfirmationSync%' AND start_model_type=8);

# Create custom table for logs of sap confirmations
CREATE TABLE IF NOT EXISTS `sap_conf` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `direction` ENUM('outgoing','incoming') NOT NULL DEFAULT 'outgoing',
  `confirmation_id` INT NOT NULL DEFAULT '0',
  `filename` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `path` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `xml` LONGTEXT NOT NULL COLLATE 'utf8_unicode_ci',
  `added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COLLATE='utf8_unicode_ci' ENGINE=InnoDB;

######################################################################################
# 2022-03-29 - Added automation to sync service materials from nZoom to SAP

# Added automation to sync service orders from nZoom to SAP
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на вложени материали по сервизни поръчки към SAP', 0, NULL, 1, 'documents', NULL, 'action', '8', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n\r\n#measurements\r\nmeasurement_1 := БР\r\nmeasurement_2 := H\r\nmeasurement_3 := M\r\nmeasurement_4 := KG\r\n', 'condition := \'[request_is_post]\' == \'1\' && in_array(\'[action]\', array(\'setstatus\'))\r\ncondition := $request->get(\'substatus\') == \'closed_24\'', 'plugin := atc\r\nmethod := nzoom2SAPMaterialSync\r\n', NULL, 0, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2SAPMaterialSync%' AND start_model_type=8);

# Create custom table for logs of sap materials
CREATE TABLE IF NOT EXISTS `sap_wmmbid` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `direction` ENUM('outgoing','incoming') NOT NULL DEFAULT 'outgoing',
  `material_id` INT NOT NULL DEFAULT '0',
  `filename` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `path` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `xml` LONGTEXT NOT NULL COLLATE 'utf8_unicode_ci',
  `added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COLLATE='utf8_unicode_ci' ENGINE=InnoDB;

######################################################################################
# 2022-04-18 - Added automation to sync materials from SAP to nZoom

# Added automation to sync materials from SAP to nZoom
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на материали от SAP', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '10', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n\r\n#measurements\r\nmeasurement_1 := БР\r\nmeasurement_2 := H\r\nmeasurement_3 := M\r\nmeasurement_4 := KG\r\n\r\nlog_stats := 1\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := atc\r\nmethod := sap2NzoomMaterialSync\r\n', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%sap2NzoomMaterialSync%' AND start_model_type=10);

# Create custom table for logs of sap materials
CREATE TABLE IF NOT EXISTS `sap_matmas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `direction` ENUM('outgoing','incoming') NOT NULL DEFAULT 'outgoing',
  `material_id` INT NOT NULL DEFAULT '0',
  `filename` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `path` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `xml` LONGTEXT NOT NULL COLLATE 'utf8_unicode_ci',
  `added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COLLATE='utf8_unicode_ci' ENGINE=InnoDB;

######################################################################################
# 2022-06-16 - Added automation to sync deliveries from SAP to nZoom

# Added automation to sync deliveries from SAP to nZoom
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация на продажби от SAP', 0, NULL, 1, 'documents', NULL, 'crontab', '6', '#test\r\nsap_path :=./sap\r\n#production\r\n#sap_path := /mnt/sap\r\n\r\n#measurements\r\nmeasurement_1 := БР\r\nmeasurement_2 := H\r\nmeasurement_3 := M\r\nmeasurement_4 := KG\r\n\r\nlog_stats := 1\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := atc\r\nmethod := sap2NzoomDeliverySync\r\n', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%sap2NzoomDeliverySync%' AND start_model_type=10);

# Create custom table for logs of sap deliveries
CREATE TABLE IF NOT EXISTS `sap_desadv` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `direction` ENUM('outgoing','incoming') NOT NULL DEFAULT 'outgoing',
  `delivery_id` INT NOT NULL DEFAULT '0',
  `filename` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `path` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
  `xml` LONGTEXT NOT NULL COLLATE 'utf8_unicode_ci',
  `added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COLLATE='utf8_unicode_ci' ENGINE=InnoDB;

########################################################################
# 2022-07-25 - Added initial REST settings for ATC

#Added REST settings for Walmark
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'allowed_rest_user_agents', 'atc'),
  ('rest', 'filter_vars_departments', 'all'),
  ('rest', 'filter_vars_nomenclatures_5', 'all'),
  ('rest', 'filter_vars_nomenclatures_6', 'all'),
  ('rest', 'filter_vars_documents_1', 'all'),
  ('rest', 'filter_vars_documents_6', 'all'),
  ('rest', 'filter_vars_users', 'id, username, firstname, lastname, employee, rights, role')
ON DUPLICATE KEY UPDATE
                     `value` = VALUES(`value`);

######################################################################################
# 2022-08-09 - Added setting for unknown nomenclature

# Added setting for unknown nomenclature
UPDATE `automations` SET settings=CONCAT(settings, '\r\nnom_unknown_id := 986\r\n') WHERE method LIKE '%sap2NzoomDeliverySync%' AND settings NOT LIKE '%nom_unknown_id%';

######################################################################################
# 2022-09-12 - Added settings for measurements

# Added settings for measurements
UPDATE `automations` SET settings=CONCAT(settings, '\r\n\r\n#ismne\r\nismne_work := H\r\nismne_distance_traveled := KM\r\n') WHERE method LIKE '%nzoom2SAPConfirmationSync%' AND settings NOT LIKE '%ismne_work%';

########################################################################
# 2022-10-03 - Added additional REST settings for ATC

#Added REST settings for ATC
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_7', 'all'),
  ('rest', 'filter_vars_documents_8', 'all')
ON DUPLICATE KEY UPDATE
                     `value` = VALUES(`value`);

########################################################################
# 2023-01-16 - Set the automation gridToModels to execute as original user, for documents of type "Sale from warehouse"

# Set the automation gridToModels to execute as original user, for documents of type "Sale from warehouse"
UPDATE `automations`
  SET `settings` = REPLACE(`settings`, 'execute_as_original_user := 0', 'execute_as_original_user := 1')
  WHERE `module` = 'documents'
    AND `start_model_type` = 1
    AND `method` LIKE '%gridToModels%';

########################################################################
# 2023-01-24 - Added additional REST settings for ATC

# Added REST settings for ATC
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_9', 'all')
ON DUPLICATE KEY UPDATE
                     `value` = VALUES(`value`);

#########################################################################################
# 2023-02-23 - Changed the character set and collation of SAP specific tables

# Changed the character set and collation of SAP specific tables
ALTER TABLE `sap_conf` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sap_debmas` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sap_desadv` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sap_iorder` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sap_matmas` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sap_wmmbid` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

######################################################################################
# 2023-09-27 - Added new automation for ATC

# Checks all the quantities in related Sale Documents (type 1) if they are less or more depending on SAP doc (type 6)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Проверка на САП документ количества', 0, NULL, 1, 'documents', NULL, 'action', '6', 'document_tag_id := 5', '#condition := (Calculator::calc_sql($registry, \'SELECT IF(SUM(dc1.value)<SUM(dc2.value) AND SUM(dc1.value)>0, 1,0) FROM documents_cstm dc1 JOIN documents_cstm dc2 on dc1.model_id=dc2.model_id and dc1.var_id=613 and dc2.var_id=606 and dc1.model_id="[b_id]" and dc1.lang=""\'))==1\r\ncondition := $request->get(\'saveSale\') == \'1\'', 'plugin := atc\r\nmethod := checkSapDocumentHandedQuantity', NULL, 3, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 6 AND automation_type = 'action' AND method LIKE '%checkSapDocumentHandedQuantity%');

######################################################################################
# 2023-10-25 - Added prefixes for the material sync from SAP

# Added prefixes for the material sync from SAP
UPDATE automations
SET settings=CONCAT(settings, "\n\rmaterial_code_valid_prefixes := 0000000000013")
WHERE method LIKE '%sap2NzoomMaterialSync%' AND settings NOT LIKE "%material_code_valid_prefixes%";

######################################################################################
# 2024-01-16 - Added new automation to create SAP documents for service orders (5)

# Added new automation to create SAP documents for service orders (5)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Създаване на SAP документи от гаранционните сервизни поръчки', 0, NULL, 1, 'documents', NULL, 'crontab', '5', '# standard startup settings\r\n#period := 1 day\r\n#start_time := 07:00\r\n#start_before := 08:00\r\n#send_to_email := \r\n\r\ndocument_tag_id := 6\r\ndefault_customer_id := 1\r\ndefault_service_order_name := Изписване по гаранционни поръчки\r\ndefault_visit_protocol_name := Изписване на вложени материали по гаранционни поръчки\r\nservice_order_type := 1165\r\nwork_center := 1129\r\nactivity_type := 1093\r\ndescription := По сервизни поръчки №%s\r\n', 'condition := 1', 'plugin := atc\r\nmethod := createSapDocuments', NULL, 10, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'crontab' AND method LIKE '%createSapDocuments%');

######################################################################################
# 2024-02-19 - Added setting for substatus in createSapDocuments automation

# Added setting for substatus in createSapDocuments automation
UPDATE automations
SET settings=CONCAT(settings, "\n\rsubstatus_id := 15")
WHERE method LIKE '%createSapDocuments%' AND settings NOT LIKE "%substatus_id%";

######################################################################################
# 2024-05-28 - Update javascript for barcode scanner

# Update javascript for barcode scanner
UPDATE `_fields_meta`
SET `source`='dont_copy_values := 1\r\n#javascript for barcode scanner v2\r\njavascript := const getElementNum=e=>(e||(e=document.activeElement),parseInt(e.id.replace(/[^\d].*_(\d+)/,"$1"))),machineCodeTypes=[{mode:"gree",test:RegExp("^(?<machine>[a-z\\d]{5})(?<serial>[a-z\\d]{8,10})$","i")},{mode:"cairox",test:RegExp("^(?<batch>[a-z][a-z\\d]{4}[a-z]\\d{4})(?<machine>\\d{2}[a-z])(?<serial>\\d{5,})$","i")},],matchMachineType=e=>{for(let t in machineCodeTypes){let n=e.match(machineCodeTypes[t].test);if(n)return{mode:machineCodeTypes[t].mode,barcode:n.input,groups:n.groups}}throw Error("No matched code type")};let handleBarcodeScan=e=>{let t,n=document.querySelector(`input[id^="air_serial_num_"][value="${e}"]:not([disabled])`);if(n)n.focus(),t=getElementNum(n),console.log("Already entered at line (updated!) "+t);else{let i=document.querySelector(''input[id^="air_serial_num_"][value=""]'');i||(addField("var_group_1","","","1"),i=document.querySelector(''input[id^="air_serial_num_"][value=""]'')),t=getElementNum(i),i.value=e,i.setAttribute("value",e),i.focus(),console.log("Entered at empty line found: "+t)}let a=matchMachineType(e);selectMachine(a.groups.machine,t)};function selectMachine(e,t){let n=env.base_url+"?"+env.module_param+"=nomenclatures&nomenclatures=ajax_select";new Ajax.Request(n,{method:"post",parameters:"field=machine_code&fill_options[0]=$type_engine_id => <id>&fill_options[1]=$type_engine => <name>&fill_options[2]=$kind_engine_id => <a_kind_engine_id>&fill_options[3]=$kind_engine => <a_kind_engine>&fill_options[4]=$model_engine => <a_model_engine>&fill_options[5]=$warranty_min => <a_warranty_min>&fill_options[6]=$warranty_max => <a_warranty_max>&fill_options[7]=$engine_desc => <a_engine_desc>&fill_types[0]=text&fill_types[1]=text&fill_types[2]=text&fill_types[3]=text&fill_types[4]=text&fill_types[5]=dropdown&fill_types[6]=dropdown&fill_types[7]=textarea&filters[<type>]=5&filters[<code>]="+e+"&id_var=type_engine_id&row="+t+"&machine_code["+t+"]="+e,asynchronous:!1,onComplete:function(t){if(!checkAjaxResponse(t.responseText))return!1;let n=document.createElement("div");if(n.innerHTML=t.responseText,!n.querySelector("input")){console.error(`Machine not found with code ''${e}''`);return}n.style.display="none",n.id="tmp",document.body.appendChild(n),selectAutocompleteItems(n.querySelector("li"),{field:"air_serial_num",scope:""}),document.body.removeChild(n)}})}"undefined"==typeof BarcodeReader&&(window.BarcodeReader=class e{readTimeout;debug=!1;barcodeChars=[];_readBounded;_onReadDoneBounded;_readCheckBounded;constructor(e){this.element=e.element?e.element:document,this.onRead=e.onRead?e.onRead:function(){},this._readBounded=this.read.bind(this),this._onReadDoneBounded=this._onReadDone.bind(this),this._readCheckBounded=this._readCheck.bind(this)}read(e){if(this.barcodeChars.length>=10&&(9===e.which||13===e.which)){e.stopPropagation(),e.preventDefault();return}let t=this._getPressedChar(e);t.match(/[0-9a-zA-Z]/)&&this.barcodeChars.push(t),clearTimeout(this.readTimeout),this.readTimeout=setTimeout(this._readCheckBounded,50)}_readCheck(){if(this.barcodeChars.length>=10){let e=this.barcodeChars.join("");this.log("Barcode Scanned: "+e),this._onReadDoneBounded(e)}this.barcodeChars=[]}_onReadDone(e){this.onRead(e)}log(e){this.debug&&console.log(e)}start(){this.element.addEventListener("keypress",this._readBounded)}stop(){this.element.removeEventListener("keypress",this._readBounded)}_getPressedChar(e){return e.code.match(/^(Key[A-Z])$/)?e.code.replace(/^Key([A-Z])$/,"$1"):String.fromCharCode(e.charCode)}});const bcr=new BarcodeReader({onRead:handleBarcodeScan});bcr.start();'
WHERE  `name`='air_group' AND model_type=1 AND model='Document' AND source NOT LIKE '%#javascript for barcode scanner v2%';

######################################################################################
# 2024-06-05 - Update javascript for barcode scanner

# Update javascript for barcode scanner
UPDATE `_fields_meta`
SET `source`='dont_copy_values := 1\r\n#javascript for barcode scanner v3\r\njavascript := const getElementNum=e=>(e||(e=document.activeElement),parseInt(e.id.replace(/[^\d].*_(\d+)/,"$1"))),machineCodeTypes=[{mode:"gree",test:RegExp("^(?<machine>[a-z\\d]{5})(?<serial>[a-z\\d]{8,10})$","i")},{mode:"cairox",test:RegExp("^(?<batch>[a-z][a-z\\d]{4}[a-z]\\d{4})(?<machine>\\d{2}[a-z])(?<serial>\\d{5,})$","i")},],matchMachineType=e=>{for(let t in machineCodeTypes){let n=e.match(machineCodeTypes[t].test);if(n)return{mode:machineCodeTypes[t].mode,barcode:n.input,groups:n.groups}}throw Error("No matched code type")};let handleBarcodeScan=e=>{let t;console.debug(`Barcode scaned ''${e}''!`);let n=document.querySelector(`input[id^="air_serial_num_"][value="${e}"]:not([disabled])`);if(n)n.focus(),t=getElementNum(n),console.log("Already entered at line (updated!) "+t);else{let i=document.querySelector(''input[id^="air_serial_num_"][value=""]'');i||(addField("var_group_1","","","1"),i=document.querySelector(''input[id^="air_serial_num_"][value=""]'')),t=getElementNum(i),i.value=e,i.setAttribute("value",e),i.focus(),console.log("Entered at empty line found: "+t)}let a=matchMachineType(e);if(!a||!a.groups||!a.groups.machine){console.error("Machine code not recognized!");return}console.debug(`Matched machine code ''${a.groups.machine}''!`),selectMachine(a.groups.machine,t)};function selectMachine(e,t){console.debug(`Try to select machine with code ''${e}'' at row ${t}!`);let n=env.base_url+"?"+env.module_param+"=nomenclatures&nomenclatures=ajax_select";new Ajax.Request(n,{method:"post",parameters:"field=machine_code&fill_options[0]=$type_engine_id => <id>&fill_options[1]=$type_engine => <name>&fill_options[2]=$kind_engine_id => <a_kind_engine_id>&fill_options[3]=$kind_engine => <a_kind_engine>&fill_options[4]=$model_engine => <a_model_engine>&fill_options[5]=$warranty_min => <a_warranty_min>&fill_options[6]=$warranty_max => <a_warranty_max>&fill_options[7]=$engine_desc => <a_engine_desc>&fill_types[0]=text&fill_types[1]=text&fill_types[2]=text&fill_types[3]=text&fill_types[4]=text&fill_types[5]=dropdown&fill_types[6]=dropdown&fill_types[7]=textarea&filters[<type>]=5&filters[<code>]="+e+"&id_var=type_engine_id&row="+t+"&machine_code["+t+"]="+e,asynchronous:!1,onComplete:function(n){if(!checkAjaxResponse(n.responseText))return!1;let i=document.createElement("div");if(i.innerHTML=n.responseText,!i.querySelector("input")){console.error(`Machine not found with code ''${e}''`);return}i.style.display="none",i.id="tmp",document.body.appendChild(i),selectAutocompleteItems(i.querySelector("li"),{field:"air_serial_num",scope:""}),document.body.removeChild(i),console.debug(`Updating data for row ''${t} with machine code ''${e}''`)}})}"undefined"==typeof BarcodeReader&&(window.BarcodeReader=class e{readTimeout;debug=!1;barcodeChars=[];_readBounded;_onReadDoneBounded;_readCheckBounded;constructor(e){this.element=e.element?e.element:document,this.onRead=e.onRead?e.onRead:function(){},this._readBounded=this.read.bind(this),this._onReadDoneBounded=this._onReadDone.bind(this),this._readCheckBounded=this._readCheck.bind(this)}read(e){if(this.barcodeChars.length>=10&&(9===e.which||13===e.which)){e.stopPropagation(),e.preventDefault();return}let t=this._getPressedChar(e);t.match(/[0-9a-zA-Z]/)&&this.barcodeChars.push(t),clearTimeout(this.readTimeout),this.readTimeout=setTimeout(this._readCheckBounded,80)}_readCheck(){if(this.barcodeChars.length>=10){let e=this.barcodeChars.join("");this.log("Barcode Scanned: "+e),this._onReadDoneBounded(e)}this.barcodeChars=[]}_onReadDone(e){try{this.onRead(e)}catch(t){console.error(`User defined onRead function failed for barcode ${e} with error: ${t.message}`)}}log(e){this.debug&&console.log(e)}start(){this.element.addEventListener("keypress",this._readBounded)}stop(){this.element.removeEventListener("keypress",this._readBounded)}_getPressedChar(e){return e.code.match(/^(Key[A-Z])$/)?e.code.replace(/^Key([A-Z])$/,"$1"):String.fromCharCode(e.charCode)}});const bcr=new BarcodeReader({onRead:handleBarcodeScan});bcr.start();'
WHERE  `name`='air_group' AND model_type=1 AND model='Document' AND source LIKE '%#javascript for barcode scanner v2%';

######################################################################################
# 2024-06-20 - Update javascript for barcode scanner

# Update javascript for barcode scanner
UPDATE `_fields_meta`
SET  `source`='dont_copy_values := 1\r\n#javascript for barcode scanner v4\r\njavascript := const getElementNum=e=>(e||(e=document.activeElement),parseInt(e.id.replace(/[^\\d].*_(\\d+)/,"$1"))),machineCodeTypes=[{mode:"gree",test:RegExp("^(?<machine>[a-z\\\\d]{5})(?<serial>[a-z\\\\d]{8,10})$","i")},{mode:"cairox",test:RegExp("^(?<batch>[a-z][a-z\\\\d]{4}[a-z]\\\\d{4})(?<machine>\\\\d{2}[a-z])(?<serial>\\\\d{5,})$","i")},],matchMachineType=e=>{for(let t in machineCodeTypes){let n=e.match(machineCodeTypes[t].test);if(n)return{mode:machineCodeTypes[t].mode,barcode:n.input,groups:n.groups}}throw Error("No matched code type")};let handleBarcodeScan=e=>{let t;console.debug(`Barcode scaned ''${e}''!`);let n=document.querySelector(`input[id^="air_serial_num_"][value="${e}"]:not([disabled])`);if(n)n.focus(),t=getElementNum(n),console.debug("Already entered at line (updated!) "+t);else{let i=document.querySelector(''input[id^="air_serial_num_"][value=""]'');i||(addField("var_group_1","","","1"),i=document.querySelector(''input[id^="air_serial_num_"][value=""]'')),t=getElementNum(i),i.value=e,i.setAttribute("value",e),i.focus(),console.debug("Entered at empty line found: "+t)}try{let a=matchMachineType(e);if(!a||!a.groups||!a.groups.machine){console.error("Machine code not recognized1!");return}console.debug(`Matched machine code ''${a.groups.machine}''!`),selectMachine(a.groups.machine,t)}catch(d){console.error("Machine code not recognized2!",d)}};function selectMachine(e,t){console.debug(`Try to select machine with code ''${e}'' at row ${t}!`);let n=env.base_url+"?"+env.module_param+"=nomenclatures&nomenclatures=ajax_select";new Ajax.Request(n,{method:"post",parameters:"field=machine_code&fill_options[0]=$type_engine_id => <id>&fill_options[1]=$type_engine => <name>&fill_options[2]=$kind_engine_id => <a_kind_engine_id>&fill_options[3]=$kind_engine => <a_kind_engine>&fill_options[4]=$model_engine => <a_model_engine>&fill_options[5]=$warranty_min => <a_warranty_min>&fill_options[6]=$warranty_max => <a_warranty_max>&fill_options[7]=$engine_desc => <a_engine_desc>&fill_types[0]=text&fill_types[1]=text&fill_types[2]=text&fill_types[3]=text&fill_types[4]=text&fill_types[5]=dropdown&fill_types[6]=dropdown&fill_types[7]=textarea&filters[<type>]=5&filters[<code>]="+e+"&id_var=type_engine_id&row="+t+"&machine_code["+t+"]="+e,asynchronous:!1,onComplete:function(n){if(!checkAjaxResponse(n.responseText))return!1;let i=document.createElement("div");if(i.innerHTML=n.responseText,!i.querySelector("input")){console.error(`Machine not found with code ''${e}''`);return}i.style.display="none",i.id="tmp",document.body.appendChild(i),selectAutocompleteItems(i.querySelector("li"),{field:"air_serial_num",scope:""}),document.body.removeChild(i),console.debug(`Updating data for row ''${t} with machine code ''${e}''`)}})}"undefined"==typeof BarcodeReader&&(window.BarcodeReader=class e{readTimeout;debug=!1;barcodeChars=[];_readBounded;_onReadDoneBounded;_readCheckBounded;constructor(e){this.element=e.element?e.element:document,this.onRead=e.onRead?e.onRead:function(){},this._readBounded=this.read.bind(this),this._onReadDoneBounded=this._onReadDone.bind(this),this._readCheckBounded=this._readCheck.bind(this)}read(e){if(this.barcodeChars.length>=10&&(9===e.which||13===e.which)){e.stopPropagation(),e.preventDefault();return}let t=this._getPressedChar(e);t.match(/[0-9a-zA-Z]/)&&this.barcodeChars.push(t),clearTimeout(this.readTimeout),this.readTimeout=setTimeout(this._readCheckBounded,80)}_readCheck(){if(this.barcodeChars.length>=10){let e=this.barcodeChars.join("");this.log("Barcode Scanned: "+e),this._onReadDoneBounded(e)}this.barcodeChars=[]}_onReadDone(e){try{this.onRead(e)}catch(t){console.error(`User defined onRead function failed for barcode ${e} with error: ${t.message}`)}}log(e){this.debug&&console.log(e)}start(){this.element.addEventListener("keypress",this._readBounded)}stop(){this.element.removeEventListener("keypress",this._readBounded)}_getPressedChar(e){return e.code.match(/^(Key[A-Z])$/)?e.code.replace(/^Key([A-Z])$/,"$1"):String.fromCharCode(e.charCode)}});const bcr=new BarcodeReader({onRead:handleBarcodeScan});bcr.start();'
WHERE `name`='air_group' AND model_type=1 AND model='Document' AND (source LIKE '%#javascript for barcode scanner v3%' OR source LIKE 'const getElementNum=e=>%');

######################################################################################
# 2024-06-20 - Update javascript for barcode scanner

# Update javascript for barcode scanner
UPDATE `_fields_meta`
SET  `source`='dont_copy_values := 1\r\n#javascript for barcode scanner v4\r\njavascript := const getElementNum=e=>(e||(e=document.activeElement),parseInt(e.id.replace(/[^\\d].*_(\\d+)/,"$1"))),machineCodeTypes=[{mode:"gree",test:RegExp("^(?<machine>[a-z\\\\d]{5})(?<serial>[a-z\\\\d]{8,10})$","i")},{mode:"cairox",test:RegExp("^(?<batch>[a-z][a-z\\\\d]{4}[a-z]\\\\d{4})(?<machine>\\\\d{2}[a-z])(?<serial>\\\\d{5,})$","i")},{mode:"cairox-h",test:RegExp("^(?<machine>[a-z]{2}[a-z\\\\d]{3})(?<serial>[a-z\\\\d]{5,20})$","i")},],matchMachineType=e=>{for(let t in machineCodeTypes){let n=e.match(machineCodeTypes[t].test);if(n)return{mode:machineCodeTypes[t].mode,barcode:n.input,groups:n.groups}}throw Error("No matched code type")};let handleBarcodeScan=e=>{let t;console.debug(`Barcode scaned ''${e}''!`);let n=document.querySelector(`input[id^="air_serial_num_"][value="${e}"]:not([disabled])`);if(n)n.focus(),t=getElementNum(n),console.debug("Already entered at line (updated!) "+t);else{let i=document.querySelector(''input[id^="air_serial_num_"][value=""]'');i||(addField("var_group_1","","","1"),i=document.querySelector(''input[id^="air_serial_num_"][value=""]'')),t=getElementNum(i),i.value=e,i.setAttribute("value",e),i.focus(),console.debug("Entered at empty line found: "+t)}try{let a=matchMachineType(e);if(!a||!a.groups||!a.groups.machine){console.error("Machine code not recognized1!");return}console.debug(`Matched machine code ''${a.groups.machine}''!`),selectMachine(a.groups.machine,t)}catch(d){console.error("Machine code not recognized2!",d)}};function selectMachine(e,t){console.debug(`Try to select machine with code ''${e}'' at row ${t}!`);let n=env.base_url+"?"+env.module_param+"=nomenclatures&nomenclatures=ajax_select";new Ajax.Request(n,{method:"post",parameters:"field=machine_code&fill_options[0]=$type_engine_id => <id>&fill_options[1]=$type_engine => <name>&fill_options[2]=$kind_engine_id => <a_kind_engine_id>&fill_options[3]=$kind_engine => <a_kind_engine>&fill_options[4]=$model_engine => <a_model_engine>&fill_options[5]=$warranty_min => <a_warranty_min>&fill_options[6]=$warranty_max => <a_warranty_max>&fill_options[7]=$engine_desc => <a_engine_desc>&fill_types[0]=text&fill_types[1]=text&fill_types[2]=text&fill_types[3]=text&fill_types[4]=text&fill_types[5]=dropdown&fill_types[6]=dropdown&fill_types[7]=textarea&filters[<type>]=5&filters[<code>]="+e+"&id_var=type_engine_id&row="+t+"&machine_code["+t+"]="+e,asynchronous:!1,onComplete:function(n){if(!checkAjaxResponse(n.responseText))return!1;let i=document.createElement("div");if(i.innerHTML=n.responseText,!i.querySelector("input")){console.error(`Machine not found with code ''${e}''`);return}i.style.display="none",i.id="tmp",document.body.appendChild(i),selectAutocompleteItems(i.querySelector("li"),{field:"air_serial_num",scope:""}),document.body.removeChild(i),console.debug(`Updating data for row ''${t} with machine code ''${e}''`)}})}"undefined"==typeof BarcodeReader&&(window.BarcodeReader=class e{readTimeout;debug=!1;barcodeChars=[];_readBounded;_onReadDoneBounded;_readCheckBounded;constructor(e){this.element=e.element?e.element:document,this.onRead=e.onRead?e.onRead:function(){},this._readBounded=this.read.bind(this),this._onReadDoneBounded=this._onReadDone.bind(this),this._readCheckBounded=this._readCheck.bind(this)}read(e){if(this.barcodeChars.length>=10&&(9===e.which||13===e.which)){e.stopPropagation(),e.preventDefault();return}let t=this._getPressedChar(e);t.match(/[0-9a-zA-Z]/)&&this.barcodeChars.push(t),clearTimeout(this.readTimeout),this.readTimeout=setTimeout(this._readCheckBounded,80)}_readCheck(){if(this.barcodeChars.length>=10){let e=this.barcodeChars.join("");this.log("Barcode Scanned: "+e),this._onReadDoneBounded(e)}this.barcodeChars=[]}_onReadDone(e){try{this.onRead(e)}catch(t){console.error(`User defined onRead function failed for barcode ${e} with error: ${t.message}`)}}log(e){this.debug&&console.log(e)}start(){this.element.addEventListener("keypress",this._readBounded)}stop(){this.element.removeEventListener("keypress",this._readBounded)}_getPressedChar(e){return e.code.match(/^(Key[A-Z])$/)?e.code.replace(/^Key([A-Z])$/,"$1"):String.fromCharCode(e.charCode)}});const bcr=new BarcodeReader({onRead:handleBarcodeScan});bcr.start();'
WHERE `name`='air_group' AND model_type=1 AND model='Document' AND (source LIKE '%#javascript for barcode scanner v3%' OR source LIKE 'const getElementNum=e=>%');

######################################################################################
# 2025-05-13 - Update javascript for barcode scanner

# Update javascript for barcode scanner
UPDATE `_fields_meta`
SET  `source`='dont_copy_values := 1\r\n#javascript for barcode scanner v5\r\njavascript := const getElementNum=e=>(e||(e=document.activeElement),parseInt(e.id.replace(/[^\\d].*_(\\d+)/,"$1"))),machineCodeTypes=[{mode:"gree",test:RegExp("^(?<machine>[a-z\\\\d]{5})(?<serial>[a-z\\\\d]{8,10})$","i")},{mode:"cairox",test:RegExp("^(?<batch>[a-z][a-z\\\\d]{4}[a-z]\\\\d{4})(?<machine>\\\\d{2}[a-z])(?<serial>\\\\d{5,})$","i")},{mode:"cairox-h",test:RegExp("^(?<machine>[a-z]{2}[a-z\\\\d]{3})(?<serial>[a-z\\\\d]{5,20})$","i")},],matchMachineType=e=>{for(let t in machineCodeTypes){let n=e.match(machineCodeTypes[t].test);if(n)return{mode:machineCodeTypes[t].mode,barcode:n.input,groups:n.groups}}throw Error("No matched code type")};let handleBarcodeScan=e=>{let t;console.debug(`Barcode scaned ''${e}''!`);let n=document.querySelector(`input[id^="air_serial_num_"][value="${e}"]:not([disabled])`);if(n)n.focus(),t=getElementNum(n),console.debug("Already entered at line (updated!) "+t);else{let i=document.querySelector(''input[id^="air_serial_num_"][value=""]'');i||(addField("var_group_1","","","1"),i=document.querySelector(''input[id^="air_serial_num_"][value=""]'')),t=getElementNum(i),i.value=e,i.setAttribute("value",e),i.focus(),console.debug("Entered at empty line found: "+t)}try{let a=matchMachineType(e);if(!a||!a.groups||!a.groups.machine){console.error("Machine code not recognized1!");return}console.debug(`Matched machine code ''${a.groups.machine}''!`),selectMachine(a.groups.machine,t)}catch(d){console.error("Machine code not recognized2!",d)}};function selectMachine(e,t){console.debug(`Try to select machine with code ''${e}'' at row ${t}!`);let n=env.base_url+"?"+env.module_param+"=nomenclatures&nomenclatures=ajax_select";new Ajax.Request(n,{method:"post",parameters:"field=machine_code&fill_options[0]=$type_engine_id => <id>&fill_options[1]=$type_engine => <name>&fill_options[2]=$kind_engine_id => <a_kind_engine_id>&fill_options[3]=$kind_engine => <a_kind_engine>&fill_options[4]=$model_engine => <a_model_engine>&fill_options[5]=$warranty_min => <a_warranty_min>&fill_options[6]=$warranty_max => <a_warranty_max>&fill_options[7]=$engine_desc => <a_engine_desc>&fill_types[0]=text&fill_types[1]=text&fill_types[2]=text&fill_types[3]=text&fill_types[4]=text&fill_types[5]=dropdown&fill_types[6]=dropdown&fill_types[7]=textarea&filters[<type>]=5&filters[<code>]="+e+"&id_var=type_engine_id&row="+t+"&machine_code["+t+"]="+e,asynchronous:!1,onComplete:function(n){if(!checkAjaxResponse(n.responseText))return!1;let i=document.createElement("div");if(i.innerHTML=n.responseText,!i.querySelector("input")){console.error(`Machine not found with code ''${e}''`);return}i.style.display="none",i.id="tmp",document.body.appendChild(i),selectAutocompleteItems(i.querySelector("li"),{field:"air_serial_num",scope:""}),document.body.removeChild(i),console.debug(`Updating data for row ''${t} with machine code ''${e}''`)}})}"undefined"==typeof BarcodeReader&&(window.BarcodeReader=class e{readTimeout;debug=!1;barcodeChars=[];_readBounded;_onReadDoneBounded;_readCheckBounded;constructor(e){this.element=e.element?e.element:document,this.onRead=e.onRead?e.onRead:function(){},this._readBounded=this.read.bind(this),this._onReadDoneBounded=this._onReadDone.bind(this),this._readCheckBounded=this._readCheck.bind(this)}read(e){if(this.barcodeChars.length>=10&&(9===e.which||13===e.which)){e.stopPropagation(),e.preventDefault();return}let t=this._getPressedChar(e);t.match(/[0-9a-zA-Z]/)&&this.barcodeChars.push(t),clearTimeout(this.readTimeout),this.readTimeout=setTimeout(this._readCheckBounded,80)}_readCheck(){if(this.barcodeChars.length>=10){let e=this.barcodeChars.join("");this.log("Barcode Scanned: "+e),this._onReadDoneBounded(e)}this.barcodeChars=[]}_onReadDone(e){try{this.onRead(e)}catch(t){console.error(`User defined onRead function failed for barcode ${e} with error: ${t.message}`)}}log(e){this.debug&&console.log(e)}start(){this.element.addEventListener("keypress",this._readBounded)}stop(){this.element.removeEventListener("keypress",this._readBounded)}_getPressedChar(e){return e.code.match(/^(Key[A-Z])$/)?e.code.replace(/^Key([A-Z])$/,"$1"):String.fromCharCode(e.charCode)}});const bcr=new BarcodeReader({onRead:handleBarcodeScan});bcr.start();'
WHERE `name`='air_group' AND model_type=1 AND model='Document' AND (source LIKE '%#javascript for barcode scanner v4%' OR source LIKE 'const getElementNum=e=>%');

######################################################################################
# 2025-06-23 - Add a new setting for dashlet atc_unmounted_machines

# Add a new setting for dashlet atc_unmounted_machines
UPDATE `dashlets_plugins`
  SET `settings` = CONCAT(`settings`, '\n\n# Лимит за брой избрани редове. Ако бъде достигнат се извежда съобщение за потвърждение, с идеята да не се обработват твърде много записи наведнъж.\nmax_checked_count := 25')
  WHERE `type` = 'atc_unmounted_machines'
    AND `settings` NOT LIKE '%max_checked_count%';
