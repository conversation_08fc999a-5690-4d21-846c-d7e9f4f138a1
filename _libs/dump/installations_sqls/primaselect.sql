####################################################################################################
### SQL nZoom Specific Updates Адвокатска Кантора Чернева (http://primaselect.n-zoom.com/) ###
####################################################################################################

######################################################################################
# 2010-09-02 - Added new report 'cherneva_payment_fee' for Cherneva installation (1736)

# Added new report 'cherneva_payment_fee' for Cherneva installation (1736)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(142, 'cherneva_payment_fee', NULL , 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(142, 'Погасителни вноски', NULL, NULL, 'bg'),
(142, 'Payment fee', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 142, 0, 1),
('reports', 'export', 142, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=142;

######################################################################################
# 2011-11-30 - Added new report 'cherneva_proceedings' for Cherneva installation (1736)

# Added new report 'cherneva_proceedings' for Cherneva installation (1736)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES 
  (191, 'cherneva_proceedings', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES 
  (191, 'Съдебни производства', NULL, NULL, 'bg'), 
  (191, 'Proceedings', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES 
  ('reports', 'generate_report', 191, 0, 1), 
  ('reports', 'export', 191, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`) 
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 191;
