-- #########################################
-- ### CONVERSION SQL FILE FOR NZOOM v25 ###
-- #########################################

#########################################################################################
# 2025-01-07 - Added new setting for 'budgets' report that allows pointing different main var for date for the expenses

# Added new setting for 'budgets' report that allows pointing different main var for date for the expenses
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_budget_item_type :=', '\r\ndoc_expanse_date_base_var := deadline\r\n\r\nnom_budget_item_type :=') WHERE `type`='budgets' AND `settings` NOT LIKE '%doc_expanse_date_base_var%';

######################################################################################
# 2025-01-07 - Added shared total of advance in fin reasons relatives

# Added shared total of advance in fin reasons relatives
UPDATE fin_reasons_relatives frr
    JOIN fin_incomes_reasons fir_inv
ON frr.parent_model_name='Finance_Incomes_Reason'
    AND frr.link_to_model_name='Finance_Incomes_Reason'
    AND frr.parent_id=fir_inv.id
    AND fir_inv.TYPE IN (1,2)
    AND fir_inv.advance!=0
    JOIN fin_incomes_reasons fir_reason
    ON frr.link_to=fir_reason.id
    AND fir_reason.TYPE>100
    SET frr.shared=fir_inv.total
WHERE frr.shared IS NULL;

######################################################################################
# 2025-01-17 - Added advance invoice id into article_barcode column for all invoices with advances

# Added advance invoice id into article_barcode column for all invoices with advances
UPDATE fin_incomes_reasons fir_advance_invoice
 JOIN gt2_details_i18n gi
  ON fir_advance_invoice.type=1
     AND fir_advance_invoice.num!=''
     AND fir_advance_invoice.status="finished"
     AND fir_advance_invoice.advance!=0
     AND gi.article_description LIKE CONCAT('%', fir_advance_invoice.num, '/%')
 JOIN gt2_details g
   ON gi.parent_id=g.id AND g.article_barcode=''
 JOIN nom n
   ON g.article_id=n.id AND n.subtype='advance'
 JOIN fin_incomes_reasons fir_invoice
   ON fir_invoice.id=g.model_id AND g.model='Finance_Incomes_Reason' AND fir_invoice.type=1
 SET g.article_barcode=fir_advance_invoice.id
 WHERE gi.article_description LIKE 'Авансово плащане по фактура%';

# FUNCTION TO CALCULATE CONTAINER SUM
DROP FUNCTION IF EXISTS calculate_container_sum;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION calculate_container_sum(current_container_id int, current_container_type varchar(255), current_container_currency varchar(3)) RETURNS DECIMAL(21,2)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE transfer_sum DECIMAL(21,2);
        DECLARE payments_sum DECIMAL(21,2);
        DECLARE new_sum DECIMAL(21,2);
        DECLARE starting_amount DECIMAL(21,2);

        # If container is bank_account then the starting amount has to be taken
        IF (current_container_type = 'bank_account') THEN
            SET starting_amount = (SELECT amount FROM fin_bank_accounts WHERE id=current_container_id);
        END IF;

        # Get the transfers sum
        SELECT SUM(IF(from_container_id=current_container_id AND from_container_type=current_container_type, (-1*from_amount), to_amount)) INTO transfer_sum
        FROM fin_transfers
        WHERE ((from_container_id=current_container_id AND from_container_type=current_container_type) OR (to_container_id=current_container_id AND to_container_type=current_container_type))
              AND (current_container_type = 'bank_account' OR (current_container_type='cashbox' AND currency=current_container_currency))
              AND status='finished';

        # Get the payments sum
        SELECT SUM(receive_flag*container_amount) INTO payments_sum
        FROM fin_payments
        WHERE     container_id=current_container_id
              AND container_type=current_container_type
              AND annulled_by=0
              AND (current_container_type = 'bank_account' OR (current_container_type='cashbox' AND currency=current_container_currency))
              AND status='finished'
              AND `type`!='TR';

        # Calculates the current amount of the container
        SET new_sum = (IF (transfer_sum IS NULL, 0, transfer_sum)) + (IF (payments_sum IS NULL, 0, payments_sum)) + (IF (starting_amount IS NULL, 0, starting_amount));
        return new_sum;
    END;//
delimiter ;


# PROCEDURE TO UPDATE THE AMOUNT IN THE REQUIRED TABLE
DROP PROCEDURE IF EXISTS update_container_amounts;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_container_amounts(IN current_container_type varchar(255), IN current_container_id int, IN new_sum DECIMAL(21,2), IN current_container_currency varchar(3), IN user_correct int)
SQL SECURITY INVOKER
    BEGIN
        IF (current_container_type='cashbox') THEN
            # Case for cashboxes
            INSERT INTO fin_cashboxes_amounts
                SET amount=new_sum,
                    parent_id=current_container_id,
                    currency=current_container_currency,
                    revised=NOW(),
                    revised_by=user_correct
            ON DUPLICATE KEY UPDATE
                amount=new_sum,
                revised=NOW(),
                revised_by=user_correct;
        ELSE
            # Case for bank accounts
            UPDATE fin_bank_accounts
            SET current_amount = new_sum
            WHERE id=current_container_id;
        END IF;
    END;//
delimiter ;


# PROCEDURE TO WRITE LOG FOR THE CHANGES
DROP PROCEDURE IF EXISTS log_changes;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE log_changes(IN current_action varchar(255), IN current_model varchar(255), IN current_model_id int, IN current_container_type varchar(255), IN current_container_id int, IN previous_sum DECIMAL(21,2), IN new_sum DECIMAL(21,2), IN current_container_currency varchar(3), IN changed_data_vars text CHARACTER SET utf8mb4)
SQL SECURITY INVOKER
    BEGIN
      # write history
      INSERT INTO diag_containers_change_history
      SET action=current_action,
          changed_model=current_model,
          changed_model_id=current_model_id,
          changed_by=USER(),
          container_type=current_container_type,
          container_id=current_container_id,
          previous_amount=(IF (previous_sum IS NULL, 0, previous_sum)),
          changed_amount=new_sum,
          currency=current_container_currency,
          changed_data=changed_data_vars,
          change_date=NOW();
    END;//
delimiter ;

# PROCEDURE TO CALCULATE CONTAINER SUM
DROP PROCEDURE IF EXISTS administrative_update_container_amounts;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE administrative_update_container_amounts()
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE container_id INT;
        DECLARE container_amount DECIMAL(21,2);
        DECLARE container_currency VARCHAR(3);

        # Get all the bank accounts which does not have the same synthetic value and analytic value
        DECLARE bank_accounts_cursor CURSOR FOR SELECT id,current_amount,currency FROM `fin_bank_accounts` WHERE current_amount!=calculate_container_sum(id, 'bank_account', currency);

        # Get all the cashboxes data which does not have the same synthetic value and analytic value
        DECLARE cashboxes_cursor CURSOR FOR SELECT parent_id,amount,currency FROM `fin_cashboxes_amounts` WHERE amount!=calculate_container_sum(parent_id, 'cashbox', currency);
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        # Start looping through the results
        OPEN bank_accounts_cursor;
        read_loop: LOOP
            FETCH bank_accounts_cursor INTO container_id, container_amount, container_currency;
            IF done THEN
                LEAVE read_loop;
            END IF;
            SET @new_sum = calculate_container_sum(container_id, 'bank_account', container_currency);
            CALL update_container_amounts('bank_account', container_id, @new_sum, container_currency, 1);
            CALL log_changes('adminitrative_update', '', 0, 'bank_account', container_id, container_amount, @new_sum, container_currency, NULL);
        END LOOP;
        CLOSE bank_accounts_cursor;

        SET done = FALSE;

        # Start looping through the results
        OPEN cashboxes_cursor;
        read_loop: LOOP
            FETCH cashboxes_cursor INTO container_id, container_amount, container_currency;
            IF done THEN
                LEAVE read_loop;
            END IF;
            SET @new_sum = calculate_container_sum(container_id, 'cashbox', container_currency);
            CALL update_container_amounts('cashbox', container_id, @new_sum, container_currency, 1);
            CALL log_changes('adminitrative_update', '', 0, 'cashbox', container_id, container_amount, @new_sum, container_currency, NULL);
        END LOOP;
        CLOSE cashboxes_cursor;
    END;//
delimiter ;

# FUNCTION TO DETERMINE THE PAYMENT STATUS OF A REASON AND ALL OF ITS RELATED REASONS
DROP FUNCTION IF EXISTS define_new_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_payment_status(searched_model_name varchar(255), searched_model_id int, side_connection varchar(255)) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        # The new payment status
        DECLARE new_payment_status VARCHAR(255);

        # The current paid amount for this reason
        SET @current_paid_amount = NULL;

        # The model type of this reason
        SET @model_type = NULL;

        # Only the incomes and expenses reasons can have their payment status changed
        IF (searched_model_name='Finance_Expenses_Reason' OR searched_model_name='Finance_Incomes_Reason') THEN
            # Get the information for the type and for the current total of the reason
            IF (searched_model_name='Finance_Expenses_Reason') THEN
                SELECT fer.total_with_vat, fer.type INTO @model_total, @model_type
                  FROM fin_expenses_reasons as fer
                  WHERE fer.id=searched_model_id;
            ELSE
                SELECT fir.total_with_vat, fir.type INTO @model_total, @model_type
                  FROM fin_incomes_reasons as fir
                  WHERE fir.id=searched_model_id;
            END IF;

            # If the calculated amount is less than 0 then absolute value is taken
            IF (@model_total<0) THEN
                SET @model_total=@model_total*-1;
            END IF;

            # Round the decimal value of @model_total to second digit because it is used to compare with the paid amount which is rounded to second digit as well
            IF (@model_total IS NOT NULL) THEN
                SET @model_total=ROUND(@model_total, 2);
            ELSE
                SET @model_total=0;
            END IF;

            # Take actions depending on the model type
            IF (@model_type>100) THEN
                IF (searched_model_name='Finance_Incomes_Reason') THEN
                    # For user-defined reasons with a custom type, the whole chain of relations should be taken
                    # Get the paid amount from invoices and proformas
                    SELECT SUM(paid_amount) INTO @invoiced_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND frr.parent_id=fir.id AND
                            fir.type IN (1, 2) AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            fr.paid_to=fir.id AND
                            fr.paid_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished";

                    # Get the directly paid amount
                    SELECT SUM(paid_amount) INTO @direct_paid_amount
                      FROM fin_balance AS fr
                      WHERE fr.paid_to=searched_model_id AND fr.paid_to_model_name="Finance_Incomes_Reason";

                    # Get the paid amount from debit notices
                    SELECT SUM(paid_amount) INTO @debit_notices_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr2.parent_id=fir.id AND
                            frr2.parent_model_name="Finance_Incomes_Reason" AND
                            fir.type=4 AND
                            frr2.link_to=frr.parent_id AND
                            frr2.link_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished" AND
                            fr.paid_to=frr2.parent_id AND
                            fr.paid_to_model_name="Finance_Incomes_Reason";

                    # Get the paid amount from credit notices
                    SELECT SUM(paid_amount) INTO @credit_notices_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr2.parent_id=fir.id AND
                            frr2.parent_model_name="Finance_Incomes_Reason" AND
                            fir.type=3 AND
                            frr2.link_to=frr.parent_id AND
                            frr2.link_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished" AND
                            fr.parent_id=frr2.parent_id AND
                            fr.parent_model_name="Finance_Incomes_Reason";

                    # The amount will be sum of invoiced amount, directly paid amount and amount from credit/debit notices
                    SET @current_paid_amount = (IF (@invoiced_amount IS NULL, 0, @invoiced_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount))+(IF (@debit_notices_amount IS NULL, 0, @debit_notices_amount))-(IF (@credit_notices_amount IS NULL, 0, @credit_notices_amount));
                ELSEIF (searched_model_name='Finance_Expenses_Reason') THEN
                    # Get the directly paid amount
                    SELECT SUM(paid_amount) INTO @direct_paid_amount
                      FROM fin_balance AS fr
                      WHERE fr.parent_id=searched_model_id AND fr.parent_model_name=searched_model_name;

                    # Get paid amount to non-invoiced proforma (if such exists)
                    SELECT SUM(paid_amount) INTO @proforma_amount
                      FROM fin_expenses_reasons AS fer, fin_reasons_relatives AS frr, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.parent_id=fer.id AND
                            fer.type=21 AND
                            frr.parent_model_name=searched_model_name AND
                            frr.link_to_model_name=searched_model_name AND
                            fr.parent_id=fer.id AND
                            fr.parent_model_name=searched_model_name AND
                            fer.annulled_by=0 AND
                            fer.active=1 AND
                            fer.status="finished" AND
                            fer.payment_status!="invoiced";

                    # The amount will be sum of direcly paid amount to reason and paid amount to non-invoiced expense proforma
                    SET @current_paid_amount = (IF (@proforma_amount IS NULL, 0, @proforma_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount));
                END IF;
            ELSE
                # For system-defined reasons only the data from the balance table is enough
                IF (side_connection='parent') THEN
                    SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name=searched_model_name AND parent_id=searched_model_id);
                ELSE
                    SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE paid_to_model_name=searched_model_name AND paid_to=searched_model_id);
                END IF;
            END IF;

            # Set default value of @current_paid_amount
            IF (@current_paid_amount IS NULL) THEN
                SET @current_paid_amount=0;
            END IF;

            # Calculate the amount difference to retrieve the status
            SET @amount_difference = ROUND(@model_total - @current_paid_amount, 2);
            IF (@amount_difference = @model_total AND @model_total>0) THEN
                SET new_payment_status = 'unpaid';
            ELSEIF (@amount_difference<=0) THEN
                SET new_payment_status = 'paid';
            ELSE
                SET new_payment_status = 'partial';
            END IF;
        END IF;

        # return the new status as string
        return new_payment_status;
    END;//
delimiter ;


# FUNCTION TO WRITE HISTORY FOR CHANGES IN FIN_BALANCE TABLE
DROP FUNCTION IF EXISTS log_payment_status_history;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION log_payment_status_history(current_action varchar(255), row_id int, changed_data_vars text CHARACTER SET utf8mb4) RETURNS INT
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE history_id INT;
        # write history
        INSERT INTO diag_payment_status_reasons_history
        SET action=current_action,
            balance_row_id=row_id,
            changed_by=USER(),
            changed_data=changed_data_vars,
            change_date=NOW();
        SELECT LAST_INSERT_ID() INTO history_id;

        # Return the history id so it can be used later for audit
        return history_id;
    END;//
delimiter ;


# PROCEDURE TO AUDIT CHANGES OF PAYMENT STATUS OF INCOMES AND EXPENSES REASONS
DROP PROCEDURE IF EXISTS log_payment_status_audit;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE log_payment_status_audit(IN history_id int, IN changed_model_name varchar(255), IN changed_model_id int, IN previous_status varchar(255), IN new_status varchar(255))
SQL SECURITY INVOKER
    BEGIN
        # write audit
        INSERT INTO diag_payment_status_reasons_audit
        SET parent_id=history_id,
            changed_model=changed_model_name,
            changed_model_id=changed_model_id,
            previous_payment_status=previous_status,
            new_payment_status=new_status;
    END;//
delimiter ;


# PROCEDURE TO UPDATE PAYMENT STATUS OF MULTIPLE EXPENSES REASONS WHICH ARE PARENTS OF PROFORMAS WHICH ARE PARENTS OF A SINGLE INVOICE
DROP PROCEDURE IF EXISTS update_multiple_expenses_reason_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_multiple_expenses_reason_payment_status(IN changed_model_id INT, IN new_payment_status VARCHAR(255), IN history_id INT)
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE reason_id INT;
        DECLARE reason_total DECIMAL(25,6);
        DECLARE reason_current_payment_status VARCHAR(255);
        DECLARE reason_payment_status VARCHAR(255);

        DECLARE reason_cursor CURSOR FOR
            SELECT fer2.id, ROUND(fer2.total_with_vat * fer.currency_rate, 2) AS total_with_vat, fer2.payment_status
            FROM fin_reasons_relatives AS frr
            INNER JOIN fin_expenses_reasons AS fer
              ON (fer.type=21 AND fer.annulled_by=0 AND fer.active=1
                AND frr.parent_model_name="Finance_Expenses_Reason"
                AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to=fer.id)
            INNER JOIN fin_reasons_relatives AS frr2
              ON (frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.parent_id=frr.link_to
                AND frr2.link_to_model_name="Finance_Expenses_Reason")
            INNER JOIN  fin_expenses_reasons AS fer2
              ON (fer2.id=frr2.link_to)
            WHERE fer2.type>100 AND fer2.annulled_by=0 AND fer2.active=1 AND frr.parent_id=changed_model_id
            ORDER BY fer2.issue_date ASC, fer2.id ASC;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        # Paid amount to expense invoice
        SET @current_paid_amount = 0;
        IF (new_payment_status = 'partial') THEN
            SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name="Finance_Expenses_Reason" AND parent_id=changed_model_id);
        END IF;

        OPEN reason_cursor;

        reason_loop: LOOP
            FETCH reason_cursor INTO reason_id, reason_total, reason_current_payment_status;

            IF done THEN
                LEAVE reason_loop;
            END IF;

            IF (new_payment_status IN ('paid', 'unpaid')) THEN
                # Update all expenses reasons with the same payment status as invoice
                SET reason_payment_status = new_payment_status;
            ELSEIF (new_payment_status = 'partial') THEN
                # Specify payment status for each reason iteratively
                IF (@current_paid_amount <= 0 AND reason_total > 0) THEN
                    SET reason_payment_status = 'unpaid';
                ELSEIF (@current_paid_amount >= reason_total) THEN
                    SET reason_payment_status = 'paid';
                ELSE
                    SET reason_payment_status = 'partial';
                END IF;

                # Subtract the amount of reason from paid amount to get remaining amount
                IF (@current_paid_amount > 0) THEN
                    SET @current_paid_amount = ROUND((IF (@current_paid_amount IS NULL, 0, @current_paid_amount))-(IF (reason_total IS NULL, 0, reason_total)), 2);
                    IF (@current_paid_amount < 0.01) THEN
                        SET @current_paid_amount = 0;
                    END IF;
                END IF;
            END IF;

            IF (reason_payment_status IS NOT NULL) THEN
                UPDATE fin_expenses_reasons SET payment_status=reason_payment_status WHERE id=reason_id;
                CALL log_payment_status_audit(history_id, 'Finance_Expenses_Reason', reason_id, reason_current_payment_status, reason_payment_status);
            END IF;
        END LOOP;

        CLOSE reason_cursor;
    END;//
delimiter ;


# PROCEDURE TO UPDATE THE REASON PAYMENT STATUS DEPENDING ON THE CHANGE IN fin_balance TABLE
DROP PROCEDURE IF EXISTS update_reason_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_reason_payment_status(IN sideConnection varchar(255), IN changedModelName varchar(255), IN changedModelId INT, IN historyId INT)
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE currentModelId INT DEFAULT NULL;
        DECLARE currentModelName varchar(255) DEFAULT NULL;
        DECLARE currentModelTotal varchar(255) DEFAULT NULL;
        DECLARE currentModelType INT DEFAULT NULL;
        DECLARE currentModelPaymentStatus varchar(255) DEFAULT NULL;
        DECLARE currentModelOrigin varchar(255) DEFAULT NULL;
        DECLARE newPaymentStatus VARCHAR(255) DEFAULT NULL;
        DECLARE relatedModelId INT DEFAULT NULL;
        DECLARE curReasons CURSOR FOR
            -- FINANCE EXPENSES
            -- direct expenses
            (SELECT
                "Finance_Expenses_Reason" as model_name,
                fer.type as current_model_type,
                IF (fer.total_with_vat<0, fer.total_with_vat*-1, fer.total_with_vat) as current_model_total,
                fer.payment_status as current_model_payment_status,
                fer.id as current_model_id,
                "original" as current_model_origin
              FROM fin_expenses_reasons as fer
              WHERE changedModelName="Finance_Expenses_Reason"
                    AND fer.id=changedModelId
            )
            UNION
            -- related expenses reasons from invoice and proforma invoice (20,21)
            -- frr - relation between expense reason (link_to) and invoice (parent_id)
            -- firinv - fin_expenses_reason for invoice
            -- fer - fin_incomes_reason for reason
            (SELECT
              "Finance_Expenses_Reason" as model_name,
                fer.type as current_model_type,
                IF (fer.total_with_vat<0, fer.total_with_vat*-1, fer.total_with_vat) as current_model_total,
                fer.payment_status as current_model_payment_status,
                fer.id as current_model_id,
                "related" as current_model_origin
              FROM fin_reasons_relatives as frr
              JOIN fin_expenses_reasons as fer
                ON (frr.link_to=fer.id AND fer.type>100)
              JOIN fin_expenses_reasons as ferinv
                ON (frr.parent_id=ferinv.id AND ferinv.type IN (20, 21))
              WHERE
                changedModelName="Finance_Expenses_Reason"
                AND frr.parent_id=changedModelId
                AND frr.parent_model_name='Finance_Expenses_Reason'
                AND frr.link_to_model_name='Finance_Expenses_Reason'
                AND fer.active=1
                AND fer.annulled_by=0
                AND fer.status="finished"
            )
            UNION
            -- FINANCE INCOMES
            -- direct incomes
            (SELECT
                "Finance_Incomes_Reason" as model_name,
                fir.type as current_model_type,
                IF (fir.total_with_vat<0, fir.total_with_vat*-1, fir.total_with_vat) as current_model_total,
                fir.payment_status as current_model_payment_status,
                fir.id as current_model_id,
                "original" as current_model_origin
              FROM fin_incomes_reasons as fir
              WHERE changedModelName="Finance_Incomes_Reason"
                    AND fir.id=changedModelId
            )
            UNION
            -- related income reasons from invoice and proforma invoice (1,2)
            -- frr - relation between income reason (link_to) and invoice (parent_id)
            -- firinv - fin_incomes_reason for invoice
            -- fir - fin_incomes_reason for reason
            -- IMPORTANT: reasons might be more than one, because the advance invoice might be related to more than one reason!
            (SELECT
              "Finance_Incomes_Reason" as model_name,
                fir.type as current_model_type,
                IF(frr.shared!=0 AND frr.shared IS NOT NULL,
                  frr.shared*(1+fir.total_vat_rate/100),
                  IF (fir.total_with_vat<0,
                     fir.total_with_vat*-1,
                     fir.total_with_vat)
                ) as current_model_total,
                fir.payment_status as current_model_payment_status,
                fir.id as current_model_id,
                "related" as current_model_origin
              FROM fin_reasons_relatives as frr
              JOIN fin_incomes_reasons as fir
                ON (frr.link_to=fir.id AND fir.type>100)
              JOIN fin_incomes_reasons as firinv
                ON (frr.parent_id=firinv.id AND firinv.type IN (1,2))
              WHERE
                changedModelName="Finance_Incomes_Reason"
                AND frr.parent_id=changedModelId
                AND frr.parent_model_name='Finance_Incomes_Reason'
                AND frr.link_to_model_name='Finance_Incomes_Reason'
                AND fir.active=1
                AND fir.annulled_by=0
                AND fir.status="finished"
            )
            UNION
            -- related income reasons from debit or credit note (3,4)
            -- frr1 - relation between invoice (link_to) and credit/debit (parent_id)
            -- firinv - fin_incomes_reason for invoice
            -- firdc - fin_incomes_reason for debit/credit
            -- frr2 - relation between income reason (link_to) and invoice (parent_id)
            -- fir - fin_incomes_reason for reason
            (SELECT
              "Finance_Incomes_Reason" as model_name,
                fir.type as current_model_type,
                IF(frr2.shared!=0 AND frr2.shared IS NOT NULL,
                  frr2.shared*(1+fir.total_vat_rate/100),
                  IF (fir.total_with_vat<0,
                     fir.total_with_vat*-1,
                     fir.total_with_vat)
                ) as current_model_total,
                fir.payment_status as current_model_payment_status,
                fir.id as current_model_id,
                "related" as current_model_origin
              FROM fin_reasons_relatives as frr1
              JOIN fin_incomes_reasons as firinv
                ON (frr1.link_to=firinv.id AND firinv.type IN (1))
              JOIN fin_incomes_reasons as firdc
                ON (frr1.parent_id=firdc.id AND firdc.type IN (3,4))
              JOIN fin_reasons_relatives as frr2
                ON (frr2.parent_id=firinv.id)
              JOIN fin_incomes_reasons as fir
                ON (frr2.link_to=fir.id AND fir.type>100)
              WHERE
                changedModelName="Finance_Incomes_Reason"
                AND frr1.parent_id=changedModelId
                AND frr1.parent_model_name='Finance_Incomes_Reason'
                AND frr1.link_to_model_name='Finance_Incomes_Reason'
                AND frr2.parent_model_name='Finance_Incomes_Reason'
                AND frr2.link_to_model_name='Finance_Incomes_Reason'
                AND firinv.active=1
                AND firinv.annulled_by=0
                AND firinv.status="finished"
                AND fir.active=1
                AND fir.annulled_by=0
                AND fir.status="finished"
            );
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        OPEN curReasons;
          read_loop: LOOP
            FETCH curReasons INTO currentModelName, currentModelType, currentModelTotal, currentModelPaymentStatus, currentModelId, currentModelOrigin;
            IF done THEN
              LEAVE read_loop;
            END IF;

            IF (currentModelName="Finance_Expenses_Reason") THEN
                IF (currentModelOrigin = "original") THEN
                    SET newPaymentStatus=define_new_payment_status(currentModelName, currentModelId, sideConnection);

                    # Update the status of the reason
                    UPDATE fin_expenses_reasons SET payment_status=newPaymentStatus WHERE id=changedModelId;
                    CALL log_payment_status_audit(
                        historyId,
                        changedModelName,
                        changedModelId,
                        currentModelPaymentStatus,
                        newPaymentStatus
                    );

                    # for invoices there might be multiple connections
                    IF (currentModelType = "20") THEN
                        # Updates payment status of reason parents of proforma parents of expense invoice
                        CALL update_multiple_expenses_reason_payment_status(changedModelId, newPaymentStatus, historyId);
                    END IF;

                ELSEIF (currentModelOrigin = "related") THEN
                    # Update reason payment status
                    IF (currentModelType=21) THEN
                        # If current model is a proforma, both reason and proforma could have distributed payments
                        SET newPaymentStatus=define_new_payment_status(currentModelName, currentModelId, sideConnection);
                    END IF;

                    UPDATE fin_expenses_reasons SET payment_status=newPaymentStatus WHERE id=currentModelId;
                    CALL log_payment_status_audit(
                        historyId,
                        currentModelName,
                        currentModelId,
                        currentModelPaymentStatus,
                        newPaymentStatus
                    );
                END IF;
            ELSEIF (currentModelName='Finance_Incomes_Reason') THEN
                SET newPaymentStatus=define_new_payment_status(currentModelName, currentModelId, sideConnection);

                # Update the status of the user defined reason
                UPDATE fin_incomes_reasons
                SET payment_status=newPaymentStatus,
                    payment_status_modified=(IF (currentModelPaymentStatus COLLATE utf8mb4_unicode_ci = newPaymentStatus COLLATE utf8mb4_unicode_ci, payment_status_modified, NOW()))
                WHERE id=currentModelId;
                CALL log_payment_status_audit(
                    historyId,
                    currentModelName,
                    currentModelId,
                    currentModelPaymentStatus,
                    newPaymentStatus
                );
            END IF;

          END LOOP;
        CLOSE curReasons;
    END;//
delimiter ;

# PROCEDURE TO UPDATE THE REASON INVOICE STATUS
DROP PROCEDURE IF EXISTS update_incomes_reason_invoice_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_incomes_reason_invoice_status(IN incomes_reason_id INT, IN incomes_reason_type INT, IN eventType VARCHAR(255), OUT changed INT)
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE reasonId INT DEFAULT NULL;
        DECLARE curReasons CURSOR FOR
            SELECT IF(fir5.id IS NOT NULL,
                fir5.id, -- reason related to invoice
                IF(fir3.id IS NOT NULL,
                    fir3.id, -- reason related to debit/credit
                    IF (fir1.type>100,
                        fir1.id, -- reason itself
                        NULL
                    )
                )
            ) as reasonId
            FROM fin_incomes_reasons as fir1
            -- join if incomes_reason_id is debit/credit notes
            LEFT JOIN fin_reasons_relatives as frr1
                ON frr1.parent_model_name='Finance_Incomes_Reason'
                AND frr1.link_to_model_name='Finance_Incomes_Reason'
                AND frr1.parent_id=incomes_reason_id
                AND frr1.parent_id=fir1.id
                AND fir1.annulled_by=0
                AND fir1.active=1
                AND fir1.status="finished"
                AND fir1.type IN (3,4)
            LEFT JOIN fin_incomes_reasons as fir2
                ON frr1.link_to=fir2.id AND fir2.type=1
            LEFT JOIN fin_reasons_relatives as frr2
                ON frr2.parent_model_name='Finance_Incomes_Reason'
                AND frr2.link_to_model_name='Finance_Incomes_Reason'
                AND frr2.parent_id=fir2.id
                AND fir2.annulled_by=0
                AND fir2.active=1
                AND fir2.status="finished"
            LEFT JOIN fin_incomes_reasons as fir3
                ON frr2.link_to=fir3.id AND fir3.type>100
            -- join if incomes_reason_id is invoice
            LEFT JOIN fin_reasons_relatives as frr4
                ON frr4.parent_model_name='Finance_Incomes_Reason'
                AND frr4.link_to_model_name='Finance_Incomes_Reason'
                AND frr4.parent_id=incomes_reason_id
                AND frr4.parent_id=fir1.id
                AND fir1.annulled_by=0
                AND fir1.active=1
                AND fir1.status="finished"
                AND fir1.type=1
            LEFT JOIN fin_incomes_reasons as fir5
                ON frr4.link_to=fir5.id AND fir5.type>100
            WHERE fir1.id=incomes_reason_id;
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        OPEN curReasons;
          read_loop: LOOP
            FETCH curReasons INTO reasonId;
            IF done THEN
              LEAVE read_loop;
            END IF;

            IF (reasonId IS NULL) THEN
                ITERATE read_loop;
            END IF;

            SELECT invoice_status INTO @current_invoiced_status FROM fin_incomes_reasons_invoice_status WHERE `parent_id`=reasonId;
            SET @new_invoiced_status = define_new_invoiced_status(reasonId);
            IF (@new_invoiced_status COLLATE utf8mb4_unicode_ci != @current_invoiced_status COLLATE utf8mb4_unicode_ci) THEN
                UPDATE `fin_incomes_reasons_invoice_status` SET `invoice_status`=@new_invoiced_status, `invoice_status_modified`=NOW() WHERE `parent_id`=reasonId;

                SET changed = 1;
                IF (eventType != '') THEN
                    SET @changed_data='';
                    SET @changed_data = CONCAT(@changed_data, 'parent_id := ', incomes_reason_id, '\r\n');
                    SET @changed_data = CONCAT(@changed_data, 'parent_model_name := ', 'Finance_Incomes_Reason', '\r\n');
                    SET @changed_data = CONCAT(@changed_data, 'link_to := ', reasonId, '\r\n');
                    SET @changed_data = CONCAT(@changed_data, 'link_to_model_name := ', 'Finance_Incomes_Reason', '\r\n');
                    CALL log_invoice_status_history_audit(eventType, reasonId, @changed_data, @current_invoiced_status, @new_invoiced_status);
                END IF;
            END IF;
          END LOOP;
        CLOSE curReasons;
    END;//
delimiter ;

DROP PROCEDURE IF EXISTS `update_payment_distribution_status`;
delimiter //
CREATE DEFINER=`nzoomer`@`localhost` PROCEDURE `update_payment_distribution_status`(IN `side_connection` VARCHAR(255), IN `changed_model_id` INT, IN amnt DECIMAL(21,2),
    OUT ds VARCHAR(255), OUT da DECIMAL(21,2), OUT nda DECIMAL(21,2), OUT io INT)
    SQL SECURITY INVOKER
BEGIN
    SET @amount = NULL;
    SET @diff = NULL;
    SET @distributed = NULL;
    SET @distribution_status = NULL;
    SET @official = NULL;

    IF (amnt IS NULL) THEN
        SELECT amount INTO @amount
        FROM fin_payments
        WHERE id=changed_model_id;
    ELSE
        SET @amount = amnt;
    END IF;

    IF (side_connection = 'parent') THEN
        SELECT ROUND(SUM(`paid_amount`),2) INTO @distributed
        FROM fin_balance
        WHERE parent_id=changed_model_id AND parent_model_name='Finance_Payment';

        SELECT fb.paid_to INTO @official
        FROM fin_balance fb
        JOIN fin_incomes_reasons fir
            ON fir.id = fb.paid_to AND (fir.type = 2 OR fir.type > 100)
        WHERE fb.parent_id=changed_model_id AND fb.parent_model_name='Finance_Payment' AND fb.paid_to_model_name = 'Finance_Incomes_Reason'
        LIMIT 1;
    END IF;
    IF (side_connection = 'paid') THEN
        SELECT ROUND(SUM(`paid_amount`),2) INTO @distributed
        FROM fin_balance
        WHERE paid_to=changed_model_id AND paid_to_model_name='Finance_Payment';

        SELECT fb.paid_to INTO @official
        FROM fin_balance fb
        JOIN fin_expenses_reasons fer
            ON fer.id = fb.paid_to AND (fer.type = 21 OR fer.type > 100)
        WHERE fb.parent_id=changed_model_id AND fb.paid_to_model_name ='Finance_Payment' AND fb.parent_model_name = 'Finance_Expenses_Reason'
        LIMIT 1;
    END IF;
    IF (@distributed IS NULL) THEN
        SET @distributed = 0;
    END IF;
    SET @diff = ROUND(@amount - @distributed, 2);
    IF (@distributed = 0) THEN
        SET @distribution_status = 'not_distributed';
    ELSEIF (@diff = 0) THEN
        SET @distribution_status = 'distributed';
    ELSE
        SET @distribution_status = 'partial';
    END IF;
    IF (@official IS NOT NULL AND @official != 0 OR @distributed = 0) THEN
        SET @official = 0;
    ELSE
        SET @official = 1;
    END IF;

    IF (amnt IS NULL) THEN
        UPDATE fin_payments SET
            distribution_status = @distribution_status,
            distributed_amount = @distributed,
            not_distributed_amount = @diff,
            invoices_only = @official
        WHERE id = changed_model_id;
    ELSE
        SET ds = @distribution_status;
        SET da = @distributed;
        SET nda = @diff;
        SET io = @official;
    END IF;
END;//
delimiter ;

# FUNCTION TO DEFINE THE INVOICE STATUS OF AN INCOMES REASON
DROP FUNCTION IF EXISTS define_new_invoiced_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_invoiced_status(incomes_reason_id INT) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE reason_cursor_done INT DEFAULT FALSE;
        DECLARE new_invoiced_status VARCHAR(255);
        DECLARE incomes_reason_total DECIMAL(25,6);
        DECLARE calculated_invoiced_amount DECIMAL(25,6);
        DECLARE difference_invoiced_amount DECIMAL(25,6);
        DECLARE related_reason_id INT;
        DECLARE related_reason_total DECIMAL(25,6);
        DECLARE related_debit_credit_sum DECIMAL(25,6);
        DECLARE num_invoices INT DEFAULT 0;

        DECLARE reason_cursor CURSOR FOR
            SELECT fir.id, SUM(fir.total_with_vat)
            FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
            WHERE frr.link_to = incomes_reason_id
              AND fir.id = frr.parent_id
              AND fir.status = "finished"
              AND frr.parent_model_name = "Finance_Incomes_Reason"
              AND frr.link_to_model_name = "Finance_Incomes_Reason"
              AND fir.annulled_by=0 AND fir.type=1
              GROUP BY fir.id;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET reason_cursor_done = TRUE;

        IF (SELECT `id` FROM fin_incomes_reasons WHERE `id`=incomes_reason_id AND `type`>100 AND `active`=1 AND `status`='finished' AND `annulled_by`=0) THEN
            SELECT ROUND(total_with_vat, 6) INTO incomes_reason_total FROM fin_incomes_reasons WHERE `id`=incomes_reason_id;
            SET calculated_invoiced_amount=0;

            OPEN reason_cursor;
            reason_loop: LOOP
                FETCH reason_cursor INTO related_reason_id, related_reason_total;

                IF reason_cursor_done THEN
                    LEAVE reason_loop;
                END IF;

                SELECT SUM(total_with_vat) INTO related_debit_credit_sum
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
                WHERE frr.link_to = related_reason_id
                  AND fir.status = "finished"
                  AND fir.id = frr.parent_id
                  AND frr.parent_model_name = "Finance_Incomes_Reason"
                  AND frr.link_to_model_name = "Finance_Incomes_Reason"
                  AND fir.annulled_by=0 AND fir.type IN (3,4);

                SET calculated_invoiced_amount=ROUND((calculated_invoiced_amount+((IF (related_reason_total IS NULL, 0, related_reason_total)) + (IF (related_debit_credit_sum IS NULL, 0, related_debit_credit_sum)))), 6);

                SET num_invoices = num_invoices + 1;
            END LOOP;

            SET difference_invoiced_amount = incomes_reason_total - calculated_invoiced_amount;

            IF (difference_invoiced_amount<=0 AND num_invoices>0) THEN
                SET new_invoiced_status = 'invoiced';
            ELSEIF (difference_invoiced_amount<incomes_reason_total AND difference_invoiced_amount>0) THEN
                SET new_invoiced_status = 'partial';
            ELSEIF (difference_invoiced_amount>=incomes_reason_total) THEN
                SET new_invoiced_status = 'not_invoiced';
            END IF;
        ELSE
            SET new_invoiced_status = 'not_invoicable';
        END IF;

        return new_invoiced_status;
    END;//
delimiter ;

# PROCEDURE TO WRITE HISTORY FOR CHANGES IN INVOICE STATUS OF THE INCOMES REASONS
DROP PROCEDURE IF EXISTS log_invoice_status_history_audit;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE log_invoice_status_history_audit(IN current_action varchar(255), IN model_id INT, IN changed_data_vars text CHARACTER SET utf8mb4, IN old_invoice_status varchar(255), IN new_invoice_status varchar(255))
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        # write history
        INSERT INTO diag_invoice_status_reasons_history
        SET action=current_action,
            changed_row_id=model_id,
            changed_by=USER(),
            changed_data=changed_data_vars,
            previous_invoice_status=old_invoice_status,
            new_invoice_status=new_invoice_status,
            change_date=NOW();
    END;//
delimiter ;

######################################################################################
# 2025-01-21 - Updated settings for 'credilink_distribute_payments' dashlet plugin
#            - Fixed the define_new_payment_status functions

# Updated settings for 'credilink_distribute_payments' dashlet plugin
UPDATE `dashlets_plugins` SET `settings`='customer_type_client := 3\r\nincomes_reason_loan_contract_type := 104\r\nadditional_incomes_reasons_types := 106,107,108,109,110\r\nloan_contract_type := 6\r\nloan_contract_substatus_active := 22\r\nloan_contract_substatus_paid := 20' WHERE `type`='credilink_distribute_payments' AND `settings` LIKE '%incomes_reason_renew_mortgage%';

# Fixed the define_new_payment_status function
# FUNCTION TO DETERMINE THE PAYMENT STATUS OF A REASON AND ALL OF ITS RELATED REASONS
DROP FUNCTION IF EXISTS define_new_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_payment_status(searched_model_name varchar(255), searched_model_id int, side_connection varchar(255)) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
BEGIN
    # The new payment status
    DECLARE new_payment_status VARCHAR(255);

    # The current paid amount for this reason
    SET @current_paid_amount = NULL;

    # The model type of this reason
    SET @model_type = NULL;

    # Only the incomes and expenses reasons can have their payment status changed
    IF (searched_model_name='Finance_Expenses_Reason' OR searched_model_name='Finance_Incomes_Reason') THEN
        # Get the information for the type and for the current total of the reason
        IF (searched_model_name='Finance_Expenses_Reason') THEN
            SELECT fer.total_with_vat, fer.type INTO @model_total, @model_type
            FROM fin_expenses_reasons as fer
            WHERE fer.id=searched_model_id;
        ELSE
            SELECT fir.total_with_vat, fir.type INTO @model_total, @model_type
            FROM fin_incomes_reasons as fir
            WHERE fir.id=searched_model_id;
        END IF;

        # If the calculated amount is less than 0 then absolute value is taken
        IF (@model_total<0) THEN
            SET @model_total=@model_total*-1;
        END IF;

        # Round the decimal value of @model_total to second digit because it is used to compare with the paid amount which is rounded to second digit as well
        IF (@model_total IS NOT NULL) THEN
            SET @model_total=ROUND(@model_total, 2);
        ELSE
            SET @model_total=0;
        END IF;

        # Take actions depending on the model type
        IF (@model_type>100) THEN
            IF (searched_model_name='Finance_Incomes_Reason') THEN
                # For user-defined reasons with a custom type, the whole chain of relations should be taken
                # Get the paid amount from invoices and proformas
                SELECT SUM(IF(fir.type=1 AND fir.advance!=0 AND frr.shared<fr.paid_amount, frr.shared, fr.paid_amount)) INTO @invoiced_amount
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_balance AS fr
                WHERE frr.link_to=searched_model_id AND frr.parent_id=fir.id AND
                        fir.type IN (1, 2) AND
                        frr.parent_model_name="Finance_Incomes_Reason" AND
                        frr.link_to_model_name="Finance_Incomes_Reason" AND
                        fr.paid_to=fir.id AND
                        fr.paid_to_model_name="Finance_Incomes_Reason" AND
                        fir.annulled_by=0 AND
                        fir.active=1 AND
                        fir.status="finished";

                # Get the directly paid amount
                SELECT SUM(paid_amount) INTO @direct_paid_amount
                FROM fin_balance AS fr
                WHERE fr.paid_to=searched_model_id AND fr.paid_to_model_name="Finance_Incomes_Reason";

                # Get the paid amount from debit notices
                SELECT SUM(paid_amount) INTO @debit_notices_amount
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                WHERE frr.link_to=searched_model_id AND
                        frr.link_to_model_name="Finance_Incomes_Reason" AND
                        frr.parent_model_name="Finance_Incomes_Reason" AND
                        frr2.parent_id=fir.id AND
                        frr2.parent_model_name="Finance_Incomes_Reason" AND
                        fir.type=4 AND
                        frr2.link_to=frr.parent_id AND
                        frr2.link_to_model_name="Finance_Incomes_Reason" AND
                        fir.annulled_by=0 AND
                        fir.active=1 AND
                        fir.status="finished" AND
                        fr.paid_to=frr2.parent_id AND
                        fr.paid_to_model_name="Finance_Incomes_Reason";

                # Get the paid amount from credit notices
                SELECT SUM(paid_amount) INTO @credit_notices_amount
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                WHERE frr.link_to=searched_model_id AND
                        frr.link_to_model_name="Finance_Incomes_Reason" AND
                        frr.parent_model_name="Finance_Incomes_Reason" AND
                        frr2.parent_id=fir.id AND
                        frr2.parent_model_name="Finance_Incomes_Reason" AND
                        fir.type=3 AND
                        frr2.link_to=frr.parent_id AND
                        frr2.link_to_model_name="Finance_Incomes_Reason" AND
                        fir.annulled_by=0 AND
                        fir.active=1 AND
                        fir.status="finished" AND
                        fr.parent_id=frr2.parent_id AND
                        fr.parent_model_name="Finance_Incomes_Reason";

                # The amount will be sum of invoiced amount, directly paid amount and amount from credit/debit notices
                SET @current_paid_amount = (IF (@invoiced_amount IS NULL, 0, @invoiced_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount))+(IF (@debit_notices_amount IS NULL, 0, @debit_notices_amount))-(IF (@credit_notices_amount IS NULL, 0, @credit_notices_amount));
            ELSEIF (searched_model_name='Finance_Expenses_Reason') THEN
                # Get the directly paid amount
                SELECT SUM(paid_amount) INTO @direct_paid_amount
                FROM fin_balance AS fr
                WHERE fr.parent_id=searched_model_id AND fr.parent_model_name=searched_model_name;

                # Get paid amount to non-invoiced proforma (if such exists)
                SELECT SUM(paid_amount) INTO @proforma_amount
                FROM fin_expenses_reasons AS fer, fin_reasons_relatives AS frr, fin_balance AS fr
                WHERE frr.link_to=searched_model_id AND
                        frr.parent_id=fer.id AND
                        fer.type=21 AND
                        frr.parent_model_name=searched_model_name AND
                        frr.link_to_model_name=searched_model_name AND
                        fr.parent_id=fer.id AND
                        fr.parent_model_name=searched_model_name AND
                        fer.annulled_by=0 AND
                        fer.active=1 AND
                        fer.status="finished" AND
                        fer.payment_status!="invoiced";

                # The amount will be sum of direcly paid amount to reason and paid amount to non-invoiced expense proforma
                SET @current_paid_amount = (IF (@proforma_amount IS NULL, 0, @proforma_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount));
            END IF;
        ELSE
            # For system-defined reasons only the data from the balance table is enough
            IF (side_connection='parent') THEN
                SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name=searched_model_name AND parent_id=searched_model_id);
            ELSE
                SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE paid_to_model_name=searched_model_name AND paid_to=searched_model_id);
            END IF;
        END IF;

        # Set default value of @current_paid_amount
        IF (@current_paid_amount IS NULL) THEN
            SET @current_paid_amount=0;
        END IF;

        # Calculate the amount difference to retrieve the status
        SET @amount_difference = ROUND(@model_total - @current_paid_amount, 2);
        IF (@amount_difference = @model_total AND @model_total>0) THEN
            SET new_payment_status = 'unpaid';
        ELSEIF (@amount_difference<=0) THEN
            SET new_payment_status = 'paid';
        ELSE
            SET new_payment_status = 'partial';
        END IF;
    END IF;

    # return the new status as string
    return new_payment_status;
END;//
delimiter ;

#########################################################################################
# 2025-01-22 - Add analyses filter validation messages translations

# Add analyses filter validation messages translations
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('analyses', 'filters_validation', 'bg', 'analyses_filters_validation_fill_required_filter', 'Моля, попълнете задължителния филтър „{$filter.label}“!'),
  ('analyses', 'filters_validation', 'en', 'analyses_filters_validation_fill_required_filter', 'Please, fill the required filter "{$filter.label}"!'),
  ('analyses', 'filters_validation', 'de', 'analyses_filters_validation_fill_required_filter', 'Bitte füllen Sie den erforderlichen Filter "{$filter.label}" aus!'),
  ('analyses', 'filters_validation', 'ro', 'analyses_filters_validation_fill_required_filter', 'Vă rugăm să completați filtrul necesar „{$filter.label}”!');

#########################################################################################
# 2025-01-23 - Fix the define_new_invoiced_status when the reason is related to advance with shared amount

DROP FUNCTION IF EXISTS define_new_invoiced_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_invoiced_status(incomes_reason_id INT) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE reason_cursor_done INT DEFAULT FALSE;
        DECLARE new_invoiced_status VARCHAR(255);
        DECLARE incomes_reason_total DECIMAL(25,6);
        DECLARE calculated_invoiced_amount DECIMAL(25,6);
        DECLARE difference_invoiced_amount DECIMAL(25,6);
        DECLARE related_reason_id INT;
        DECLARE related_reason_total DECIMAL(25,6);
        DECLARE related_debit_credit_sum DECIMAL(25,6);
        DECLARE num_invoices INT DEFAULT 0;

        DECLARE reason_cursor CURSOR FOR
            SELECT fir.id, SUM(IF(fir.advance!=0 AND frr.shared!=0,
                                frr.shared*(1+fir.total_vat_rate/100),
                                fir.total_with_vat))
            FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
            WHERE frr.link_to = incomes_reason_id
              AND fir.id = frr.parent_id
              AND fir.status = "finished"
              AND frr.parent_model_name = "Finance_Incomes_Reason"
              AND frr.link_to_model_name = "Finance_Incomes_Reason"
              AND fir.annulled_by=0 AND fir.type=1
              GROUP BY fir.id;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET reason_cursor_done = TRUE;

        IF (SELECT `id` FROM fin_incomes_reasons WHERE `id`=incomes_reason_id AND `type`>100 AND `active`=1 AND `status`='finished' AND `annulled_by`=0) THEN
            SELECT ROUND(total_with_vat, 6) INTO incomes_reason_total FROM fin_incomes_reasons WHERE `id`=incomes_reason_id;
            SET calculated_invoiced_amount=0;

            OPEN reason_cursor;
            reason_loop: LOOP
                FETCH reason_cursor INTO related_reason_id, related_reason_total;

                IF reason_cursor_done THEN
                    LEAVE reason_loop;
                END IF;

                SELECT SUM(total_with_vat) INTO related_debit_credit_sum
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
                WHERE frr.link_to = related_reason_id
                  AND fir.status = "finished"
                  AND fir.id = frr.parent_id
                  AND frr.parent_model_name = "Finance_Incomes_Reason"
                  AND frr.link_to_model_name = "Finance_Incomes_Reason"
                  AND fir.annulled_by=0 AND fir.type IN (3,4);

                SET calculated_invoiced_amount=ROUND((calculated_invoiced_amount+((IF (related_reason_total IS NULL, 0, related_reason_total)) + (IF (related_debit_credit_sum IS NULL, 0, related_debit_credit_sum)))), 6);

                SET num_invoices = num_invoices + 1;
            END LOOP;

            SET difference_invoiced_amount = incomes_reason_total - calculated_invoiced_amount;

            IF (difference_invoiced_amount<=0 AND num_invoices>0) THEN
                SET new_invoiced_status = 'invoiced';
            ELSEIF (difference_invoiced_amount<incomes_reason_total AND difference_invoiced_amount>0) THEN
                SET new_invoiced_status = 'partial';
            ELSEIF (difference_invoiced_amount>=incomes_reason_total) THEN
                SET new_invoiced_status = 'not_invoiced';
            END IF;
        ELSE
            SET new_invoiced_status = 'not_invoicable';
        END IF;

        return new_invoiced_status;
    END;//
delimiter ;

#########################################################################################
# 2025-02-03 - Fixed the update_incomes_reason_invoice_status
#            - that defines which is the reason to be updated upon invoice, debit or credit annulment


# Fixed the update_incomes_reason_invoice_status that defines which is the reason to be updated upon invoice, debit or credit annulment

# PROCEDURE TO UPDATE THE REASON INVOICE STATUS
DROP PROCEDURE IF EXISTS update_incomes_reason_invoice_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_incomes_reason_invoice_status(IN incomes_reason_id INT, IN incomes_reason_type INT, IN eventType VARCHAR(255), OUT changed INT)
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE reasonId INT DEFAULT NULL;
        DECLARE curReasons CURSOR FOR
            SELECT IF(fir5.id IS NOT NULL,
                fir5.id, -- reason related to invoice
                IF(fir3.id IS NOT NULL,
                    fir3.id, -- reason related to debit/credit
                    IF (fir1.type>100,
                        fir1.id, -- reason itself
                        NULL
                    )
                )
            ) as reasonId
            FROM fin_incomes_reasons as fir1
            -- join if incomes_reason_id is debit/credit notes
            LEFT JOIN fin_reasons_relatives as frr1
                ON frr1.parent_model_name='Finance_Incomes_Reason'
                AND frr1.link_to_model_name='Finance_Incomes_Reason'
                AND frr1.parent_id=incomes_reason_id
                AND frr1.parent_id=fir1.id
                -- AND fir1.annulled_by=0
                -- AND fir1.active=1
                -- AND fir1.status="finished"
                AND fir1.type IN (3,4)
            LEFT JOIN fin_incomes_reasons as fir2
                ON frr1.link_to=fir2.id AND fir2.type=1
            LEFT JOIN fin_reasons_relatives as frr2
                ON frr2.parent_model_name='Finance_Incomes_Reason'
                AND frr2.link_to_model_name='Finance_Incomes_Reason'
                AND frr2.parent_id=fir2.id
                AND fir2.annulled_by=0
                AND fir2.active=1
                AND fir2.status="finished"
            LEFT JOIN fin_incomes_reasons as fir3
                ON frr2.link_to=fir3.id AND fir3.type>100
            -- join if incomes_reason_id is invoice
            LEFT JOIN fin_reasons_relatives as frr4
                ON frr4.parent_model_name='Finance_Incomes_Reason'
                AND frr4.link_to_model_name='Finance_Incomes_Reason'
                AND frr4.parent_id=incomes_reason_id
                AND frr4.parent_id=fir1.id
                -- AND fir1.annulled_by=0
                -- AND fir1.active=1
                -- AND fir1.status="finished"
                AND fir1.type=1
            LEFT JOIN fin_incomes_reasons as fir5
                ON frr4.link_to=fir5.id AND fir5.type>100
            WHERE fir1.id=incomes_reason_id;
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        OPEN curReasons;
          read_loop: LOOP
            FETCH curReasons INTO reasonId;
            IF done THEN
              LEAVE read_loop;
            END IF;

            IF (reasonId IS NULL) THEN
                ITERATE read_loop;
            END IF;

            SELECT invoice_status INTO @current_invoiced_status FROM fin_incomes_reasons_invoice_status WHERE `parent_id`=reasonId;
            SET @new_invoiced_status = define_new_invoiced_status(reasonId);
            IF (@new_invoiced_status COLLATE utf8mb4_unicode_ci != @current_invoiced_status COLLATE utf8mb4_unicode_ci) THEN
                UPDATE `fin_incomes_reasons_invoice_status` SET `invoice_status`=@new_invoiced_status, `invoice_status_modified`=NOW() WHERE `parent_id`=reasonId;

                SET changed = 1;
                IF (eventType != '') THEN
                    SET @changed_data='';
                    SET @changed_data = CONCAT(@changed_data, 'parent_id := ', incomes_reason_id, '\r\n');
                    SET @changed_data = CONCAT(@changed_data, 'parent_model_name := ', 'Finance_Incomes_Reason', '\r\n');
                    SET @changed_data = CONCAT(@changed_data, 'link_to := ', reasonId, '\r\n');
                    SET @changed_data = CONCAT(@changed_data, 'link_to_model_name := ', 'Finance_Incomes_Reason', '\r\n');
                    CALL log_invoice_status_history_audit(eventType, reasonId, @changed_data, @current_invoiced_status, @new_invoiced_status);
                END IF;
            END IF;
          END LOOP;
        CLOSE curReasons;
    END;//
delimiter ;

#########################################################################################
# 2025-02-04 - Add trigger for automatically adding permission for admin to all new analyses
#            - Create table analyses_history for recording history of analyses changes
#            - Add trigger for recording history of analyses changes

# Add trigger for automatically adding permission for admin to all new analyses
DROP TRIGGER IF EXISTS `after_insert_analyses`;
DELIMITER $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER `after_insert_analyses` AFTER INSERT ON `analyses`
FOR EACH ROW
BEGIN
  DECLARE newDefinitionId INT;

  # Add roles definition for the new analysis
  INSERT IGNORE INTO `roles_definitions`
    SET `module`   = 'analyses',
      `controller` = '',
      `action`     = 'generate_analysis',
      `model_type` = NEW.id,
      `position`   = 0;

  # Get the roles definition ID for the new analysis
  SELECT `id` INTO newDefinitionId
    FROM `roles_definitions`
  WHERE `module` = 'analyses'
    AND `controller` = ''
    AND `action` = 'generate_analysis'
    AND `model_type` = NEW.id
  LIMIT 1;

  # Add full admin rights to the new analysis
  INSERT IGNORE INTO `roles_permissions`
    SET `parent_id` = 1,
      `definition_id` = newDefinitionId,
      `permission` = 'all';
END;$$$
DELIMITER ;

# Create table analyses_history for recording history of analyses changes
DROP TABLE IF EXISTS `analyses_history`;
CREATE TABLE `analyses_history` (
  `history_id`       INT(11)      NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'Unique ID of the current history record.',
  `history_added`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when the current history record was added.',
  `history_added_by` VARCHAR(255) NULL COMMENT 'MySQL username of the user who added the current history record.',
  `analysis_id`      INT(11)      NOT NULL COMMENT 'ID of the analysis.',
  `filters`          LONGTEXT     NULL COMMENT 'Previous value of the analysis filters.',
  `data_sources`     LONGTEXT     NULL COMMENT 'Previous value of the analysis data_sources.',
  `display_objects`  LONGTEXT     NULL COMMENT 'Previous value of the analysis display_objects.',
  `placeholders`     LONGTEXT     NULL COMMENT 'Previous value of the analysis placeholders.',
  `css_template`     LONGTEXT     NULL COMMENT 'Previous value of the analysis css_template.',
  `position`         SMALLINT     NOT NULL COMMENT 'Previous value of the analysis position.',
  `is_portal`        TINYINT(1)   NOT NULL COMMENT 'Previous value of the analysis is_portal.',
  `visible`          TINYINT(1)   NOT NULL COMMENT 'Previous value of the analysis visible.',
  `modified`         DATETIME     NULL COMMENT 'Previous value of the analysis modified.',
  `modified_by`      VARCHAR(255) NULL COMMENT 'Previous value of the analysis modified_by.'
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'Contains the old version of each analysis.';

# Add trigger for recording history of analyses changes
DROP TRIGGER IF EXISTS `after_update_analyses`;
DELIMITER $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER `after_update_analyses` AFTER UPDATE ON `analyses`
FOR EACH ROW
BEGIN
  # Record history for the previous version of the changed analysis
  INSERT INTO `analyses_history`
    SET `history_added`  = NOW(),
      `history_added_by` = USER(),
      `analysis_id`      = OLD.id,
      `filters`          = OLD.filters,
      `data_sources`     = OLD.data_sources,
      `display_objects`  = OLD.display_objects,
      `placeholders`     = OLD.placeholders,
      `css_template`     = OLD.css_template,
      `position`         = OLD.position,
      `is_portal`        = OLD.is_portal,
      `visible`          = OLD.visible,
      `modified`         = OLD.modified,
      `modified_by`      = OLD.modified_by;
END;$$$
DELIMITER ;

#########################################################################################
# 2025-02-11 - Add missing permissions to all existing analyses for the admin role

# Add missing permissions to all existing analyses for the admin role
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'analyses', '', 'generate_analysis', `id`, 0, 0
    FROM `analyses`;
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all'
    FROM `roles_definitions` AS `rd`
    WHERE rd.module = 'analyses'
      AND rd.controller = ''
      AND rd.action = 'generate_analysis';

#########################################################################################
# 2025-02-19 - Add permissions for edit and view of expenses documents

# Add permissions for edit and view of expenses documents
# The new action permissions (edit and view) will be added from position 4, so move the other definitions
UPDATE `roles_definitions` AS `rd`
  JOIN `fin_documents_types` AS `fdt`
    ON (rd.module = 'finance'
      AND rd.controller = LOWER(CONCAT(REPLACE(fdt.model, 'Finance_', ''), 's'))
      AND fdt.model != 'Finance_Invoices_Template'
      AND rd.model_type = fdt.id)
  SET rd.position = rd.position + 1
  WHERE rd.position >= 4;
UPDATE `roles_definitions` AS `rd`
  JOIN `fin_documents_types` AS `fdt`
    ON (rd.module = 'finance'
      AND rd.controller = LOWER(CONCAT(REPLACE(fdt.model, 'Finance_', ''), 's'))
      AND fdt.model != 'Finance_Invoices_Template'
      AND rd.model_type = fdt.id)
  SET rd.position = rd.position + 1
  WHERE rd.position >= 4;
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'finance', LOWER(CONCAT(REPLACE(`model`, 'Finance_', ''), 's')), 'edit', `id`, 1, 4
    FROM `fin_documents_types`
    WHERE `model` != 'Finance_Invoices_Template';
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'finance', LOWER(CONCAT(REPLACE(`model`, 'Finance_', ''), 's')), 'view', `id`, 1, 5
    FROM `fin_documents_types`
    WHERE `model` != 'Finance_Invoices_Template';

# For the new definitions set permissions as for list or for general action (whichever is less)
  INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
    SELECT
      rp_type_list.parent_id,
      rd_type_action.id,
      CASE
        WHEN rp_type_list.permission = 'none'  OR rp_action.permission = 'none'  THEN 'none'
        WHEN rp_type_list.permission = 'mine'  OR rp_action.permission = 'mine'  THEN 'mine'
        WHEN rp_type_list.permission = 'group' OR rp_action.permission = 'group' THEN 'group'
        ELSE 'all'
      END
    FROM `fin_documents_types` AS `fdt`
    JOIN `roles_definitions` AS `rd_type_list`
      ON (rd_type_list.module = 'finance'
        AND rd_type_list.controller = LOWER(CONCAT(REPLACE(fdt.model, 'Finance_', ''), 's'))
        AND rd_type_list.action = 'list'
        AND rd_type_list.model_type = fdt.id)
    JOIN `roles_permissions` AS `rp_type_list`
      ON (rp_type_list.definition_id = rd_type_list.id)
    JOIN `roles_definitions` AS `rd_type_action`
      ON (rd_type_action.module = rd_type_list.module
        AND rd_type_action.controller = rd_type_list.controller
        AND rd_type_action.model_type = rd_type_list.model_type
        AND rd_type_action.action IN ('edit', 'view'))
    JOIN `roles_definitions` AS `rd_action`
      ON (rd_action.module = rd_type_action.module
        AND rd_action.controller = rd_type_action.controller
        AND rd_action.model_type = 0
        AND rd_action.action = rd_type_action.action)
    JOIN `roles_permissions` AS `rp_action`
      ON (rp_action.definition_id = rd_action.id
        AND rp_action.parent_id = rp_type_list.parent_id);

# Change typeless roles definitions for edit and view to have only two options
# as they will be used only for allowed/forbidden check
UPDATE `fin_documents_types` AS `fdt`
  JOIN `roles_definitions` AS `rd`
    ON (rd.module = 'finance'
      AND rd.controller = LOWER(CONCAT(REPLACE(fdt.model, 'Finance_', ''), 's'))
      AND rd.model_type = 0)
  SET rd.requires_model = 0
  WHERE rd.action IN ('edit', 'view');
UPDATE `fin_documents_types` AS `fdt`
  JOIN `roles_definitions` AS `rd`
    ON (rd.module = 'finance'
      AND rd.controller = LOWER(CONCAT(REPLACE(fdt.model, 'Finance_', ''), 's'))
      AND rd.model_type = 0)
  JOIN `roles_permissions` AS `rp`
    ON (rd.id = rp.definition_id)
  SET rp.permission = 'all'
  WHERE rd.action IN ('edit', 'view')
    AND rp.permission IN ('mine', 'group');

#########################################################################################
# 2025-03-18 - Add permissions for edit, multiedit and view of documents

# Add permissions for edit, multiedit and view of documents
UPDATE `roles_definitions` AS `rd`
  JOIN `documents_types` AS `dt`
    ON (rd.module = 'documents'
      AND rd.controller = ''
      AND rd.model_type = dt.id)
  LEFT JOIN `roles_definitions` AS `rd1`
    ON (rd1.module = rd.module
      AND rd1.controller = rd.controller
      AND rd1.model_type = rd.model_type
      AND rd1.action = 'edit')
  SET rd.position = rd.position + 3
  WHERE rd.position >= 6
    AND rd1.id IS NULL;
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'documents', '', 'edit', `id`, 1, 6
    FROM `documents_types`;
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'documents', '', 'multiedit', `id`, 1, 7
    FROM `documents_types`;
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'documents', '', 'view', `id`, 1, 8
    FROM `documents_types`;

# For the new definitions set permissions as for list or for general action (whichever is less)
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT
    rp_type_list.parent_id,
    rd_type_action.id,
    CASE
      WHEN rp_type_list.permission = 'none'  OR rp_action.permission = 'none'  THEN 'none'
      WHEN rp_type_list.permission = 'mine'  OR rp_action.permission = 'mine'  THEN 'mine'
      WHEN rp_type_list.permission = 'group' OR rp_action.permission = 'group' THEN 'group'
      ELSE 'all'
    END
  FROM `documents_types` AS `dt`
  JOIN `roles_definitions` AS `rd_type_action`
    ON (rd_type_action.module = 'documents'
      AND rd_type_action.controller = ''
      AND rd_type_action.model_type = dt.id
      AND rd_type_action.action IN ('edit', 'multiedit', 'view'))
  JOIN `roles_definitions` AS `rd_type_list`
    ON (rd_type_list.module = rd_type_action.module
      AND rd_type_list.controller = rd_type_action.controller
      AND rd_type_list.action = 'list'
      AND rd_type_list.model_type = rd_type_action.model_type)
  JOIN `roles_permissions` AS `rp_type_list`
    ON (rp_type_list.definition_id = rd_type_list.id)
  JOIN `roles_definitions` AS `rd_action`
    ON (rd_action.module = rd_type_action.module
      AND rd_action.controller = rd_type_action.controller
      AND rd_action.model_type = 0
      AND rd_action.action = rd_type_action.action)
  JOIN `roles_permissions` AS `rp_action`
    ON (rp_action.definition_id = rd_action.id
      AND rp_action.parent_id = rp_type_list.parent_id);

# Change typeless roles definitions for edit, multiedit and view to have only two options
# as they will be used only for allowed/forbidden check
UPDATE `roles_definitions`
  SET `requires_model` = 0
  WHERE `module` = 'documents'
    AND `controller` = ''
    AND `model_type` = 0
    AND `action` IN ('edit', 'multiedit', 'view');
UPDATE `roles_definitions` AS `rd`
  JOIN `roles_permissions` AS `rp`
    ON (rd.module = 'documents'
      AND rd.controller = ''
      AND rd.model_type = 0
      AND rd.action IN ('edit', 'multiedit', 'view')
      AND rp.definition_id = rd.id)
  SET rp.permission = 'all'
  WHERE rp.permission IN ('mine', 'group');

#########################################################################################
# 2025-03-19 - Added tables for Callback and Eurotrust modules
#            - Added Eurotrust settings

# Added tables for Callback and Eurotrust modules
DROP TABLE IF EXISTS `callbacks`;
CREATE TABLE `callbacks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hash` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `vendor` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `method` text COLLATE utf8mb4_unicode_ci,
  `added` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `processed` datetime DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `hash` (`hash`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `callbacks_requests`;
CREATE TABLE `callbacks_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_id` int NOT NULL,
  `hash` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `request_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uri` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remote_addr` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `get` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `post` json DEFAULT NULL,
  `body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `files` json DEFAULT NULL,
  `headers` json DEFAULT NULL,
  `added` datetime NOT NULL,
  `processed` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `eurotrust_files`;
CREATE TABLE `eurotrust_files` (
  `parent_id` int NOT NULL,
  `thread_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_id` int DEFAULT NULL,
  `source` json DEFAULT NULL,
  `signed_file_id` int DEFAULT NULL,
  `destination` text COLLATE utf8mb4_unicode_ci,
  `added` datetime DEFAULT NULL,
  `added_by` int DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  `modified_by` int DEFAULT NULL,
  UNIQUE KEY `thread_id` (`thread_id`,`parent_id`,`file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `eurotrust_log`;
CREATE TABLE `eurotrust_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `endpoint` text COLLATE utf8mb4_unicode_ci,
  `code` int DEFAULT NULL,
  `request` text COLLATE utf8mb4_unicode_ci,
  `response` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `request_headers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `response_headers` text COLLATE utf8mb4_unicode_ci,
  `curl_info` json DEFAULT NULL,
  `context` json DEFAULT NULL,
  `added` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `eurotrust_operations`;
CREATE TABLE `eurotrust_operations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `thread_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `status` enum('sent','partially_signed','signed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'sent',
  `model` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `model_id` int NOT NULL DEFAULT '0',
  `context` json DEFAULT NULL,
  `publicKey` text COLLATE utf8mb4_unicode_ci,
  `privateKey` text COLLATE utf8mb4_unicode_ci,
  `added` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `added_by` int NOT NULL DEFAULT '0',
  `modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `modified_by` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `thread_id` (`thread_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `eurotrust_transactions`;
CREATE TABLE `eurotrust_transactions` (
  `parent_id` int NOT NULL,
  `thread_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_identified_by` enum('email','identificationNumber','phone') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_identity` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` int DEFAULT NULL,
  `added` datetime DEFAULT NULL,
  `added_by` int DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  `modified_by` int DEFAULT NULL,
  UNIQUE KEY `parent_id` (`parent_id`,`thread_id`,`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

# Added Eurotrust settings
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
  (NULL, 'eurotrust', 'baseUrl', 'https://v.evrotrust.com/vendor'),
  (NULL, 'eurotrust', 'baseUrlTest', 'https://et.test.iteco.bg/vendor'),
  (NULL, 'eurotrust', 'vendorKey', ''),
  (NULL, 'eurotrust', 'vendorNumber', ''),
  (NULL, 'eurotrust', 'testMode', '1');

INSERT IGNORE INTO `settings_i18n` VALUES
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'baseUrl'),      'Адрес на продукционното API на Eurotrust', '', 'bg', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'baseUrlTest'),  'Адрес на тестовото API на Eurotrust', '', 'bg', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'vendorKey'),    'Вендор ключ за ползване на API-то на Eurotrust', '', 'bg', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'vendorNumber'), 'Вендор номер за ползване на API-то на Eurotrust', '', 'bg', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'testMode'),     'Тестов режим за ползване на API-то на Eurotrust', '', 'bg', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'baseUrl'),      'Base URL of production API of Eurotrust', '', 'en', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'baseUrlTest'),  'Base URL of test API of Eurotrust', '', 'en', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'vendorKey'),    'Vendor Key for API of Eurotrust', '', 'en', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'vendorNumber'), 'Vendor Number for API of Eurotrust', '', 'en', NOW()),
((SELECT `id` FROM `settings` WHERE `section` = 'eurotrust'     AND `name` = 'testMode'),     'Test mode of Eurotrust', '', 'en', NOW());

# Fixed typo in a i18n param from rev 20296
UPDATE `i18n` SET name='error_config_empty_params' WHERE name='error_config_empty_парамс';

# Added Eurotrust settings
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
  (NULL, 'eurotrust', 'logRequest', '0');

#########################################################################################
# 2025-03-20 - Added column expires to the callback table

ALTER TABLE `callbacks`
    ADD COLUMN `expires` DATETIME NOT NULL DEFAULT '0000-00-00 00:00:00' AFTER `added`;

#########################################################################################
# 2025-03-21 - Fixed i18n params, incorrectly added with rev 20296

# Fixed i18n params, incorrectly added with rev 20296
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  # de
  ('configurators', '', 'de', 'error_config_empty_name', 'Leerer Name'),
  ('configurators', '', 'de', 'error_config_empty_model', 'Leeres Modell'),
  ('configurators', '', 'de', 'error_config_empty_model_type', 'Leerer Modelltyp'),
  ('configurators', '', 'de', 'error_config_empty_group_num', 'Leerer Gruppierungsindex'),
  ('configurators', '', 'de', 'error_config_empty_config_num', 'Leerer Gruppierungsindex'),
  ('configurators', '', 'de', 'error_config_empty_params', 'Leere Daten'),
  # ro
  ('configurators', '', 'ro', 'error_config_empty_name', 'Nume gol'),
  ('configurators', '', 'ro', 'error_config_empty_model', 'Model gol'),
  ('configurators', '', 'ro', 'error_config_empty_model_type', 'Tip de model gol'),
  ('configurators', '', 'ro', 'error_config_empty_group_num', 'Index de grupare gol'),
  ('configurators', '', 'ro', 'error_config_empty_config_num', 'Index de grupare gol'),
  ('configurators', '', 'ro', 'error_config_empty_params', 'Date goale');

#########################################################################################
# 2025-03-24 - Fixed define_new_payment_status function when calculating incomes_reason payment status with advance

# Fixed define_new_payment_status function when calculating incomes_reason payment status with advance
DROP FUNCTION IF EXISTS define_new_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_payment_status(searched_model_name varchar(255), searched_model_id int, side_connection varchar(255)) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        # The new payment status
        DECLARE new_payment_status VARCHAR(255);

        # The current paid amount for this reason
        SET @current_paid_amount = NULL;

        # The model type of this reason
        SET @model_type = NULL;

        # Only the incomes and expenses reasons can have their payment status changed
        IF (searched_model_name='Finance_Expenses_Reason' OR searched_model_name='Finance_Incomes_Reason') THEN
            # Get the information for the type and for the current total of the reason
            IF (searched_model_name='Finance_Expenses_Reason') THEN
                SELECT fer.total_with_vat, fer.type INTO @model_total, @model_type
                  FROM fin_expenses_reasons as fer
                  WHERE fer.id=searched_model_id;
            ELSE
                SELECT fir.total_with_vat, fir.type INTO @model_total, @model_type
                  FROM fin_incomes_reasons as fir
                  WHERE fir.id=searched_model_id;
            END IF;

            # If the calculated amount is less than 0 then absolute value is taken
            IF (@model_total<0) THEN
                SET @model_total=@model_total*-1;
            END IF;

            # Round the decimal value of @model_total to second digit because it is used to compare with the paid amount which is rounded to second digit as well
            IF (@model_total IS NOT NULL) THEN
                SET @model_total=ROUND(@model_total, 2);
            ELSE
                SET @model_total=0;
            END IF;

            # Take actions depending on the model type
            IF (@model_type>100) THEN
                IF (searched_model_name='Finance_Incomes_Reason') THEN
                    # For user-defined reasons with a custom type, the whole chain of relations should be taken
                    # Get the paid amount from invoices and proformas
                    SELECT SUM(IF(fir.type=1 AND fir.advance!=0 AND frr.shared*(1+fir.total_vat_rate/100)<fr.paid_amount, frr.shared*(1+fir.total_vat_rate/100), fr.paid_amount)) INTO @invoiced_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND frr.parent_id=fir.id AND
                            fir.type IN (1, 2) AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            fr.paid_to=fir.id AND
                            fr.paid_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished";

                    # Get the directly paid amount
                    SELECT SUM(paid_amount) INTO @direct_paid_amount
                      FROM fin_balance AS fr
                      WHERE fr.paid_to=searched_model_id AND fr.paid_to_model_name="Finance_Incomes_Reason";

                    # Get the paid amount from debit notices
                    SELECT SUM(paid_amount) INTO @debit_notices_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr2.parent_id=fir.id AND
                            frr2.parent_model_name="Finance_Incomes_Reason" AND
                            fir.type=4 AND
                            frr2.link_to=frr.parent_id AND
                            frr2.link_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished" AND
                            fr.paid_to=frr2.parent_id AND
                            fr.paid_to_model_name="Finance_Incomes_Reason";

                    # Get the paid amount from credit notices
                    SELECT SUM(paid_amount) INTO @credit_notices_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr2.parent_id=fir.id AND
                            frr2.parent_model_name="Finance_Incomes_Reason" AND
                            fir.type=3 AND
                            frr2.link_to=frr.parent_id AND
                            frr2.link_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished" AND
                            fr.parent_id=frr2.parent_id AND
                            fr.parent_model_name="Finance_Incomes_Reason";

                    # The amount will be sum of invoiced amount, directly paid amount and amount from credit/debit notices
                    SET @current_paid_amount = (IF (@invoiced_amount IS NULL, 0, @invoiced_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount))+(IF (@debit_notices_amount IS NULL, 0, @debit_notices_amount))-(IF (@credit_notices_amount IS NULL, 0, @credit_notices_amount));
                ELSEIF (searched_model_name='Finance_Expenses_Reason') THEN
                    # Get the directly paid amount
                    SELECT SUM(paid_amount) INTO @direct_paid_amount
                      FROM fin_balance AS fr
                      WHERE fr.parent_id=searched_model_id AND fr.parent_model_name=searched_model_name;

                    # Get paid amount to non-invoiced proforma (if such exists)
                    SELECT SUM(paid_amount) INTO @proforma_amount
                      FROM fin_expenses_reasons AS fer, fin_reasons_relatives AS frr, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.parent_id=fer.id AND
                            fer.type=21 AND
                            frr.parent_model_name=searched_model_name AND
                            frr.link_to_model_name=searched_model_name AND
                            fr.parent_id=fer.id AND
                            fr.parent_model_name=searched_model_name AND
                            fer.annulled_by=0 AND
                            fer.active=1 AND
                            fer.status="finished" AND
                            fer.payment_status!="invoiced";

                    # The amount will be sum of direcly paid amount to reason and paid amount to non-invoiced expense proforma
                    SET @current_paid_amount = (IF (@proforma_amount IS NULL, 0, @proforma_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount));
                END IF;
            ELSE
                # For system-defined reasons only the data from the balance table is enough
                IF (side_connection='parent') THEN
                    SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name=searched_model_name AND parent_id=searched_model_id);
                ELSE
                    SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE paid_to_model_name=searched_model_name AND paid_to=searched_model_id);
                END IF;
            END IF;

            # Set default value of @current_paid_amount
            IF (@current_paid_amount IS NULL) THEN
                SET @current_paid_amount=0;
            END IF;

            # Calculate the amount difference to retrieve the status
            SET @amount_difference = ROUND(@model_total - @current_paid_amount, 2);
            IF (@amount_difference = @model_total AND @model_total>0) THEN
                SET new_payment_status = 'unpaid';
            ELSEIF (@amount_difference<=0) THEN
                SET new_payment_status = 'paid';
            ELSE
                SET new_payment_status = 'partial';
            END IF;
        END IF;

        # return the new status as string
        return new_payment_status;
    END;//
delimiter ;

#########################################################################################
# 2025-03-25 - Remove edit permission for annulments, because they can't be edited and the permission was incorrectly added in previous edit

# Remove edit permission for annulments, because they can't be edited and the permission was incorrectly added in previous edit
DELETE FROM `roles_definitions`
  WHERE `module` = 'finance'
    AND `controller` = 'annulments'
    AND `action` = 'edit'
    AND `model_type` != 0;

#########################################################################################
# 2025-03-26 - Added function that can normalize the new lines between settings in text field in the DB
#            - Added triggers on insert and update that will normalize the new line symbol between settings

# Added function that can normalize the new lines between settings in text field in the DB
SET NAMES utf8mb4;
SET collation_connection=utf8mb4_unicode_ci;
DROP FUNCTION IF EXISTS fix_new_lines;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION fix_new_lines(x TEXT CHARACTER SET utf8mb4) RETURNS text CHARACTER SET utf8mb4
  LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
  BEGIN
    SET x = REGEXP_REPLACE(x, '\\r\\n|\\n|\\r', '\n');
    return x;
  END;//
delimiter ;

# Added triggers on insert and update that will normalize the new line symbol between settings
DROP TRIGGER IF EXISTS reports_fix_new_lines_insert;
delimiter $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER reports_fix_new_lines_insert BEFORE INSERT ON reports
FOR EACH ROW
BEGIN
    SET NEW.`settings`=fix_new_lines(NEW.`settings`);
END;$$$
delimiter ;

DROP TRIGGER IF EXISTS reports_fix_new_lines_update;
delimiter $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER reports_fix_new_lines_update BEFORE UPDATE ON reports
FOR EACH ROW
BEGIN
    SET NEW.`settings`=fix_new_lines(NEW.`settings`);
END;$$$
delimiter ;

#########################################################################################
# 2025-03-27 - Updated all settings in reports to use only the \n symbol for new lines

# Updated all settings in reports to use only the \n symbol for new lines
UPDATE `reports` SET `settings`=fix_new_lines(`settings`) WHERE `settings` like '%\r%';

#########################################################################################
# 2025-04-02 - Updated functions to define payment status and invoice status

# FUNCTION TO DETERMINE THE PAYMENT STATUS OF A REASON AND ALL OF ITS RELATED REASONS
DROP FUNCTION IF EXISTS define_new_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_payment_status(searched_model_name varchar(255), searched_model_id int, side_connection varchar(255)) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        # The new payment status
        DECLARE new_payment_status VARCHAR(255);

        # The current paid amount for this reason
        SET @current_paid_amount = NULL;

        # The model type of this reason
        SET @model_type = NULL;

        # Only the incomes and expenses reasons can have their payment status changed
        IF (searched_model_name='Finance_Expenses_Reason' OR searched_model_name='Finance_Incomes_Reason') THEN
            # Get the information for the type and for the current total of the reason
            IF (searched_model_name='Finance_Expenses_Reason') THEN
                SELECT fer.total_with_vat, fer.type INTO @model_total, @model_type
                  FROM fin_expenses_reasons as fer
                  WHERE fer.id=searched_model_id;
            ELSE
                SELECT fir.total_with_vat, fir.type INTO @model_total, @model_type
                  FROM fin_incomes_reasons as fir
                  WHERE fir.id=searched_model_id;
            END IF;

            # If the calculated amount is less than 0 then absolute value is taken
            IF (@model_total<0) THEN
                SET @model_total=@model_total*-1;
            END IF;

            # Round the decimal value of @model_total to second digit because it is used to compare with the paid amount which is rounded to second digit as well
            IF (@model_total IS NOT NULL) THEN
                SET @model_total=ROUND(@model_total, 2);
            ELSE
                SET @model_total=0;
            END IF;

            # Take actions depending on the model type
            IF (@model_type>100) THEN
                IF (searched_model_name='Finance_Incomes_Reason') THEN
                    # For user-defined reasons with a custom type, the whole chain of relations should be taken
                    # Get the paid amount from invoices and proformas
                    SELECT value INTO @prec_total_with_vat FROM settings WHERE `section`='precision' AND `name`='gt2_total_with_vat';
                    SELECT SUM(IF(fir.type=1 AND fir.advance!=0 AND ROUND(frr.shared*(1+fir.total_vat_rate/100), @prec_total_with_vat)<fr.paid_amount,
                            ROUND(frr.shared*(1+fir.total_vat_rate/100), @prec_total_with_vat),
                            fr.paid_amount)) INTO @invoiced_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND frr.parent_id=fir.id AND
                            fir.type IN (1, 2) AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            fr.paid_to=fir.id AND
                            fr.paid_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished";

                    # Get the directly paid amount
                    SELECT SUM(paid_amount) INTO @direct_paid_amount
                      FROM fin_balance AS fr
                      WHERE fr.paid_to=searched_model_id AND fr.paid_to_model_name="Finance_Incomes_Reason";

                    # Get the paid amount from debit notices
                    SELECT SUM(paid_amount) INTO @debit_notices_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr2.parent_id=fir.id AND
                            frr2.parent_model_name="Finance_Incomes_Reason" AND
                            fir.type=4 AND
                            frr2.link_to=frr.parent_id AND
                            frr2.link_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished" AND
                            fr.paid_to=frr2.parent_id AND
                            fr.paid_to_model_name="Finance_Incomes_Reason";

                    # Get the paid amount from credit notices
                    SELECT SUM(paid_amount) INTO @credit_notices_amount
                      FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr, fin_reasons_relatives AS frr2, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.link_to_model_name="Finance_Incomes_Reason" AND
                            frr.parent_model_name="Finance_Incomes_Reason" AND
                            frr2.parent_id=fir.id AND
                            frr2.parent_model_name="Finance_Incomes_Reason" AND
                            fir.type=3 AND
                            frr2.link_to=frr.parent_id AND
                            frr2.link_to_model_name="Finance_Incomes_Reason" AND
                            fir.annulled_by=0 AND
                            fir.active=1 AND
                            fir.status="finished" AND
                            fr.parent_id=frr2.parent_id AND
                            fr.parent_model_name="Finance_Incomes_Reason";

                    # The amount will be sum of invoiced amount, directly paid amount and amount from credit/debit notices
                    SET @current_paid_amount = (IF (@invoiced_amount IS NULL, 0, @invoiced_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount))+(IF (@debit_notices_amount IS NULL, 0, @debit_notices_amount))-(IF (@credit_notices_amount IS NULL, 0, @credit_notices_amount));
                ELSEIF (searched_model_name='Finance_Expenses_Reason') THEN
                    # Get the directly paid amount
                    SELECT SUM(paid_amount) INTO @direct_paid_amount
                      FROM fin_balance AS fr
                      WHERE fr.parent_id=searched_model_id AND fr.parent_model_name=searched_model_name;

                    # Get paid amount to non-invoiced proforma (if such exists)
                    SELECT SUM(paid_amount) INTO @proforma_amount
                      FROM fin_expenses_reasons AS fer, fin_reasons_relatives AS frr, fin_balance AS fr
                      WHERE frr.link_to=searched_model_id AND
                            frr.parent_id=fer.id AND
                            fer.type=21 AND
                            frr.parent_model_name=searched_model_name AND
                            frr.link_to_model_name=searched_model_name AND
                            fr.parent_id=fer.id AND
                            fr.parent_model_name=searched_model_name AND
                            fer.annulled_by=0 AND
                            fer.active=1 AND
                            fer.status="finished" AND
                            fer.payment_status!="invoiced";

                    # The amount will be sum of direcly paid amount to reason and paid amount to non-invoiced expense proforma
                    SET @current_paid_amount = (IF (@proforma_amount IS NULL, 0, @proforma_amount))+(IF (@direct_paid_amount IS NULL, 0, @direct_paid_amount));
                END IF;
            ELSE
                # For system-defined reasons only the data from the balance table is enough
                IF (side_connection='parent') THEN
                    SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name=searched_model_name AND parent_id=searched_model_id);
                ELSE
                    SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE paid_to_model_name=searched_model_name AND paid_to=searched_model_id);
                END IF;
            END IF;

            # Set default value of @current_paid_amount
            IF (@current_paid_amount IS NULL) THEN
                SET @current_paid_amount=0;
            END IF;

            # Calculate the amount difference to retrieve the status
            SET @amount_difference = ROUND(@model_total - @current_paid_amount, 2);
            IF (@amount_difference = @model_total AND @model_total>0) THEN
                SET new_payment_status = 'unpaid';
            ELSEIF (@amount_difference<=0) THEN
                SET new_payment_status = 'paid';
            ELSE
                SET new_payment_status = 'partial';
            END IF;
        END IF;

        # return the new status as string
        return new_payment_status;
    END;//
delimiter ;

# PROCEDURE TO UPDATE THE REASON PAYMENT STATUS DEPENDING ON THE CHANGE IN fin_balance TABLE
DROP PROCEDURE IF EXISTS update_reason_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_reason_payment_status(IN sideConnection varchar(255), IN changedModelName varchar(255), IN changedModelId INT, IN historyId INT)
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE currentModelId INT DEFAULT NULL;
        DECLARE currentModelName varchar(255) DEFAULT NULL;
        DECLARE currentModelTotal varchar(255) DEFAULT NULL;
        DECLARE currentModelType INT DEFAULT NULL;
        DECLARE currentModelPaymentStatus varchar(255) DEFAULT NULL;
        DECLARE currentModelOrigin varchar(255) DEFAULT NULL;
        DECLARE newPaymentStatus VARCHAR(255) DEFAULT NULL;
        DECLARE relatedModelId INT DEFAULT NULL;
        DECLARE curReasons CURSOR FOR
            -- FINANCE EXPENSES
            -- direct expenses
            (SELECT
                "Finance_Expenses_Reason" as model_name,
                fer.type as current_model_type,
                IF (fer.total_with_vat<0, fer.total_with_vat*-1, fer.total_with_vat) as current_model_total,
                fer.payment_status as current_model_payment_status,
                fer.id as current_model_id,
                "original" as current_model_origin
              FROM fin_expenses_reasons as fer
              WHERE changedModelName="Finance_Expenses_Reason"
                    AND fer.id=changedModelId
            )
            UNION
            -- related expenses reasons from invoice and proforma invoice (20,21)
            -- frr - relation between expense reason (link_to) and invoice (parent_id)
            -- firinv - fin_expenses_reason for invoice
            -- fer - fin_incomes_reason for reason
            (SELECT
              "Finance_Expenses_Reason" as model_name,
                fer.type as current_model_type,
                IF (fer.total_with_vat<0, fer.total_with_vat*-1, fer.total_with_vat) as current_model_total,
                fer.payment_status as current_model_payment_status,
                fer.id as current_model_id,
                "related" as current_model_origin
              FROM fin_reasons_relatives as frr
              JOIN fin_expenses_reasons as fer
                ON (frr.link_to=fer.id AND fer.type>100)
              JOIN fin_expenses_reasons as ferinv
                ON (frr.parent_id=ferinv.id AND ferinv.type IN (20, 21))
              WHERE
                changedModelName="Finance_Expenses_Reason"
                AND frr.parent_id=changedModelId
                AND frr.parent_model_name='Finance_Expenses_Reason'
                AND frr.link_to_model_name='Finance_Expenses_Reason'
                AND fer.active=1
                AND fer.annulled_by=0
                AND fer.status="finished"
            )
            UNION
            -- FINANCE INCOMES
            -- direct incomes
            (SELECT
                "Finance_Incomes_Reason" as model_name,
                fir.type as current_model_type,
                IF (fir.total_with_vat<0, fir.total_with_vat*-1, fir.total_with_vat) as current_model_total,
                fir.payment_status as current_model_payment_status,
                fir.id as current_model_id,
                "original" as current_model_origin
              FROM fin_incomes_reasons as fir
              WHERE changedModelName="Finance_Incomes_Reason"
                    AND fir.id=changedModelId
            )
            UNION
            -- related income reasons from invoice and proforma invoice (1,2)
            -- frr - relation between income reason (link_to) and invoice (parent_id)
            -- firinv - fin_incomes_reason for invoice
            -- fir - fin_incomes_reason for reason
            -- IMPORTANT: reasons might be more than one, because the advance invoice might be related to more than one reason!
            (SELECT
              "Finance_Incomes_Reason" as model_name,
                fir.type as current_model_type,
                IF(frr.shared!=0 AND frr.shared IS NOT NULL,
                  ROUND(frr.shared*(1+fir.total_vat_rate/100), s.value),
                  IF (fir.total_with_vat<0,
                     fir.total_with_vat*-1,
                     fir.total_with_vat)
                ) as current_model_total,
                fir.payment_status as current_model_payment_status,
                fir.id as current_model_id,
                "related" as current_model_origin
              FROM fin_reasons_relatives as frr
              JOIN fin_incomes_reasons as fir
                ON (frr.link_to=fir.id AND fir.type>100)
              JOIN fin_incomes_reasons as firinv
                ON (frr.parent_id=firinv.id AND firinv.type IN (1,2))
              JOIN settings AS s
                ON s.section='precision' AND s.name='gt2_total_with_vat'
              WHERE
                changedModelName="Finance_Incomes_Reason"
                AND frr.parent_id=changedModelId
                AND frr.parent_model_name='Finance_Incomes_Reason'
                AND frr.link_to_model_name='Finance_Incomes_Reason'
                AND fir.active=1
                AND fir.annulled_by=0
                AND fir.status="finished"
            )
            UNION
            -- related income reasons from debit or credit note (3,4)
            -- frr1 - relation between invoice (link_to) and credit/debit (parent_id)
            -- firinv - fin_incomes_reason for invoice
            -- firdc - fin_incomes_reason for debit/credit
            -- frr2 - relation between income reason (link_to) and invoice (parent_id)
            -- fir - fin_incomes_reason for reason
            (SELECT
              "Finance_Incomes_Reason" as model_name,
                fir.type as current_model_type,
                IF(frr2.shared!=0 AND frr2.shared IS NOT NULL,
                  ROUND(frr2.shared*(1+fir.total_vat_rate/100), s.value),
                  IF (fir.total_with_vat<0,
                     fir.total_with_vat*-1,
                     fir.total_with_vat)
                ) as current_model_total,
                fir.payment_status as current_model_payment_status,
                fir.id as current_model_id,
                "related" as current_model_origin
              FROM fin_reasons_relatives as frr1
              JOIN fin_incomes_reasons as firinv
                ON (frr1.link_to=firinv.id AND firinv.type IN (1))
              JOIN fin_incomes_reasons as firdc
                ON (frr1.parent_id=firdc.id AND firdc.type IN (3,4))
              JOIN fin_reasons_relatives as frr2
                ON (frr2.parent_id=firinv.id)
              JOIN fin_incomes_reasons as fir
                ON (frr2.link_to=fir.id AND fir.type>100)
              JOIN settings AS s
                ON s.section='precision' AND s.name='gt2_total_with_vat'
              WHERE
                changedModelName="Finance_Incomes_Reason"
                AND frr1.parent_id=changedModelId
                AND frr1.parent_model_name='Finance_Incomes_Reason'
                AND frr1.link_to_model_name='Finance_Incomes_Reason'
                AND frr2.parent_model_name='Finance_Incomes_Reason'
                AND frr2.link_to_model_name='Finance_Incomes_Reason'
                AND firinv.active=1
                AND firinv.annulled_by=0
                AND firinv.status="finished"
                AND fir.active=1
                AND fir.annulled_by=0
                AND fir.status="finished"
            );
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        OPEN curReasons;
          read_loop: LOOP
            FETCH curReasons INTO currentModelName, currentModelType, currentModelTotal, currentModelPaymentStatus, currentModelId, currentModelOrigin;
            IF done THEN
              LEAVE read_loop;
            END IF;

            IF (currentModelName="Finance_Expenses_Reason") THEN
                IF (currentModelOrigin = "original") THEN
                    SET newPaymentStatus=define_new_payment_status(currentModelName, currentModelId, sideConnection);

                    # Update the status of the reason
                    UPDATE fin_expenses_reasons SET payment_status=newPaymentStatus WHERE id=changedModelId;
                    CALL log_payment_status_audit(
                        historyId,
                        changedModelName,
                        changedModelId,
                        currentModelPaymentStatus,
                        newPaymentStatus
                    );

                    # for invoices there might be multiple connections
                    IF (currentModelType = "20") THEN
                        # Updates payment status of reason parents of proforma parents of expense invoice
                        CALL update_multiple_expenses_reason_payment_status(changedModelId, newPaymentStatus, historyId);
                    END IF;

                ELSEIF (currentModelOrigin = "related") THEN
                    # Update reason payment status
                    IF (currentModelType=21) THEN
                        # If current model is a proforma, both reason and proforma could have distributed payments
                        SET newPaymentStatus=define_new_payment_status(currentModelName, currentModelId, sideConnection);
                    END IF;

                    UPDATE fin_expenses_reasons SET payment_status=newPaymentStatus WHERE id=currentModelId;
                    CALL log_payment_status_audit(
                        historyId,
                        currentModelName,
                        currentModelId,
                        currentModelPaymentStatus,
                        newPaymentStatus
                    );
                END IF;
            ELSEIF (currentModelName='Finance_Incomes_Reason') THEN
                SET newPaymentStatus=define_new_payment_status(currentModelName, currentModelId, sideConnection);

                # Update the status of the user defined reason
                UPDATE fin_incomes_reasons
                SET payment_status=newPaymentStatus,
                    payment_status_modified=(IF (currentModelPaymentStatus COLLATE utf8mb4_unicode_ci = newPaymentStatus COLLATE utf8mb4_unicode_ci, payment_status_modified, NOW()))
                WHERE id=currentModelId;
                CALL log_payment_status_audit(
                    historyId,
                    currentModelName,
                    currentModelId,
                    currentModelPaymentStatus,
                    newPaymentStatus
                );
            END IF;

          END LOOP;
        CLOSE curReasons;
    END;//
delimiter ;

# FUNCTION TO DEFINE THE INVOICE STATUS OF AN INCOMES REASON
DROP FUNCTION IF EXISTS define_new_invoiced_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_invoiced_status(incomes_reason_id INT) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE reason_cursor_done INT DEFAULT FALSE;
        DECLARE new_invoiced_status VARCHAR(255);
        DECLARE incomes_reason_total DECIMAL(25,6);
        DECLARE calculated_invoiced_amount DECIMAL(25,6);
        DECLARE difference_invoiced_amount DECIMAL(25,6);
        DECLARE related_reason_id INT;
        DECLARE related_reason_total DECIMAL(25,6);
        DECLARE related_debit_credit_sum DECIMAL(25,6);
        DECLARE num_invoices INT DEFAULT 0;

        DECLARE reason_cursor CURSOR FOR
            SELECT fir.id, SUM(IF(fir.advance!=0 AND frr.shared!=0,
                                ROUND(frr.shared*(1+fir.total_vat_rate/100), s.value),
                                fir.total_with_vat))
            FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
            JOIN settings AS s
              ON s.section='precision' AND s.name='gt2_total_with_vat'
            WHERE frr.link_to = incomes_reason_id
              AND fir.id = frr.parent_id
              AND fir.status = "finished"
              AND frr.parent_model_name = "Finance_Incomes_Reason"
              AND frr.link_to_model_name = "Finance_Incomes_Reason"
              AND fir.annulled_by=0 AND fir.type=1
              GROUP BY fir.id;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET reason_cursor_done = TRUE;

        IF (SELECT `id` FROM fin_incomes_reasons WHERE `id`=incomes_reason_id AND `type`>100 AND `active`=1 AND `status`='finished' AND `annulled_by`=0) THEN
            SELECT ROUND(total_with_vat, 6) INTO incomes_reason_total FROM fin_incomes_reasons WHERE `id`=incomes_reason_id;
            SET calculated_invoiced_amount=0;

            OPEN reason_cursor;
            reason_loop: LOOP
                FETCH reason_cursor INTO related_reason_id, related_reason_total;

                IF reason_cursor_done THEN
                    LEAVE reason_loop;
                END IF;

                SELECT SUM(total_with_vat) INTO related_debit_credit_sum
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
                WHERE frr.link_to = related_reason_id
                  AND fir.status = "finished"
                  AND fir.id = frr.parent_id
                  AND frr.parent_model_name = "Finance_Incomes_Reason"
                  AND frr.link_to_model_name = "Finance_Incomes_Reason"
                  AND fir.annulled_by=0 AND fir.type IN (3,4);

                SET calculated_invoiced_amount=ROUND((calculated_invoiced_amount+((IF (related_reason_total IS NULL, 0, related_reason_total)) + (IF (related_debit_credit_sum IS NULL, 0, related_debit_credit_sum)))), 6);

                SET num_invoices = num_invoices + 1;
            END LOOP;

            SET difference_invoiced_amount = ROUND(incomes_reason_total - calculated_invoiced_amount, 2);

            IF (difference_invoiced_amount<=0 AND num_invoices>0) THEN
                SET new_invoiced_status = 'invoiced';
            ELSEIF (difference_invoiced_amount<incomes_reason_total AND difference_invoiced_amount>0) THEN
                SET new_invoiced_status = 'partial';
            ELSEIF (difference_invoiced_amount>=incomes_reason_total) THEN
                SET new_invoiced_status = 'not_invoiced';
            END IF;
        ELSE
            SET new_invoiced_status = 'not_invoicable';
        END IF;

        return new_invoiced_status;
    END;//
delimiter ;

#########################################################################################
# 2025-04-16 - Add permissions for edit and view of nomenclatures by type

#####################################################################
# START: Add permissions for edit and view of nomenclatures by type #
#####################################################################
# Add permission definitions for edit and view of nomenclatures by type
UPDATE `roles_definitions` AS `rd`
  JOIN `nom_types` AS `nt`
    ON (rd.module = 'nomenclatures'
      AND rd.controller = ''
      AND rd.model_type = nt.id)
  LEFT JOIN `roles_definitions` AS `rd1`
    ON (rd1.module = rd.module
      AND rd1.controller = rd.controller
      AND rd1.model_type = rd.model_type
      AND rd1.action = 'edit')
  SET rd.position = rd.position + 1
  WHERE rd.position >= 4
    AND rd1.id IS NULL;
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'nomenclatures', '', 'edit', `id`, 1, 4
    FROM `nom_types`;
UPDATE `roles_definitions` AS `rd`
  JOIN `nom_types` AS `nt`
    ON (rd.module = 'nomenclatures'
      AND rd.controller = ''
      AND rd.model_type = nt.id)
  LEFT JOIN `roles_definitions` AS `rd1`
    ON (rd1.module = rd.module
      AND rd1.controller = rd.controller
      AND rd1.model_type = rd.model_type
      AND rd1.action = 'view')
  SET rd.position = rd.position + 1
  WHERE rd.position >= 5
    AND rd1.id IS NULL;
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'nomenclatures', '', 'view', `id`, 1, 5
    FROM `nom_types`;

# For the new definitions, copy the permission from list or the module action (whichever is less)
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT
    rp_type_list.parent_id,
    rd_type_action.id,
    CASE
      WHEN rp_type_list.permission = 'none'  OR rp_action.permission = 'none'  THEN 'none'
      WHEN rp_type_list.permission = 'mine'  OR rp_action.permission = 'mine'  THEN 'mine'
      WHEN rp_type_list.permission = 'group' OR rp_action.permission = 'group' THEN 'group'
      ELSE 'all'
    END
  FROM `nom_types` AS `nt`
  JOIN `roles_definitions` AS `rd_type_action`
    ON (rd_type_action.module = 'nomenclatures'
      AND rd_type_action.controller = ''
      AND rd_type_action.model_type = nt.id
      AND rd_type_action.action IN ('edit', 'view'))
  JOIN `roles_definitions` AS `rd_type_list`
    ON (rd_type_list.module = rd_type_action.module
      AND rd_type_list.controller = rd_type_action.controller
      AND rd_type_list.action = 'list'
      AND rd_type_list.model_type = rd_type_action.model_type)
  JOIN `roles_permissions` AS `rp_type_list`
    ON (rp_type_list.definition_id = rd_type_list.id)
  JOIN `roles_definitions` AS `rd_action`
    ON (rd_action.module = rd_type_action.module
      AND rd_action.controller = rd_type_action.controller
      AND rd_action.model_type = 0
      AND rd_action.action = rd_type_action.action)
  JOIN `roles_permissions` AS `rp_action`
    ON (rp_action.definition_id = rd_action.id
      AND rp_action.parent_id = rp_type_list.parent_id);

# Change typeless roles definitions for edit and view to have only two options as they will be used only for allowed/forbidden check
UPDATE `roles_definitions`
  SET `requires_model` = 0
  WHERE `module` = 'nomenclatures'
    AND `controller` = ''
    AND `model_type` = 0
    AND `action` IN ('edit', 'view');
UPDATE `roles_definitions` AS `rd`
  JOIN `roles_permissions` AS `rp`
    ON (rd.module = 'nomenclatures'
      AND rd.controller = ''
      AND rd.model_type = 0
      AND rd.action IN ('edit', 'view')
      AND rp.definition_id = rd.id)
  SET rp.permission = 'all'
  WHERE rp.permission IN ('mine', 'group');
###################################################################
# END: Add permissions for edit and view of nomenclatures by type #
###################################################################

#########################################################################################
# 2025-04-28 - Add new module field for group in nomenclatures module

# Add new module field for group in nomenclatures module
INSERT INTO `_modules_fields` (`id`, `module`, `controller`, `field_name`, `depends_on`, `standard`) VALUES
(NULL, 'nomenclatures', 'nomenclatures', 'group', 'group_name', 0);

######################################################################################
# 2025-05-02 - Added new setting always setting deadline of a payment as the last date of the selected month in 'createPaymentPlan' automation
#            - Fix setting name in 'createPaymentPlan' automation

# Added new setting always setting deadline of a payment as the last date of the selected month in 'createPaymentPlan' automation
UPDATE `automations` SET `settings`=CONCAT(`settings`,  '\r\nalways_set_last_month_date_deadline := 0') WHERE `method` LIKE '%credits%' AND `method` LIKE '%createPaymentPlan%' AND `settings` NOT LIKE '%always_set_last_month_date_deadline%' ;

# Fix setting name in 'createPaymentPlan' automation
UPDATE `automations` SET `settings`=REPLACE(`settings`,  'always_set_last_month_date_deadline', 'credit_month_last_date') WHERE `method` LIKE '%credits%' AND `method` LIKE '%createPaymentPlan%' AND `settings` LIKE '%always_set_last_month_date_deadline%' ;

######################################################################################
# 2025-05-14 - Update existing settings of removeAssignments automations with the new settings

# Update existing settings of removeAssignments automations with the new settings
UPDATE `automations` SET `method`=REPLACE(`method`, 'remove_assign_owner := 1', 'remove_assign_owner := [all]') WHERE `method` LIKE '%method := removeAssignments%';
UPDATE `automations` SET `method`=REPLACE(`method`, 'remove_assign_responsible := 1', 'remove_assign_responsible := [all]') WHERE `method` LIKE '%method := removeAssignments%';
UPDATE `automations` SET `method`=REPLACE(`method`, 'remove_assign_observer := 1', 'remove_assign_observer := [all]') WHERE `method` LIKE '%method := removeAssignments%';
UPDATE `automations` SET `method`=REPLACE(`method`, 'remove_assign_decision := 1', 'remove_assign_decision := [all]') WHERE `method` LIKE '%method := removeAssignments%';
UPDATE `automations` SET `method`=REPLACE(`method`, 'remove_assign_participant := 1', 'remove_assign_participant := [all]') WHERE `method` LIKE '%method := removeAssignments%';

#########################################################################################
# 2025-05-28 - Add new field `settings` for table `analyses`
#            - Add new field `settings` for table `analyses_history`
#            - Rename field `css_template` to `css` in `analyses` table
#            - Rename field `css_template` to `css` in `analyses_history` table
#            - Change the trigger after update of `analyses` to handle processing of the new `settings` field and the renamed `css` field

# Add new field `settings` for table `analyses`
SET @preparedStatement = (
  SELECT IF(
    (
      SELECT COUNT(*)
      FROM `information_schema`.`columns` AS `is_columnds`
      WHERE is_columnds.table_schema = DATABASE()
        AND is_columnds.table_name = 'analyses'
        AND is_columnds.column_name = 'settings'
    ) > 0,
    'SELECT 1',
    CONCAT(
      'ALTER TABLE `analyses`
        ADD COLUMN `settings` LONGTEXT DEFAULT NULL COMMENT \'YAML settings, read after applying user and system placeholders.\'
        AFTER `display_objects`;')
  )
);
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;
# Add new field `settings` for table `analyses_history`
SET @preparedStatement = (
  SELECT IF(
    (
      SELECT COUNT(*)
      FROM `information_schema`.`columns` AS `is_columnds`
      WHERE is_columnds.table_schema = DATABASE()
        AND is_columnds.table_name = 'analyses_history'
        AND is_columnds.column_name = 'settings'
    ) > 0,
    'SELECT 1',
    CONCAT(
      'ALTER TABLE `analyses_history`
        ADD COLUMN `settings` LONGTEXT DEFAULT NULL COMMENT \'YAML settings, read after applying user and system placeholders.\'
        AFTER `display_objects`;')
  )
);
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;
# Rename field `css_template` to `css` in `analyses` table
SET @preparedStatement = (
  SELECT IF(
    (
      SELECT COUNT(*)
      FROM `information_schema`.`columns` AS `is_columnds`
      WHERE is_columnds.table_schema = DATABASE()
        AND is_columnds.table_name = 'analyses'
        AND is_columnds.column_name = 'css'
    ) > 0,
    'SELECT 1',
    CONCAT(
      'ALTER TABLE `analyses`
        RENAME COLUMN `css_template` TO `css`;')
  )
);
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;
# Rename field `css_template` to `css` in `analyses_history` table
SET @preparedStatement = (
  SELECT IF(
    (
      SELECT COUNT(*)
      FROM `information_schema`.`columns` AS `is_columnds`
      WHERE is_columnds.table_schema = DATABASE()
        AND is_columnds.table_name = 'analyses_history'
        AND is_columnds.column_name = 'css'
    ) > 0,
    'SELECT 1',
    CONCAT(
      'ALTER TABLE `analyses_history`
        RENAME COLUMN `css_template` TO `css`;')
  )
);
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

# Change the trigger after update of `analyses` to handle processing of the new `settings` field and the renamed `css` field
DROP TRIGGER IF EXISTS `after_update_analyses`;
DELIMITER $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER `after_update_analyses` AFTER UPDATE ON `analyses`
FOR EACH ROW
BEGIN
  # Record history for the previous version of the changed analysis
  INSERT INTO `analyses_history`
    SET `history_added`  = NOW(),
      `history_added_by` = USER(),
      `analysis_id`      = OLD.id,
      `filters`          = OLD.filters,
      `data_sources`     = OLD.data_sources,
      `display_objects`  = OLD.display_objects,
      `settings`         = OLD.settings,
      `placeholders`     = OLD.placeholders,
      `css`              = OLD.css,
      `position`         = OLD.position,
      `is_portal`        = OLD.is_portal,
      `visible`          = OLD.visible,
      `modified`         = OLD.modified,
      `modified_by`      = OLD.modified_by;
END;$$$
DELIMITER ;

#########################################################################################
# 2025-05-30 - Labels and messages from 'quick_search_damages_and_signals' are inserted in the DB
#            - Added new setting in 'quick_search_damages_and_signals' report for reserve amount

# Labels and messages from 'quick_search_damages_and_signals' are inserted in the DB
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_filter_from', 'Дата на ПТП'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_type', 'Тип щета'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_status', 'Статус'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_client', 'Клиент/Пострадал'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_income_num', 'Входящ №'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_signal_date_hour', 'Дата/Час на сигнал'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_ptp_location', 'Място на ПТП'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_policy_num', '№ на полица'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_owner', 'Собственик'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_reg_num', 'Рег. №'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_car', 'Автомобил (марка и модел)'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_type_damage', 'Вид щета'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_reported_by', 'Подал сигнал/адвокат'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_heavy_damage', 'Тежки случаи'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_event_date_place', 'Дата и място на събитие'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_incident_description', 'Описание на инцидента / случката'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_ptp_participants', 'Участници в ПТП'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_collateral', 'Вреди'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_signal_num', 'Щета (сигнал) №'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_damage_num', 'Щета №'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_type_risk', 'Вид на риска'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_responsible', 'Отговорник'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_region', 'Регион'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_city', 'Населено място'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_heir', 'Наследник (имена/ЕГН)'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_district', 'Област'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_highway', 'Магистрала'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_show_damage_signals', 'Покажи щети по сигнали'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_show_damage_agreements', 'Покажи щети със споразумения'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_filter_comment', 'Коментар'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_filter_comment_help', 'Търсят се записи, които съдържат посочения текст в коментарите си.'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_claimed_amount', 'Първоначално предявен размер'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_claimed_resserved_amount', 'Заделен резерв'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_counterclaim_evaluation', 'Оценка претенция'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_nonmaterial_damages', 'Неимуществени вреди'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_material_damages', 'Имуществени вреди'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_case_law', 'Съдебна практика'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_location_template', '""Рег.: %s\nНас.място: %s\nДоп.инф.: %s""'),
('reports', 'quick_search_damages_and_signals', 'bg', 'documents_status_opened', 'Отворен'),
('reports', 'quick_search_damages_and_signals', 'bg', 'documents_status_locked', 'Заключен'),
('reports', 'quick_search_damages_and_signals', 'bg', 'documents_status_closed', 'Затворен'),
('reports', 'quick_search_damages_and_signals', 'bg', 'reports_ptp_date', 'Дата на ПТП'),
('reports', 'quick_search_damages_and_signals', 'bg', 'error_reports_complete_at_least_one_filter', 'Моля, попълнете поне един от наличните филтри!');
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
('reports', 'quick_search_damages_and_signals', 'en', 'reports_filter_from', 'Date PTP'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_type', 'Type damage'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_status', 'Status'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_client', 'Cliant/Injured'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_income_num', 'Incoming num'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_signal_date_hour', 'Signal date/hour'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_ptp_location', 'Place PTP'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_policy_num', 'Policy num'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_owner', 'Owner'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_reg_num', 'Reg. num'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_car', 'Car'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_type_damage', 'Damage type'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_reported_by', 'Reported by/advocate'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_heavy_damage', 'Critical cases'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_event_date_place', 'Event date and place'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_incident_description', 'Incident description'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_ptp_participants', 'PTP Participants'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_collateral', 'Collateral'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_signal_num', 'Damage (signal) num'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_damage_num', 'Damage num'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_type_risk', 'Risk type'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_responsible', 'Responsible'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_region', 'Region'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_city', 'City'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_heir', 'Heir (names/UCN)'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_district', 'District'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_highway', 'Highway'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_show_damage_signals', 'Show damage signals'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_show_damage_agreements', 'Show damage agreements'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_filter_comment', 'Comment'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_filter_comment_help', 'We are looking for entries that contain the specified text in their comments.'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_claimed_amount', 'Initially claimed amount'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_claimed_resserved_amount', 'Reserve'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_counterclaim_evaluation', 'Counterclaim evaluation'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_nonmaterial_damages', 'Non-material damages'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_material_damages', 'Material damages'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_case_law', 'Case law'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_location_template', '"Region: %s\nCity: %s\nExtra info: %s"'),
('reports', 'quick_search_damages_and_signals', 'en', 'documents_status_opened', 'Opened'),
('reports', 'quick_search_damages_and_signals', 'en', 'documents_status_locked', 'Locked'),
('reports', 'quick_search_damages_and_signals', 'en', 'documents_status_closed', 'Closed'),
('reports', 'quick_search_damages_and_signals', 'en', 'reports_ptp_date', 'PTP Date'),
('reports', 'quick_search_damages_and_signals', 'en', 'error_reports_complete_at_least_one_filter', 'Please, complete at least one of the available filters!');

# Added new setting in 'quick_search_damages_and_signals' report for reserve amount
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndocument_var_counterclaim_evaluation :=', '\ndocument_var_claimed_reserved_amount :=\ndocument_var_counterclaim_evaluation :=') WHERE `type`='quick_search_damages_and_signals' AND `settings` NOT LIKE '%document_var_claimed_reserved_amount%';

#########################################################################################
# 2025-06-04 - Added new column in documents_counters to allow adding trademark code per year

# Added new column in documents_counters to allow adding trademark code per year
ALTER TABLE `documents_counters` ADD `trademark_year` TINYINT(4) NOT NULL DEFAULT '0' AFTER `office_year`;
