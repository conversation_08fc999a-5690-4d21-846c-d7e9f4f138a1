/*
 *
 * Copyright (c) 2004-2005 by Zapatec, Inc.
 * http://www.zapatec.com
 * 1700 MLK Way, Berkeley, California,
 * 94709, U.S.A.
 * All rights reserved.
 *
 *
 */


Zapatec.Menu = function (objArgs) {
    if (arguments.length > 1) {
        var objConfig = arguments[1];
        objConfig.source = arguments[0];
        objArgs = objConfig;
    }
    Zapatec.Menu.SUPERconstructor.call(this, objArgs);
};
Zapatec.Menu.id = "Zapatec.Menu";
Zapatec.inherit(Zapatec.Menu, Zapatec.Widget);
Zapatec.Menu.prototype.init = function (objArgs) {
    this.config.container = null;
    this.config.dynamic = false;
    this.config.showDelay = 100;
    this.config.hideDelay = 500;
    this.config.onClick = false;
    this.config.vertical = false;
    this.config.scrollWithWindow = false;
    this.config.dropShadow = 0;
    this.config.drag = false;
    this.config.slide = false;
    this.config.glide = false;
    this.config.fade = false;
    this.config.wipe = false;
    this.config.unfurl = false;
    this.config.animSpeed = 10;
    this.config.defaultIcons = null;
    this.config.zIndex = 0;
    this.config.rememberPath = false;
    this.config.pathCookie = '__zp_item';
    this.config.triggerEvent = null;
    this.config.triggerKey = null;
    this.config.triggerObject = null;
    this.config.top = null;
    this.config.right = null;
    this.config.bottom = null;
    this.config.left = null;
    this.config.onInit = null;
    this.config.preventDoubleCall = false;
    this.called = false;
    Zapatec.Menu.SUPERclass.init.call(this, objArgs);
};
Zapatec.Menu.prototype.discard = function () {
    Zapatec.ScrollWithWindow.unregister(this.rootMenu);
    Zapatec.Menu.SUPERclass.discard.call(this);
};
Zapatec.Menu.prototype.addStandardEventListeners = function () {
    Zapatec.Menu.SUPERclass.addStandardEventListeners.call(this);
    this.addEventListener('loadThemeEnd', function () {
        this.onThemeLoad();
    });
};
Zapatec.Menu.onTop = null;
Zapatec.Menu.prototype.restoreZIndex = function () {
    this.top_parent.style.zIndex = this.config.zIndex;
    Zapatec.Menu.onTop = null;
};
Zapatec.Menu.prototype.putOnTop = function () {
    var objOnTop = Zapatec.Menu.onTop;
    if (objOnTop) {
        objOnTop.restoreZIndex();
    }
    this.top_parent.style.zIndex = 2147483583;
    Zapatec.Menu.onTop = this;
};
Zapatec.Menu.prototype.onThemeLoad = function () {
    var self = this;
    this.triggerObject = null;
    this.triggerArgs = null;
    this.animations = [];
    this.container = Zapatec.Widget.getElementById(this.config.container);
    this.loadData();
    this.openMenus = [];
    this.clickDone = false;
    var objMenu = this;
    if (this.config.triggerEvent) {
        this.setTriggerObject(this.config.triggerObject || window.document);
        Zapatec.Utils.addEvent(window.document, 'mouseup', function () {
            objMenu.hideMenu()
        });
        Zapatec.Utils.addEvent(this.top_parent, 'mouseup', function (objEvent) {
            return Zapatec.Utils.stopEvent(objEvent);
        });
        Zapatec.Utils.addEvent(window.document, 'keypress', function (objEvent) {
            objEvent || (objEvent = window.event);
            if (objEvent.keyCode == 27) {
                for (var i = 0; i < Zapatec.Menu.selectedItemsStack.length; i++) {
                    if (Zapatec.Menu.all[Zapatec.Menu.selectedItemsStack[i].__zp_tree] == objMenu) {
                        return;
                    }
                }
                objMenu.hideMenu();
            }
        });
    } else {
        if (this.config.scrollWithWindow && this.rootMenu) {
            Zapatec.ScrollWithWindow.register(this.rootMenu);
        }
        if (this.config.drag) {
            this.dragging = false;
            Zapatec.Transport.loadJS({
                url: Zapatec.zapatecPath + "zpobjects.js",
                onLoad: function () {
                    Zapatec.Transport.loadJS({
                        url: Zapatec.zapatecPath + "dom.js",
                        onLoad: function () {
                            Zapatec.Transport.loadJS({
                                url: Zapatec.zapatecPath + "movable.js",
                                onLoad: function () {
                                    Zapatec.Transport.loadJS({
                                        url: Zapatec.zapatecPath + "draggable.js",
                                        onLoad: function () {
                                            new Zapatec.Utils.Draggable({
                                                container: objMenu.rootMenu.parentNode,
                                                preserveSizes: false,
                                                eventCapture: true
                                            });
                                        },
                                        onError: function () {
                                            self.config.drag = false;
                                        }
                                    });
                                },
                                onError: function () {
                                    self.config.drag = false;
                                }
                            });
                        },
                        onError: function () {
                            self.config.drag = false;
                        }
                    });
                },
                onError: function () {
                    self.config.drag = false;
                }
            });
        }
    }
    if (this.config.fade) {
        this.addAnimation('fade');
    }
    if (this.config.slide) {
        this.addAnimation('slide');
    } else if (this.config.glide) {
        this.addAnimation('glide');
    } else if (this.config.wipe) {
        this.addAnimation('wipe');
    } else if (this.config.unfurl) {
        this.addAnimation('unfurl');
    }
    if (typeof this.config.onInit == 'function') {
        setTimeout(function () {
            objMenu.config.onInit();
        }, 0);
    }
};
Zapatec.Menu.prototype.loadDataHtml = function (objSource) {
    if (!objSource) {
        return;
    }
    this.list = objSource;
    this.items = {};
    this.trees = {};
    this.selectedItem = null;
    this.lastContainerNumber = 0;
    this.menuId = objSource.id || Zapatec.Utils.generateID("tree");
    var objContainer = this.top_parent = Zapatec.Utils.createElement("div");
    objContainer.style.display = 'none';
    objContainer.style.textAlign = 'left';
    objContainer.__zp_menu = Zapatec.Utils.createElement("div", objContainer);
    objContainer.__zp_menu.className = 'zpMenu';
    objContainer.className = "zpMenuContainer zpMenu-top";
    if (this.config.vertical) {
        Zapatec.Utils.addClass(objContainer, "zpMenu-vertical-mode");
    } else {
        Zapatec.Utils.addClass(objContainer, "zpMenu-horizontal-mode");
    }
    var strRootMenuId = this.createTree(objSource, objContainer, 0);
    this.rootMenu = this.trees[strRootMenuId];
    var objTheme = Zapatec.Utils.createElement('div');
    objTheme.className = this.getClassName({
        prefix: 'zpMenu'
    });
    objTheme.appendChild(objContainer);
    if (this.container) {
        this.container.appendChild(objTheme);
    } else {
        objSource.parentNode.insertBefore(objTheme, objSource);
        objSource.parentNode.removeChild(objSource);
    }
    Zapatec.Menu.all[this.menuId] = this;
    if (this.selectedItem) {
        this.sync(this.selectedItem.__zp_item);
    }
    this.path = Zapatec.Utils.getCookie(this.config.pathCookie);
    if (this.path) {
        Zapatec.Utils.writeCookie(this.config.pathCookie, '');
    }
    if (!this.config.triggerEvent) {
        this.showMenu();
        if (!this.config.triggerEvent && this.config.scrollWithWindow && this.rootMenu) {
            Zapatec.ScrollWithWindow.register(this.rootMenu);
        }
    }
};
Zapatec.Menu.all = {};
Zapatec.Menu.prototype.createTree = function (list, objContainer, level) {
    objContainer.id = 'zpMenu' + this.id + 'Container' + this.lastContainerNumber;
    this.lastContainerNumber++;
    var id;
    var intItem = 1,
        bFirst = true;
    if (list) id = list.id;
    if (!id) id = Zapatec.Utils.generateID("tree.sub");
    var
    objMenu = this;

    function _makeIt() {
        objMenu.creating_now = true;
        var last_li = null;
        var next_li;
        var i = (list ? list.firstChild : null);
        var items = objContainer.__zp_items = [];
        objMenu.trees[id] = objContainer;
        objContainer.__zp_level = level;
        objContainer.__zp_treeid = id;
        objContainer.__zp_keymap = {};
        var strOddEven;
        while (i) {
            if (last_li) last_li.className += " zpMenu-lines-c";
            if (i.nodeType != 1) i = i.nextSibling;
            else {
                next_li = Zapatec.Utils.getNextSibling(i, 'li');
                if (i.tagName.toLowerCase() == 'li') {
                    last_li = objMenu.createItem(i, objContainer, next_li, level, intItem);
                    if (last_li) {
                        if (!/zpMenu-item-hr/i.test(last_li.className)) {
                            strOddEven = "zpMenu-item-" + (intItem % 2 == 1 ? "odd" : "even");
                            Zapatec.Utils.addClass(last_li, strOddEven)
                            intItem++
                        }
                        if (bFirst) {
                            bFirst = false;
                            Zapatec.Utils.addClass(last_li, "zpMenu-item-first");
                        }
                        items[items.length] = last_li.__zp_item;
                    }
                }
                i = next_li;
            }
        }
        if (last_li) Zapatec.Utils.addClass(last_li, "zpMenu-item-last");
        if (last_li && (last_li.className.indexOf("zpMenu-item-first") >= 0) && (last_li.className.indexOf("zpMenu-item-last") >= 0)) {
            Zapatec.Utils.removeClass(last_li, "zpMenu-item-last");
            Zapatec.Utils.removeClass(last_li, "zpMenu-item-first");
            Zapatec.Utils.addClass(last_li, "zpMenu-item-single");
        }
        i = objContainer.firstChild;
        if (i && !level) {
            i.className = i.className.replace(/ zpMenu-lines-./g, "");
            i.className += (i === last_li) ? " zpMenu-lines-s" : " zpMenu-lines-t";
        }
        if (last_li && (level || last_li != i)) {
            last_li.className = last_li.className.replace(/ zpMenu-lines-./g, "");
            last_li.className += " zpMenu-lines-b";
        }
        objMenu.creating_now = false;
    };
    if (this.config.dynamic && level > 0) this.trees[id] = _makeIt;
    else _makeIt();
    return id;
};
Zapatec.Menu.tabIndex = 1000;
Zapatec.Menu.prototype.createItem = function (li, objContainer, next_li, level, intItem) {
    if (!li.firstChild) {
        return;
    }
    var id = li.id || Zapatec.Utils.generateID("tree.item");
    var item = this.items[id] = Zapatec.Utils.createElement("div", objContainer.__zp_menu);
    item.id = this.formElementId({
        prefix: 'zpMenu',
        suffix: 'Item'
    });
    var t = Zapatec.Utils.createElement("table", item);
    var tb = Zapatec.Utils.createElement("tbody", t);
    var tr = Zapatec.Utils.createElement("tr", tb);
    var td = Zapatec.Utils.createElement("td", tr);
    var has_icon = false;
    if (!level) {
        td.style.whiteSpace = 'nowrap';
    }
    t.className = "zpMenu-table";
    t.cellSpacing = 0;
    t.cellPadding = 0;
    td.className = "zpMenu-label"
    var title = li.getAttribute('title');
    if (title) {
        td.setAttribute('title', title);
    }
    item.className = "zpMenu-item" + (li.className ? ' ' + li.className : '');
    Zapatec.Utils.addClass(item, "zpMenu-level-" + (level + 1));
    item.__zp_item = id;
    item.__zp_tree = this.menuId;
    item.__zp_parent = objContainer.__zp_treeid;
    item.onmouseover = new Function('Zapatec.Widget.callMethod(' + this.id + ',"mouseOver","' + item.id + '")');
    item.onmouseout = new Function('Zapatec.Widget.callMethod(' + this.id + ',"mouseOut","' + item.id + '")');
    item.onclick = Zapatec.Menu.onItemClick;
    Zapatec.Utils.addClass(item, "zpMenu-item-" + (intItem % 2 == 1 ? "odd" : "even"));
    var fc, subtree = false,
        accessKey = null;
    var getAccessKey = function (node) {
        var key = null;
        if (node.nodeType == 1) {
            if (key = node.getAttribute('accesskey')) {
                node.removeAttribute('accesskey', false);
                if (/^[a-z0-9]$/i.test(key)) {
                    return key;
                } else {
                    key = null;
                }
            }
            var childNodes = node.childNodes;
            for (var i = 0; i < childNodes.length; i++) {
                if (key = getAccessKey(childNodes[i])) {
                    break;
                }
            }
        } else if (node.nodeType == 3) {
            var label = node.data.replace(/(^\s+|\s+$)/g, '');
            if (/_([a-z0-9])/i.test(label)) {
                label = label.replace(/_([a-z0-9])/i, '<span style="text-decoration:underline">$1</span>');
                key = RegExp.$1;
                var span = Zapatec.Utils.createElement("span");
                span.innerHTML = label;
                var objParentNode = node.parentNode;
                objParentNode.insertBefore(span, node);
                objParentNode.removeChild(node);
            }
        }
        return key;
    };
    while (fc = li.firstChild) {
        if (fc.nodeType == 1 && (/^[ou]l$/i.test(fc.tagName.toLowerCase()))) {
            if (!subtree) {
                this.item_addIcon(item, null);
                var np = Zapatec.Utils.createElement("div", objContainer);
                np.style.position = 'absolute';
                if (!this.config.triggerEvent) {
                    np.style.left = '-9999px';
                    np.style.top = '-9999px';
                }
                if (this.config.dropShadow) {
                    var ds = np.__zp_dropshadow = Zapatec.Utils.createElement('div');
                    objContainer.insertBefore(ds, np);
                    ds.style.position = 'absolute';
                    if (!this.config.triggerEvent) {
                        ds.style.left = '-9999px';
                        ds.style.top = '-9999px';
                    }
                    ds.style.backgroundColor = '#000';
                    if (window.opera) {
                        ds.style.backgroundColor = '#666';
                    } else {
                        ds.style.filter = 'alpha(opacity=' + this.config.dropShadow + ')';
                    }
                    ds.style.opacity = this.config.dropShadow / 100;
                }
                np.__zp_item = id;
                np.__zp_menu = Zapatec.Utils.createElement("div", np);
                np.__zp_menu.className = 'zpMenu' + (fc.className ? ' ' + fc.className : '');
                np.className = 'zpMenuContainer';
                np.__zp_menu.onmouseover = Zapatec.Menu.onItemMouseOver;
                np.__zp_menu.onmouseout = Zapatec.Menu.onItemMouseOut;
                if (next_li) {
                    np.__zp_menu.className += " zpMenu-lined";
                }
                np.__zp_icons = [];
                item.__zp_subtree = this.createTree(fc, np, level + 1);
                if (np.__zp_icons.length) {
                    this.alignSubMenu(np);
                }
                np.__zp_icons = null;
                item.className += " zpMenu-item-collapsed";
                this.toggleItem(id);
                if (/(^|\s)selected(\s|$)/i.test(li.className)) {
                    this.selectedItem = item;
                }
                subtree = true;
            }
            li.removeChild(fc);
        } else {
            li.removeChild(fc);
            if (fc.nodeType == 3) {
                var label = fc.data.replace(/(^\s+|\s+$)/g, '');
                if (label) {
                    var strInnerHtml = label;
                    if (Zapatec.Menu.onDocumentKeyDown && !accessKey) {
                        strInnerHtml = label.replace(/_([a-z0-9])/i, '<span style="text-decoration:underline">$1</span>');
                        accessKey = RegExp.$1;
                    }
                    var span = Zapatec.Utils.createElement("span", td);
                    if (strInnerHtml == label) {
                        span.appendChild(document.createTextNode(strInnerHtml));
                    } else {
                        span.innerHTML = strInnerHtml;
                    }
                    if (title) span.setAttribute('title', title);
                }
            } else if (fc.tagName) {
                if (fc.tagName.toLowerCase() == 'img') {
                    this.item_addIcon(item, fc);
                    has_icon = true;
                    if (objContainer.__zp_icons instanceof Array) {
                        objContainer.__zp_icons.push(fc);
                    }
                } else {
                    if (fc.tagName.toLowerCase() == 'hr') {
                        Zapatec.Utils.addClass(item, "zpMenu-item-hr");
                    } else if (fc.tagName.toLowerCase() == 'input' && fc.getAttribute('type') == 'checkbox') {
                        fc.onmousedown = function (ev) {
                            if (this.checked) {
                                this.checked = false;
                            } else {
                                this.checked = true;
                            }
                            return Zapatec.Utils.stopEvent(ev);
                        };
                    } else if (fc.tagName.toLowerCase() == 'input' && fc.getAttribute('type') == 'radio') {
                        fc.onmousedown = function (ev) {
                            this.checked = true;
                            return Zapatec.Utils.stopEvent(ev);
                        };
                    } else if (fc.tagName.toLowerCase() == 'a') {
                        if (Zapatec.Menu.onDocumentKeyDown && !accessKey) {
                            accessKey = getAccessKey(fc);
                        }
                        fc.tabIndex = ++Zapatec.Menu.tabIndex;
                        fc.onfocus = Zapatec.Menu.onItemMouseOver;
                        fc.onblur = Zapatec.Menu.onItemMouseOut;
                    }
                    td.appendChild(fc);
                    if (title && !fc.getAttribute('title')) fc.setAttribute('title', title);
                }
            }
        }
    }
    if (accessKey) {
        accessKey = accessKey.toUpperCase().charCodeAt(0);
        objContainer.__zp_keymap[accessKey] = item;
    }
    if (!has_icon && !/zpMenu-item-hr/i.test(item.className)) if (this.config.defaultIcons) this.item_addDefaultIcon(item, this.config.defaultIcons);
    else this.item_addDefaultIcon(item, "zpMenu-noicon");
    return item;
};
Zapatec.Menu.prototype.alignSubMenu = function (objSubMenu) {
    var arrIcons = objSubMenu.__zp_icons;
    var arrIconsSrc = [];
    for (var iIcon = 0; iIcon < arrIcons.length; iIcon++) {
        arrIconsSrc.push(arrIcons[iIcon].src);
    }
    var objMenu = this;
    Zapatec.Transport.preloadImages({
        urls: arrIconsSrc,
        onLoad: function () {
            var iMaxIconWidth = 0;
            for (var iIcon = 0; iIcon < arrIcons.length; iIcon++) {
                var iIconWidth = arrIcons[iIcon].width;
                if (iIconWidth && iMaxIconWidth < iIconWidth) {
                    iMaxIconWidth = iIconWidth;
                }
            }
            if (!objMenu.styleSheet) {
                objMenu.styleSheet = new Zapatec.StyleSheet();
            }
            objMenu.styleSheet.addRule('#' + objSubMenu.id + ' .icon div', 'width:' + iMaxIconWidth + 'px');
        },
        timeout: 60000
    });
};
Zapatec.Menu.prototype.item_addDefaultIcon = function (item, className) {
    if (!className) {
        return;
    }
    var last_td = item.firstChild.firstChild.firstChild.lastChild,
        td;
    var td = Zapatec.Utils.createElement("td");
    td.className = "tgb icon " + className;
    last_td.parentNode.insertBefore(td, last_td);
    Zapatec.Utils.createElement('div', td);
};
Zapatec.Menu.prototype.item_addIcon = function (item, img) {
    var last_td = item.firstChild.firstChild.firstChild;
    var td;
    last_td = img ? last_td.lastChild : last_td.firstChild;
    if (!img || !item.__zp_icon) {
        td = Zapatec.Utils.createElement("td");
        td.className = "tgb " + (img ? "icon" : "minus");
        last_td.parentNode.insertBefore(td, last_td);
    } else {
        td = item.__zp_icon;
        img.style.display = "none";
    }
    var objDiv = Zapatec.Utils.createElement('div', td);
    if (!img) {
        objDiv.innerHTML = "&nbsp;";
        item.className += " zpMenu-item-more";
        item.__zp_state = true;
        item.__zp_expand = td;
    } else {
        objDiv.appendChild(img);
        item.__zp_icon = td;
    }
};
Zapatec.Menu.prototype.itemClicked = function (item_id) {
    this.selectedItem = this.toggleItem(item_id);
    if (this.selectedItem) {
        Zapatec.Menu.selectItem(this.selectedItem);
    }
    this.onItemSelect(item_id);
};
Zapatec.Menu.prototype.toggleItem = function (item_id, state) {
    if (!item_id) {
        return null;
    }
    if (this.selectedItem) {
        Zapatec.Menu.unselectItem(this.selectedItem);
    }
    var item = this.items[item_id];
    if (typeof state == "undefined") state = !item.__zp_state;
    if (state != item.__zp_state) {
        var subtree = this._getTree(item.__zp_subtree, this.creating_now);
        if (subtree) {
            if (state) {
                for (var i = 0; i < subtree.__zp_items.length; i++) {
                    var subItemID = subtree.__zp_items[i];
                    Zapatec.Menu.unselectItem(this.items[subItemID]);
                    if (subtree.__zp_activeitem == subItemID) subtree.__zp_activeitem = '';
                }
            } else {
                for (var i = 0; i < subtree.__zp_items.length; i++) {
                    var subItemID = subtree.__zp_items[i];
                    this.toggleItem(subItemID, state);
                    Zapatec.Menu.unselectItem(this.items[subItemID]);
                    if (subtree.__zp_activeitem == subItemID) subtree.__zp_activeitem = '';
                }
            }
            this.treeSetDisplay(subtree, state);
            Zapatec.Utils.removeClass(item, "zpMenu-item-expanded");
            Zapatec.Utils.removeClass(item, "zpMenu-item-collapsed");
            Zapatec.Utils.addClass(item, state ? "zpMenu-item-expanded" : "zpMenu-item-collapsed");
        }
        var img = item.__zp_expand;
        if (img) img.className = "tgb " + (state ? "minus" : "plus");
        item.__zp_state = state;
        if (state) {
            var hideItems = this._getTree(item.__zp_parent).__zp_items;
            for (var i = hideItems.length; --i >= 0;) {
                if (hideItems[i] != item_id && hideItems[i].__zp_state) {
                    this.toggleItem(hideItems[i], false);
                }
            }
        }
    }
    return item;
};
Zapatec.Menu.prototype.collapseAll = function () {
    for (var i in this.trees)
    this.toggleItem(this._getTree(i).__zp_item, false);
};
Zapatec.Menu.prototype.expandAll = function () {
    for (var i in this.trees)
    this.toggleItem(this._getTree(i).__zp_item, true);
};
Zapatec.Menu.prototype.toggleAll = function () {
    for (var i in this.trees)
    this.toggleItem(this._getTree(i).__zp_item);
};
Zapatec.Menu.prototype.sync = function (item_id) {
    var item = this.items[item_id];
    if (item) {
        this.collapseAll();
        this.selectedItem = item;
        var path = [];
        while (item.__zp_parent) {
            path[path.length] = item;
            var parentItem = this._getTree(item.__zp_parent);
            if (parentItem.__zp_item) {
                item = this.items[parentItem.__zp_item];
            } else {
                break;
            }
        }
        for (var ii = path.length; --ii >= 0;) {
            var item = path[ii];
            var item_id = item.__zp_item;
            this.itemShow(item_id);
            var menu = this._getTree(item.__zp_parent);
            menu.__zp_activeitem = item_id;
            Zapatec.Menu.selectItem(item);
        }
    }
};
Zapatec.Menu.prototype.highlightPath = function (item_id) {
    this.putOnTop();
    var item = this.items[item_id];
    if (item) {
        var a = [];
        while (item.__zp_parent) {
            a[a.length] = item;
            var pt = this._getTree(item.__zp_parent);
            if (pt.__zp_item) item = this.items[pt.__zp_item];
            else break;
        }
        for (var i = a.length; --i >= 0;) {
            Zapatec.Utils.addClass(a[i], 'zpMenuPath');
        }
    }
};
Zapatec.Menu.prototype.destroy = function () {
    var p = this.top_parent;
    p.parentNode.removeChild(p);
};
Zapatec.Menu.prototype._getTree = function (tree_id, dont_call) {
    var tree = this.trees[tree_id];
    if (typeof tree == "function") {
        if (dont_call) {
            tree = null;
        } else {
            tree();
            tree = this.trees[tree_id];
            this.treeSetDisplay(tree, false);
        }
    }
    return tree;
};
Zapatec.Menu.prototype.onItemSelect = function () {};
Zapatec.Menu.onItemToggle = function () {
    var item = this;
    var body = document.body;
    while (item && item != body && !/zpMenu-item/.test(item.className))
    item = item.parentNode;
    Zapatec.Menu.all[item.__zp_tree].itemClicked(item.__zp_item);
};
Zapatec.Menu.prototype.setTriggerObject = function (triggerObject) {
    if (!this.config.triggerEvent) {
        return;
    }
    var strTriggerEvent = this.config.triggerEvent;
    var strTriggerKey = this.config.triggerKey;
    var objTriggerElements = [];
    if (triggerObject) {
        if (typeof triggerObject == 'string') {
            var objElement = document.getElementById(triggerObject);
            if (objElement) {
                objTriggerElements.push({
                    triggerObject: objElement,
                    triggerArgs: null
                });
            }
        } else if (typeof triggerObject == 'object') {
            if (triggerObject == window.document || typeof triggerObject.length == 'undefined') {
                objTriggerElements.push({
                    triggerObject: triggerObject,
                    triggerArgs: null
                });
            } else {
                for (var iObj = 0; iObj < triggerObject.length; iObj++) {
                    var triggerElement = triggerObject[iObj];
                    if (triggerElement) {
                        if (typeof triggerElement == 'string') {
                            var objElement = document.getElementById(triggerElement);
                            if (objElement) {
                                objTriggerElements.push({
                                    triggerObject: objElement,
                                    triggerArgs: null
                                });
                            }
                        } else if (typeof triggerElement == 'object') {
                            if (typeof triggerElement.triggerObject != 'undefined' && typeof triggerElement.triggerArgs != 'undefined') {
                                if (typeof triggerElement.triggerObject == 'string') {
                                    var objElement = document.getElementById(triggerElement.triggerObject);
                                    if (objElement) {
                                        objTriggerElements.push({
                                            triggerObject: objElement,
                                            triggerArgs: triggerElement.triggerArgs
                                        });
                                    }
                                } else if (typeof triggerElement.triggerObject == 'object') {
                                    objTriggerElements.push(triggerElement);
                                }
                            } else {
                                objTriggerElements.push({
                                    triggerObject: triggerElement,
                                    triggerArgs: null
                                });
                            }
                        }
                    }
                }
            }
        }
    }
    if (objTriggerElements.length == 0) {
        return;
    }
    var objMenu = this;
    if (strTriggerEvent == 'mousedown' || strTriggerEvent == 'mouseup' || strTriggerEvent == 'click') {
        var funcSetupTriggerEvent = function (objTriggerElement) {
            if (strTriggerKey == null || strTriggerKey == "right" || strTriggerKey == "both") {
                objTriggerElement.triggerObject.oncontextmenu = function () {
                    return false
                };
            }
            Zapatec.Utils.addEvent(objTriggerElement.triggerObject, 'mouseup', function (objEvent) {
                objEvent || (objEvent = window.event);
                var objMousePos = Zapatec.Utils.getMousePos(objEvent);
                var button;
                if (objEvent.button) {
                    button = objEvent.button;
                } else {
                    button = objEvent.which;
                }
                if (window.opera) {
                    if (button == 1 && objMenu.top_parent.style.display == 'none') {
                        setTimeout(function () {
                            objMenu.triggerObject = objTriggerElement.triggerObject;
                            objMenu.triggerArgs = objTriggerElement.triggerArgs;
                            objMenu.popupMenu(objMousePos.pageX, objMousePos.pageY);
                        }, 100);
                        return Zapatec.Utils.stopEvent(objEvent);
                    }
                } else {
                    if (strTriggerKey == 'both' || (strTriggerKey == 'left' && button == 1) || ((!strTriggerKey || strTriggerKey == 'right') && (button > 1 || objEvent.metaKey))) {
                        setTimeout(function () {
                            objMenu.triggerObject = objTriggerElement.triggerObject;
                            objMenu.triggerArgs = objTriggerElement.triggerArgs;
                            objMenu.popupMenu(objMousePos.pageX, objMousePos.pageY);
                        }, 100);
                        Zapatec.Utils.stopEvent(objEvent);
                        objEvent.returnValue = true;
                        return false;
                    }
                }
            });
        };
        for (var iEl = 0; iEl < objTriggerElements.length; iEl++) {
            funcSetupTriggerEvent(objTriggerElements[iEl]);
        }
    } else if (strTriggerEvent == 'keydown' || strTriggerEvent == 'keyup' || strTriggerEvent == 'keypress') {
        var funcSetupTriggerEvent = function (objTriggerElement) {
            Zapatec.Utils.addEvent(objTriggerElement.triggerObject, 'keydown', function (objEvent) {
                objEvent || (objEvent = window.event);
                if (objEvent.keyCode == strTriggerKey) {
                    objMenu.triggerObject = objTriggerElement.triggerObject;
                    objMenu.triggerArgs = objTriggerElement.triggerArgs;
                    objMenu.popupMenu();
                    return Zapatec.Utils.stopEvent(objEvent);
                }
            });
        };
        for (var iEl = 0; iEl < objTriggerElements.length; iEl++) {
            funcSetupTriggerEvent(objTriggerElements[iEl])
        }
    }
};
Zapatec.Menu.MOUSEOUT = 0;
Zapatec.Menu.MOUSEOVER = 1;
Zapatec.Menu.CLICK = 2;
Zapatec.Menu.animations = {};
Zapatec.Menu.animations.fade = function (ref, counter) {
    var f = ref.filters,
        done = (counter == 100);
    if (f) {
        if (!done && ref.style.filter.indexOf("alpha") == -1) {
            ref.style.filter += ' alpha(opacity=' + counter + ')';
        }
        else if (f.length && f.alpha) with(f.alpha) {
            if (done) enabled = false;
            else {
                opacity = counter;
                enabled = true
            }
        }
    }
    else {
        ref.style.opacity = ref.style.MozOpacity = counter / 100.1;
    }
};
Zapatec.Menu.animations.slide = function (ref, counter) {
    if (counter != 100) {
        var cP = Math.pow(Math.sin(Math.PI * counter / 200), 0.75);
        if (typeof ref.__zp_origmargintop == 'undefined') {
            ref.__zp_origmargintop = ref.style.marginTop;
        }
        ref.style.marginTop = '-' + parseInt(ref.offsetHeight * (1 - cP)) + 'px';
        ref.style.clip = 'rect(' + parseInt(ref.offsetHeight * (1 - cP)) + 'px,' + ref.offsetWidth + 'px,' + ref.offsetHeight + 'px,0)';
    } else {
        if (typeof ref.__zp_origmargintop != 'undefined') {
            ref.style.marginTop = ref.__zp_origmargintop;
        }
        try {
            ref.style.clip = '';
        } catch (objException) {
            ref.style.clip = 'rect(auto,auto,auto,0)';
        }
    }
};
Zapatec.Menu.animations.glide = function (ref, counter) {
    if (counter != 100) {
        var cP = Math.pow(Math.sin(Math.PI * counter / 200), 0.75);
        ref.style.clip = 'rect(0,' + ref.offsetWidth + 'px,' + parseInt(ref.offsetHeight * cP) + 'px,0)';
    } else {
        try {
            ref.style.clip = '';
        } catch (objException) {
            ref.style.clip = 'rect(0,auto,auto,0)';
        }
    }
};
Zapatec.Menu.animations.wipe = function (ref, counter) {
    if (counter != 100) {
        ref.style.clip = 'rect(0,' + parseInt(ref.offsetWidth * (counter / 100)) + 'px,' + parseInt(ref.offsetHeight * (counter / 100)) + 'px,0)';
    } else {
        try {
            ref.style.clip = '';
        } catch (objException) {
            ref.style.clip = 'rect(0,auto,auto,0)';
        }
    }
};
Zapatec.Menu.animations.unfurl = function (ref, counter) {
    if (counter <= 50) {
        ref.style.clip = 'rect(0,' + parseInt(ref.offsetWidth * (counter / 50)) + 'px,10px,0)';
    } else if (counter < 100) {
        ref.style.clip = 'rect(0,' + ref.offsetWidth + 'px,' + parseInt(ref.offsetHeight * ((counter - 50) / 50)) + 'px,0)';
    } else {
        try {
            ref.style.clip = '';
        } catch (objException) {
            ref.style.clip = 'rect(0,auto,auto,0)';
        }
    }
};
Zapatec.Menu.prototype.addAnimation = function (animation) {
    this.animations[this.animations.length] = Zapatec.Menu.animations[animation];
};
Zapatec.Menu.prototype.treeSetDisplay = function (menu, show) {
    if (!menu.__zp_initialized) {
        menu.style.visibility = 'hidden';
        menu.style.left = '-9999px';
        menu.style.top = '-9999px';
        if (menu.__zp_dropshadow) {
            menu.__zp_dropshadow.style.visibility = 'hidden';
            menu.__zp_dropshadow.style.left = '-9999px';
            menu.__zp_dropshadow.style.top = '-9999px';
        }
        menu.__zp_initialized = true;
        return;
    }

    /* FIX FOR CREATING EXTRA NEW ROWS IN BB WHEN ADDING ROWS IN INTERNET EXPLORER */
    //var treeId = menu.__zp_tree || menu.__zp_menu.firstChild.__zp_tree;
    var treeId = menu.__zp_tree || menu.__zp_menu.firstChild && menu.__zp_menu.firstChild.__zp_tree;

    var tree;
    if (treeId) {
        tree = Zapatec.Menu.all[treeId];
    }
    if (!tree) {
        return;
    }
    if (tree.animations.length == 0) {
        if (show) {
            menu.style.visibility = 'inherit';
            if (menu.__zp_dropshadow) {
                menu.__zp_dropshadow.style.visibility = 'inherit';
            }
        } else {
            menu.style.visibility = 'hidden';
            menu.style.left = '-9999px';
            menu.style.top = '-9999px';
            if (menu.__zp_dropshadow) {
                menu.__zp_dropshadow.style.visibility = 'hidden';
                menu.__zp_dropshadow.style.left = '-9999px';
                menu.__zp_dropshadow.style.top = '-9999px';
            }
        }
        return;
    }
    menu.__zp_anim_timer |= 0;
    clearTimeout(menu.__zp_anim_timer);
    menu.__zp_anim_counter |= 0;
    if (show && !menu.__zp_anim_counter) {
        menu.style.visibility = 'inherit';
        if (menu.__zp_dropshadow) {
            menu.__zp_dropshadow.style.visibility = 'inherit';
        }
    }
    for (var ii = 0; ii < tree.animations.length; ii++) {
        tree.animations[ii](menu, menu.__zp_anim_counter);
        if (menu.__zp_dropshadow && tree.animations[ii] != Zapatec.Menu.animations.fade) {
            tree.animations[ii](menu.__zp_dropshadow, menu.__zp_anim_counter);
        }
    }
    if (!(show && menu.__zp_anim_counter == 100)) {
        menu.__zp_anim_counter += tree.config.animSpeed * (show ? 1 : -1);
        if (menu.__zp_anim_counter > 100) {
            menu.__zp_anim_counter = 100;
            menu.__zp_anim_timer = setTimeout(function () {
                tree.treeSetDisplay(menu, show);
            }, 50);
        } else if (menu.__zp_anim_counter <= 0) {
            menu.__zp_anim_counter = 0;
            menu.style.visibility = 'hidden';
            menu.style.left = '-9999px';
            menu.style.top = '-9999px';
            if (menu.__zp_dropshadow) {
                menu.__zp_dropshadow.style.visibility = 'hidden';
                menu.__zp_dropshadow.style.left = '-9999px';
                menu.__zp_dropshadow.style.top = '-9999px';
            }
        } else {
            menu.__zp_anim_timer = setTimeout(function () {
                tree.treeSetDisplay(menu, show);
            }, 50);
        }
    }
};
Zapatec.Menu.prototype.mouseOver = function (sId) {
    var oEl = document.getElementById(sId);
    if (oEl) {
        var oTree = oEl.__zp_tree || oEl.firstChild.__zp_tree;
        if (oTree) {
            oTree = Zapatec.Menu.all[oTree];
            if (oTree) {
                oTree.itemMouseHandler(oEl.__zp_item, Zapatec.Menu.MOUSEOVER);
            }
        }
    }
};
Zapatec.Menu.onItemMouseOver = function () {
    var item = this,
        tree = null;
    while (item && item != document.body) {
        var t_id = item.__zp_tree || item.firstChild.__zp_tree;
        if (t_id) tree = Zapatec.Menu.all[t_id];
        var itemClassName = item.className;
        if (/zpMenu-item/.test(itemClassName) && !/zpMenu-item-hr/.test(itemClassName)) {
            tree.itemMouseHandler(item.__zp_item, Zapatec.Menu.MOUSEOVER);
        }
        item = tree && item.__zp_treeid ? tree.items[item.__zp_item] : item.parentNode;
    }
    return true;
};
Zapatec.Menu.prototype.mouseOut = function (sId) {
    var oEl = document.getElementById(sId);
    if (oEl) {
        var oTree = oEl.__zp_tree || oEl.firstChild.__zp_tree;
        if (oTree) {
            oTree = Zapatec.Menu.all[oTree];
            if (oTree) {
                oTree.itemMouseHandler(oEl.__zp_item, Zapatec.Menu.MOUSEOUT);
            }
        }
    }
};
Zapatec.Menu.onItemMouseOut = function () {
    var item = this,
        tree = null;
    while (item && item != document.body) {
        var t_id = item.__zp_tree || item.firstChild.__zp_tree;
        if (t_id) tree = Zapatec.Menu.all[t_id];
        var itemClassName = item.className;
        if (/zpMenu-item/.test(itemClassName) && !/zpMenu-item-hr/.test(itemClassName) && !(/zpMenu-level-1/.test(itemClassName) && !/zpMenu-item-selected/.test(itemClassName))) {
            tree.itemMouseHandler(item.__zp_item, Zapatec.Menu.MOUSEOUT);
        }
        item = tree && item.__zp_treeid ? tree.items[item.__zp_item] : item.parentNode;
    }
    return false;
};
Zapatec.Menu.onItemClick = function (ev) {
    var item = this;
    if (!/zpMenuDisabled/.test(item.className)) {
        while (item && item != document.body) {
            if (item.nodeName && item.nodeName.toLowerCase() == 'a') {
                return true;
            }
            if (/zpMenu-item/.test(item.className)) {
                var objMenu = Zapatec.Menu.all[item.__zp_tree];
                if (!objMenu.config.preventDoubleCall) {
                    objMenu.called = false;
                }
                if (!objMenu.called) {
                    if (objMenu.config.onClick && item.__zp_subtree && (/zpMenu-top/.test(objMenu.trees[item.__zp_parent].className))) {
                        objMenu.itemMouseHandler(item.__zp_item, Zapatec.Menu.CLICK);
                        return Zapatec.Utils.stopEvent(ev);
                    }

                    // search for IMG which has the special class
                    //if there is such image defined its onclick event is taken and it is activated
                    var imgEl = $$('.menu_additional_option_button_img');

                    // define clicked element
                    if (event.srcElement) {
                        // exception for IE
                        clicked_element = event.srcElement;
                    } else {
                        // exception for Mozilla
                        clicked_element = event.originalTarget;
                    }

                    if (!imgEl || !imgEl[0] || !imgEl[0].onclick || clicked_element!=imgEl[0]) {
                        var itemLink = item.getElementsByTagName('a');
                        var spanEl = item.getElementsByTagName('span');

                        var res = true;
                        if (spanEl.item(0)) {
                            if (spanEl.item(0).onclick) {
                                res = spanEl.item(0).onclick();
                            }
                        }

                        var itemInput = item.getElementsByTagName('input');
                        var itemSelect = item.getElementsByTagName('select');
                        if (itemLink && itemLink.item(0) && itemLink.item(0).getAttribute('href') && itemLink.item(0).getAttribute('href') != '#' && itemLink.item(0).getAttribute('href') != window.document.location.href + '#' && itemLink.item(0).getAttribute('href') != 'javascript:void(0)') {
                            var href = itemLink.item(0).getAttribute('href');
                            var target = itemLink.item(0).getAttribute('target');
                            if (objMenu.config.rememberPath || objMenu.config.pathCookie != '__zp_item') {
                                Zapatec.Utils.writeCookie(objMenu.config.pathCookie, item.__zp_item);
                            }
                            try {
                                ev || (ev = window.event);
                                switch(ev.button) {
                                    case 1:
                                        window.open(href, "_blank");
                                    break;
                                    default:
                                        if (res) {
                                            if (target) {
                                                window.open(href, target);
                                            } else if (ev && ev.shiftKey) {
                                                window.open(href, "_blank");
                                            } else {
                                                window.location.href = href;
                                            }
                                        }
                                    break;
                                }
                            } catch (e) {};
                            if (objMenu.config.triggerEvent) {
                                objMenu.hideMenu();
                            } else {
                                objMenu.collapseAll();
                                while (Zapatec.Menu.selectedItemsStack.length) {
                                    var oTopItem = Zapatec.Menu.selectedItemsStack.pop();
                                    oTopItem.onmouseout();
                                }
                            }
                        } else if (itemInput && itemInput.item(0)) {
                            var inp = itemInput.item(0);
                            var type = inp.getAttribute('type');
                            if (type == 'checkbox') {
                                if (inp.checked) {
                                    inp.checked = false;
                                } else {
                                    inp.checked = true;
                                }
                            } else if (type == 'radio') {
                                inp.checked = true;
                            }
                        } else if (itemSelect && itemSelect.item(0)) {
                            return true;
                        } else if (item.__zp_subtree) {
                            objMenu.itemMouseHandler(item.__zp_item, Zapatec.Menu.CLICK);
                        } else if (objMenu.config.triggerEvent) {
                            objMenu.hideMenu();
                        }
                    }
                    if (objMenu.config.preventDoubleCall) {
                        objMenu.called = true;
                    }
                    return Zapatec.Utils.stopEvent(ev);
                }
            }
            item = item.parentNode;
        }
    }
    return false;
};
Zapatec.Menu.prototype.itemMouseHandler = function (item_id, type) {
    if (type) {
        this.putOnTop();
    } else {
        this.restoreZIndex();
    }
    var item = this.items[item_id];
    if (!item) return;
    var menu = this._getTree(item.__zp_parent);
    if (type < 2 && window.opera && this.config.slide) {
        var objSubtree = this._getTree(item.__zp_subtree);
        if (objSubtree && objSubtree.__zp_anim_counter && objSubtree.__zp_anim_counter < 100) {
            return;
        }
    }
    if (menu && menu.__zp_activeitem != item_id) {
        if (menu.__zp_activeitem) {
            var lastItem = this.items[menu.__zp_activeitem];
            clearTimeout(lastItem.__zp_dimtimer);
            clearTimeout(lastItem.__zp_mousetimer);
            var objMenu = this;
            setTimeout(function () {
                Zapatec.Menu.unselectItem(lastItem);
                if (lastItem.__zp_state) objMenu.toggleItem(lastItem.__zp_item, false);
                Zapatec.Menu.selectItem(item);
            }, 0);
        } else {
            setTimeout(function () {
                Zapatec.Menu.selectItem(item);
            }, 0);
        }
        menu.__zp_activeitem = item_id;
    }
    clearTimeout(item.__zp_dimtimer);
    if (type == Zapatec.Menu.MOUSEOUT) {
        item.__zp_dimtimer = setTimeout(function () {
            Zapatec.Menu.unselectItem(item);
            if (menu.__zp_activeitem == item_id) menu.__zp_activeitem = '';
        }, this.config.hideDelay);
    }
    clearTimeout(item.__zp_mousetimer);
    if (this.config.onClick && !this.clickDone) {
        if (/zpMenu-top/.test(this.trees[item.__zp_parent].className) && (type == Zapatec.Menu.MOUSEOVER)) return;
        if (type == Zapatec.Menu.CLICK) this.clickDone = true;
    }
    if (!item.__zp_state && type) {
        item.__zp_mousetimer = setTimeout('Zapatec.Menu.all["' + item.__zp_tree + '"].itemShow("' + item.__zp_item + '")', (this.config.showDelay || 1));
    } else if (item.__zp_state && !type) {
        item.__zp_mousetimer = setTimeout('Zapatec.Menu.all["' + item.__zp_tree + '"].itemHide("' + item.__zp_item + '")', (this.config.hideDelay || 1));
    }
};
Zapatec.Menu.prototype.itemShow = function (item_id) {
    var item = this.items[item_id];
    if (/zpMenuDisabled/.test(item.className)) {
        return;
    }
    var subMenu = this._getTree(item.__zp_subtree);
    if (!subMenu) {
        return;
    }
    var parMenu = this._getTree(item.__zp_parent);
    if (!subMenu.offsetHeight) {
        subMenu.style.visibility = 'visible';
    }
    if (subMenu.style.zIndex === '') {
        subMenu.style.zIndex = 'inherit';
    }
    var subMenuBorderLeft, subMenuBorderTop;
    if (typeof subMenu.clientLeft != 'undefined') {
        subMenuBorderLeft = subMenu.clientLeft;
        subMenuBorderTop = subMenu.clientTop;
    } else {
        subMenuBorderLeft = (subMenu.offsetWidth - subMenu.clientWidth) / 2;
        subMenuBorderTop = (subMenu.offsetHeight - subMenu.clientHeight) / 2;
    }
    var fc = subMenu.firstChild;
    var subMenuMarginLeft = fc.offsetLeft;
    var subMenuMarginTop = fc.offsetTop;
    var scrollX = window.pageXOffset || document.body.scrollLeft || document.documentElement.scrollLeft || 0;
    var scrollY = window.pageYOffset || document.body.scrollTop || document.documentElement.scrollTop || 0;
    var objWindowSize = Zapatec.Utils.getWindowSize();
    var winW = objWindowSize.width;
    var winH = objWindowSize.height;
    if (!subMenu.style.width || !subMenu.style.height) {
        var maxHeight = winH - 7;
        if (subMenu.offsetHeight > maxHeight) {
            var iSubMenuOffsetHeight = subMenu.offsetHeight;
            fc.__zp_first = fc.firstChild;
            fc.__zp_last = fc.lastChild;
            var objUp = Zapatec.Utils.createElement("div");
            objUp.__zp_tree = fc.firstChild.__zp_tree;
            objUp.className = 'zpMenuScrollUpInactive';
            objUp.__zp_mouseover = false;
            objUp.__zp_timer = null;
            var funcMoveUp = function () {
                var objContainer = objUp.parentNode;
                var iContainerHeight = objContainer.parentNode.clientHeight;
                var objUpArrow = objContainer.firstChild;
                var objDownArrow = objContainer.lastChild;
                if (objContainer.__zp_first.previousSibling != objUpArrow) {
                    if (objContainer.__zp_first.style.height) {
                        objContainer.__zp_first.style.height = '';
                        objContainer.__zp_first.style.overflow = '';
                    } else {
                        objContainer.__zp_first = objContainer.__zp_first.previousSibling;
                        objContainer.__zp_first.style.display = 'block';
                    }
                    var iNewHeight = objContainer.offsetHeight;
                    while (iNewHeight > iContainerHeight) {
                        objContainer.__zp_last.style.display = 'none';
                        if (objContainer.__zp_last.style.height) {
                            objContainer.__zp_last.style.height = '';
                            objContainer.__zp_last.style.overflow = '';
                        }
                        objContainer.__zp_last = objContainer.__zp_last.previousSibling;
                        iNewHeight = objContainer.offsetHeight;
                    }
                    var iSpace = iContainerHeight - iNewHeight;
                    if (iSpace > 0) {
                        objContainer.__zp_last = objContainer.__zp_last.nextSibling;
                        objContainer.__zp_last.style.display = 'block';
                        var iItemHeight = iSpace - (objContainer.__zp_last.offsetHeight - objContainer.__zp_last.clientHeight);
                        if (iItemHeight >= 0) {
                            objContainer.__zp_last.style.display = 'none';
                            objContainer.__zp_last.style.height = iItemHeight + 'px';
                            objContainer.__zp_last.style.overflow = 'hidden';
                            objContainer.__zp_last.style.display = 'block';
                            iNewHeight = objContainer.offsetHeight;
                            if (iNewHeight != iContainerHeight) {
                                iItemHeight -= iNewHeight - iContainerHeight;
                                if (iItemHeight > 0) {
                                    objContainer.__zp_last.style.height = iItemHeight + 'px';
                                } else {
                                    objContainer.__zp_last.style.display = 'none';
                                    objContainer.__zp_last.style.height = '';
                                    objContainer.__zp_last.style.overflow = '';
                                    objContainer.__zp_last = objContainer.__zp_last.previousSibling;
                                }
                            }
                        } else {
                            objContainer.__zp_last.style.display = 'none';
                            objContainer.__zp_last = objContainer.__zp_last.previousSibling;
                        }
                    }
                    objDownArrow.className = 'zpMenuScrollDownActive';
                    if (objContainer.__zp_first.previousSibling == objUpArrow) {
                        objUpArrow.className = 'zpMenuScrollUpInactive';
                    }
                    if (objUp.__zp_timer) clearTimeout(objUp.__zp_timer);
                    if (objUp.__zp_mouseover) {
                        objUp.__zp_timer = setTimeout(funcMoveUp, 50);
                    }
                }
                return true;
            };
            objUp.onmouseover = function () {
                objUp.__zp_mouseover = true;
                return funcMoveUp();
            }
            objUp.onmouseout = function () {
                objUp.__zp_mouseover = false;
                if (objUp.__zp_timer) {
                    clearTimeout(objUp.__zp_timer);
                    objUp.__zp_timer = null;
                }
            };
            fc.insertBefore(objUp, fc.firstChild);
            var objDown = Zapatec.Utils.createElement("div");
            objDown.__zp_tree = fc.firstChild.__zp_tree;
            objDown.className = 'zpMenuScrollDownActive';
            objDown.__zp_mouseover = false;
            objDown.__zp_timer = null;
            var funcMoveDown = function () {
                var objContainer = objDown.parentNode;
                var iContainerHeight = objContainer.parentNode.clientHeight;
                var objUpArrow = objContainer.firstChild;
                var objDownArrow = objContainer.lastChild;
                if (objContainer.__zp_last.nextSibling != objDownArrow) {
                    if (objContainer.__zp_last.style.height) {
                        objContainer.__zp_last.style.height = '';
                        objContainer.__zp_last.style.overflow = '';
                    } else {
                        objContainer.__zp_last = objContainer.__zp_last.nextSibling;
                        objContainer.__zp_last.style.display = 'block';
                    }
                    var iNewHeight = objContainer.offsetHeight;
                    while (iNewHeight > iContainerHeight) {
                        objContainer.__zp_first.style.display = 'none';
                        if (objContainer.__zp_first.style.height) {
                            objContainer.__zp_first.style.height = '';
                            objContainer.__zp_first.style.overflow = '';
                        }
                        objContainer.__zp_first = objContainer.__zp_first.nextSibling;
                        iNewHeight = objContainer.offsetHeight;
                    }
                    var iSpace = iContainerHeight - iNewHeight;
                    if (iSpace > 0) {
                        objContainer.__zp_first = objContainer.__zp_first.previousSibling;
                        objContainer.__zp_first.style.display = 'block';
                        var iItemHeight = iSpace - (objContainer.__zp_first.offsetHeight - objContainer.__zp_first.clientHeight);
                        if (iItemHeight > 0) {
                            objContainer.__zp_first.style.display = 'none';
                            objContainer.__zp_first.style.height = iItemHeight + 'px';
                            objContainer.__zp_first.style.overflow = 'hidden';
                            objContainer.__zp_first.style.display = 'block';
                            iNewHeight = objContainer.offsetHeight;
                            if (iNewHeight != iContainerHeight) {
                                iItemHeight -= iNewHeight - iContainerHeight;
                                if (iItemHeight > 0) {
                                    objContainer.__zp_first.style.height = iItemHeight + 'px';
                                } else {
                                    objContainer.__zp_first.style.display = 'none';
                                    objContainer.__zp_first.style.height = '';
                                    objContainer.__zp_first.style.overflow = '';
                                    objContainer.__zp_first = objContainer.__zp_first.nextSibling;
                                }
                            }
                        } else {
                            objContainer.__zp_first.style.display = 'none';
                            objContainer.__zp_first = objContainer.__zp_first.nextSibling;
                        }
                    }
                    objUpArrow.className = 'zpMenuScrollUpActive';
                    if (objContainer.__zp_last.nextSibling == objDownArrow) {
                        objDownArrow.className = 'zpMenuScrollDownInactive';
                    }
                    if (objDown.__zp_timer) clearTimeout(objDown.__zp_timer);
                    if (objDown.__zp_mouseover) {
                        objDown.__zp_timer = setTimeout(funcMoveDown, 50);
                    }
                }
                return true;
            };
            objDown.onmouseover = function () {
                objDown.__zp_mouseover = true;
                return funcMoveDown();
            }
            objDown.onmouseout = function () {
                objDown.__zp_mouseover = false;
                if (objDown.__zp_timer) {
                    clearTimeout(objDown.__zp_timer);
                    objDown.__zp_timer = null;
                }
            };
            fc.appendChild(objDown);
            var lc = fc.__zp_last;
            iSubMenuOffsetHeight += objUp.offsetHeight + objDown.offsetHeight;
            while (iSubMenuOffsetHeight > maxHeight) {
                iSubMenuOffsetHeight -= lc.offsetHeight;
                lc.style.display = 'none';
                lc = lc.previousSibling;
                fc.__zp_last = lc;
            }
        }
        var width = fc.offsetWidth;
        var height = fc.offsetHeight;
        if (typeof subMenu.clientLeft != 'undefined' && !window.opera && !(document.compatMode && document.compatMode == 'CSS1Compat')) {
            width += subMenuBorderLeft * 2 + subMenuMarginLeft * 2;
            height += subMenuBorderTop * 2 + subMenuMarginTop * 2;
        }
        subMenu.style.width = width + 'px';
        subMenu.style.height = height + 'px';
        if (subMenu.__zp_dropshadow) {
            subMenu.__zp_dropshadow.style.width = subMenu.offsetWidth + 'px';
            subMenu.__zp_dropshadow.style.height = subMenu.offsetHeight + 'px';
        }
        fc.style.position = 'absolute';
        fc.style.visibility = 'inherit';
    }
    var newLeft = 0,
        newTop = 0;
    var menuPos = Zapatec.Utils.getAbsolutePos(parMenu);
    if ((/zpMenu-top/.test(this.trees[item.__zp_parent].className)) && (!(this.config.vertical))) {
        newLeft = item.offsetLeft;
        newTop = item.offsetHeight;
        if (menuPos.x + newLeft + subMenu.offsetWidth + subMenuMarginLeft + 7 > scrollX + winW) {
            newLeft += item.offsetWidth - subMenu.offsetWidth - subMenuMarginLeft;
            if (subMenu.__zp_dropshadow) newLeft -= 6;
        } else {
            newLeft -= subMenuBorderLeft;
        }
        if (menuPos.y + newTop + subMenu.offsetHeight + subMenuMarginTop + 7 > scrollY + winH) {
            newTop = -subMenu.offsetHeight;
            if (subMenu.__zp_dropshadow) newTop -= 5;
        }
    } else {
        newLeft = item.offsetWidth;
        newTop = item.offsetTop;
        if (menuPos.x + newLeft + subMenu.offsetWidth + subMenuMarginLeft + 7 > scrollX + winW) {
            newLeft = -subMenu.offsetWidth;
            if (subMenu.__zp_dropshadow) newLeft -= 5;
        }
        if (menuPos.y + newTop + subMenu.offsetHeight + subMenuMarginTop + 7 > scrollY + winH) {
            newTop -= subMenu.offsetHeight - item.offsetHeight;
            if (subMenu.__zp_dropshadow) newTop -= 5;
        } else {
            newTop -= subMenuBorderTop;
        }
    }
    if (menuPos.x + newLeft < 0) {
        newLeft = 0 - menuPos.x;
    }
    if (menuPos.y + newTop < 0) {
        newTop = 0 - menuPos.y;
    }
    subMenu.style.left = newLeft + 'px';
    subMenu.style.top = newTop-1 + 'px'; //dummy change to fix the vertical position of submenus
    if (subMenu.__zp_dropshadow) {
        subMenu.__zp_dropshadow.style.left = newLeft + 5 + 'px';
        subMenu.__zp_dropshadow.style.top = newTop + 5 + 'px';
    }
    if (Zapatec.is_ie && !Zapatec.is_ie5 && !Zapatec.is_ie7) {
        if (!subMenu.__zp_wch) {
            subMenu.__zp_wch = Zapatec.Utils.createWCH(subMenu);
        }
        if (subMenu.__zp_wch) {
            subMenu.__zp_wch.style.zIndex = -1;
            if (this.config.dropShadow) {
                Zapatec.Utils.setupWCH(subMenu.__zp_wch, -subMenuBorderLeft, -subMenuBorderTop, subMenu.offsetWidth + 6, subMenu.offsetHeight + 5);
            } else {
                Zapatec.Utils.setupWCH(subMenu.__zp_wch, -subMenuBorderLeft, -subMenuBorderTop, subMenu.offsetWidth, subMenu.offsetHeight);
            }
        }
    }
    this.toggleItem(item_id, true);
};
Zapatec.Menu.prototype.itemHide = function (item_id) {
    var item = this.items[item_id];
    var subMenu = this._getTree(item.__zp_subtree);
    var parMenu = this._getTree(item.__zp_parent);
    if (subMenu) {
        this.toggleItem(item_id, false);
        parMenu.__zp_activeitem = '';
        subMenu.__zp_activeitem = '';
        for (var i in this.items) {
            if (this.items[i].__zp_state) return;
        }
        this.clickDone = false;
    }
};
Zapatec.Menu.dragStart = function (ev, menu) {
    ev || (ev = window.event);
    if (menu.dragging) {
        return true;
    }
    var rootMenu = menu.rootMenu;
    if (!(/(absolute|fixed)/).test(rootMenu.style.position)) {
        rootMenu.style.position = 'absolute';
        var pos = Zapatec.Utils.getAbsolutePos(rootMenu);
        rootMenu.style.left = pos.x + 'px';
        rootMenu.style.top = pos.y + 'px';
    }
    var testElm = ev.srcElement || ev.target;
    while (1) {
        if (testElm == rootMenu) break;
        else testElm = testElm.parentNode;
        if (!testElm) return true;
    }
    menu.dragging = true;
    var posX = ev.pageX || ev.clientX + window.document.body.scrollLeft || 0;
    var posY = ev.pageY || ev.clientY + window.document.body.scrollTop || 0;
    var L = parseInt(rootMenu.style.left) || 0;
    var T = parseInt(rootMenu.style.top) || 0;
    menu.xOffs = (posX - L);
    menu.yOffs = (posY - T);
    if (menu.config.scrollWithWindow) {
        Zapatec.ScrollWithWindow.unregister(menu.rootMenu);
    }
};
Zapatec.Menu.dragMove = function (ev, menu) {
    ev || (ev = window.event);
    var rootMenu = menu.rootMenu;
    if (!(menu && menu.dragging)) {
        return false;
    }
    var posX = ev.pageX || ev.clientX + window.document.body.scrollLeft || 0;
    var posY = ev.pageY || ev.clientY + window.document.body.scrollTop || 0;
    var st = rootMenu.style,
        L = posX - menu.xOffs,
        T = posY - menu.yOffs;
    st.left = L + "px";
    st.top = T + "px";
    return Zapatec.Utils.stopEvent(ev);
};
Zapatec.Menu.dragEnd = function (ev, menu) {
    if (!menu) {
        return false;
    }
    if (menu.dragging) {
        menu.dragging = false;
        var rootMenu = menu.rootMenu;
        var st = rootMenu.style,
            L = parseInt(st.left),
            T = parseInt(st.top);
        var scrollX = window.pageXOffset || document.body.scrollLeft || document.documentElement.scrollLeft || 0;
        var scrollY = window.pageYOffset || document.body.scrollTop || document.documentElement.scrollTop || 0;
        var objWindowSize = Zapatec.Utils.getWindowSize();
        var winW = objWindowSize.width;
        var winH = objWindowSize.height;
        if (L < 0) {
            st.left = '0px';
        } else if (L + rootMenu.offsetWidth > scrollX + winW) {
            st.left = scrollX + winW - rootMenu.offsetWidth + 'px';
        }
        if (T < 0) {
            st.top = '0px';
        } else if (T + rootMenu.offsetHeight > scrollY + winH) {
            st.top = scrollY + winH - rootMenu.offsetHeight + 'px';
        }
        if (menu.config.scrollWithWindow) {
            Zapatec.ScrollWithWindow.register(rootMenu);
        }
    }
};
Zapatec.Menu.prototype.itemDisable = function (item_id) {
    var item = this.items[item_id];
    if (item) {
        Zapatec.Utils.addClass(item, "zpMenuDisabled");
    }
};
Zapatec.Menu.prototype.itemEnable = function (item_id) {
    var item = this.items[item_id];
    if (item) {
        Zapatec.Utils.removeClass(item, "zpMenuDisabled");
    }
};
Zapatec.Menu.prototype.popupMenu = function (iLeft, iTop) {
    for (var menuId in Zapatec.Menu.all) {
        if (!Zapatec.Menu.all.hasOwnProperty(menuId)) {
            continue;
        }
        var menu = Zapatec.Menu.all[menuId];
        if (menu.config.triggerEvent) {
            menu.hideMenu();
        }
    }
    if (arguments.length > 1) {
        this.showMenu(iLeft, iTop);
    } else {
        this.showMenu();
    }
};
Zapatec.Menu.prototype.showMenu = function (iLeft, iTop) {
    var objTopParent = this.top_parent;
    var objMenu = objTopParent.__zp_menu;
    var objWindowSize = Zapatec.Utils.getWindowSize();
    var iScrollX = Zapatec.Utils.getPageScrollX();
    var iScrollY = Zapatec.Utils.getPageScrollY();
    if (arguments.length > 1) {
        objTopParent.style.position = 'absolute';
        objTopParent.style.left = iLeft + 'px';
        objTopParent.style.top = iTop + 'px';
    }
    objTopParent.style.display = 'block';
    if (!objMenu.style.width) {
        if (objMenu.childNodes) {
            var iMenuWidth = 0;
            for (var iItem = 0; iItem < objMenu.childNodes.length; iItem++) {
                var objItem = objMenu.childNodes[iItem];
                var iItemMargin = 0;
                if (iItem == 0) {
                    iItemMargin = objItem.offsetLeft;
                }
                if (this.config.vertical) {
                    if (objItem.offsetWidth > iMenuWidth) {
                        iMenuWidth = objItem.offsetWidth + iItemMargin;
                    }
                } else {
                    iMenuWidth += objItem.offsetWidth + iItemMargin;
                }
            }
            if (typeof objMenu.clientLeft != 'undefined') {
                iMenuWidth += objMenu.clientLeft * 2;
            } else {
                iMenuWidth += objMenu.offsetWidth - objMenu.clientWidth;
            }
            if (objMenu.clientWidth > iMenuWidth) {
                objMenu.style.width = objMenu.clientWidth + 'px';
            } else {
                objMenu.style.width = iMenuWidth + 'px';
            }
        }
    }
    if (arguments.length > 1) {
        if (iLeft + objTopParent.offsetWidth > iScrollX + objWindowSize.width) {
            objTopParent.style.left = iScrollX + objWindowSize.width - objTopParent.offsetWidth + 'px';
        }
        if (iTop + objTopParent.offsetHeight > iScrollY + objWindowSize.height) {
            objTopParent.style.top = iScrollY + objWindowSize.height - objTopParent.offsetHeight + 'px';
        }
    } else {
        if (typeof this.config.top != 'object' || typeof this.config.right != 'object' || typeof this.config.bottom != 'object' || typeof this.config.left != 'object') {
            objTopParent.style.position = 'absolute';
            if (typeof this.config.top != 'object') {
                objTopParent.style.top = parseInt(this.config.top) + 'px';
            } else if (typeof this.config.bottom != 'object') {
                objTopParent.style.top = (objWindowSize.height - parseInt(this.config.bottom) - objMenu.offsetHeight - (objTopParent.offsetHeight - objTopParent.clientHeight)) + 'px';
            }
            if (typeof this.config.left != 'object') {
                objTopParent.style.left = parseInt(this.config.left) + 'px';
            } else if (typeof this.config.right != 'object') {
                objTopParent.style.left = (objWindowSize.width - parseInt(this.config.right) - objMenu.offsetWidth - (objTopParent.offsetWidth - objTopParent.clientWidth)) + 'px';
            }
        } else if (window.opera && (this.config.drag || this.config.scrollWithWindow)) {
            objTopParent.style.position = 'absolute';
            var pos = Zapatec.Utils.getElementOffset(objTopParent);
            objTopParent.style.left = pos.left + 'px';
            objTopParent.style.top = pos.top + 'px';
        }
    }
    objTopParent.style.zIndex = this.config.zIndex;
    if ((this.config.rememberPath || this.config.pathCookie != '__zp_item') && this.path) {
        this.highlightPath(this.path);
        if (this.config.rememberPath == 'expand') {
            this.sync(this.path);
        }
    }
    if (Zapatec.is_ie && !Zapatec.is_ie5 && !Zapatec.is_ie7) {
        if (!objTopParent.__zp_wch) {
            objTopParent.__zp_wch = Zapatec.Utils.createWCH(objTopParent);
        }
        if (objTopParent.__zp_wch) {
            objTopParent.__zp_wch.style.zIndex = -1;
            Zapatec.Utils.setupWCH(objTopParent.__zp_wch, -objTopParent.clientLeft, -objTopParent.clientTop, objTopParent.offsetWidth, objTopParent.offsetHeight);
        }
    }
    this.fireEvent('menuShown');
};
Zapatec.Menu.prototype.hideMenu = function () {
    this.collapseAll();
    this.top_parent.style.display = 'none';
    this.fireEvent('menuHidden');
};
Zapatec.Menu.selectedItemsStack = [];
Zapatec.Menu.selectItem = function (item) {
    Zapatec.Utils.addClass(item, "zpMenu-item-selected");
    if (/zpMenu-item-collapsed/i.test(item.className)) {
        Zapatec.Utils.addClass(item, "zpMenu-item-selected-collapsed");
    }
    for (var i = Zapatec.Menu.selectedItemsStack.length - 1; i >= 0; i--) {
        if (Zapatec.Menu.selectedItemsStack[i] == item) {
            Zapatec.Menu.selectedItemsStack.splice(i, 1);
        }
    }
    Zapatec.Menu.selectedItemsStack.push(item);
};
Zapatec.Menu.unselectItem = function (item) {
    Zapatec.Utils.removeClass(item, "zpMenu-item-selected");
    Zapatec.Utils.removeClass(item, "zpMenu-item-selected-collapsed");
    for (var i = Zapatec.Menu.selectedItemsStack.length - 1; i >= 0; i--) {
        if (Zapatec.Menu.selectedItemsStack[i] == item) {
            Zapatec.Menu.selectedItemsStack.splice(i, 1);
        }
    }
};
Zapatec.Utils.addEvent(window, 'load', Zapatec.Utils.checkActivation);
