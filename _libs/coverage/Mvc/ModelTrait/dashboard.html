<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Mvc/ModelTrait</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/Nzoom-Hella/_libs/Nzoom</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Mvc</a></li>
         <li class="breadcrumb-item"><a href="index.html">ModelTrait</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BelongsToTrait.php.html#10">Nzoom\Mvc\ModelTrait\BelongsToTrait</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BelongsToTrait.php.html#10">Nzoom\Mvc\ModelTrait\BelongsToTrait</a></td><td class="text-right">1560</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BelongsToTrait.php.html#45"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::generateBelongsToUserCacheKey">generateBelongsToUserCacheKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#50"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::getBelongsToUserCache">getBelongsToUserCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#56"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::addBelongsToUserCache">addBelongsToUserCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#62"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::getInGroupsCache">getInGroupsCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#67"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::addInGroupsCache">addInGroupsCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#72"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::getInUserDepartmentsCache">getInUserDepartmentsCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#77"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::addInUserDepartmentsCache">addInUserDepartmentsCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#82"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isOriginRequest">isOriginRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#87"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isAddedBy">isAddedBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#98"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isUserAssigned">isUserAssigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#118"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isUserAssignedAs">isUserAssignedAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#129"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::userInUserPermissions">userInUserPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#135"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isViewableAccessGrantedToUser">isViewableAccessGrantedToUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#148"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::inGroups">inGroups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#180"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::inDepartments">inDepartments</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#210"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::belongsToUser">belongsToUser</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BelongsToTrait.php.html#210"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::belongsToUser">belongsToUser</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#148"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::inGroups">inGroups</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#180"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::inDepartments">inDepartments</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#98"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isUserAssigned">isUserAssigned</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#118"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isUserAssignedAs">isUserAssignedAs</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#87"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isAddedBy">isAddedBy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#129"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::userInUserPermissions">userInUserPermissions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BelongsToTrait.php.html#135"><abbr title="Nzoom\Mvc\ModelTrait\BelongsToTrait::isViewableAccessGrantedToUser">isViewableAccessGrantedToUser</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Wed Jun 25 14:33:35 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([1,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([16,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,39,"<a href=\"BelongsToTrait.php.html#10\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BelongsToTrait.php.html#45\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::generateBelongsToUserCacheKey<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#50\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::getBelongsToUserCache<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#56\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::addBelongsToUserCache<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#62\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::getInGroupsCache<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#67\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::addInGroupsCache<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#72\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::getInUserDepartmentsCache<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#77\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::addInUserDepartmentsCache<\/a>"],[0,1,"<a href=\"BelongsToTrait.php.html#82\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::isOriginRequest<\/a>"],[0,2,"<a href=\"BelongsToTrait.php.html#87\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::isAddedBy<\/a>"],[0,3,"<a href=\"BelongsToTrait.php.html#98\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::isUserAssigned<\/a>"],[0,3,"<a href=\"BelongsToTrait.php.html#118\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::isUserAssignedAs<\/a>"],[0,2,"<a href=\"BelongsToTrait.php.html#129\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::userInUserPermissions<\/a>"],[0,2,"<a href=\"BelongsToTrait.php.html#135\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::isViewableAccessGrantedToUser<\/a>"],[0,6,"<a href=\"BelongsToTrait.php.html#148\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::inGroups<\/a>"],[0,5,"<a href=\"BelongsToTrait.php.html#180\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::inDepartments<\/a>"],[0,8,"<a href=\"BelongsToTrait.php.html#210\">Nzoom\\Mvc\\ModelTrait\\BelongsToTrait::belongsToUser<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
