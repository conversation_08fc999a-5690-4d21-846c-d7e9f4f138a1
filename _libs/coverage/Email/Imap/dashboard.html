<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Email/Imap</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/Nzoom-Hella/_libs/Nzoom</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Email</a></li>
         <li class="breadcrumb-item"><a href="index.html">Imap</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Attachment.php.html#5">Nzoom\Email\Imap\Attachment</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#17">Nzoom\Email\Imap\Box</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BoxFactory.php.html#8">Nzoom\Email\Imap\BoxFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#13">Nzoom\Email\Imap\ClientManagerFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#11">Nzoom\Email\Imap\Message</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#9">Nzoom\Email\Imap\MessageListPaginated</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Message.php.html#11">Nzoom\Email\Imap\Message</a></td><td class="text-right">4160</td></tr>
       <tr><td><a href="Box.php.html#17">Nzoom\Email\Imap\Box</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#9">Nzoom\Email\Imap\MessageListPaginated</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Attachment.php.html#5">Nzoom\Email\Imap\Attachment</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#13">Nzoom\Email\Imap\ClientManagerFactory</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Attachment.php.html#7"><abbr title="Nzoom\Email\Imap\Attachment::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#17"><abbr title="Nzoom\Email\Imap\MessageListPaginated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#184"><abbr title="Nzoom\Email\Imap\Message::removeOriginalEmail">removeOriginalEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#228"><abbr title="Nzoom\Email\Imap\Message::getBetterBody">getBetterBody</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#236"><abbr title="Nzoom\Email\Imap\Message::isAutoresponder">isAutoresponder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#257"><abbr title="Nzoom\Email\Imap\Message::getDeliveryStatusPart">getDeliveryStatusPart</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#272"><abbr title="Nzoom\Email\Imap\Message::getDiagnosticCodes">getDiagnosticCodes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#300"><abbr title="Nzoom\Email\Imap\Message::move">move</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#29"><abbr title="Nzoom\Email\Imap\MessageListPaginated::getPage">getPage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#143"><abbr title="Nzoom\Email\Imap\Message::getAction">getAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#38"><abbr title="Nzoom\Email\Imap\MessageListPaginated::getLastPage">getLastPage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#47"><abbr title="Nzoom\Email\Imap\MessageListPaginated::current">current</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#52"><abbr title="Nzoom\Email\Imap\MessageListPaginated::next">next</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#57"><abbr title="Nzoom\Email\Imap\MessageListPaginated::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#62"><abbr title="Nzoom\Email\Imap\MessageListPaginated::valid">valid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#67"><abbr title="Nzoom\Email\Imap\MessageListPaginated::rewind">rewind</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#75"><abbr title="Nzoom\Email\Imap\MessageListPaginated::isReverse">isReverse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#165"><abbr title="Nzoom\Email\Imap\Message::getDate">getDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#112"><abbr title="Nzoom\Email\Imap\Message::isBounced">isBounced</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Attachment.php.html#20"><abbr title="Nzoom\Email\Imap\Attachment::getFilenameForMessage">getFilenameForMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#80"><abbr title="Nzoom\Email\Imap\Box::expunge">expunge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#26"><abbr title="Nzoom\Email\Imap\Box::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#35"><abbr title="Nzoom\Email\Imap\Box::getConfig">getConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#43"><abbr title="Nzoom\Email\Imap\Box::getClientManager">getClientManager</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#51"><abbr title="Nzoom\Email\Imap\Box::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#62"><abbr title="Nzoom\Email\Imap\Box::isConnected">isConnected</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#71"><abbr title="Nzoom\Email\Imap\Box::connect">connect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#86"><abbr title="Nzoom\Email\Imap\Box::disconnect">disconnect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#99"><abbr title="Nzoom\Email\Imap\Message::getSubject">getSubject</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Box.php.html#111"><abbr title="Nzoom\Email\Imap\Box::getMessagesFromPath">getMessagesFromPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BoxFactory.php.html#15"><abbr title="Nzoom\Email\Imap\BoxFactory::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#19"><abbr title="Nzoom\Email\Imap\ClientManagerFactory::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#48"><abbr title="Nzoom\Email\Imap\ClientManagerFactory::fetchAccessToken">fetchAccessToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#59"><abbr title="Nzoom\Email\Imap\ClientManagerFactory::prepAccountConfig">prepAccountConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#31"><abbr title="Nzoom\Email\Imap\Message::getCode">getCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Message.php.html#60"><abbr title="Nzoom\Email\Imap\Message::getMails">getMails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#83"><abbr title="Nzoom\Email\Imap\MessageListPaginated::setReverse">setReverse</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Message.php.html#60"><abbr title="Nzoom\Email\Imap\Message::getMails">getMails</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Message.php.html#31"><abbr title="Nzoom\Email\Imap\Message::getCode">getCode</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Message.php.html#112"><abbr title="Nzoom\Email\Imap\Message::isBounced">isBounced</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Box.php.html#111"><abbr title="Nzoom\Email\Imap\Box::getMessagesFromPath">getMessagesFromPath</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Message.php.html#184"><abbr title="Nzoom\Email\Imap\Message::removeOriginalEmail">removeOriginalEmail</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Message.php.html#143"><abbr title="Nzoom\Email\Imap\Message::getAction">getAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Message.php.html#165"><abbr title="Nzoom\Email\Imap\Message::getDate">getDate</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Message.php.html#300"><abbr title="Nzoom\Email\Imap\Message::move">move</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Message.php.html#272"><abbr title="Nzoom\Email\Imap\Message::getDiagnosticCodes">getDiagnosticCodes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Message.php.html#257"><abbr title="Nzoom\Email\Imap\Message::getDeliveryStatusPart">getDeliveryStatusPart</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Message.php.html#236"><abbr title="Nzoom\Email\Imap\Message::isAutoresponder">isAutoresponder</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Message.php.html#228"><abbr title="Nzoom\Email\Imap\Message::getBetterBody">getBetterBody</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Attachment.php.html#7"><abbr title="Nzoom\Email\Imap\Attachment::getName">getName</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Attachment.php.html#20"><abbr title="Nzoom\Email\Imap\Attachment::getFilenameForMessage">getFilenameForMessage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#59"><abbr title="Nzoom\Email\Imap\ClientManagerFactory::prepAccountConfig">prepAccountConfig</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#83"><abbr title="Nzoom\Email\Imap\MessageListPaginated::setReverse">setReverse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Message.php.html#99"><abbr title="Nzoom\Email\Imap\Message::getSubject">getSubject</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ClientManagerFactory.php.html#48"><abbr title="Nzoom\Email\Imap\ClientManagerFactory::fetchAccessToken">fetchAccessToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Box.php.html#86"><abbr title="Nzoom\Email\Imap\Box::disconnect">disconnect</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Box.php.html#80"><abbr title="Nzoom\Email\Imap\Box::expunge">expunge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Box.php.html#51"><abbr title="Nzoom\Email\Imap\Box::getClient">getClient</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#38"><abbr title="Nzoom\Email\Imap\MessageListPaginated::getLastPage">getLastPage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#52"><abbr title="Nzoom\Email\Imap\MessageListPaginated::next">next</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#62"><abbr title="Nzoom\Email\Imap\MessageListPaginated::valid">valid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MessageListPaginated.php.html#67"><abbr title="Nzoom\Email\Imap\MessageListPaginated::rewind">rewind</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Wed Jun 25 14:33:35 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([37,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"Attachment.php.html#5\">Nzoom\\Email\\Imap\\Attachment<\/a>"],[0,17,"<a href=\"Box.php.html#17\">Nzoom\\Email\\Imap\\Box<\/a>"],[0,1,"<a href=\"BoxFactory.php.html#8\">Nzoom\\Email\\Imap\\BoxFactory<\/a>"],[0,6,"<a href=\"ClientManagerFactory.php.html#13\">Nzoom\\Email\\Imap\\ClientManagerFactory<\/a>"],[0,64,"<a href=\"Message.php.html#11\">Nzoom\\Email\\Imap\\Message<\/a>"],[0,16,"<a href=\"MessageListPaginated.php.html#9\">Nzoom\\Email\\Imap\\MessageListPaginated<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"Attachment.php.html#7\">Nzoom\\Email\\Imap\\Attachment::getName<\/a>"],[0,3,"<a href=\"Attachment.php.html#20\">Nzoom\\Email\\Imap\\Attachment::getFilenameForMessage<\/a>"],[0,1,"<a href=\"Box.php.html#26\">Nzoom\\Email\\Imap\\Box::__construct<\/a>"],[0,1,"<a href=\"Box.php.html#35\">Nzoom\\Email\\Imap\\Box::getConfig<\/a>"],[0,1,"<a href=\"Box.php.html#43\">Nzoom\\Email\\Imap\\Box::getClientManager<\/a>"],[0,2,"<a href=\"Box.php.html#51\">Nzoom\\Email\\Imap\\Box::getClient<\/a>"],[0,1,"<a href=\"Box.php.html#62\">Nzoom\\Email\\Imap\\Box::isConnected<\/a>"],[0,1,"<a href=\"Box.php.html#71\">Nzoom\\Email\\Imap\\Box::connect<\/a>"],[0,2,"<a href=\"Box.php.html#80\">Nzoom\\Email\\Imap\\Box::expunge<\/a>"],[0,2,"<a href=\"Box.php.html#86\">Nzoom\\Email\\Imap\\Box::disconnect<\/a>"],[0,6,"<a href=\"Box.php.html#111\">Nzoom\\Email\\Imap\\Box::getMessagesFromPath<\/a>"],[0,1,"<a href=\"BoxFactory.php.html#15\">Nzoom\\Email\\Imap\\BoxFactory::__invoke<\/a>"],[0,1,"<a href=\"ClientManagerFactory.php.html#19\">Nzoom\\Email\\Imap\\ClientManagerFactory::__invoke<\/a>"],[0,2,"<a href=\"ClientManagerFactory.php.html#48\">Nzoom\\Email\\Imap\\ClientManagerFactory::fetchAccessToken<\/a>"],[0,3,"<a href=\"ClientManagerFactory.php.html#59\">Nzoom\\Email\\Imap\\ClientManagerFactory::prepAccountConfig<\/a>"],[0,10,"<a href=\"Message.php.html#31\">Nzoom\\Email\\Imap\\Message::getCode<\/a>"],[0,12,"<a href=\"Message.php.html#60\">Nzoom\\Email\\Imap\\Message::getMails<\/a>"],[0,2,"<a href=\"Message.php.html#99\">Nzoom\\Email\\Imap\\Message::getSubject<\/a>"],[0,8,"<a href=\"Message.php.html#112\">Nzoom\\Email\\Imap\\Message::isBounced<\/a>"],[0,4,"<a href=\"Message.php.html#143\">Nzoom\\Email\\Imap\\Message::getAction<\/a>"],[0,4,"<a href=\"Message.php.html#165\">Nzoom\\Email\\Imap\\Message::getDate<\/a>"],[0,5,"<a href=\"Message.php.html#184\">Nzoom\\Email\\Imap\\Message::removeOriginalEmail<\/a>"],[0,3,"<a href=\"Message.php.html#228\">Nzoom\\Email\\Imap\\Message::getBetterBody<\/a>"],[0,4,"<a href=\"Message.php.html#236\">Nzoom\\Email\\Imap\\Message::isAutoresponder<\/a>"],[0,4,"<a href=\"Message.php.html#257\">Nzoom\\Email\\Imap\\Message::getDeliveryStatusPart<\/a>"],[0,4,"<a href=\"Message.php.html#272\">Nzoom\\Email\\Imap\\Message::getDiagnosticCodes<\/a>"],[0,4,"<a href=\"Message.php.html#300\">Nzoom\\Email\\Imap\\Message::move<\/a>"],[0,1,"<a href=\"MessageListPaginated.php.html#17\">Nzoom\\Email\\Imap\\MessageListPaginated::__construct<\/a>"],[0,1,"<a href=\"MessageListPaginated.php.html#29\">Nzoom\\Email\\Imap\\MessageListPaginated::getPage<\/a>"],[0,2,"<a href=\"MessageListPaginated.php.html#38\">Nzoom\\Email\\Imap\\MessageListPaginated::getLastPage<\/a>"],[0,1,"<a href=\"MessageListPaginated.php.html#47\">Nzoom\\Email\\Imap\\MessageListPaginated::current<\/a>"],[0,2,"<a href=\"MessageListPaginated.php.html#52\">Nzoom\\Email\\Imap\\MessageListPaginated::next<\/a>"],[0,1,"<a href=\"MessageListPaginated.php.html#57\">Nzoom\\Email\\Imap\\MessageListPaginated::key<\/a>"],[0,2,"<a href=\"MessageListPaginated.php.html#62\">Nzoom\\Email\\Imap\\MessageListPaginated::valid<\/a>"],[0,2,"<a href=\"MessageListPaginated.php.html#67\">Nzoom\\Email\\Imap\\MessageListPaginated::rewind<\/a>"],[0,1,"<a href=\"MessageListPaginated.php.html#75\">Nzoom\\Email\\Imap\\MessageListPaginated::isReverse<\/a>"],[0,3,"<a href=\"MessageListPaginated.php.html#83\">Nzoom\\Email\\Imap\\MessageListPaginated::setReverse<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
