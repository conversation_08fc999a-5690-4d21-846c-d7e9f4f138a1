# Default labels
default_branch = Office
default_branches = Offices
default_central_office = Central office

# Main labels
branch = [name]
branches = [name_plural]
empty_branch = [name_lowercase]
help_branch = [name] to selected contractor

# Customers labels
customers_branch_name = [name] name
customers_contact_person_parent_customer = [name]
customers_central_office = [name_main_branch]
customers_branches = %s of %s
customers_log_add_branch = %s adds [name_lowercase] (%s) of customer
customers_log_edit_branch = %s edits [name_lowercase] (%s) of customer
customers_log_translate_branch = %s translates [name_lowercase] (%s) of customer
customers_log_delete_branch = %s deletes [name_lowercase]/[name_plural_lowercase] of customer (%s)
customers_log_activate_branch = %s activates [name_lowercase]/[name_plural_lowercase] of customer (%s)
customers_log_deactivate_branch = %s deactivates [name_lowercase]/[name_plural_lowercase] of customer (%s)
customers_logtype_add_branch = Add [name_lowercase]
customers_logtype_edit_branch = Edit [name_lowercase]
customers_logtype_translate_branch = Translate [name_lowercase]
customers_logtype_delete_branch = Delete [name_lowercase]/[name_plural_lowercase]
customers_logtype_activate_branch = Activate [name_lowercase]/[name_plural_lowercase]
customers_logtype_deactivate_branch = Deactivate [name_lowercase]/[name_plural_lowercase]

# Customer's branches labels
customers_branches_name = [name] name
customers_branches_add_branch = Add [name_lowercase]
customers_branches_edit_branch = Edit [name_lowercase]
customers_branches_translate_branch = Translate / Edit [name_lowercase]
customers_branches_main_branch = [name_main_branch]
message_customers_branch_edit_success = [name] successfully edited
message_customers_branch_add_success = [name] successfully added
message_customers_branch_translate_success = [name] successfully translated
error_customers_branch_add_failed = Error in adding [name_lowercase]
error_customers_branch_edit_failed = Error in editing [name_lowercase]
error_customers_branch_translate_failed = Error in translating [name_lowercase]
error_no_parent_customer_specified = No contractor selected to add the [name_lowercase] to
error_customers_delete_main_branch_notallowed = [name_main_branch] cannot be deleted!
error_customers_delete_branch_with_contacts_notallowed = This [name_lowercase] has some contact persons assigned. You cannot delete it until you delete the contact persons first!
error_combined_actions_branches_notallowed =  You cannot activate, deactivate or delete a [name_main_branch_lowercase]!

# Customer's contact person labels
customers_contact_persons_branch = [name]
error_no_parent_branch_specified = No [name_lowercase] selected to add the contact person to

# Contracts labels
contracts_branch = [name]
contracts_branch_address = [name] address
help_contracts_branch = [name] to selected contractor
help_contracts_branch_address = Address of the main [name_lowercase]
help_contracts_contact_person = Contact person to selected [name_lowercase]
help_contracts_main_contact_person = Main contact person for the selected [name_lowercase]

# Documents labels
documents_branch = [name]
documents_branch_address = [name] address
help_documents_branch = [name] to selected contractor
help_documents_branch_address = Address of the main [name_lowercase]
help_documents_contact_person = Contact person to selected [name_lowercase]
help_documents_main_contact_person = Main contact person for the selected [name_lowercase]

# Roles labels
module_customers_branches = Contractors: [name_plural]
action_branches = [name_plural]

# Users labels
users_branch = [name]
help_users_branch = [name] към избрания контрагент
help_users_contact_person = Лице за контакт към [name]

# Events labels
events_branch = [name]
events_branch_address = [name] address
help_events_branch = [name] to selected contractor
help_events_branch_address = Address of the main [name_lowercase]
help_events_contact_person = Contact person to selected [name_lowercase]
help_events_main_contact_person = Main contact person for the selected [name_lowercase]

# Projects labels
projects_branch = [name]
projects_branch_address = [name] address
help_projects_branch = [name] to selected contractor
help_projects_branch_address = Address of the main [name_lowercase]
help_projects_contact_person = Contact person to selected [name_lowercase]
help_projects_main_contact_person = Main contact person for the selected [name_lowercase]

# Tasks labels
tasks_branch = [name]
tasks_branch_address = [name] address
help_tasks_branch = [name] to selected contractor
help_tasks_branch_address = Address of the main [name_lowercase]
help_tasks_contact_person = Contact person to selected [name_lowercase]
help_tasks_main_contact_person = Main contact person for the selected [name_lowercase]
