automation_gig_validateleaverequest_required_settings = Липсват задължителни настройки за АД и поради това не може да се валидира заявката за отпуск!
automation_gig_validateleaverequest_no_report = Не може да се провери колко дни отпуск можете да се използват!
automation_gig_validateleaverequest_no_data_for_customer = Не са открити данни за служител и поради това не може да се валидира заявката за отпуск!
automation_gig_validateleaverequest_no_data_for_year = Няма неизползвани дни за %s!
automation_gig_validateleaverequest_not_enough_day = Заявен e %s ден, а за %s г. има %s!
automation_gig_validateleaverequest_not_enough_days = Заявени са %s дни, а за %s г. има %s!
automation_gig_validateleaverequest_period_older_than_leave_year = Периода на заявката за отпуск не може да е преди „%s“!
automation_gig_validateleaverequest_report_failed = Заявката за отпуск не може да се валидира поради проблем със справката за отпуски!
automation_gig_validateleaverequest_different_years_start_end = Началната дата и крайната дата не може да са в различни години!
automation_gig_validateleaverequest_overlap_denied_one = Служителят по текущата заявка за отпуск има предходен <a target="_blank" href="%s">отказан отпуск</a>, застъпващ се с избрания период!
automation_gig_validateleaverequest_overlap_denied_several = Служителят по текущата заявка за отпуск има предходни <a target="_blank" href="%s">отказани отпуски</a>. застъпващи се с избрания период!
automation_gig_validateleaverequest_overlap_undenied_one = Служителят по текущата заявка за отпуск има предходен <a target="_blank" href="%s">заявен/използван отпуск</a>, застъпващ се с избрания период!
automation_gig_validateleaverequest_overlap_undenied_several = Служителят по текущата заявка за отпуск има предходни <a target="_blank" href="%s">заявени/използвани отпуски</a>, застъпващи се с избрания период!