<?php

trait Aon_Automations_MedicalNotifications_Trait
{
    /**
     * AD To send notifications for new and changed reservations
     *
     * [settings]
     * # Брой дни преди датата на резервация, в случай че не е е било зададено в документа - plat_corr_days. [не е задължителен. По подразбиране е 5]
     * #corr_days := 5
     *
     * # ИД на шаблона за известяване, който да се използва. [не е задължителен. По подразбиране е 1001]
     * mail_pattern_id := 1001
     *
     * [conditions]
     * condition := ('[action]' == 'edit')
     *
     * [method]
     * plugin := aon
     * method := medicalExamsNotificationSend
     *
     * @param $params array of automation settings (fetched from the DB)
     * @return bool result of the operation
     */
    function medicalExamsNotificationSend ($params) : bool
    {
        $document = $params['model'];

        $correctionsLimit = 2;
        if (! empty($settings['corr_limit'])) {
            $correctionsLimit = $settings['corr_limit'];
        }

        $correctionsWindowDays = 5;
        if (! empty($settings['corr_days'])) {
            $correctionsWindowDays = $settings['corr_days'];
        }

        $patternId = $settings['mail_pattern_id'] ?? '1001';
        try {
            $mailPattern = $this->fetchEmailPattern($patternId);
        } catch (Exception $e) {
            General::log($this->registry, __CLASS__.'::'.__FUNCTION__, $e->getMessage());
            $this->registry['messages']->setWarning($this->i18n('error_medicalExamsNotificationSend_missing_pattern'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        try {
            $scheduleId = $this->fetchScheduleId($document->get('id'));
            $scheduleModel = $this->fetchDocumentById($scheduleId);
            $scheduleCorrectionsLimit = $scheduleModel->getAdditionalVarValue('insured_person_corr_limit');
            if ($scheduleCorrectionsLimit) {
                $correctionsLimit = $scheduleCorrectionsLimit;
            }
        } catch (Exception $e) {
            General::log($this->registry, __CLASS__.'::'.__FUNCTION__, $e->getMessage());
            $this->registry['messages']->setWarning($this->i18n('error_medicalExamsNotificationSend_missing_document'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        try {
            $documentModel = $this->fetchDocumentById($document->get('id'));
            $windowDays = $documentModel->getAdditionalVarValue('plat_corr_days');
            if (! empty($windowDays)) {
                $correctionsWindowDays = $windowDays;
            }
        } catch(Exception $e) {
            General::log($this->registry, __CLASS__.'::'.__FUNCTION__, $e->getMessage());
            $this->registry['messages']->setWarning($this->i18n('error_medicalExamsNotificationSend_missing_document'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        $changedRows = $this->compareReservationTbls(
            $this->getReservationTbl($document),
            $this->getReservationTbl($params['model']->get('old_model'))
        );

        $sentMsgs = [];
        $notSentMsgs = [];
        foreach ($changedRows as $v) {
            $newData = $v['new'];
            $email = isset($newData['insured_person_email']) ? trim($newData['insured_person_email']) : '';
            if (empty($email)) {
                continue;
            }

            $UCN   = isset($newData['insured_person_egn']) ? trim($newData['insured_person_egn']) : '';
            $HIN   = isset($newData['health_insurance_number']) ? trim($newData['health_insurance_number']) : '';
            $hin_id = $this->fetchHinId($UCN, $HIN);

            $correctionsMade = $this->fetchCorrectionsMade($hin_id, $document->get('id'));

            //set additional placeholders
            $additionalEmailVars = array(
                'hin_name' => $newData['insured_person_name'],
                'reservation_date' => General::strftime('%d.%m.%Y (%A)', $newData['reserved_date']),
                'reservation_start_hour' => $newData['reserved_hour'],
                'reservation_corrections' => number_format($correctionsLimit - $correctionsMade),
                'reservation_window_days' => $correctionsWindowDays,
            );
            $documentModel->set('additionalEmailVars', $additionalEmailVars, true);

            $recipientStr = "{$newData['insured_person_name']} <{$email}>";
            try {
                $this->sendMailToPerson($documentModel, $mailPattern, $recipientStr);
                if ($errors = $this->registry['messages']->getErrors()) {
                    $this->registry['messages']->flush();
                    foreach($errors as $error) {
                        $this->registry['messages']->setWarning($error);
                    }
                } else {
                    $sentMsgs[] = $newData['insured_person_name'];
                }
            } catch (Exception $e) {
                General::log($this->registry, __CLASS__.'::'.__FUNCTION__, $e->getMessage());
                $notSentMsgs[] = $newData['insured_person_name'];

            }
        }

        if (! empty($sentMsgs)) {
            $this->registry['messages']->setMessage(
                sprintf($this->i18n('msg_medicalExamsNotificationSend_email_sent'), implode(', ', $sentMsgs))
            );
        }

        if (! empty($notSentMsgs)) {
            $this->registry['messages']->setWarning($this->i18n('error_medicalExamsNotificationSend_email_error'));
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Extract the group table values by rows and field names.
     *
     * @param $document
     * @param string $tableName
     * @return array
     */
    private function getTableValues($document, string $tableName): array
    {
        $vars = $document->get('vars');
        foreach ($vars as $tableVar) {
            if ($tableVar['name'] === $tableName) {
                break;
            }
        }
        $tbl = [];
        foreach ($document->get('group_vars') as $var) {
            if ($var['grouping'] === $tableVar['grouping'] && $var['name'] !== $tableVar['name']) {
                //$tblVars[$var['name']] = $var;
                foreach ($var['value'] as $idx => $val) {
                    if (!isset($tbl[$idx])) {
                        $tbl[$idx] = [];
                    }
                    $tbl[$idx][$var['name']] = $val;
                }
            }
        }
        return $tbl;
    }

    /**
     * Fetches a document from the DB and returns a Document model
     * Throws exception on failure
     *
     * @param $documentId
     * @return Document
     * @throws Exception
     */
    private function fetchDocumentById($documentId): Document
    {
        $filters = array(
            'where' => array(
                'd.id = ' . $documentId,
                'd.status = "opened"'
            ),
            'skip_permissions_check' => true
        );
        $document = Documents::searchOne($this->registry, $filters);
        if (!$document) {
            throw new Exception("Can't fetch document with ID '{$documentId}'!");
        }
        return $document;
    }

    /**
     * Fetches the schedule document id by reservation id
     *
     * @param $reservationDocumentId - id of the reservation
     * @return Document
     * @throws Exception
     */
    private function fetchScheduleId($reservationDocumentId): int
    {
        $tbl_documents = DB_TABLE_DOCUMENTS;
        $tbl_documents_relatives = DB_TABLE_DOCUMENTS_RELATIVES;

        $query = <<<QUERY
            SELECT d.id
            FROM `{$tbl_documents_relatives}` dr
            JOIN `{$tbl_documents}` d
                ON dr.`parent_id`=$reservationDocumentId
                   AND dr.`parent_model_name`='Document'
                   AND dr.`link_to_model_name`='Document'
                   AND dr.`link_to`=d.id
                   AND d.status = 'opened'
                   AND d.`type`=14
            QUERY;
        $scheduleId = $this->registry['db']->GetOne($query);
        if (!$scheduleId) {
            throw new Exception("Can't fetch schedule ID for reservation '{$reservationDocumentId}'!");
        }

        return $scheduleId;
    }

    /**
     * Sends a mail from a pattern to the recipient or throws an error
     *
     * @param $document
     * @param $mailPattern
     * @param $recipient
     * @throws Exception
     */
    private function sendMailToPerson(Document $document, Email $mailPattern, string $recipient): void
    {
        $document->set('body', $mailPattern->get('body'), true);
        $document->set('email_subject', $mailPattern->get('subject'), true);
        $document->set('email_template', $mailPattern->get('id'), true);
        $document->set('add_signature', true, true);
        $document->set('customer_email', $recipient, true);

        $sa = false;
        if ($document->sanitized) {
            $document->unsanitize();
            $sa = true;
        }
        $result = $document->sendAsMail();
        if ($sa) {
            $document->sanitize();
        }

        if (! empty($result['erred'])) {
            $failedRecipients = implode(', ', $result['erred']);
            throw new Exception("Can't send e-mails to recipients ({$failedRecipients})");
        }
    }

    /**
     *  Fetch the allowed number of corrections from document of type 15
     *
     * @param $hinId
     * @param $reservationId
     * @return int
     */
    private function fetchCorrectionsMade($hinId, $reservationId) {
        $tbl_documents_cstm = DB_TABLE_DOCUMENTS_CSTM;

        $var_stats_hin_id_id = 3126; //3126: stats_hin_id (doc type 15)
        $var_stats_corrections_id = 3127; //3127: stats_corrections (doc type 15)

        $query = <<<QUERY
            SELECT `dc2`.`value` as `corrections`
                FROM `{$tbl_documents_cstm}` as `dc1`
                JOIN `{$tbl_documents_cstm}` as `dc2`
                  ON dc1.model_id='{$reservationId}'
                     AND dc1.var_id='{$var_stats_hin_id_id}'
                     AND dc2.var_id='{$var_stats_corrections_id}'
                     AND dc2.model_id=dc1.model_id
                     AND dc1.num=dc2.num
                     AND dc1.value='{$hinId}'
            QUERY;
        return (int) $this->registry['db']->GetOne($query);
    }

    /**
     * Fetch the ID of nomenclature of type 8 (Health Insurance Number)
     *
     * @param string $UCN EGN, Unique citizenship number
     * @param string $HIN HIN, health insurance number
     * @return int the id of the nomenclature storing the HIN
     */
    private function fetchHinId($UCN, $HIN) : int
    {
        $tbl_documents = DB_TABLE_DOCUMENTS;
        $tbl_documents_cstm = DB_TABLE_DOCUMENTS_CSTM;
        $tbl_nomenclatures = DB_TABLE_NOMENCLATURES;
        $tbl_nomenclatures_cstm = DB_TABLE_NOMENCLATURES_CSTM;

        $var_policy_id_14 = 3016;             //3016: policy_id (document type 14)
        $var_policy_id_8 = 909;               //909: policy_id (nom type 8)
        $var_EGN_8 = 917;                     //917: EGN (nom type 8)
        $var_health_insurance_number_8 = 901; //901: health_insurance_number (nom type 8)
        $UCNEsc = General::slashesEscape($UCN);
        $HINEsc = General::slashesEscape($HIN);

        $queryNom = <<<QUERY
            SELECT n.id, trim(nc3.value) as policyId
            FROM `{$tbl_nomenclatures}` as n
            JOIN `{$tbl_nomenclatures_cstm}` as nc1
                ON n.id=nc1.model_id AND nc1.var_id='{$var_EGN_8}' AND TRIM(nc1.value)='{$UCNEsc}'
                    AND nc1.num=1 AND nc1.lang=''
                    AND n.active AND n.deleted_by=0 AND n.type=8
            JOIN `{$tbl_nomenclatures_cstm}` as nc2
                ON n.id=nc2.model_id AND nc2.var_id='{$var_health_insurance_number_8}' AND TRIM(nc2.value)='{$HINEsc}'
                    AND nc2.num=1 AND nc2.lang=''
            JOIN `{$tbl_nomenclatures_cstm}` as nc3
                ON n.id=nc3.model_id AND nc3.var_id='{$var_policy_id_8}'
                    AND nc3.num=1 AND nc3.lang=''
            QUERY;
        $zons = $this->registry['db']->GetAll($queryNom);

        if (! $zons || count($zons) === 0) {
            throw new Exception("Can't fetch HIN ID for UCN '{$UCN}' and HIN '{$HIN}'");
        }

        $zonIdsByPolicyId = array_combine(array_column($zons, 'policyId'), array_column($zons, 'id'));
        $policyIds = implode(',', array_keys($zonIdsByPolicyId));

        $queryDocs = <<<QUERY
            SELECT trim(dc1.value)
            FROM `{$tbl_documents}` as d
            JOIN `{$tbl_documents_cstm}` as dc1
                ON d.type=14 AND d.active=1 AND d.deleted_by=0 AND d.status='opened'
                    AND dc1.model_id=d.id AND dc1.var_id='{$var_policy_id_14}' AND dc1.num=1 AND dc1.lang=''
                    AND dc1.value IN ($policyIds)
            QUERY;
        $validPolicyIds = $this->registry['db']->GetCol($queryDocs);

        foreach ($validPolicyIds as $pid) {
            if (array_key_exists($pid, $zonIdsByPolicyId)) {
                return (int) $zonIdsByPolicyId[$pid];
            }
        }

        throw new Exception("Can't fetch HIN ID for UCN '{$UCN}' and HIN '{$HIN}'");

        // Turns out single query is slower than 2 separate queries
        // I leave it here for reference
        /*$query = <<<QUERY
            SELECT n.id FROM `{$tbl_documents}` as d
                JOIN `{$tbl_documents_cstm}` as dc1
                 ON d.type=14 AND d.active=1 AND d.deleted_by=0 AND d.status='opened' AND
                     dc1.model_id=d.id AND dc1.var_id='{$var_policy_id_14}' AND dc1.num=1 AND dc1.lang=''
                JOIN `{$tbl_nomenclatures_cstm}` as nc3
                  ON nc3.var_id='{$var_policy_id_8}' AND nc3.num=1 AND nc3.lang='' AND dc1.value=nc3.value
                JOIN `{$tbl_nomenclatures}` as n
                  ON n.id=nc3.model_id AND n.active AND n.deleted_by=0 AND n.type=8
                JOIN `{$tbl_nomenclatures_cstm}` as nc1
                  ON n.id=nc1.model_id AND nc1.var_id='{$var_EGN_8}' AND TRIM(nc1.value)='{$UCNEsc}'
                     AND nc1.num=1 AND nc1.lang=''
                JOIN `{$tbl_nomenclatures_cstm}` as nc2
                  ON n.id=nc2.model_id AND nc2.var_id='{$var_health_insurance_number_8}' AND TRIM(nc2.value)='{$HINEsc}'
                     AND nc2.num=1 AND nc2.lang=''
            QUERY;

        return (int) $this->registry['db']->GetOne($query);*/
    }

    /**
     * Fetches a pattern from the DB and returns an Email model
     * Throws exception on failure
     *
     * @param $patternId
     * @return Email
     * @throws Exception
     */
    private function fetchEmailPattern($patternId): Email
    {
        $filters = [
            'where' => ["e.id = '{$patternId}'"],
            'sanitize' => true
        ];
        $mail = Emails::searchOne($this->registry, $filters);

        if (! $mail) {
            throw new Exception("Can't fetch email pattern with id '{$patternId}' from DB");
        }
        return $mail;
    }

    /**
     * Extracts and processes the reservation table from the document
     * Returns an array of witch the keys are the  insured_person_id and the value is the row in a form of keyed array
     *
     * @param $document
     * @return array
     */
    private function getReservationTbl($document): array
    {
        $tableName = 'reservation_table';
        $tbl = $this->getTableValues($document, $tableName);

        $tblById = [];
        foreach ($tbl as $v) {
            $tblById[$v['insured_person_id']] = $v;
        }
        return $tblById;
    }

    /**
     * Compares new and old reservation tables and returns an array with the differences
     * Every item of the returned array has 'new' and 'old' key. If the row is just added,
     * the old is null if the row is deleted the new is null. If anything changed the new
     * will contain the new version and the old - the old version
     *
     * @param array $newTblById
     * @param array $oldTblById
     * @return array
     */
    private function compareReservationTbls(array $newTblById, array $oldTblById): array
    {
        $changedRows = [];
        foreach ($newTblById as $k => $v) {
            if (isset($oldTblById[$k])) {
                if ($oldTblById[$k] == $v) {
                    continue;
                }
                $changedRows[] = [
                    'new' => $v,
                    'old' => $oldTblById[$k],
                ];
            } else {
                $changedRows[] = [
                    'new' => $v,
                    'old' => null,
                ];
            }
        }
        foreach ($oldTblById as $k => $v) {
            if (! isset($newTblById[$k])) {
                $changedRows[] = [
                    'new' => null,
                    'old' => $v,
                ];
            }
        }
        return $changedRows;
    }
}
