<?php

class Primatechpro_Automations_Controller extends Automations_Controller {
    /**
     * Requesting the card Api and returning the result.
     * @return bool|string
     */
    private function apiRequest() {

        //make the date filter
        $date = new DateTime("-{$this->settings['worktime_days_before']} days");
        $date = $date->format('Y-m-d');

        //test date is used for the QA to test different days from the API. Must be empty in production
        if(isset($this->settings['testDate']) && !empty($this->settings['testDate'])) {
            $date = $this->settings['testDate'];
        }
        $url = $this->settings['apiAddress'] . "/client/PersonPasses?From={$date}T00:00:00&To={$date}T23:59:59";
        // create curl resource
        $ch = curl_init();

        // set url
        curl_setopt($ch, CURLOPT_URL, $url);

        //return the transfer as a string
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        // $output contains the output string
        $output = curl_exec($ch);

        //if no result set error and return
        if ($output == null) {
            $this->executionErrors[] = curl_error($ch);
            curl_close($ch);
            return false;
        }
        // close curl resource to free up system resources
        curl_close($ch);

        return $output;
    }

    /**
     * @param string $rawApiData json encoded response from api
     * @return array|bool Formatted results|false
     */
    private function formatApiData($rawApiData) {
        $results = array();
        if($rawApiData == '[]') {
            $this->executionErrors[] = $this->i18n('no_data_returned');
            return false;
        }
        $apiData = json_decode($rawApiData, true);
        //if invalid json set error and return
        if ($apiData == null) {
            $this->executionErrors[] = $this->i18n('wrong_data_format');
            return false;
        }
        //identifier for the user. The identifier inside can be ether ucn or card_id
        $idfr = 'Tag';
        //take all none repeating identifiers for users
        $identifiers = array_unique(array_column($apiData, $idfr));
        if (count($identifiers) == 0) {
            $this->executionErrors[] = $this->i18n('not_enough_apidata');
            return false;
        }
        $preparedString = str_repeat(' ?,', count($identifiers));
        $preparedString = rtrim($preparedString, ',');
        $idfrField = '';
        if($this->settings['identifier'] == 'tag') {
            $idfrField = 'cc.value';
        } elseif ($this->settings['identifier'] == 'ucn') {
            $idfrField = 'c.ucn';
        } else {
            $this->executionErrors[] = $this->i18n('missing_or_wrong_setting');
            return false;
        }

        //now we take all the active, undeleted users from our system that match the identifiers from the api.
        //use prepared statements to protect from SQL injection
        $query = $this->registry['db']->prepare("
          SELECT {$idfrField}                      AS identifier,
                 c.id, 
                 CONCAT(ci.name, ' ', ci.lastname) AS `name`
            FROM " . DB_TABLE_CUSTOMERS. " AS c
            LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N. " AS ci
	          ON (c.id = ci.parent_id)
	        JOIN . " . DB_TABLE_CUSTOMERS_CSTM . " AS cc
              ON (c.id = cc.model_id  AND cc.var_id = 12069)
            WHERE {$idfrField} IN ({$preparedString})
                  AND c.active = 1
                  AND c.deleted = 0;");
        $users = $this->registry['db']->GetAssoc($query, $identifiers);
        if (empty($users)) {
            $this->executionErrors[] = $this->i18n('no_matching_users_found');
            return false;
        }

        //closure for creating new row
        $createRow = function ($apiVal, $time) use ($users, $idfr) {
            return array(
                'employee_id' => $users[$apiVal[$idfr]]['id'],
                'employee_name' => $users[$apiVal[$idfr]]['name'],
                'hour_in' => ($apiVal['Dir'] == 'in') ? $time : '',
                'hour_out' => ($apiVal['Dir'] == 'out') ? $time : '',
                'hour_sum' => '',
                'location_door' => addslashes($apiVal['Location']),
                'node_door' => addslashes($apiVal['Node']),
                'card_id' => addslashes($apiVal['Tag'])
            );
        };
        //lastUserRow is used to manage the missing data for hour_in and hour_out. If some data is missing the employee will lose only part of the working day as total worked hours.
        $lastUserRow = array();
        $reverse = array(
            'in' => 'out',
            'out' => 'in'
        );

        //closure to update lastUserRow. We put values for 'in' and 'out' as 0 or 1 to indicate if the last row for the specific user has value for the in or out directions.
        $updateUserRows = function ($rowNum, $dir, $rowVals = array()) use ($reverse) {
            $res = array(
                'row' => $rowNum,
                $dir => 1,
            );
            if ($rowVals) {
                $res[$reverse[$dir]] = (empty($rowVals['hour_' . $reverse[$dir]])) ? 0 : 1;
            } else {
                $res[$reverse[$dir]] = 0;
            }
            return $res;
        };

        foreach ($apiData as $k => $apiVal) {
            //format the time value as we need
            $time = null;
            try {
                $time = new DateTime($apiVal['TS']);
                $time = $time->format('H:i');
            } catch (Exception $e) {
                $this->executionErrors[] = sprintf($this->i18n('wrong_datetime_format_at'), $apiVal['EventID']);
            }
            $dir = $apiVal['Dir'];
            //we have such user?
            if (isset($users[$apiVal[$idfr]])) {
                //as start we need to create the first row in result with index 1
                if (empty($results)) {
                    $results[1] = $createRow($apiVal, $time);
                    $lastUserRow[$users[$apiVal[$idfr]]['id']] = $updateUserRows(1,$dir);
                } else {
                    //find the result row that current api value belongs to. If can`t find create new row
                    $sorted = false;
                    foreach ($results as $rowNum => $rowVals) {
                        //if we have the same user and the value of dir_(direction) is empty we put the value and stop the iteration.
                        if ($rowVals['employee_id'] == $users[$apiVal[$idfr]]['id'] && empty($rowVals['hour_' . $dir])) {
                            //if in one of the lower rows for the user, the hour_in is added and the hour_out is missing we put empty value to the current record.
                            if(($lastUserRow[$users[$apiVal[$idfr]]['id']]['row'] > $rowNum
                                && $lastUserRow[$users[$apiVal[$idfr]]['id']]['in'] == 1
                                && $lastUserRow[$users[$apiVal[$idfr]]['id']]['out'] == 0)) {
                                //missing hour_out
                                $results[$rowNum]['hour_' . $dir] = '';
                                $lastUserRow[$rowVals['employee_id']] = $updateUserRows($rowNum,$dir,$rowVals);
                                $sorted = true;
                                continue;
                            } elseif ($dir == 'in' && !empty($rowVals['hour_out']) && $rowVals['hour_out'] < $time ) {
                                //we have missing hour_in in the data. Skip this and make new row
                                break;
                            }
                            $results[$rowNum]['hour_' . $dir] = $time;
                            $sorted = true;
                            //save the last user row
                            $lastUserRow[$rowVals['employee_id']] = $updateUserRows($rowNum,$dir,$rowVals);
                            break;

                        }
                    }
                    //we iterated all the existing rows but did not put the current api value anywhere. So we make new row.
                    if ($sorted == false) {
                        $results[] = $createRow($apiVal, $time);
                        $lastUserRow[$users[$apiVal[$idfr]]['id']] = $updateUserRows(count($results),$dir);
                    }
                }

            }
        }

        //we iterate the results to calculate the hour_sum
        foreach ($results as &$result) {
            if (!empty($result['hour_in']) && !empty($result['hour_out'])) {
                $hourSum = Calendars_Calendar::timeDifference($result['hour_in'], $result['hour_out']);
                $result['hour_sum'] = $hourSum->format('%H:%I');
            } else {
                $result['hour_sum'] = '00:00';
            }

        }
        return $results;
    }


    public function updateWorkedTime($params) {

        //take the response from the api
        $response = $this->apiRequest();
        if (!$response) {
            $this->executionErrors[] = $this->i18n('error_while_requesting_data');
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        //format the returned data as we need
        $fdata = $this->formatApiData($response);
        if (!$fdata) {
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        //record the new data with the help of the document model
        $registry = $this->registry;
        $documentType = $this->settings['document_type'];
        $date = "-{$this->settings['worktime_days_before']} days";
        $date = new DateTime($date);
        $date = $date->format('Y-m-d');
        $filters = array(
            'where' => array(
                "d.type = {$documentType}",
                "d.date = '{$date}'"
            )
        );
        $workedTimeDocument = Documents::searchOne($registry, $filters);
        $rowCount = count($fdata);
        $action = 'edit';

        //if no Document found for the specified day we start building new one.
        if (!$workedTimeDocument) {
            $action = 'add';
            //start building new document
            $workedTimeDocument = new Document($this->registry, array('type' => $documentType));
            $workedTimeDocument->set('date', $date);
            $filters = array(
                'where' => array(
                    "dt.id = {$documentType}",
                    'dt.active = 1',
                    'dt.inheritance = 0'
                ),
                'sanitize' => true,
                'model_lang' => $registry['lang']
            );
            $docType = Documents_Types::searchOne($registry, $filters);
            $workedTimeDocument->set('name', $docType->get('default_name'));
            $workedTimeDocument->set('customer', $docType->get('default_customer'));

            if ($workedTimeDocument->save() == false) {
                $this->executionErrors[] = $this->i18n('error_while_saving');
                $this->updateAutomationHistory($params, $workedTimeDocument, 0);
                return false;
            }
        }
        $registry->set('get_old_vars', true, true);
        $workedTimeDocument->getVars();
        $registry->set('get_old_vars', false, true);
        $oldDocument = clone $workedTimeDocument;
        $addVars = $workedTimeDocument->get('vars');
        //we set the additional vars from the data
        foreach ($addVars as &$var) {
            //for every row from the new data we put the value as rowNum=>val
            for ($c = 1; $c <= $rowCount; $c++) {
                if(isset($fdata[$c][$var['name']])) {
                    $var['value'][$c] = $fdata[$c][$var['name']];
                } else {
                    continue;
                }
            }
        }

        $workedTimeDocument->set('vars', $addVars, true);

        if ($workedTimeDocument->saveVars() && $workedTimeDocument->save()) {
            $filters    = array('where'    => array('d.id = ' . $workedTimeDocument->get('id')),
                'sanitize' => true);
            $new_model  = Documents::searchOne($registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_model->getVars();
            $this->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');
            $this->updateAutomationHistory($params, $workedTimeDocument, 1);
            $audit_parent = Documents_History::saveData($registry,
                array('model'     => $workedTimeDocument,
                    'action_type' => $action,
                    'new_model'   => $new_model,
                    'old_model'   => $oldDocument));
            return true;
        } else {
            $this->executionErrors[] = $this->i18n('error_while_saving');
            $this->updateAutomationHistory($params, $workedTimeDocument, 0);
            return false;
        }
    }
}