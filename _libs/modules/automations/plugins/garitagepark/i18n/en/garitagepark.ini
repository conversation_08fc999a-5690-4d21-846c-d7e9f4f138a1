automation_error_fail_to_save_histrory = <PERSON>rror ocurred while trying to save history after adding related object to customer with ID %s
automation_error_fail_to_save_customer = <PERSON>rror ocurred while trying to add related object to customer with ID %s
automation_error_validation_errors = While trying to update the customer %s the following errors have ocurred: %s

portal_users_error_universal = Error has occurred while trying to create portal users. Please contact nZoom administrator.
portal_users_error_fail_to_activate_user = Error occupied while trying to activate %s %s with username  %s.
portal_users_error_fail_to_deactivate_user = Error occupied while trying to deactivate %s %s with username  %s.
portal_users_error_user_exists = User with username or email %s already exists (%s %s).
portal_users_error_fail_to_save_user = Error occupied while trying to create user for %s %s with email  %s.
portal_users_error_role_missing = Please select role for fortal user %s.
portal_users_error_email_missing = Please fill valid email in section contact data.
portal_users_error_fail_to_change_role = <PERSON>rror occupied while trying to change the role of %s %s with username  %s.

portal_users_message_user_deactivated = The user %s %s with username %s has been deactivated.
portal_users_message_user_activated = The user %s %s with username %s has been activated.
portal_users_message_user_created = New user to %s %s with username %s has been created. Notification email is sent to the new user.
portal_users_message_user_created_nomail = New user to %s %s with username %s has been created.
portal_users_message_userrole_update = The role of %s %s with username %s was changed.

portal_users_warning_user_created_email_not_sent = User of %s %s has been crated with username %s, but error occured while trying to send notification email to the new user.
portal_users_warning_customer_history_not_saved = Users were created but history was not saved.

equipment_error_validation_repeatingvalues = The equipment %s is repeating. Please remove the dublicates.
equipment_error_validation_usedinobjects = The equipment %s is already used in object %s.

finance_distribution_message_distribution_successful_cron = Successful distribution of finance document with ID %s.
finance_distribution_message_distribution_successful = Successful distribution.

finance_distribution_warning_missing_column_data = While trying to distribute finance document, the following error have occurred: Missing data from column "%s".

finance_distribution_error_missing_column_data = While trying to distribute finance document with ID %s, the following error have occurred: Missing data from column "%s".
finance_distribution_error_fail_to_distribute =  While trying to distribute finance document with ID %s, the following errors have occurred: %s
income_basis_article_description = Communal service charges for month %s.

communal_expenses_name = Communal charges invoice
communal_expenses_incomes_basis_name = Communal charges

communal_expenses_error_income_basis = Error occurred while trying to save income basis.
communal_expenses_error_invoice = Error occurred while trying to save invoice.
communal_expenses_error_invoice_file = Error occurred while trying to save file from invoice %s.
communal_expenses_error_invoice_notsend = Invoice was not sent to customer with %s due to not found email address.
communal_expenses_error_setstatus_uninvoiced = Protocol with ID %s was not marked as not invoiced!

protocol_device_subtype_electro = electrometer
protocol_device_subtype_water = watermeter
protocol_device_subtype_gas = gasometer
protocol_device_subtype_hotcool = HVAC device

error_editing_protocol = An error occurred while editing the fare values in <a href="%s" target="_blank">protocol %s</a>! Please, contact nZoom support!
error_status_change_protocol = An error occurred while changing the status of <a href="%s" target="_blank">protocol %s</a>! Please, contact nZoom support!
error_getting_automation_settings = An error occurred while defining the settings of the automation! Please, contact nZoom support!
error_protocol_dates_in_different_months = The dates completed in fields '%s' and '%s' must be in the same month!
error_protocol_dates_mismatch = The date in the field '%s' must be before the date in '%s'!
error_protocol_protocol_contracts_no_fares = The selected contract is missing data for fares for consumables! Please, select contract which have at least one fare!
error_protocol_devices_missing_from_protocol = According to the contract the data for %s '%s' needs to be invoiced too! Please, complete the data for this device too!
error_protocol_devices_extra_in_protocol = According to the contract the data for %s '%s' must not be invoiecd! Please, remove this device!
error_protocol_wrong_contract_type_selected = Please select contract from these types: %s!
error_protocol_devices_missing_fare = Missing fare for '%s'! The protocol can not be locked/closed!
error_protocol_devices_missing_fare_value = Missing value for '%s'! The protocol can not be locked/closed!
error_protocol_devices_missing_settings = Missing settings for sums for %s! Please, contact nZoom support!
error_protocol_complete_contract = Please, complete contract!
error_protocol_devices_consumption_complete_missing_device = Row %d has been completed with consumption value for %s, but no measuring device is selected! Please, complete a device or remove the consumption value!
error_protocol_missing_fare = Missing fare for '%s'! The protocol can not be locked/closed!
error_protocol_fare_value_does_not_match = The sum for '%s' does not match the completed in the respected month fare (%.6f)! Please, correct the sum or contact the nZoom support!
error_updating_objects_equipment = Error occurred while trying to save the equipment into the related objects! Please, contact nZoom support!
