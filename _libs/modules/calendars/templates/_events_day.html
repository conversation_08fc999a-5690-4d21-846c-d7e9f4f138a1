{foreach name='i' from=$events item=event}
    {strip}
      {capture assign='event_label'}{$day->timestamp|date_format:#date_calendar_day_short#}{/capture}
      {capture assign='event_priority'}events_priority_{$event.priority}{/capture}
      {capture assign='event_info'}
        {include file=`$templatesDir`_events_info.html}
      {/capture}

      {*colors*}
      {if $event.ownership eq 'other'}
        {capture assign='event_color'}{$calendar_settings.color_other}{/capture}
        {capture assign='event_background_color'}{$calendar_settings.background_color_other}{/capture}
      {elseif $event.ownership eq 'mine'}
        {capture assign='mine_color'}color_{$event.type}{/capture}
        {capture assign='mine_background_color'}background_color_{$event.type}{/capture}
        {capture assign='event_color'}{$calendar_settings.$mine_color}{/capture}
        {capture assign='event_background_color'}{$calendar_settings.$mine_background_color}{/capture}
      {else}
        {capture assign='event_color'}{$calendar_settings.color_none}{/capture}
        {capture assign='event_background_color'}{$calendar_settings.background_color_none}{/capture}
      {/if}

      {*positioning*}
      {math equation='4*(es*h/60)+adc*had-4*(ds*h/60)' es=$event.start h=$event_cell_height had=$event_allday_cell_height adc=$all_days_count ds=$day_start assign='event_top'}
      {math equation='ep*(ew+(ec-1)*eol)/ec-ep*eol+eos' ec=$event.count ep=$event.pos ew=$event_cell_width eol=$event_overlapping eos=$event_area_offset_left assign='event_left'}
      {math equation='(ew+(ec-1)*eol)/ec-4*eb' ec=$event.count ew=$event_cell_width eol=$event_overlapping eos=$event_area_offset_left eb=$event_area_border_width assign='event_width'}
      {math equation='4*(ee-es)*h/60-4*eb' ee=$event.end es=$event.start h=$event_cell_height eb=$event_area_border_width assign='event_height'}
      {assign var='event_name_truncate' value=80}
      {capture assign='event_url'}
        {if $event.type eq $smarty.const.PH_REMINDER_EVENT_TYPE}
          {if $event.related_id and $event.related_origin}{$smarty.server.SCRIPT_NAME}?{$module_param}={$event.related_origin}s&amp;{$event.related_origin}s=view&amp;view={$event.related_id}{else}#{/if}
        {else}
          {$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event.id}
        {/if}
      {/capture}
      {assign var='currentUserId' value=$smarty.session.currentUserId}
      {capture assign='private_event'}{if $event.visibility eq 'private' && $event.access ne 'edit'}1{else}0{/if}{/capture}
    {/strip}
    <div class="cal_event{if $private_event} cal_event_private{/if}{if $event.users_assignments.$currentUserId && $event.users_assignments.$currentUserId.user_status == 'denied'} cal_event_denied{/if}" style="background-color: {$event_background_color}; top:{$event_top}px; left:{$event_left}px; width:{$event_width}px; height:{$event_height}px;" {help text_content=$event_info label_content=$event_label|escape popup_only=1}{if !$private_event} onclick="window.location.href='{$event_url}'"{/if}>
    {if $private_event}
      <span style="color:{$event_color}">{if $event.allday_event == 1}{elseif $event.allday_event == -1}{$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}{elseif $event.event_start lt $event.selected_date}&raquo;&raquo;:&raquo;&raquo;{else}{$event.event_start|date_format:#time_short#}{/if} {#calendars_private_event#}</span>
    {else}
      {if $event.type neq $smarty.const.PH_REMINDER_EVENT_TYPE}
      <div>
        {if $event.access eq 'edit' && !($event.type_keyword == 'plannedtime' && $event.status != 'planning')}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=edit&amp;edit={$event.id}{if $event.recurrence_type && $event.parent_event_id eq 0}&amp;new_recurrence_date={$event.event_start|date_format:#date_iso_short#}&amp;parent_event_id={$event.id}{/if}"{if $event.recurrence_type && $event.parent_event_id eq 0} onclick="return selectEventEditType(this)"{/if} title="{#edit#|escape}"><img src="{$theme->imagesUrl}small/{if $event.recurrence_type && $event.parent_event_id eq 0}edit3{else}edit2{/if}.png" width="10" height="10" border="0" alt="{#edit#|escape}" /></a>
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=delete&amp;delete={$event.id}" title="{#delete#|escape}" onclick="Event.stop(event); return confirmAction('delete', function(el) {ldelim} window.location.href = el.href; {rdelim}, this);"><img src="{$theme->imagesUrl}small/delete2.png" width="10" height="10" border="0" alt="{#delete#|escape}" /></a>
        {/if}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event.id}" title="{#view#|escape}"><img src="{$theme->imagesUrl}small/view2.png" width="10" height="10" border="0" alt="{#view#|escape}" /></a>
        {if $event.access}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=communications&amp;communications={$event.id}&amp;communication_type=comments{if $event.recurrence_type && $event.parent_event_id eq 0}&amp;new_recurrence_date={$calendar->dateISO|date_format:#date_iso_short#}{/if}" title="{#comments#|escape}"><img src="{$theme->imagesUrl}small/comments.png" width="10" height="10" border="0" alt="{#comments#|escape}" /></a>
        {/if}
        {if $event.reminder_type && $event.event_end gt $smarty.now|date_format:"%Y-%m-%d %H:%M:%S"}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=remind&amp;remind={$event.id}{if $event.recurrence_type && $event.parent_event_id eq 0}&amp;new_recurrence_date={$event.event_start|date_format:#date_iso_short#}{/if}"{if $event.recurrence_type && $event.parent_event_id eq 0} onclick="return selectEventRemindType(this)"{/if} title="{#remind#|escape}"><img src="{$theme->imagesUrl}small/remind.png" width="10" height="10" border="0" alt="{#remind#|escape}" /></a>
        {/if}
      </div>
      {/if}
      {strip}
        {capture assign='event_visible_info'}
          {if (preg_match('#name#', $calendar_settings.day_show_info) || empty($calendar_settings.day_show_info)) && $event.name}&nbsp;{$event.name|mb_truncate:$event_name_truncate:'...':true}<br />{/if}
          {if (preg_match('#employee#', $calendar_settings.day_show_info)) && $event.added_by}&nbsp;{$event.added_by_name|mb_truncate:$event_name_truncate:'...':true}<br />{/if}
          {if (preg_match('#customer#', $calendar_settings.day_show_info)) && $event.customer}&nbsp;{$event.customer_name|mb_truncate:$event_name_truncate:'...':true}<br />{/if}
          {if (preg_match('#project#', $calendar_settings.day_show_info)) && $event.project}&nbsp;{$event.project_name|mb_truncate:$event_name_truncate:'...':true}<br />{/if}
          {if (preg_match('#location#', $calendar_settings.day_show_info)) && $event.location}&nbsp;{$event.location|mb_truncate:$event_name_truncate:'...':true}<br />{/if}
        {/capture}
      {/strip}
      <a href="{$event_url}" style="color:{$event_color}">{if $event.allday_event == 1}{elseif $event.allday_event == -1}{$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}{elseif $event.event_start lt $event.selected_date}&raquo;&raquo;:&raquo;&raquo;{else}{$event.event_start|date_format:#time_short#}{/if}{$event_visible_info}</a>
    {/if}
    </div>
{/foreach}
