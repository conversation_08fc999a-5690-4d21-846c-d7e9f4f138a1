<?php

class Customers_Relatives_Viewer extends Viewer {
    public $template = 'relatives.html';

    public function prepare() {
        $this->model = $this->registry['customer'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare relatives tree
        $this->data['relatives_tree'] = $this->registry['relatives_tree'];

        $this->prepareTranslations();
        $this->prepareTitleBar();

        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->registry->push('custom_js', "{$this->viewJsUrl}list.js");
            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }

    public function prepareTitleBar() {
        // TODO: find a consensus for the title!
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('customers_relatives'), $this->model->getModelTypeName());
        }

        $this->data['title'] = $title;
    }
}

?>
