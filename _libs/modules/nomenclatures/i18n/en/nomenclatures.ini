nomenclature = Nomenclature
nomenclatures = Nomenclatures
nomenclatures_sg = Nomenclature
nomenclatures_pl = Nomenclatures
nomenclatures_all = All Nomenclatures
nomenclatures_type = Type
nomenclatures_type_section = Section
nomenclatures_code = Code
nomenclatures_num = Number
nomenclatures_name = Name
nomenclatures_category = Category
nomenclatures_added = Added on
nomenclatures_added_by = Added by
nomenclatures_modified = Modified on
nomenclatures_modified_by = Modified by
nomenclatures_customer = Contractor
nomenclatures_name_code = [Code] Name
nomenclatures_minitask = Mini task
nomenclatures_create = Create
nomenclatures_model_id = For record
nomenclatures_description = Description
nomenclatures_deadline = Due date
nomenclatures_assigned_to = Assigned to
nomenclatures_severity = Priority
nomenclatures_status = Status
nomenclatures_comment = Comment
nomenclatures_sell_price = Sell price
nomenclatures_sell_price_currency = Sell price currency
nomenclatures_sell_price_and_currency = Sell price+currency
nomenclatures_last_delivery_price = Last delivery price
nomenclatures_last_delivery_price_currency = Last delivery price currency
nomenclatures_last_delivery_price_and_currency = Last delivery price+currency
nomenclatures_average_weighted_delivery_price = Average weighted delivery price
nomenclatures_average_weighted_delivery_price_currency = Average weighted delivery price currency
nomenclatures_average_weighted_delivery_price_and_currency = Average weighted delivery price+currency
nomenclatures_tags = Tags
nomenclatures_warehouse = Warehouse
nomenclatures_group = Group

nomenclatures_batch_options = Batch options
nomenclatures_has_batch = Batch
nomenclatures_has_serial = Serial #
nomenclatures_has_expire = Expire date
nomenclatures_has_batch_code = Batch #

nomenclatures_subtype = Subtype
nomenclatures_subtype_commodity = Commodity
nomenclatures_subtype_service = Service
nomenclatures_subtype_other = Other
nomenclatures_subtype_advance = Advance
nomenclatures_subtype_discount = Discount
nomenclatures_subtype_surplus = Surplus

nomenclatures_percentage = Percentage
nomenclatures_items = Items
nomenclatures_items_income = Income items
nomenclatures_items_expense = Expense items
nomenclatures_distributed_percentage = Distributed
nomenclatures_remaining_percentage = Remaining

nomenclatures_search = Search nomenclature &raquo; Filters
nomenclatures_add_ = Added on
nomenclatures_view_ = View

nomenclatures_categories = Categories
nomenclatures_categories_legend = Add, edit and delete categories to nomenclatures module
nomenclatures_pricelists = Pricelists
nomenclatures_pricelists_legend = Add, edit and delete pricelists.

nomenclatures_pricelist_name = Pricelist name
nomenclatures_pricelist_criteria = Criteria
nomenclatures_pricelist_options = Options
nomenclatures_pricelist_update = Update prices
nomenclatures_pricelist_add_items = Add into pricelist
nomenclatures_pricelist_select_all = Select all found
nomenclatures_pricelists_criteria_no_change  = No change
nomenclatures_pricelists_criteria_increase  = Increase
nomenclatures_pricelists_criteria_decrease  = Decrease
nomenclatures_pricelists_criteria_hard  = Fixed
nomenclatures_pricelists_criteria_integer = Digitally
nomenclatures_pricelists_criteria_percentage= By percentage
nomenclatures_pricelists_new = <i>[Add new]</i>

nomenclatures_add_new_type = Add new nomenclature type
nomenclatures_edit_type = Edit nomenclature type

nomenclatures_hotlinks = Quick links

nomenclatures_add = Add %s
nomenclatures_edit = Edit %s
nomenclatures_view = View %s
nomenclatures_translate = Translation of %s to %s
nomenclatures_distribute = Distribute %s by items
nomenclatures_history = History of %s
nomenclatures_history_activity = Activity
nomenclatures_history_legend2 = View nomenclature history (click on history row to view audit of changes)
nomenclatures_remind = Reminds for %s
nomenclatures_communications = Communications
nomenclatures_comments = Comments
nomenclatures_emails = E-mails
nomenclatures_minitasks = Mini tasks
nomenclatures_attachments = Files to %s

nomenclatures_date = Date

var_type = Type
var_name = Field
var_value = New text
old_value = Previous text
no_old_value = no previous text

audit_vars = History of the modifications
audit_legend = Detailed information of the modifications performed by %s on %s

message_nomenclatures_add_success = %s added successfully
message_nomenclatures_edit_success = %s edited successfully
message_nomenclatures_clone_success =  %s cloned successfully
message_nomenclatures_translate_success = %s translated successfully
message_nomenclatures_file_deleted_success = File successfully deleted!
message_nomenclatures_distribute_success = %s distributed by items successfully

error_nomenclatures_edit_failed = Failed to edit %s:
error_nomenclatures_add_failed = Failed to add %s:
error_nomenclatures_clone_failed = Failed to clone %s:
error_nomenclatures_translate_failed = Failed translation of %s:
error_nomenclatures_distribute_failed = Failed to distribute %s by items:
error_invalid_type = Invalid or inactive type selected!
error_no_such_nomenclature_type = This record is not available for you!
error_no_such_nomenclature = This record is not available for you!
error_nomenclatures_file_deleted_failed = Error deleting file!
error_code_not_unique = %s already in use. Enter another %s.
error_isValidNumber = The field [var_label] must be numbers
error_no_available_nomenclatures_types = You have no permissions to add any nomenclature type!
error_total_items_percentage = Items should be distributed up to 100%, each item can be selected only once!
error_no_code = Enter %s, please!
error_no_name = Enter %s, please!

nomenclatures_added_record = 1 [type] record was marked for adding to customer.
nomenclatures_error_added_record_duplicate = Selected [type] record is already added or marked for adding to customer.
nomenclatures_error_added_record_invalid = Selected record cannot be added to customer as [type].
nomenclatures_added_records = [num] new [type] records were marked for adding to customer.
nomenclatures_error_added_records = Selected records were not marked for adding to customer.
nomenclatures_deleted_record = Selected [type] record was marked for removal from customer.
nomenclatures_error_deleted_record = An error occurred while marking [type] for removal from customer.
nomenclatures_modified_default_record = Modification of default value for [type] was marked for save.
nomenclatures_save_records = To save all made changes, press "Save".

nomenclatures_log_add = %s adds %s with code %s
nomenclatures_log_multiadd = %s adds %s with code %s
nomenclatures_log_edit = %s edits %s with code %s
nomenclatures_log_managevars = %s edits %s with code %s
nomenclatures_log_multiedit = %s edits %s with code %s
nomenclatures_log_translate = %s translates %s with code %s into %s
nomenclatures_log_activate = %s activates %s with code %s
nomenclatures_log_deactivate = %s deactivates %s with code %s
nomenclatures_log_delete = %s deletes %s with code %s
nomenclatures_log_restore = %s restores %s with code %s
nomenclatures_log_clone = %s clones %s with code %s from %s ("%s")
nomenclatures_log_export = %s exports file for %s with code %s
nomenclatures_log_modified_attachments = %s modifies attachment for %s with code %s
nomenclatures_log_add_attachments = %s adds attachment for %s with code %s
nomenclatures_log_distribute = %s distributes %s with code %s by items
nomenclatures_log_email = %s sends e-mail about %s with code %s
nomenclatures_log_receive_email = %s receives %s e-mail
nomenclatures_log_receive_email_detailed = There is an email from %s sent
nomenclatures_log_add_comment = %s adds comment about %s with code %s
nomenclatures_log_edit_comment = %s edits comment about %s with code %s
nomenclatures_log_add_minitask = %s add mini task to %s with code %s
nomenclatures_log_edit_minitask = %s edit mini task to %s with code %s
nomenclatures_log_status_minitask = %s change status of mini task to %s with code %s
nomenclatures_log_multistatus_minitask = %s change status of mini task to %s with code %s
nomenclatures_log_tag = %s changes tags of %s with code %s
nomenclatures_log_multitag = %s changes tags of %s with code %s

nomenclatures_logtype_add = Add
nomenclatures_logtype_multiadd = Multiple add
nomenclatures_logtype_edit = Edit
nomenclatures_logtype_managevars = Edit data
nomenclatures_logtype_multiedit = Multiple edit
nomenclatures_logtype_translate = Translate
nomenclatures_logtype_activate = Activation
nomenclatures_logtype_deactivate = Deactivation
nomenclatures_logtype_delete = Deletion
nomenclatures_logtype_restore = Restore
nomenclatures_logtype_clone = Clone
nomenclatures_logtype_modified_attachments = Modify file
nomenclatures_logtype_add_attachments = Add file
nomenclatures_logtype_distribute = Distribute
nomenclatures_logtype_email = Send e-mail
nomenclatures_logtype_receive_email = Received e-mail
nomenclatures_logtype_add_comment = Add comment
nomenclatures_logtype_edit_comment = Edit comment
nomenclatures_logtype_add_minitask = Add mini task
nomenclatures_logtype_edit_minitask = Edit mini task
nomenclatures_logtype_status_minitask = Change status of mini task
nomenclatures_logtype_multistatus_minitask = Multiple status change of mini tasks
nomenclatures_logtype_tag = Modify tags
nomenclatures_logtype_multitag = Multiple modification of tags

warning_nomenclature_batch_edit_disabled = Article has already been included in warehouse operations and its batch and/or commodity properties cannot be edited.
