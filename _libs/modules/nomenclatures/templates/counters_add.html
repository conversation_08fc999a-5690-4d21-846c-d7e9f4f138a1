<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="nomenclatures" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$nomenclatures_counter->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='counters_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox doubled" name="name" id="name" value="{$nomenclatures_counter->get('name')|escape}" title="{#nomenclatures_counters_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_next_number"><label for="next_number"{if $messages->getErrors('next_number')} class="error"{/if}>{help label='counters_next_number'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox small hright" name="next_number" id="next_number" value="{if $nomenclatures_counter->isDefined('next_number')}{$nomenclatures_counter->get('next_number')}{else}1{/if}" title="{#nomenclatures_counters_next_number#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_formula"><label for="formula"{if $messages->getErrors('formula')} class="error"{/if}>{help label='counters_formula'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {include file=`$templatesDir`_counters_formula.html}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='counters_description'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox doubled" name="description" id="description" title="{#nomenclatures_counters_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$nomenclatures_counter->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3" class="t_caption3 strong">{#nomenclatures_counters_formula_legend#|escape}</td>
        </tr>
        <tr>
          <td colspan="3">
            {include file=`$templatesDir`_counters_formula_legend.html}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$nomenclatures_counter}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
