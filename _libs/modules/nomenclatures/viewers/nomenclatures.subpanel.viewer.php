<?php

class Nomenclatures_Subpanel_Viewer extends Viewer {
    public $template = 'subpanel.html';

    public function prepare() {
        require_once $this->modelsDir . 'nomenclatures.factory.php';

        $use_ajax = ($this->registry['request']->get('source')) ? true : false;
        $session_param = $this->registry['request']->get('session_param');
        $session_param_prefix = preg_replace('#nomenclature$#', '', $session_param);

        // get default trademark from session search params
        $default_trademark = $this->registry['session']->get('default_trademark', $session_param);

        $filters = Nomenclatures::saveSearchParams($this->registry, array(), $session_param_prefix);

        // check if subpanel is for trademark nomenclatures
        if ($trademark_nomenclature = preg_match('#^customer(\d+)_ajax_trademark_nomenclature$#', $session_param, $matches)) {
            // all links should be opened in a new tab/page
            $this->data['link_target'] = '_blank';
            // update target (the id of the container of the table of records) for AJAX requests
            $this->data['update_target'] = $session_param;
            // redirect_to_url for AJAX requests (do not set &amp; for &)
            $this->data['redirect_to_url'] =
                rawurlencode(sprintf('%s?%s=%s&%s=%s&%s=%s&%s=%s&%s=%s',
                                     $_SERVER['PHP_SELF'],
                                     $this->registry['module_param'], $this->module,
                                     $this->registry['action_param'], $this->action,
                                     'source', 'ajax',
                                     'session_param', $session_param,
                                     'page', ''));

            // get current trademarks of customer from db
            $customer_id = $matches[1];
            $c = new Customer($this->registry, array('id' => $customer_id));
            $customer_trademarks = $c->getTrademarks();
            unset($c);

            $db_ids = array_keys($customer_trademarks);
            $default_trademark_db = array_keys(array_filter($customer_trademarks, function($a) { return $a['is_default']; }));
            $default_trademark_db = $default_trademark_db ? reset($default_trademark_db) : '0';
            // on initial load get default trademark from db data
            if (!$this->registry['request']->isPost()) {
                $default_trademark = $default_trademark_db;
            }

            $action = $this->registry['request']->get('action');

            if ($action) {
                // get all old ids, ignoring permissions
                $filters_ids = array_diff_key($filters, array_flip(array('page', 'display')));
                $real_action = $this->registry['action'];
                $this->registry->set('action', 'custom', true);
                $old_ids = Nomenclatures::getIds($this->registry, $filters_ids);
                $this->registry->set('action', $real_action, true);
            } else {
                // if not making changes, compare against data from db in order to display prompt for save or not
                $old_ids = $db_ids;
            }

            // get submitted ids for action (if any)
            $nom_ids = array_unique(array_filter(array_map('intval', preg_split('#\s*,\s*#', $this->registry['request']->get('nom_ids')))));
            if ($action == 'default') {
                // change or remove default trademark
                $default_trademark = $nom_ids ? reset($nom_ids) : 0;
            } elseif ($action && $nom_ids) {
                $idx_found = false;
                $session_ids = array();
                foreach ($filters['where'] as $idx => $where) {
                    if (preg_match('#^\s*n\.id\s+IN\s+\(([\d,\s]+)\)#i', $where, $matches)) {
                        $idx_found = $idx;
                        $session_ids = array_filter(array_map('intval', preg_split('#\s*,\s*#', trim($matches[1]))));
                        break;
                    }
                }
                if ($idx_found !== false) {
                    if ($action == 'delete') {
                        // delete nomenclatures
                        $session_ids = array_diff($session_ids, $nom_ids);
                        if (empty($session_ids)) {
                            $session_ids = array(0);
                        }
                        if ($default_trademark && in_array($default_trademark, $nom_ids)) {
                            $default_trademark = 0;
                        }
                    } else {
                        // add nomenclatures
                        $session_ids = array_unique(array_merge($session_ids, $nom_ids));
                    }

                    $filters['where'][$idx_found] = preg_replace('#(^\s*n\.id\s+IN\s+)\([\d,\s]+\)#i', '$1(' . implode(',', $session_ids) . ')', $filters['where'][$idx_found]);

                    // save modified filters in session
                    Nomenclatures::saveSearchParams($this->registry, $filters, $session_param_prefix);
                }
            }

            // add/remove key into the session filters
            if ($default_trademark) {
                $this->registry['session']->set('default_trademark', $default_trademark, $session_param, true);
            } else {
                $this->registry['session']->remove('default_trademark', $session_param);
            }
        }
        // end trademark processing

        $customize = array();
        if (!empty($filters['where'])) {
            $found = 0;
            foreach($filters['where'] as $where) {
                if (preg_match('/n\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/n\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'type', 'value' => $val);
                    $found++;
                }
                if (preg_match('/nt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/nt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'section', 'value' => $val);
                    $found++;
                }
            }
            if ($found > 1) {
                $customize = array();
            }
        }

        $this->setCustomTemplate($customize);
        $this->setFrameset('frameset_blank.html');

        // store old registry value for get tags flag
        $prev_tags = $this->registry->get('getTags');

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {

            $filters['get_fields'] = $this->modelFields;

            //set flag (not) to get tags for current model
            $this->registry->set('getTags', (in_array('tags', $this->modelFields) ? true : false), true);
        }

        list($nomenclatures, $pagination) = Nomenclatures::pagedSearch($this->registry, $filters);
        // if no records were found on page > 1, try first page
        if (!$nomenclatures && isset($filters['page']) && $filters['page'] > 1) {
            $filters['page'] = 1;
            list($nomenclatures, $pagination) = Nomenclatures::pagedSearch($this->registry, $filters);
        }

        if (empty($this->modelFields) || in_array('categories', $this->modelFields)) {
            foreach ($nomenclatures as $nom) {
                $nom->getCategoryNames();
            }
        }

        $this->registry->set('getTags', $prev_tags, true);

        // more trademark processing
        if ($trademark_nomenclature) {
            // get type name and id
            $type_name = $nomenclatures ? $nomenclatures[0]->get('type_name') : '';
            $type_id = $nomenclatures ? $nomenclatures[0]->get('type') : '';
            if (!$type_name || !$type_id) {
                require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.types.factory.php';
                $filters_nt = array('model_lang' => $this->registry['lang'],
                                    'sanitize' => true,
                                    'where' => array('nt.keyword = "' . PH_NOMENCLATURES_TYPE_KEYWORD_TRADEMARK . '"'));
                $nomenclatures_type = Nomenclatures_Types::searchOne($this->registry, $filters_nt);
                $type_name = $nomenclatures_type && $nomenclatures_type->get('name') ? $nomenclatures_type->get('name') : $this->i18n('nomenclature');
                $type_id = $nomenclatures_type ? $nomenclatures_type->get('id') : '';
                unset($nomenclatures_type);
            }

            // get all new ids, ignoring permissions
            $filters_ids = array_diff_key($filters, array_flip(array('page', 'display', 'limit', 'paginate', 'get_fields')));
            $real_action = $this->registry['action'];
            $this->registry->set('action', 'custom', true);
            $new_ids = Nomenclatures::getIds($this->registry, $filters_ids);
            $this->registry->set('action', $real_action, true);

            // add data used for visualisation of unsaved changes
            foreach ($nomenclatures as $idx => $nom) {
                $nomenclatures[$idx]->set('parent_id', (in_array($nom->get('id'), $db_ids) ? $customer_id : ''), true);
                if ($default_trademark == $nom->get('id')) {
                    $nomenclatures[$idx]->set('is_default', 1, true);
                }
            }

            // start messages
            if ($action == 'delete') {
                if (array_diff($old_ids, $new_ids)) {
                    $this->registry['messages']->setMessage($this->i18n('nomenclatures_deleted_record', array('type' => $type_name)));
                } else {
                    $this->registry['messages']->setError($this->i18n('nomenclatures_error_deleted_record', array('type' => $type_name)));
                }
            } elseif ($action == 'default') {
                $this->registry['messages']->setMessage($this->i18n('nomenclatures_modified_default_record', array('type' => $type_name)));
            } elseif ($action) {
                if ($diff = array_diff($new_ids, $old_ids)) {
                    if (count($diff) > 1) {
                        $this->registry['messages']->setMessage($this->i18n('nomenclatures_added_records', array('num' => count($diff), 'type' => $type_name)));
                    } else {
                        $this->registry['messages']->setMessage($this->i18n('nomenclatures_added_record', array('type' => $type_name)));
                    }
                } else {
                    if (count($nom_ids) == 1) {
                        if (in_array($nom_ids[0], $old_ids)) {
                            // duplicate
                            $this->registry['messages']->setError($this->i18n('nomenclatures_error_added_record_duplicate', array('type' => $type_name)));
                        } else {
                            // invalid
                            $this->registry['messages']->setError($this->i18n('nomenclatures_error_added_record_invalid', array('type' => $type_name)));
                        }
                    } else {
                        // multiple duplicate/invalid ids or no ids submitted
                        $this->registry['messages']->setError($this->i18n('nomenclatures_error_added_records', array('type' => $type_name)));
                    }
                }
            }
            // display prompt for save of changes when the new state has any differences from the db state
            if (array_diff($db_ids, $new_ids) || array_diff($new_ids, $db_ids) || $default_trademark != $default_trademark_db) {
                $this->registry['messages']->setWarning($this->i18n('nomenclatures_save_records'));
            }
            $this->data['messages'] = $this->registry['messages'];
            // end messages
        }
        // end trademark processing

        $this->data['nomenclatures'] = $nomenclatures;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareAjaxSort($filters, $session_param);

        //action that will be executed on model when its table row is clicked
        $this->data['row_link_action'] = $this->registry['config']->getParam('nomenclatures', 'row_link_action');

        $this->data['session_param'] = $session_param;
        $this->data['use_ajax'] = $use_ajax;

        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }
}
