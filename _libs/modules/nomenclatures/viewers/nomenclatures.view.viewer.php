<?php

use Nzoom\Mvc\ViewTrait\ModelPermissionsTrait;

class Nomenclatures_View_Viewer extends Viewer {
    use ModelPermissionsTrait;

    public $template = 'view.html';

    public function prepare() {
        $this->prepareModelsPermissionsForRest([$this->model]);

        // structure the layout vars into two-dimensional array
        $this->model->getLayoutVars();

        // prepare layout index
        $this->prepareLayoutIndex();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                                    $_SERVER['PHP_SELF'],
                                    $this->registry['module_param'], $this->module,
                                    $this->registry['action_param'], $this->action,
                                    $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('g.id = ' . $this->model->get('group'),
                                              'g.deleted IS NOT NULL'));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }

        //prepare nomenclature type
        require_once($this->modelsDir . 'nomenclatures.types.factory.php');
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'sanitize' => true,
                         'where' => array('nt.id = ' . $this->model->get('type')));
        $this->data['nomenclature_type'] = Nomenclatures_Types::searchOne($this->registry, $filters);

        //prepare categories tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $this->data['categories_tree'] = array_values(Nomenclatures_Categories::getTree($this->registry, array('sanitize' => true)));

        //get nomenclature categories
        $this->data['cats'] = $this->model->getCategories();

        $this->registry['include_tree'] = true;

        $this->prepareSubpanels();

        $this->prepareTranslations();

        $this->prepareTitleBar();
        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }

    public function prepareTitleBar() {
        // TODO: find a consensus for the title!
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('nomenclatures_view'), $this->model->getModelTypeName());
        }
        $this->data['title'] = $title;
    }
}

?>
