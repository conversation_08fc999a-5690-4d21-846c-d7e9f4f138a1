<?php

class Nomenclatures_Categories_List_Viewer extends Viewer {
    public $template = 'categories_list.html';
    public $filters = array();

    public function prepare() {
        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_js', $this->theme->scriptsUrl . '/special/NzGrid.js');
            $this->registry->push('custom_js', $this->theme->scriptsUrl . '/special/NzoomGridDataAdaptor.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');
            $this->registry->push('custom_js', "{$this->viewJsUrl}list.js");

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
            //$this->data['dont_wrap_content'] = true;
        } else {
            $prepareModels = $this->registry->get('prepareModels');
            $this->registry->set('prepareModels', true, true);

            $categories = Nomenclatures_Categories::getTree($this->registry, array('where' => array('c.deleted IS NOT NULL')));

            $this->registry->set('prepareModels', $prepareModels, true);

            $this->data['categories'] = array_values($categories);
            $this->registry['include_tree'] = true;
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures_categories');
        $this->data['title'] = $title;
    }
}

?>
