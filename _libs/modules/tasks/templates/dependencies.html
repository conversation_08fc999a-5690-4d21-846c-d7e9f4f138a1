<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="dependencies" id="dependencies" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$task->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$task->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
  <tr>
    <td class="nopadding">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {include file=`$templatesDir`_info_header.html}
      </table>
    </td>
  </tr>

  <!-- Task dependencies -->
  <tr>
    <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="tasks_dependencies_switch"><div class="switch_{if $smarty.cookies.tasks_dependencies_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$title|escape}</div></td>
  </tr>
  <tr id="tasks_dependencies"{if $smarty.cookies.tasks_dependencies_box eq 'off'} style="display: none"{/if}>
    <td class="relatives_cell_style">
      {if !empty($parents_tree)}
        <h1><img src="{$theme->imagesUrl}dependencies.png" width="16" height="16" border="0" title="{#tasks_tree#|escape}" alt="{#tasks_tree#|escape}" class="help" /> {#tasks_tree_parents_dependencies#|escape}</h1>
        {include file=`$templatesDir`_tree.html list=$parents_tree sfx='parents' objects=$tasks}
      {/if}

      {if !empty($children_tree)}
        <h1><img src="{$theme->imagesUrl}dependencies.png" width="16" height="16" border="0" title="{#tasks_tree#|escape}" alt="{#tasks_tree#|escape}" class="help" /> {#tasks_tree_children_dependencies#|escape}</h1>
        {include file=`$templatesDir`_tree.html list=$children_tree sfx='children' objects=$tasks}
      {/if}

      <br />
      <a name="error_referer"></a><h3>{help label='dependencies_action'}</h3>
      <div id="tasks_referers">
        <div id="tasks_toggleCheckboxes" style="width: 300px; display: {if is_array($task->get('tasks_dependencies')) && @count($task->get('tasks_dependencies')) gt 4}block{else}none{/if};">
          <span onclick="toggleCheckboxes(this, 'tasks_referers', true)" class="pointer">{#check_all#|escape}</span> |
          <span onclick="toggleCheckboxes(this, 'tasks_referers', false)" class="pointer">{#check_none#|escape}</span>
        </div>
{if $task->get('tasks_dependencies')}
{foreach name='i' from=$task->get('tasks_dependencies') key='ref_id' item='ref'}
        <input type="checkbox" name="tasks_referers[]" id="tasks_ref{$ref.id}" value="{$ref.id}" checked="checked" />
        <select class="selbox" style="width: 400px;" name="origin[{$ref.id}]" id="origin_{$ref.id}">
          <option value="F2S"{if $ref.origin eq 'F2S'} selected="selected"{/if}>{#tasks_F2S#}</option>
          <option value="F2F"{if $ref.origin eq 'F2F'} selected="selected"{/if}>{#tasks_F2F#}</option>
          <option value="S2S"{if $ref.origin eq 'S2S'} selected="selected"{/if}>{#tasks_S2S#}</option>
          <option value="S2F"{if $ref.origin eq 'S2F'} selected="selected"{/if}>{#tasks_S2F#}</option>
        </select>
        {#tasks_chosen_task#|escape}: <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$ref_id}" target="_blank">{$ref.name|escape}</a>
        <br />
{/foreach}
{/if}
      </div>
      <br />
      <button type="button" name="filterButton" class="button" onclick="var popUrl='{$smarty.server.SCRIPT_NAME}?{$module_param}=tasks&amp;tasks=filter&amp;open_from=tasks&amp;model_id={$task->get('id')}'; pop(popUrl, 820, 580);">{#select#|escape}...</button>
    </td>
  </tr>

  <tr>
    <td class="relatives_button_cell">
      <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
