<?php

class Tasks_Relatives_Viewer extends Viewer {
    public $template = 'relatives.html';

    public function prepare() {
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare relatives tree
        $this->data['relatives_tree'] = $this->registry['relatives_tree'];

        // display referers index as a dropdown menu
        $referers_index_options = array();
        foreach(array('relatives_tree', 'tasks_referers', 'documents_referers') as $ref) {
            $label = '';
            if ($ref == 'relatives_tree') {
                $label = sprintf($this->i18n('tasks_relatives'), $this->model->getModelTypeName());
            } elseif ($ref == 'tasks_referers') {
                $label = $this->i18n('tasks_referer');
            } elseif ($ref == 'documents_referers') {
                $label = $this->i18n('tasks_documents_referer');
            }
            $referers_index_options[] =
                array(
                    'url'   => '#' . $this->module . '_' . $ref . '_index',
                    'name'  => $this->module . '_' . $ref . '_index',
                    'label' => $label,
                    'help'  => $label,
                    'img'   => 'arrow_bottom'
                );
        }
        $this->data['vars_index'] = array(
            'label' => $this->i18n('tasks_referers_index'),
            'name' => 'referers_index',
            //'no_tab' => true,
            'no_img' => true,
            'options' => $referers_index_options
        );

        if ($this->theme->isModern()) {
            $this->data['dont_wrap_content'] = true;
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        // TODO: find a consensus for the title!
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('tasks_relatives'), $this->model->getModelTypeName());
        }
        $this->data['title'] = $title;
    }
}

?>
