<?php

class Tasks_Sections_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Tasks_Section';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Tasks_Sections';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit');

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate');

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'search':
            $this->_search();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $tasks_section = Tasks_Sections::buildModel($this->registry);
            if ($tasks_section->save()) {
                if (!$tasks_section->imageCreate()) {
                    $this->registry['messages']->setWarning($this->i18n('warning_tasks_sections_add_success'),'',-1);
                }
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_tasks_sections_add_success'),'',-2);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_tasks_sections_add_failed'),'',-1);
            }
        } else {
            //create empty user model
            $tasks_section = Tasks_Sections::buildModel($this->registry);
        }

        if (!empty($tasks_section)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('tasks_section', $tasks_section->sanitize());
        }

        return true;
    }


    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $tasks_section = Tasks_Sections::buildModel($this->registry);
            if ($tasks_section->save()) {
                if ($request->get('icon_delete')) {
                    $tasks_section->updateTableIcon($id, '', $request->get('icon_name'));
                }
                if (!$tasks_section->imageCreate()) {
                    $this->registry['messages']->setWarning($this->i18n('warning_tasks_sections_edit_success'),'',-1);
                }
                //show message 'message_tasks_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_tasks_sections_edit_success'),'',-2);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_tasks_sections_edit_failed'),'',-1);

                //register the model, with all the posted details
                $this->registry->set('tasks_section', $tasks_section);
            }

        } elseif ($id > 0) {

            // the model from the DB
            $filters = array('where' => array('ts.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $tasks_section = Tasks_Sections::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($tasks_section);
        }

        if (!empty($tasks_section)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('tasks_section')) {
                $this->registry->set('tasks_section',  $tasks_section->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task_section'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('ts.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $tasks_section = Tasks_Sections::searchOne($this->registry, $filters);

        //check access and ownership of the model
        $this->checkAccessOwnership($tasks_section);

        if (!empty($tasks_section)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('tasks_section')) {
                $this->registry->set('tasks_section',  $tasks_section->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task_section'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $tasks_section = Tasks_Sections::buildModel($this->registry);
            if ($tasks_section->save()) {
                if ($request->get('icon_delete')) {
                    $tasks_section->updateTableIcon($id, '', $request->get('icon_name'));
                }
                if (!$tasks_section->imageCreate()) {
                    $this->registry['messages']->setWarning($this->i18n('warning_tasks_sections_edit_success'),'',-1);
                }
                //show message 'message_tasks_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_tasks_sections_translate_success'),'',-2);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_tasks_sections_translate_failed'),'',-1);

                //register the model, with all the posted details
                $this->registry->set('tasks_section', $tasks_section);
            }

        } elseif ($id > 0) {

            // the model from the DB
            $filters = array('where' => array('ts.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $tasks_section = Tasks_Sections::searchOne($this->registry, $filters);
            if (empty($tasks_section)) {
                //if not translated get existing model
                $filters = array('where' => array('ts.id = ' . $id));
                $tasks_section = Tasks_Sections::searchOne($this->registry, $filters);
                if (!empty($tasks_section)) {
                    $tasks_section->set('model_lang', $request->get('model_lang'), true);
                }
            }

            //check access and ownership of the model
            $this->checkAccessOwnership($tasks_section);
        }

        if (!empty($tasks_section)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('tasks_section')) {
                $this->registry->set('tasks_section',  $tasks_section->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task_section'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Tasks_Sections::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete items
        $result = Tasks_Sections::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Tasks_Sections::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Tasks_Sections::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

}

?>
