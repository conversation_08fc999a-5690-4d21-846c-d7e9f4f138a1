{if $object->checkPermissions('view')}
  {capture assign="tootipId"}tree-info-{$object->get('id')}-{$level}{/capture}
  <span class="nz--bullet material-icons nz-tooltip-trigger nz-tooltip-autoinit nz-tooltip-helpcursor"
        data-tooltip-position="panel: bottom left at: top center"
        data-tooltip-title="{$system_info|escape}"
        data-tooltip-element="#{$tootipId}"
    >{$theme->getIconForRecord('tasks')}</span>
  <div id="{$tootipId}" class="nz-tooltip-content nz-tooltip-notch__bottom-left">
    {include file="`$templatesDir`_info.html" task=$object}
  </div>

  {capture assign="statusTootipId"}tree-info-status-{$object->get('id')}-{$level}{/capture}
  <div id="{$statusTootipId}" class="nz-tooltip-content nz-tooltip-notch__bottom-left">
    {capture assign="status_label_name"}help_tasks_status_{$object->get('status')}{/capture}
    {$smarty.config.$status_label_name}{if $object->get('substatus')} <br /> <strong>{$object->get('substatus_name')}</strong>{/if}
  </div>

  {if $object->get('icon_name')}
    <img src="{$smarty.const.PH_TASKS_STATUSES_URL}{$object->get('icon_name')}"
         class="t_info_image nz-tooltip-trigger nz-tooltip-autoinit nz-tooltip-helpcursor"
         alt="{$object->get('substatus_name')}"
         data-tooltip-position="panel: bottom left at: top center"
         data-tooltip-element="#{$statusTootipId}"
    />
  {else}
    {capture assign="status_icon"}tasks_statuses_{$object->get('status')}{/capture}
    <span
        class="material-icons nz-status-icon nz-status__{$object->get('status')} nz-tooltip-trigger nz-tooltip-autoinit nz-tooltip-helpcursor"
        data-tooltip-position="panel: bottom left at: top center"
        data-tooltip-element="#{$statusTootipId}"
      >{$theme->getIconForRecord($status_icon)}</span>
  {/if}

  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$object->get('id')}">
  {if $object->get(id) eq $smarty.get.relatives}
    <span class="tree_root {if $object->get('deleted_by')} deleted{/if}">
      {$object->get('full_num')|escape}
      <span {if $object->get('deleted_by')} class="deleted"{/if}>{$object->get('name')|escape}</span>
    </span>
  {else}
      {$object->get('full_num')|escape}
      <span {if $object->get('deleted_by')} class="deleted"{/if}>{$object->get('name')|escape}</span>
  {/if}
  </a>

  {if $action eq 'dependencies' && $node.origin}
    {capture assign="dependence"}tasks_{$node.origin}{/capture}
    <span class="legend">({$smarty.config.$dependence})</span>
  {/if}
{else}
  {$object->get('name')|escape}
{/if}

