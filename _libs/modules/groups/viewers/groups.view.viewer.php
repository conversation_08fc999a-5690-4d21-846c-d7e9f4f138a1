<?php

class Groups_View_Viewer extends Viewer {
    public $template = 'view.html';

    public function prepare() {
        $this->model = $this->registry['group'];
        $this->data['group'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare parents
        $this->data['groups_parents'] = Groups::getTreeParents($this->registry, $this->model->get('id'));

    }

    public function prepareTitleBar() {
        $title = $this->i18n('groups');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('groups_view');
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s&amp;model_lang=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'), $this->model->get('model_lang'));

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
