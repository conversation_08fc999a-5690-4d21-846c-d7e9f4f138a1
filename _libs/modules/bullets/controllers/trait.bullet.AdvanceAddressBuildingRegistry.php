<?php

include_once PH_MODULES_DIR . 'automations/plugins/advance_address/controllers/advance_address.automations.controller.php';

/**
 * Bullet trait for filling residential building nomenclature
 */
trait AdvanceAddressBuildingRegistry
{
    /**
     * Database instance
     *
     * @var ADODB_mysqli
     */
    private ADODB_mysqli $db;

    /**
     * Building registry bullet.
     *
     *
     * @return bool
     * @throws Exception
     */
    public function fillBuildingRegistry(): bool
    {
        set_time_limit(0);
        ini_set('memory_limit', '4095M');

        $this->updateBulletStatus(__FUNCTION__, 1);
        try {
            $buildingTypesSettings = preg_split('/\s*,\s*/', trim($this->settings['building_types']));
            $buildingTypes = is_array($buildingTypesSettings) ? $buildingTypesSettings : [$buildingTypesSettings];
            $reportsPerPage = (int) $this->settings['reports_per_page'] ?? 100;
            $this->processAllReports($buildingTypes, $reportsPerPage, __METHOD__);
        } catch (Exception $e) {
            General::log($this->registry, __METHOD__, $e->getMessage());
        } finally {
            $this->updateBulletStatus(__FUNCTION__, 0);
        }

        return true;
    }

    /**
     * Process all reports paginated from settings.
     * If reports per page is not set, default are 100
     *
     * @throws Exception
     */
    private function processAllReports(array $objectType, int $itemsPerPage, string $method): bool
    {
        $worker = $this->getWorker();
        $limitRecords = (int) isset($this->settings['limit_reports']) ? $this->settings['limit_reports'] : 0;
        $filter = [
            'where' => [
                "d.type = '2'",
                "d.active = '1'",
                "d.status = 'locked'",
                "d.substatus = '6'",
                "a__system_made_by = '1'",
            ],
            'page' => 0,
            'itemsPerPage' => $itemsPerPage,
        ];
        if ($limitRecords !== 0) {
            $filter['limit'] = $limitRecords;
        }

        $counter = 0;
        do {
            $filter['page']++;
            $reportsType2 = Documents::search($this->registry, $filter);

            $this->processReports($reportsType2, $objectType, $worker, $method);

            if (($limitRecords !== 0) && (isset($filter['limit']) && $counter === $filter['limit'])) {
                break;
            }
            $counter++;
        } while (count($reportsType2) < $itemsPerPage);

        unset($objectType);
        unset($filter);
        unset($reportsType2);

        return true;
    }

    /**
     * Process paginated reports
     *
     * @param array                                  $reportsType2
     * @param array                                  $objectType
     * @param Advance_Address_Automations_Controller $worker
     * @param string                                 $method
     *
     * @return bool
     * @throws Exception
     */
    private function processReports(
        array $reportsType2,
        array $objectType,
        Advance_Address_Automations_Controller $worker,
        string $method
    ): bool {
        $processedReports = [];

        foreach ($reportsType2 as $reportType2) {
            try {
                if ($this->processSingleReport($reportType2, $objectType, $worker)) {
                    $processedReports[] = $reportType2->get('id');
                }
            } catch (Exception $e) {
                General::log(
                    $this->registry,
                    $method,
                    "Failed processing report {$reportType2->get('id')}. Processed reports: " . implode(
                        ', ',
                        $processedReports
                    )
                );
                throw new Exception("Failed process single report {$reportType2->get('id')}", 0, $e);
            }
        }
        General::log($this->registry, $method, 'Processed reports: ' . implode(', ', $processedReports));
        return true;
    }


    /**
     * Process single report
     *
     * @param Document                               $report
     * @param array                                  $objectType
     * @param Advance_Address_Automations_Controller $worker
     *
     * @return bool
     * @throws Exception
     */
    private function processSingleReport(
        Document $report,
        array $objectType,
        Advance_Address_Automations_Controller $worker
    ): bool {
        $db = $this->getDb();
        $reportType2Id = $report->get('id');
        $report13Id = $worker->getReport13Id($reportType2Id);

        $buildings = $worker->getBuildingsFromReport($report13Id, $objectType);
        $missingBuildings = $worker->getBuildingsNotInRegistry($buildings, $objectType);

        if (empty($missingBuildings)) {
            return false;
        }
        $report13VarsAssoc = $worker->getReport13Vars($report13Id);
        $db->StartTrans();
        if (!$worker->createBuildingNomFromDoc($missingBuildings, $report13Id, $report13VarsAssoc, $report)) {
            $db->FailTrans();
        }

        if (!$db->CompleteTrans()) {
            throw new Exception("Transaction failed for report {$reportType2Id} !");
        }
        unset($db);
        unset($buildings);
        unset($missingBuildings);
        unset($report13VarsAssoc);
        unset($worker);

        return true;
    }


    /**
     * Get ResidentialBuildingRegistryTrait functionalities by calling automation controller
     *
     * @throws Exception
     */
    private function getWorker(): Advance_Address_Automations_Controller
    {
        return new Advance_Address_Automations_Controller($this->registry);
    }

    /**
     * Get database instance
     *
     * @return ADODB_mysqli|array|mixed|null
     */
    private function getDb(): ADODB_mysqli
    {
        return $this->db ?? ($this->db = $this->registry['db']);
    }
}
