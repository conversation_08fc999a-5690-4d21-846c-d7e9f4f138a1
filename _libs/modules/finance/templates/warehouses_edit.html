<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$warehouse->get('model_lang')|default:$lang}" />
<input type="hidden" name="id" id="id" value="{$warehouse->get('id')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='warehouses_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox" name="name" id="name" value="{$warehouse->get('name')|escape}" title="{#finance_warehouses_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_code"><label for="code"{if $messages->getErrors('code')} class="error"{/if}>{help label='warehouses_code'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox" name="code" id="code" value="{$warehouse->get('code')|escape}" title="{#finance_warehouses_code#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_company"><label for="company"{if $messages->getErrors('company')} class="error"{/if}>{help label='warehouses_company'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {$warehouse->get('company_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_office"><label for="office"{if $messages->getErrors('office')} class="error"{/if}>{help label='warehouses_office'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {include file=`$theme->templatesDir`input_dropdown.html
                     name='office'
                     options=$offices
                     value=$warehouse->get('office')
                     required=1
                     really_required=1
                     width=200
                     standalone=true
                     label=#finance_warehouses_office#
            }
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_location"><label for="location"{if $messages->getErrors('location')} class="error"{/if}>{help label='warehouses_location'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <input type="text" class="txtbox" name="location" id="location" value="{$warehouse->get('location')|escape}" title="{#finance_warehouses_location#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='warehouses_description'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox" name="description" id="description" title="{#finance_warehouses_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$warehouse->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_employees"><label for="employees"{if $messages->getErrors('employees')} class="error"{/if}>{help label='warehouses_employees'}</label></a></td>
          <td colspan="2" class="nopadding">
            <table id="employees_container" cellspacing="0" cellpadding="2" border="0" class="floatl">
              <tr style="display: none;">
                <td colspan="2"></td>
              </tr>
              {assign var='employees' value=$warehouse->getEmployees()}
              {if !$employees}
                {array assign='employees' eval='array(array(\'id\' => \'\', \'code\' => \'\', \'name\' => \'\'))'}
              {/if}
              {foreach from=$employees key='j' item='employee' name='i'} 
              <tr id="employees_container_{$smarty.foreach.i.iteration}">
                <td style="text-align: center; padding-right: 3px;">
                  <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if $employees|@count le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('employees_container','{$smarty.foreach.i.iteration}'); {rdelim}, this);" />
                  <a href="javascript: disableField('employees_container','{$smarty.foreach.i.iteration}');" style="display: none;">{$smarty.foreach.i.iteration}</a>
                </td>
                <td nowrap="nowrap">
                  {include file=`$theme->templatesDir`input_autocompleter.html
                           name='employees'
                           index=$smarty.foreach.i.iteration
                           autocomplete=$autocomplete_employee
                           autocomplete_var_type='basic'
                           value=$employee.id
                           value_code=$employee.code
                           value_name=$employee.name
                           width=244
                           standalone=true
                           label=#employee#
                  }
                </td>
              </tr>
              {/foreach}
            </table>
            <div class="floatl" style="padding: 5px; width: 30px;">
              <div class="t_buttons">
                <div id="employees_container_plusButton" onclick="addField('employees_container', false, false, true);" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                <div id="employees_container_minusButton"{if $employees|@count le 1} class="disabled"{/if} onclick="removeField('employees_container');" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#edit#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$warehouse exclude='is_portal'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
