        {counter start=0 print=false}
          <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <tr>
              <td class="t_caption3 t_border" nowrap="nowrap" width="10"><div class="t_caption3_title">{#num#}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='model_to_distribute'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='model_amount'}</div></td>
              {if $selected_tab eq 'incomes_reason'}
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='model_invoice_amount'}</div></td>
              {/if}
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='model_distributed_amount'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='model_distributed_amount_from_document'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='model_remaining_distributed_amount'}</div></td>
              <td class="t_caption3" nowrap="nowrap"><div class="t_caption3_title">{help label='model_distribute'}</div></td>
            </tr>
            {capture assign='paid_var'}paid_amount_Finance_Expenses_Reason_{$finance_expenses_reason->get('id')}{/capture}
            {capture assign=items}{$selected_tab}s{/capture}
            {assign var=items value=$finance_expenses_reason->get($items)}
            {foreach name='i' from=$items item='model'}
            {strip}
            {capture assign='model_info'}
              {if $model->get('name')}<strong>{#finance_expenses_reasons_name#|escape}:</strong> {$model->get('name')|escape}<br />{/if}
              <strong>{#added#|escape}:</strong> {$model->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$model->get('added_by_name')|escape}<br />
              <strong>{#modified#|escape}:</strong> {$model->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$model->get('modified_by_name')|escape}<br />
            {/capture}
            {/strip}
            {if $model->modelName eq 'Finance_Expenses_Reason'}
              {assign var=controller_str value="expenses_reasons&amp;expenses_reasons"}
            {else}
              {assign var=controller_str value="incomes_reasons&amp;incomes_reasons"}
            {/if}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border hright">{$smarty.foreach.i.iteration} </td>
              <td class="t_border" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller={$controller_str}=view&amp;view={$model->get('id')}"{if @in_array($model->get('id'),$finance_expenses_reason->get('repayment_plan_documents'))} style="color:red"{assign var='show_repayment_plans' value=1}{/if} {help label_content=$model->get('type_name') text_content=$model_info popup_only=1}>{$model->get('num')}{if $model->get('invoice_num')} ({$model->get('invoice_num')}){/if}</a></td>
              <td class="t_border hright" nowrap="nowrap">{$model->get('total_with_vat')} {$finance_expenses_reason->get('currency')|escape}</td>
              {if $selected_tab eq 'incomes_reason'}
              <td class="t_border hright" nowrap="nowrap">{$model->getInvoicedAmount()|string_format:"%.2f"} {$finance_expenses_reason->get('currency')|escape}</td>
              {/if}
              <td class="t_border hright" nowrap="nowrap">{$model->get('paid_amount')|string_format:"%.2f"} {$finance_expenses_reason->get('currency')|escape}</td>
              <td class="t_border hright" nowrap="nowrap">{$model->get($paid_var)|string_format:"%.2f"} {$finance_expenses_reason->get('currency')|escape}
              {if $model->get($paid_var) gt 0}<img onclick="return confirmAction('delete', function() {ldelim} updateBalance('', 'payment', '{$submitLink}&amp;empty={$model->get('id')}'); {rdelim}, this, '{#finance_payments_confirm_empty#|escape:'quotes'|escape}');" src="{$theme->imagesUrl}small/delete2.png" width="10" height="10" border="0" alt="{#delete#|escape}" title="{#delete#|escape}" class="pointer" /> 
              {/if}
              </td>
              <td class="t_border hright" nowrap="nowrap">
              {if $selected_tab eq 'incomes_reason'}
                {math equation="x - y - z" x=$model->get('total_with_vat')|default:0|string_format:"%.2F" y=$model->get('paid_amount') z=$model->get('invoices_amount')|default:0|string_format:"%.2F" format="%.2F"} {$finance_expenses_reason->get('currency')|escape}
              {else}
                {math equation="x - y" x=$model->get('total_with_vat')|default:0|string_format:"%.2F" y=$model->get('paid_amount')|default:0 format="%.2F"} {$finance_expenses_reason->get('currency')|escape}
              {/if}
              </td>
              <td class="hright" nowrap="nowrap">
                {if $finance_expenses_reason->get('remaining_amount') && ($selected_tab eq 'incomes_reason' && ($model->get('paid_amount') + $model->get('invoices_amount')) ne $model->get('total_with_vat') || $selected_tab neq 'incomes_reason' && $model->get('paid_amount') ne $model->get('total_with_vat'))}
                <input type="text" class="pricebox small relatives_payments" name="relatives_payments[{$model->get('id')}]" id="relatives_payments_{counter}" value="{$model->get('suggest_amount')|string_format:'%.2f'}" onkeyup="recalculatePaymentAmount()" onkeypress="return changeKey(this, event, insertOnlyFloats);" /> {$finance_expenses_reason->get('currency')|escape}
                  {assign var=show_submit value=1}
                {else}
                  -
                {/if}
              </td>
            </tr>
            {foreachelse}
            <tr>
              <td colspan="7" class="error">{#finance_no_records_customer#}</td>
              </td>
            </tr>
            {/foreach}
            <tr>
              <td colspan="7">
              {if $show_submit}
                <button type="button" name="saveButton1" class="button" onclick="updateBalance(this.form, 'payment', '{$submitLink}');">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
              {/if}
              {if $show_repayment_plans}
                <span class="red">*{#help_finance_show_repayment_plans#}</span>
              {/if}
              </td>
            </tr>
          </table>
