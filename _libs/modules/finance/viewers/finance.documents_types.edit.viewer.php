<?php

class Finance_Documents_Types_Edit_Viewer extends Viewer {
    public $template = 'documents_types_edit.html';

    public function prepare() {
        $this->model = $this->registry['finance_documents_type'];
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        $modelName = $this->model->get('model');
        /**
         * @var $singleModel Finance_Incomes_Reason|Finance_Expenses_Reason|Finance_Annulment|Finance_Warehouses_Document
         */
        $singleModel = new $modelName($this->registry, array('type' => $this->model->get('id')));
        $layoutDetails = $singleModel->getLayoutsDetails();

        if ($this->model->get('model') != 'Finance_Invoices_Template') {
            $var_name = '';
            switch ($this->model->get('model')) {
                case 'Finance_Incomes_Reason':
                    $var_name = 'l.model8';
                    break;
                case 'Finance_Expenses_Reason':
                    $var_name = 'l.model9';
                    break;
                case 'Finance_Annulment':
                    $var_name = 'l.model10';
                    break;
                case 'Finance_Warehouses_Document':
                    $var_name = 'l.model14';
                    $this->data['default_date_of_payment_label'] = $layoutDetails['date']['name'];
                    $this->data['default_date_of_payment_help'] = $layoutDetails['date']['description'];
                    break;
            }
            // layouts search URL
            $this->data['layouts_search_url'] =
                sprintf('%s?%s=layouts&amp;layouts=search&amp;search_layout=1&amp;search_module=layouts&amp;search_controller=&amp;search_fields[0]=%s&amp;compare_options[0]=%%3D+\'%%25s\'&amp;values[0]=%s_%s&amp;sort[0]=l.place&amp;sort[1]=l.active&amp;order[0]=ASC&amp;order[1]=DESC&amp;display=100',
                        $_SERVER['PHP_SELF'], $this->registry['module_param'],
                        $var_name, $this->model->get('model'), $this->model->get('id'));
        }

        //get all available additional validate fields
        $settngs_section = strtolower($this->model->get('model')) . 's';
        $validate_fields = $this->registry['config']->getParamAsArray($settngs_section, 'additional_validate_fields_' . $this->model->get('id'));
        $validate_options = array();
        foreach ($validate_fields as $field) {
            $validate_options[] = array(
                'label' => $layoutDetails[$field]['name'],
                'option_value' => $field,
                'active_option' => true
            );
        }
        $this->data['validate_options'] = $validate_options;

        //get additional required fields and unique fields of current type
        if (!$this->model->get('validate') && !$this->registry['request']->isPost()) {
            $validate = $this->registry['config']->getParamAsArray($settngs_section, 'validate_' . $this->model->get('id'));
//            $validate_unique = array();
//            foreach ($validate as $idx => $field) {
//                if (strpos($field, 'unique_') === 0) {
//                    $validate_unique[] = $field;
//                    unset($validate[$idx]);
//                } elseif ($field == 'current_year') {
//                    $this->model->set('validate_unique_current_year', true, true);
//                    unset($validate[$idx]);
//                }
//            }
            $this->model->set('validate', array_values($validate), true);
//            $this->model->set('validate_unique', $validate_unique, true);
        }

        //prepare department tree
        require_once(PH_MODULES_DIR . 'departments/models/departments.factory.php');
        $this->data['departments'] = Departments::getTree($this->registry);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare sections
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_sections.factory.php';
        $lang_file = PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_documents_sections.ini';
        $this->loadCustomI18NFiles($lang_file);
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('fds.active = 1',
                                          'fds.model = "' . $this->model->get('model') . '" OR fds.model = ""'),
                         'sanitize' => true);
        $this->data['type_section'] = Finance_Documents_Sections::search($this->registry, $filters);

        //prepare patterns
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('p.model = \'' . $this->model->get('model') . '\'',
                                          'p.model_type = \'' . $this->model->get('id') . '\'',
                                          'p.active = 1',
                                          'p.list = 0'),
                         'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                         'sanitize' => true);
        $patterns = Patterns::search($this->registry, $filters);
        $this->data['patterns'] = $patterns;

        if (!in_array($this->model->get('model'), array('Finance_Invoices_Template', 'Finance_Annulment'))) {
            // assignment types for installation (hard-coded)
            $assignment_types = array('owner', 'responsible', 'observer', 'decision');
            $assignment_types_options = array();
            foreach($assignment_types as $a_type) {
                $assignment_types_options[] = array(
                    'label' => $this->i18n($this->module . '_assign_' . $a_type),
                    'option_value' => $a_type,
                    'active_option' => true);
            }
            $this->data['assignment_types_options'] = $assignment_types_options;

            //get assignment types of current type
            if (!$this->model->get('assignment_types') && !$this->registry['request']->isPost()) {
                $assignment_types = $this->registry['config']->getParamAsArray($this->module, 'assignment_types_' . $this->model->get('id'));
                $this->model->set('assignment_types', $assignment_types, true);
            }
        }

        //prepare customers types for related records
        require_once PH_MODULES_DIR . 'customers/models/customers.types.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('ct.active IS NOT NULL'),
                         'sort' => array('cti18n.name'),
                         'sanitize' => true);
        $cstm_types = Customers_Types::search($this->registry, $filters);
        $customers_types = array();
        foreach($cstm_types as $cstm_type) {
            $customers_types[] = array(
                'label' => $cstm_type->get('name'),
                'option_value' => $cstm_type->get('id'),
                'active_option' => $cstm_type->get('active'));
        }
        $this->data['customers_types'] = $customers_types;

        if ($this->model->get('default_customer')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $this->model->get('default_customer'),
                                              'c.deleted IS NOT NULL'));
            $customer = Customers::searchOne($this->registry, $filters);
            $customer_name = $customer->get('name');
            if (!$customer->get('is_company')) {
                $customer_name .= ' ' . $customer->get('lastname');
            }
            $this->model->set('customer_code', $customer->get('code'), true);
            $this->model->set('customer_name', $customer_name, true);
        }

        $this->data['finance_documents_type'] = $this->model;

        // get companies
        require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
        $filters = array('sanitize' => true);
        $companies = Finance_Companies::search($this->registry, $filters);
        foreach ($companies as $c) {
            // get only active VAT rates
            $c->set('vats', Finance_Companies::getVatRates($this->registry, $c->get('id'), true), true);
        }
        $this->data['companies'] = $companies;

        // get only active installation default VAT rates
        $this->data['default_vat_rates'] = Finance_Companies::getVatRates($this->registry, 0, true);

        // prepare available actions
        $available_actions = $this->model->get('available_actions');
        if (!is_array($available_actions)) {
            $available_actions = $available_actions ? explode(',', $available_actions) : array();
        }
        foreach ($available_actions as $idx => $act) {
            $label = preg_match('#^add#', $act) ? preg_replace('#^add#', 'finance_', $act) . ($act == 'addcorrect' ? '_document' : '') : $act;
            $available_actions[$idx] = array('name' => $act, 'label' => $this->i18n($label));
        }
        $this->model->set('available_actions', $available_actions, true);
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_documents_types_edit');
        $this->data['title'] = $title;
    }
}

?>
