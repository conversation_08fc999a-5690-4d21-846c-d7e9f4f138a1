finance_documents_types = Types of Financial Documents
finance_documents_types_name = Name
finance_documents_type = Type
finance_documents_types_name_plural = Name (for list and menu)
finance_documents_types_code = Code
finance_documents_types_description = Description
finance_documents_types_pattern = Pattern for print
finance_documents_types_email = E-mail template
finance_documents_types_all_companies = [For all companies]
finance_documents_types_department = Department
finance_documents_types_group = Group
finance_documents_types_default_user_group = [Default user group]
finance_documents_types_parent_group = [Group is inherited from parent record]
finance_documents_types_default_user_department = [Default user department]
finance_documents_types_parent_department = [Department is inherited from parent record]
finance_documents_types_fiscal_event_date = Default fiscal event date
finance_documents_types_date_of_payment = Default maturity
finance_documents_types_assignment_types = Assignment types
finance_documents_types_related_customers_types = Customers types for AC
finance_documents_types_available_actions = Order of buttons
finance_documents_types_default_customer = Default customer
finance_documents_types_company = Company
finance_documents_types_mandatory_num = Mandatory number

finance_documents_types_additional_settings_of_fields = Additional settings of fields
finance_documents_types_validate = Required fields
help_finance_documents_types_validate =

finance_documents_types_for_model = Model

finance_pattern = Print form
finance_documents_types_basic_settings = Basic settings
finance_documents_types_counter_settings = Counter settings
finance_documents_types_additional_settings_of_fields = Additional settings of fields
finance_documents_default_settings = Default settings

finance_documents_types_VAT = Fiscal behaviour
finance_documents_types_no_VAT = Document without VAT
finance_documents_types_cstm_VAT = VAT only if invoice is issued
finance_documents_types_include_VAT = VAT document
finance_documents_types_default_VAT = Default VAT rate
finance_documents_types_company_no_VAT = Not VAT registered
finance_documents_types_commodity = Stock
finance_documents_types_add_invoice = Invoice
finance_documents_types_add_proforma = Proforma Invoice
finance_documents_types_credit = Debit/Credit
finance_documents_types_payment_way = Way of payment
finance_documents_types_payments = Payments
finance_documents_types_nopay = Without payment

finance_documents_types_calculated_price = Price for calculations
finance_documents_types_gt2_price = Sell price
finance_documents_types_gt2_last_delivery_price = Delivery price

finance_documents_types_list = Finance documents types list
finance_documents_types_search = Finance documents search
finance_documents_types_add = Add new finance document type
finance_documents_types_edit = Edit finance document type
finance_documents_types_view = View finance document type
finance_documents_types_translate = Translate finance document type

finance_commodity_relation_none = No stock documents
finance_commodity_relation_incoming = Only receiving stock
finance_commodity_relation_outgoing = Only delivery of stock
finance_commodity_relation_both = Both stock receiving and delivering

finance_documents_layouts_num = Number
finance_documents_layouts_type = Type
finance_documents_layouts_name = About
finance_documents_layouts_customer_fer = Contractor
finance_documents_layouts_customer_fir = Contractor
finance_documents_layouts_project = Project
finance_documents_layouts_invoice_num = Invoice No.
finance_documents_layouts_company_data = Cashbox/Bank account
finance_documents_layouts_issue_date = Issue date
finance_documents_layouts_date_of_payment = Maturity
finance_documents_layouts_admit_VAT_credit = Admit VAT credit
finance_documents_layouts_allocated_status = Allocate costs to deliveries
finance_documents_layouts_invoice_status = Billing status
finance_documents_layouts_description = Description
finance_documents_layouts_department = Department
finance_documents_layouts_employee = Employee
finance_documents_types_batch_vars = Batch data vars

message_finance_documents_types_edit_success = Type edited successfully
message_finance_documents_types_add_success = Type added successfully
message_finance_documents_types_translate_success = Type translated successfully
message_finance_documents_displaysettings_save_success = The display settings of "%s" were successfully saved
message_finance_documents_printsettings_save_success = The print settings of "%s" were successfully saved
message_finance_documents_emailsettings_save_success = The e-mail settings of "%s" were successfully saved

error_finance_documents_types_edit_failed = Error while editing type
error_finance_documents_types_add_failed = Error while adding type
error_finance_documents_types_translate_failed = Error while translating type
error_finance_documents_displaysettings_save_failed = Error saving the display settings of "%s"
error_finance_documents_printsettings_save_failed = Error saving the print settings of "%s"
error_finance_documents_emailsettings_save_failed = Error saving the e-mail settings of "%s"

error_no_such_finance_document_type = No such type
error_no_name_specified = Please, specify name!
error_no_typename_plural_specified = Please, specify name (for list and menu)!
error_no_code = Please enter code!
error_code_not_unique = Code already in use! Please enter new code!

#Help SECTION for label info 

help_finance_documents_types_assignment_types = 
help_finance_documents_types_related_customers_types = 
help_finance_documents_types_available_actions = Buttons for actions with finished record can be moved by drag and drop to the desired position.<br /><br />Users will see only buttons available according to their permissions, settings in document type and previously issued documents.
help_finance_documents_types_commodity = Setting specifies whether warehouse documents can be issued from document and in which direction.
help_finance_documents_types_add_invoice = Setting specifies whether invoices can be issued from document.
help_finance_documents_types_add_proforma = Setting specifies whether proforma invoices can be issued from document.
help_finance_documents_types_credit = Setting specifies whether correction can be issued from document and whether credit and debit notes can be issued from invoices from document.
help_finance_documents_types_payment_way = 
help_finance_documents_types_payments = Setting specifies whether payments can be issued from document.
help_finance_documents_types_nopay = Setting specifies whether payment status of document can be set to "Without payment".

finance_parent_document_settings = Parent document's settings will be used
finance_expenses_invoice_commodity_exception = If the invoice is derivative document, the settings of the parent will be used!
