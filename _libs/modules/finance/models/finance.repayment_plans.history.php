<?php

class Finance_Repayment_Plans_History extends History {

    /**
     * prepare history data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, $params = array()) {
        //set action type
        self::$action_type = $params['action_type'];

        $repayment_plan = $params['new_model']->sanitize();

        switch (self::$action_type) {
            case 'add':
            case 'edit':
            default:
                $data = array (
                    'amount' => $repayment_plan->get('amount'),
                    'lang' => $repayment_plan->get('model_lang'),
                    'currency' => $repayment_plan->get('currency')
                );
                break;
        }

        self::$data = $data;

        return true;
    }

    /**
     * prepare history data for view
     *
     * @return bool
     */
    public static function prepareGetData(&$registry, array $records, array $params) {
        foreach ($records as $k => $record) {
            $i18n_param = 'finance_repayment_plans_log_';
            if ($record['user_id'] == PH_AUTOMATION_USER) {
                $i18n_param .= 'system_';
            }
            $i18n_param .= $record['action_type'];

            $log_text = $registry['translater']->translate($i18n_param);

            $date = General::strftime($registry['translater']->translate('date_short'), strtotime($record['h_date']));

            $records[$k]['action_type_name'] = $registry['translater']->translate('finance_repayment_plans_logtype_' . $record['action_type']);

            //switch for additional options
            switch ($record['action_type']) {
            case 'add':
            case 'edit':
            case 'payment':
            default:
                //default options
                $records[$k]['data'] = sprintf($log_text,
                                                $record['user_name'],
                                                //$date,
                                                $record['data']['amount'],
                                                @$record['data']['currency']);
                break;
            }
        }

        return $records;
    }

    /**
     * save model history
     *
     * @return bool
     */
    public static function saveData(&$registry, $params) {
        //prepare the data before saving the history
        self::prepareData($registry, $params);

        //save history record and get the history id
        $history_id = parent::saveData($registry, $params);

    }

    /**
     * get model history
     *
     * @return array data
     */
    public static function getData(&$registry, $params) {
        if (!empty($params['paginate'])) {
            $params['sanitize'] = false;
            list($records, $pagination) = Model_Factory::paginatedSearch($registry, $params, 'History', 'getData');
        } else {
            $records = parent::getData($registry, $params);
        }
        $records = self::prepareGetData($registry, $records, $params);
        if (isset($pagination)) {
            $records = array($records, $pagination);
        }

        return $records;
    }
}

?>
