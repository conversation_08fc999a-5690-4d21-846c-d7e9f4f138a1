<?php

/**
 * Finance_Repayment_Plans_Substatuses model class
 */
class Finance_Repayment_Plans_Substatuses extends Model_Factory {
    public static $modelName = 'Finance_Repayment_Plan_Substatus';

    public static function search(&$registry, $filters = array()) {
        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#fds\.active#', $sort)) {
                $sort = 'ORDER BY fds.active DESC, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }
        } else {
            $sort = 'ORDER BY fds.active DESC, fds.status, fds.sequence ASC';
        }

        //select clause
        $sql['select'] = 'SELECT fds.*' . "\n";

        //from clause
        $sql['from']   = 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS fds' . "\n";

        $sql['where']  = $where . ' AND fds.lang="' . $model_lang . '"';

        //group clause
        $sql['order'] = $sort . "\n";

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        $substatuses = array('opened' => array(), 'locked' => array(), 'finished' => array());
        foreach ($records as $idx => $recs) {
            $substatuses[$recs['status']][$idx]['id'] = $recs['id'];
            $substatuses[$recs['status']][$idx]['name'] = $recs['name'];
            $substatuses[$recs['status']][$idx]['description'] = $recs['description'];
            $substatuses[$recs['status']][$idx]['sequence'] = $recs['sequence'];
            $substatuses[$recs['status']][$idx]['requires_comment'] = $recs['requires_comment'];
            $substatuses[$recs['status']][$idx]['parent_status'] = $recs['status'];
            $substatuses[$recs['status']][$idx]['active'] = $recs['active'] && !$recs['deleted_by'];
        }

        return $substatuses;
    }

    /**
     * construct the where clause
     */
    public static function constructWhere(&$registry, $filters = array()) {
        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            $current_user_id = $registry['currentUser']->get('id');
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#fds\.deleted#', $where)) {
            $where .= ' AND fds.deleted = 0';
        }
        if (!preg_match('#fds\.active#', $where)) {
            $where .= ' AND fds.active = 1';
        }

        return $where;
    }
}

?>
