</w:t></w:r></w:p></w:tc></w:tr>
<w:tr>
  <w:trPr>
    <w:tblHeader />
  </w:trPr>
  <w:tc>
    <w:tcPr>
      <w:tcW w:w="5400" w:type="dxa"/>
      <w:vMerge/>
      {if !empty($noBorders)}
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      {/if}
    </w:tcPr>
    <w:p>
      <w:pPr>
        <w:rPr>
          <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
          <w:sz w:val="18"/>
          <w:szCs w:val="18"/>
          <w:lang w:val="bg-BG"/>
        </w:rPr>
      </w:pPr>
    </w:p>
  </w:tc>
  <w:tc>
    <w:tcPr>
      <w:tcW w:w="5400" w:type="dxa"/>
      {if !empty($noBorders)}
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      {/if}
    </w:tcPr>
    <w:p>
      <w:pPr>
        <w:rPr>
          <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
          <w:sz w:val="18"/>
          <w:szCs w:val="18"/>
          <w:lang w:val="bg-BG"/>
        </w:rPr>
      </w:pPr>
    </w:p>
  </w:tc>
</w:tr>
<w:tr>
<w:trPr>
  <w:tblHeader />
</w:trPr>
<w:tc>
  <w:tcPr>
    <w:tcW w:w="5400" w:type="dxa"/>
    <w:vMerge/>
    {if !empty($noBorders)}
      <w:tcBorders>
        <w:top w:val="nil"/>
        <w:left w:val="nil"/>
        <w:bottom w:val="nil"/>
        <w:right w:val="nil"/>
        <w:insideH w:val="nil"/>
        <w:insideV w:val="nil"/>
      </w:tcBorders>
    {/if}
  </w:tcPr>
  <w:p>
    <w:pPr>
      <w:rPr>
        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
        <w:sz w:val="18"/>
        <w:szCs w:val="18"/>
        <w:lang w:val="bg-BG"/>
      </w:rPr>
    </w:pPr>
  </w:p>
</w:tc>
<w:tc>
  <w:tcPr>
    <w:tcW w:w="5400" w:type="dxa"/>
    {if !empty($noBorders)}
      <w:tcBorders>
        <w:top w:val="nil"/>
        <w:left w:val="nil"/>
        <w:bottom w:val="nil"/>
        <w:right w:val="nil"/>
        <w:insideH w:val="nil"/>
        <w:insideV w:val="nil"/>
      </w:tcBorders>
    {/if}
  </w:tcPr>
  <w:p>
    <w:pPr>
      <w:rPr>
        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
        <w:b/>
        <w:sz w:val="18"/>
        <w:szCs w:val="18"/>
        <w:lang w:val="bg-BG"/>
      </w:rPr>
    </w:pPr>
    <w:r>
      <w:rPr>
        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
        <w:b/>
        <w:sz w:val="18"/>
        <w:szCs w:val="18"/>
        <w:lang w:val="bg-BG"/>
      </w:rPr>
      <w:t>{$label}</w:t>
    </w:r>
  </w:p>
</w:tc>
</w:tr>
<w:tr>
<w:tc>
  <w:tcPr>
    <w:tcW w:w="5400" w:type="dxa"/>
    <w:vMerge/>
    {if !empty($noBorders)}
      <w:tcBorders>
        <w:top w:val="nil"/>
        <w:left w:val="nil"/>
        <w:bottom w:val="nil"/>
        <w:right w:val="nil"/>
        <w:insideH w:val="nil"/>
        <w:insideV w:val="nil"/>
      </w:tcBorders>
    {/if}
  </w:tcPr>
  <w:p>
    <w:pPr>
      <w:rPr>
        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
        <w:sz w:val="18"/>
        <w:szCs w:val="18"/>
        <w:lang w:val="bg-BG"/>
      </w:rPr>
    </w:pPr>
  </w:p>
</w:tc>
<w:tc>
  <w:tcPr>
    <w:tcW w:w="5400" w:type="dxa"/>
    {if !empty($noBorders)}
      <w:tcBorders>
        <w:top w:val="nil"/>
        <w:left w:val="nil"/>
        <w:bottom w:val="nil"/>
        <w:right w:val="nil"/>
        <w:insideH w:val="nil"/>
        <w:insideV w:val="nil"/>
      </w:tcBorders>
    {/if}
  </w:tcPr>
  <w:p>
    <w:pPr>
      <w:rPr>
        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
        <w:sz w:val="18"/>
        <w:szCs w:val="18"/>
        <w:lang w:val="bg-BG"/>
      </w:rPr>
    </w:pPr>
    <w:r>
      <w:rPr>
        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:cs="Arial"/>
        <w:sz w:val="18"/>
        <w:szCs w:val="18"/>
        <w:lang w:val="bg-BG"/>
      </w:rPr>
      <w:t>{$value}