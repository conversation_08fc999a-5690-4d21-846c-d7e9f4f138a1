<?php

/**
 *
 */
class DocxTableCell
{
    /**
     * @var bool
     */
    private $isBold = false;
    /**
     * @var bool
     */
    private $canSplit = false;
    /**
     * @var mixed|null
     */
    private $textalign;
    /**
     * @var string
     */
    private $verticalAlign = 'top';
    /**
     * @var string
     */
    private $content;
    /**
     * @var int
     */
    private $colspan;
    /**
     * @var null
     */
    private $fontsize = null;
    /**
     * @var null
     */
    private $backgroundColor = null;
    /**
     * @var null
     */
    private $rowspan = null;
    /**
     * @var int
     */
    private $rowspanTail = 0;
    /**
     * @var bool
     */
    private $isPain = false;
    /**
     * @var
     */
    private $width;


    /**
     * @param string $content
     * @param $colspan
     * @param $textAlign
     * @param $verticalAlign
     */
    public function __construct(string $content, $colspan = 1, $textAlign = null, $verticalAlign = 'center')
    {
        $this->content = $content;
        $this->textalign = $textAlign;
        $this->setVerticalAlign($verticalAlign);
        $this->colspan = (int)$colspan;
    }

    /**
     * @return string|null
     */
    public function getTextAlign(): ?string
    {
        return $this->textalign;
    }

    /**
     * @param  $verticalAlign
     * @return void
     */
    public function setVerticalAlign($verticalAlign): void
    {
        $this->verticalAlign = $verticalAlign;
    }

    /**
     * @return string
     */
    public function getVerticalAlign(): string
    {
        return $this->verticalAlign;
    }

    /**
     * @return string|null
     */
    public function getBackgroundColor(): ?string
    {
        return $this->backgroundColor;
    }

    /**
     * @param  $backgroundColor
     * @return void
     */
    public function setBackgroundColor($backgroundColor): void
    {
        $this->backgroundColor = $backgroundColor;
    }

    /**
     * @param  bool $isBold
     * @return void
     */
    public function setBold(bool $isBold = true): void
    {
        $this->isBold = $isBold;
    }

    /**
     * @return bool
     */
    public function isBold(): bool
    {
        return $this->isBold;
    }


    /**
     * @param  $canSplit
     * @return void
     */
    public function setCanSplit($canSplit = true): void
    {
        $this->canSplit = $canSplit;
    }

    /**
     * @return bool
     */
    public function canSplit(): bool
    {
        return $this->canSplit;
    }

    /**
     * @return int
     */
    public function getColspan(): int
    {
        return $this->colspan;
    }

    /**
     * @param  $rowspan
     * @return void
     */
    public function setRowspan($rowspan): void
    {
        $this->rowspan = $rowspan;
    }

    /**
     * @param  $rowspanTail
     * @return void
     */
    public function setRowspanTail($rowspanTail): void
    {
        $this->rowspanTail = $rowspanTail;
    }

    /**
     * @return int|null
     */
    public function getRowspan(): ?int
    {
        return $this->rowspan;
    }

    /**
     * @return void
     */
    public function getRowspanTailIncrement()
    {
        if ($this->rowspanTail > $this->rowspan) {
            $this->rowspanTail = 0;
        }
        $this->rowspanTail++;
    }

    /**
     * @return int|null
     */
    public function getRowspanTail(): ?int
    {
        return $this->rowspanTail;
    }

    /**
     * @param  float|null $fontsize
     * @return void
     */
    public function setFontsize(float $fontsize = null): void
    {
        $this->fontsize = $fontsize;
    }

    /**
     * @return float|null
     */
    public function getFontsize(): ?float
    {
        return $this->fontsize;
    }

    /**
     * @param  bool $isPlain
     * @return void
     */
    public function setIsPain(bool $isPlain): void
    {
        $this->isPain = $isPlain;
    }

    /**
     * @return bool
     */
    public function isPain(): bool
    {
        return $this->isPain;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @return int|null
     */
    public function getWidth(): ?int
    {
        return $this->width;
    }

    /**
     * @param  int|null $width
     * @return void
     */
    public function setWidth(?int $width): void
    {
        $this->width = $width;
    }
}
