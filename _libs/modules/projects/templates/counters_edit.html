<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="projects" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$projects_counter->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$projects_counter->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='counters_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox doubled" name="name" id="name" value="{$projects_counter->get('name')|escape}" title="{#projects_counters_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_next_number"><label for="next_number"{if $messages->getErrors('next_number')} class="error"{/if}>{help label='counters_next_number'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox small hright" name="next_number" id="next_number" value="{if $projects_counter->isDefined('next_number')}{$projects_counter->get('next_number')}{else}1{/if}" title="{#projects_counters_next_number#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_formula"><label for="formula"{if $messages->getErrors('formula')} class="error"{/if}>{help label='counters_formula'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {include file=`$templatesDir`_counters_formula.html}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='counters_description'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox doubled" name="description" id="description" title="{#projects_counters_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$projects_counter->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_count_projects"><label for="count_projects">{help label='counters_count_projects'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$projects_counter->get('count_projects')|escape|default:0}
            <input type="hidden" name="count_projects" id="count_projects" value="{$projects_counter->get('count_projects')|escape}" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_types_used"><label for="types_used">{help label='counters_types_used'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $projects_counter->get('types')}
              {foreach name='i' from=$projects_counter->get('types') item='type_name' key='type_id'}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=types&amp;types=view&amp;view={$type_id}" target="_blank">{$smarty.foreach.i.iteration}. {$type_name}</a><br />
              {/foreach}
            {else}
              <span class="error">{#error_no_types_used#|escape}</span>
            {/if}
          </td>
        </tr>
        <tr>
          <td colspan="3" class="t_caption3 strong">{#projects_counters_formula_legend#|escape}</td>
        </tr>
        <tr>
          <td colspan="3">
            {include file=`$templatesDir`_counters_formula_legend.html}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$projects_counter}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
