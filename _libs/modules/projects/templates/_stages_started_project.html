  <table cellpadding="0" cellspacing="0" class="t_table" width="100%" style="border-top: 1px solid #CCCCCC;">
    <!-- TITLE ROW -->
    <tr class="strong t_even1 t_even2">
      <td class="t_border" nowrap="nowrap" colspan="3"><div style="padding: 3px; width: 241px;">{#projects_stage#|escape}</div></td>
      <td class="t_border" nowrap="nowrap"><div style="padding: 3px; width: 150px;">{#projects_stage_current_responsible#|escape}</div></td>
      <td class="t_border" nowrap="nowrap"><div style="padding: 3px; width: 94px;">{#projects_stage_deadline#|escape}</div></td>
      <td class="t_border" nowrap="nowrap"><div style="padding: 3px; width: 150px;">{#projects_stage_started_at_from#|escape}</div></td>
      <td class="t_border" nowrap="nowrap"><div style="padding: 3px; width: 94px;">{#projects_stage_actual_durability_late#|escape}</div></td>
      <td class="t_border" nowrap="nowrap"><div style="padding: 3px; width: 150px;">{#projects_stage_ended_at_from#|escape}</div></td>
      <td><div style="padding: 3px; width: 23px;">&nbsp;</div></td>
    </tr>
    {counter name='row_counter' start=0 print=false}
    {foreach name='i' from=$project->get('stages_info') item='stages_status' key='status'}
      <tr>
        {capture assign='current_status_name'}projects_status_{$status}{/capture}
        <td class="t_caption3 strong" colspan="9"><label class="projects_status {$status}">{$smarty.config.$current_status_name}</label></td>
      </tr>
      {foreach from=$stages_status item='stage'}
        {if $project->get('stage') eq $stage->get('id')}
          {assign var='current_stage_model' value=$stage}
        {/if}
        <tr class="t_odd1 t_odd2 {if $stage->get('id') eq $project->get('stage')} current_project_stage{/if}">
          <td width="16">
            {if $stage->get('id') eq $project->get('stage')}
              <img src="{$theme->imagesUrl}small/project_current_stage.png" border="0" alt="{#expired#|escape}" />
            {elseif $stage->get('started_by')}
              <img src="{$theme->imagesUrl}small/project_stage_complete.png" border="0" alt="{#projects_stage_finished_stage#|escape}" />
            {else}
              &nbsp;
            {/if}
          </td>
          <td nowrap="nowrap" width="14">
            {counter name='row_counter'}
          </td>
          <td class="t_border" nowrap="nowrap" width="550" title="{$stage->get('name')|escape}">
            {$stage->get('name')|mb_truncate:85:"...":true|escape|default:'&nbsp;'}
          </td>
          <td class="t_border" nowrap="nowrap">
            {$stage->get('charge_in_name')|escape|default:'&nbsp;'}
          </td>

          <!-- PLANNED DEADLINE -->
          <td class="t_border" nowrap="nowrap">
            {if $stage->get('stage_deadline_type') eq $smarty.const.PH_DEADLINE_DATE}
              {$stage->get('stage_deadline')|date_format:#date_mid#|escape}
            {elseif $stage->get('stage_deadline_type') eq $smarty.const.PH_DEADLINE_DAYS}
              {if $stage->get('stage_limit_days')} {$stage->get('stage_limit_days')} {#projects_days#}{/if}{if $stage->get('stage_limit_hours')} {$stage->get('stage_limit_hours')} {#projects_hours#}{/if}
            {elseif $stage->get('stage_deadline_type') eq $smarty.const.PH_DEADLINE_NONE}
              <i>{#projects_no_deadline#|escape}</i>
            {/if}
          </td>

          <!-- STARTED BY -->
          <td class="t_border">
            {if $stage->get('started_by')}
              {$stage->get('started')|date_format:#date_mid#|escape} / <br />{$stage->get('started_by_name')|escape}
            {else}
              &nbsp;
            {/if}
          </td>

          <!-- DURABILITY -->
          <td class="t_border" nowrap="nowrap" align="right">
            {if $stage->get('finished_by')}
              {if $stage->get('actual_durability_days')}{$stage->get('actual_durability_days')} {#projects_days#} {/if}
              {if $stage->get('actual_durability_hours')}{$stage->get('actual_durability_hours')} {#projects_hours#} {/if}
              {if $stage->get('actual_durability_days') || $stage->get('actual_durability_hours')}/ {/if}
              <img src="{$theme->imagesUrl}small/{if $stage->get('late')}project_late{else}project_no_late{/if}.png"
                   border="0"
                   alt="{#comment#|escape}"
                   {if $stage->get('late')}
                     {popup text=#projects_over_deadline#|escape caption=#projects_late#}
                   {else}
                     {popup text=#projects_before_deadline#|escape caption=#projects_late#}
                   {/if}
              />
            {else}
              &nbsp;
            {/if}
          </td>

          <!-- FINISHED BY -->
          <td class="t_border">
            {if $stage->get('finished_by')}
              {$stage->get('finished')|date_format:#date_mid#|escape} / <br />{$stage->get('finished_by_name')|escape}
            {else}
              &nbsp;
            {/if}
          </td>

          <!-- COMMENTS & ACTIVITIES -->
          <td nowrap="nowrap" style="padding: 0;">
            {if $stage->get('stage_comment')}
              <img src="{$theme->imagesUrl}small/comments.png" style="float: left; padding-left: 5px;" border="0" alt="{#comment#|escape}" {popup text=$stage->get('comment_content')|escape caption=$stage->get('comment_subject')|escape} />
            {/if}
            {if $stage->get('finished_by') && $stage->get('activities')}
              {capture assign='projects_past_activities_info'}
                {assign var='show_activities_panel' value=0}
                <div id="activities_panel_{$stage->get('id')}" class="activities_panel">
                  <div class="t_caption3 strong" id="activities_panel_title_{$stage->get('id')}">
                    <div style="float: right" class="drag">
                      <img src="{$theme->imagesUrl}small/move.png" alt="" width="10" height="10" title="{#draggable#}" />
                      <img src="{$theme->imagesUrl}small/delete2.png" width="10" height="10" alt="" onclick="closeActivitiesPanel('activities_panel_{$stage->get('id')}')" title="{#close#}" style="cursor: default" />
                    </div>
                    <div class="drag">
                      <img src="{$theme->imagesUrl}small/info.png" style="padding-right: 1px;cursor: help" alt="" {help label_content=#projects_past_activities_info# text_content=#help_projects_past_activities_info#  popup_only=1} /> {#projects_past_activities_info#|escape}
                    </div>
                  </div>
                  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table activities" style="width: 500px;">
                    <tr class="activities_title_row">
                      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 10px">&nbsp;</div></td>
                      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 100px">{#projects_activity_name#|escape}</div></td>
                      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#projects_activity_finished_at_by#|escape}</div></td>
                    </tr>
                    {if $stage->get('activities')|@count > 0}
                      {assign var='show_activities_panel' value=1}
                    {/if}
                    {foreach from=$stage->get('activities') item='activity'}
                      {capture assign='counter_name'}finished_activities_info_{$stage->get('id')}{/capture}
                      <tr class="{cycle values='t_odd,t_even' name=$counter_name}">
                        <td class="t_border" style="text-align: center;">
                          {if $activity.finished_by!=0}
                            <img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="{#projects_activities_finished#|escape}" title="{#projects_activities_finished#|escape}" />
                          {else}
                            <img src="{$theme->imagesUrl}small/delete.png" border="0" alt="{#projects_activities_not_finished#|escape}" title="{#projects_activities_not_finished#|escape}" />
                          {/if}
                        </td>
                        <td class="t_border">{$activity.name|escape}</td>
                        <td>
                          {if $activity.finished_by!=0}
                            {$activity.finished|date_format:#date_mid#|escape} {#by#|escape} {$activity.finished_by_name|escape}
                          {else}
                            &nbsp;
                          {/if}
                        </td>
                      </tr>
                    {/foreach}
                  </table>
                </div>
                <script type="text/javascript">
                    new Draggable('activities_panel_{$stage->get('id')}', {ldelim}handle: 'activities_panel_title_{$stage->get('id')}'{rdelim});
                </script>
              {/capture}
              {if $show_activities_panel}
                <img src="{$theme->imagesUrl}small/stageactivities.png" alt="{#projects_past_activities_info#|escape}" border="0" onclick="showAvailableActivities('activities_panel_{$stage->get('id')}')" style="cursor:pointer; float: left; padding-left: 3px;" {popup text=#help_projects_past_activities_info#|escape caption=#projects_past_activities_info#|escape} />
                {$projects_past_activities_info}
              {else}
                &nbsp;
              {/if}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {/foreach}
    {/foreach}
  </table>

  {if !$project->get('finalized_project')}
    <!--  STAGE PROPERTIES -->
    <table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
      <tr>
        <td class="t_caption3">
          <div class="t_caption2_title" style="vertical-align: middle;">
            <div style="float: left;">{#projects_manipulate_phases_data#}</div>
            {if $current_stage_model && $current_stage_model->get('expired_time')}
              {capture assign='stage_expired'}
                {#projects_stage_expired_legend#|escape}: <strong>{$current_stage_model->get('expired_time')|date_format:#date_mid#|escape}</strong>
              {/capture}
              <div style="float: left; padding-left: 7px;"><img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="{#expired#|escape}" {popup text=$stage_expired|escape caption=#projects_stage_expired#|escape} /></div>
            {/if}
          </div>
        </td>
      </tr>

      <!-- STAGE DATA -->
      <tr>
        <td class="nopadding">
          {if $current_stage_model}
            <table cellspacing="0" cellpadding="0" border="0" width="100%">
              <tr>
                <td class="t_border nopadding vtop">

                  <!-- CURRENT STAGE INFO -->
                  <table cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td class="labelbox"><label>{#stages_phase#|escape}:</label></td>
                      <td>&nbsp;</td>
                      <td nowrap="nowrap">{$current_stage_model->get('name')|escape}</td>
                    </tr>

                    <tr>
                      <td class="labelbox"><label>{#projects_status#|escape}:</label></td>
                      <td>&nbsp;</td>
                      {capture assign='status_name'}projects_status_{$current_stage_model->get('status')}{/capture}
                      <td nowrap="nowrap">
                        <div class="projects_status {$current_stage_model->get('status')}" style="font-size: 10px; height: auto!important;">{$smarty.config.$status_name}</div>
                      </td>
                    </tr>

                    <tr>
                      <td class="labelbox"><label>{#stage_start#|escape}:</label></td>
                      <td>&nbsp;</td>
                      <td nowrap="nowrap">{$current_stage_model->get('started')|date_format:#date_mid#|escape}</td>
                    </tr>

                    <tr>
                      <td class="labelbox"><label>{#stages_time_limit#|escape}:</label></td>
                      <td>&nbsp;</td>
                      <td nowrap="nowrap">
                        {if $current_stage_model->get('stage_recalculated_deadline') ne '0000-00-00 00:00:00'}
                          {$current_stage_model->get('stage_recalculated_deadline')|date_format:#date_mid#|escape}
                        {else}
                          {if $current_stage_model->get('stage_deadline')}
                            {$current_stage_model->get('stage_deadline')|date_format:#date_mid#|escape}
                          {else}
                            <i>{#projects_no_deadline#|escape}</i>
                          {/if}
                        {/if}
                      </td>
                    </tr>

                    <tr>
                      <td class="labelbox" style="width: {$labelbox_width}px!important;"><a name="error_stage_date_finish"><label{if $messages->getErrors('stage_date_finish')} class="error"{/if}>{#projects_stage_date_finish#|escape}:</label></a></td>
                      <td class="required">{#required#}</td>
                      <td nowrap="nowrap" class="vtop">
                        {if $project->isDefined('stage_date_finish')}
                          {assign var='stage_date_finish' value=$project->get('stage_date_finish')}
                        {else}
                          {assign var='stage_date_finish' value=$smarty.now|date_format:'%Y-%m-%d %H:%M:00'}
                        {/if}
                        {include file="input_datetime.html"
                          standalone=true
                          name='stage_date_finish'
                          label=#projects_stage_date_finish#
                          help=#projects_stage_date_finish#
                          value=$stage_date_finish
                          width=200
                          disallow_date_after=true
                          hide_calendar_icon=true}
                      </td>
                    </tr>

                    {if !$current_stage_model->get('last_stage')}
                      <tr>
                        <td colspan="2" class="labelbox">
                          <label for="start_stage"{if $messages->getErrors('start_stage')} class="error"{/if}>{help label='start_next_stage'}</label>
                        </td>
                        <td class="vtop">
                          <input type="checkbox" name="start_stage" id="start_stage" value="1" onclick="showStagesDateStart(this)" title="{#projects_start_next_stage#|escape}" checked="checked" />
                        </td>
                      </tr>

                      <!-- Start stage date -->
                      <tr>
                        <td class="labelbox" style="width: {$labelbox_width}px!important;"><a name="error_start_stage_date"><label for="start_stage_date"{if $messages->getErrors('start_stage_date')} class="error"{/if}>{help label='start_next_stage_date'}</label></a></td>
                        <td class="required">{#required#}</td>
                        <td class="vtop">
                          {if $project->isDefined('start_stage_date')}
                            {assign var='start_stage_date' value=$project->get('start_stage_date')}
                          {else}
                            {assign var='start_stage_date' value=$smarty.now|date_format:'%Y-%m-%d %H:%M:00'}
                          {/if}
                          {include file="input_datetime.html"
                            standalone=true
                            name='start_stage_date'
                            label=#projects_start_next_stage_date#
                            help=#projects_start_next_stage_date#
                            value=$start_stage_date
                            width=200
                            disallow_date_after=true
                            hide_calendar_icon=true}
                        </td>
                      </tr>
                    {/if}

                    {if $current_stage_model->get('last_stage')}
                      <tr>
                        <td class="labelbox" style="width: {$labelbox_width}px!important;"><label>{#projects_finished_project#|escape}:</label></td>
                        <td>&nbsp;</td>
                        <td nowrap="nowrap" class="nopadding">
                          <input type="radio" name="finished" id="substatus_1" value="1"{if $project->get('finished') === '1' || $project->get('finished') !== '0'} checked="checked"{/if} /><label for="substatus_1" class="projects_status finished_success">{#projects_substatus_finished_success#}</label><br />
                          <input type="radio" name="finished" id="substatus_0" value="0"{if $project->get('finished') === '0'} checked="checked"{/if} /><label for="substatus_0" class="projects_status finished_failed">{#projects_substatus_finished_failed#}</label>
                        </td>
                      </tr>
                    {/if}
                  </table>
                </td>

                <!-- ACTIVITIES -->
                {if $current_stage_model->get('activities')}
                  <td class="t_border nopadding vtop" style="width: 300px;">
                    <table cellpadding="0" cellspacing="0" style="width: 100%;">
                      <tr>
                        <td colspan="2" style="color:#666666; font-size:11px;">{#projects_activity#|escape}:</td>
                      </tr>
                      {foreach from=$current_stage_model->get('activities') item='activity'}
                        <tr>
                          <td style="padding: 0; width: 20px;">
                            {if $activity.finished_by}
                              {capture assign='finished_stage_activity_info'}
                                <strong>{#projects_activity_name#|escape}:</strong> {$activity.name|escape}<br />
                                <strong>{#projects_activity_description#|escape}:</strong> {$activity.description|escape}<br />
                                <strong>{#projects_activity_finished_at#|escape}:</strong> {$activity.finished|date_format:#date_mid#|escape} {#by#|escape} {$activity.finished_by_name|escape}
                              {/capture}
                            {/if}
                            <input type="checkbox" name="activities[]" value="{$activity.id}" id="activity_{$activity.id}" title="{$activity.name|escape}"{if $activity.finished_by} disabled="disabled" checked="checked"{/if} />
                          </td>
                          <td style="padding: 0;">
                            <label for="activity_{$activity.id}"{if $activity.finished_by} style="color: #999999;" {popup text=$finished_stage_activity_info|escape caption=#system_info#|escape}{/if}>{$activity.name|escape|default:"&nbsp;"}</label>
                          </td>
                        </tr>
                      {/foreach}
                    </table>
                  </td>
                {/if}

                <!-- COMMENT -->
                <td class="nopadding vtop" style="width: 210px;">
                  <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                      <td class="labelbox"><a name="error_comment"><label for="comment"{if $messages->getErrors('comment')} class="error"{/if}>{help label='comment'}</label></a></td>
                    </tr>
                    <tr>
                      <td style="padding-top: 0;">
                        <textarea class="areabox" name="comment" id="comment" style="height: 110px;"></textarea>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- BUTTONS -->
              <tr>
                <td class="t_border" nowrap="nowrap">
                  {strip}
                    <input type="hidden" name="stages_action" id="stages_action" value="" />
                    <button class="button" type="submit" name="edit_stage" id="edit_stage" onclick="setHiddenInput('stages_action','complete_stage_info')">{#save#|escape}</button>
                    {if $project->checkPermissions('revision')}
                      <button class="button" name="revision" id="revision" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=revision&amp;revision={$project->get('id')}'; return false;">{#projects_stage_revision#|escape}</button>
                    {/if}
                  {/strip}
                </td>
                {if $current_stage_model->get('activities')}
                  <td class="t_border">
                    <button class="button" type="submit" name="complete_activities" id="complete_activities" onclick="setHiddenInput('stages_action','complete_stage_activities')">{#projects_stage_complete_activity#|escape}</button>
                  </td>
                {/if}
                <td>
                  &nbsp;
                </td>
              </tr>
            </table>
          {else}
            <!-- STAGE START INFO -->
            <table cellspacing="0" cellpadding="0" border="0" style="width: 455px;">
              <tr>
                <td colspan="2" class="labelbox">
                  <label for="start_stage"{if $messages->getErrors('start_stage')} class="error"{/if}>{help label='start_next_stage'}</label>
                </td>
                <td class="vtop" style="width: 205px;">
                  <input type="checkbox" name="start_stage" id="start_stage" value="1" onclick="showStagesDateStart(this)" title="{#projects_start_next_stage#|escape}"{if $project->isDefined('start_stage_date')} checked="checked"{/if} />
                </td>
              </tr>

              <!-- Start stage date -->
              <tr{if !$project->get('start_stage')} style="display: none;"{/if}>
                <td class="labelbox" style="width: 180px!important;"><a name="error_start_stage_date"><label for="start_stage_date"{if $messages->getErrors('start_stage_date')} class="error"{/if}>{help label='start_next_stage_date'}</label></a></td>
                <td class="required">{#required#}</td>
                <td class="vtop">
                  {if $project->isDefined('start_stage_date')}
                    {assign var='start_stage_date' value=$project->get('start_stage_date')}
                    {assign var='start_stage_date_disable' value=0}
                  {else}
                    {assign var='start_stage_date' value=$smarty.now|date_format:'%Y-%m-%d %H:%M:00'}
                    {assign var='start_stage_date_disable' value=1}
                  {/if}
                  {include file="input_datetime.html"
                    standalone=true
                    name='start_stage_date'
                    label=#projects_start_next_stage_date#
                    help=#projects_start_next_stage_date#
                    value=$start_stage_date
                    disabled=$start_stage_date_disable
                    width=200
                    disallow_date_after=true
                    hide_calendar_icon=true}
                </td>
              </tr>
              <tr>
                <td nowrap="nowrap" colspan="3">
                  {strip}
                    <input type="hidden" name="stages_action" id="stages_action" value="" />
                    <button class="button" type="submit" name="edit_stage" id="edit_stage" onclick="setHiddenInput('stages_action','complete_stage_info')">{#save#|escape}</button>
                    {if $project->checkPermissions('revision')}
                      <button class="button" name="revision" id="revision" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=revision&amp;revision={$project->get('id')}'; return false;">{#projects_stage_revision#|escape}</button>
                    {/if}
                  {/strip}
                </td>
              </tr>
            </table>
          {/if}
        </td>
      </tr>
    </table>
  {/if}