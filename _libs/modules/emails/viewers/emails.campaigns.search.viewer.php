<?php

class Emails_Campaigns_Search_Viewer extends Viewer {
    public $template = 'campaigns_search.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'emails.campaigns.factory.php';

        $filters = Emails_Campaigns::saveSearchParams($this->registry, array(), 'search_');
        list($emails_campaigns, $pagination) = Emails_Campaigns::pagedSearch($this->registry, $filters);

        $this->data['emails_campaigns'] = $emails_campaigns;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('emails_campaigns');
        $this->data['title'] = $title;
    }
}

?>
