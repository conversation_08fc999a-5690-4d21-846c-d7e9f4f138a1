<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="emails_campaigns" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$emails_campaign->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$emails_campaign->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='campaigns_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$emails_campaign->get('name')|escape}" title="{#emails_campaigns_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='campaigns_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#emails_campaigns_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$emails_campaign->get('description')|escape}</textarea>
          </td>
        </tr>
        {include file="input_datetime.html"
                standalone=false
                name='send_date'
                label=#emails_campaigns_send_date#
                help=#help_emails_campaigns_send_date#
                value=$emails_campaign->get('send_date')
                disallow_date_before=1
                required=1
        }
        {include file="input_text.html"
                standalone=false
                name='send_period'
                label=#emails_campaigns_send_period#
                help=#help_emails_campaigns_send_period#
                value=$emails_campaign->get('send_period')
                required=1
                custom_class='hright small'
                restrict='insertOnlyPositiveIntegers'
        }
        {include file="input_text.html"
                standalone=false
                name='send_limit'
                label=#emails_campaigns_send_limit#
                help=#help_emails_campaigns_send_limit#
                value=$emails_campaign->get('send_limit')
                required=1
                custom_class='hright small'
                restrict='insertOnlyPositiveIntegers'
        }
        {include file="input_text.html"
                standalone=false
                name='sender'
                label=#emails_campaigns_sender#
                help=#help_emails_campaigns_sender#
                value=$emails_campaign->get('sender')
                required=1
                readonly=$emails_campaign->get('sender_readonly')
        }
        {include file="input_text.html"
                standalone=false
                name='sender_name'
                label=#emails_campaigns_sender_name#
                help=#help_emails_campaigns_sender_name#
                value=$emails_campaign->get('sender_name')
                required=1
                readonly=$emails_campaign->get('sender_readonly')
        }
        <tr>
          <td colspan="3" class="t_v_border"></td>
        </tr>
        <tr>
          <td colspan="3" class="nopadding">
          {include file=`$templatesDir`_campaigns_configurations.html}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$emails_campaign}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
