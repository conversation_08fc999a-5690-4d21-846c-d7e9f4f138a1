<?php

require_once('translations.model.php');

/**
 * Translations model class
 */
Class Translations extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Translation';

    /**
     * Table alias to be used in search methods
     */
    public static $alias = 't';

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {
        $records = array(0 => array('id' => 1));
        $models = self::createModels($registry, $records, self::$modelName, true);

        if (!empty($filters['paginate'])) {
            $results = array($models, 1);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }
}

?>
