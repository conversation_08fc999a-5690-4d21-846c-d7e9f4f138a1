<?php

require_once('exports.model.php');

/**
 * Exports model class
 */
Class Exports extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Export';

    /**
     * Table alias to be used in search methods
     */
    public static $alias = 'ex';

    /**
     * Filters to save in session
     */
    public static $filtersToStore = array('file_name', 'format', 'group_tables', 'separator', 'export_what');

    /**
     *  Get list of reports
     *
     * @param object $registry - the global registry with all needed parameters
     * @param array $filters - filters to search the necessary reports
     */
    public static function search(&$registry, $filters=array()) {
        $db = $registry['db'];
        $sql['select']  = 'SELECT ex.*, exi18n.*';
        $sql['from']    = 'FROM ' . DB_TABLE_EXPORTS . ' AS ex ' . "\n" .
                          'LEFT JOIN ' . DB_TABLE_EXPORTS_I18N . ' AS exi18n ' . "\n" .
                          'ON (ex.id=exi18n.parent_id AND exi18n.lang="' . $registry->get('lang') . '")';

        $where = array();
        if (isset($filters['model_type'])) {
            $where[] = 'ex.model_type="' . $filters['model_type'] . '"';
        }
        if (isset($filters['model'])) {
            $where[] = 'ex.model="' . $filters['model'] . '"';
        }
        if (isset($filters['id'])) {
            $where[] = 'ex.id= ' . $filters['id'];
        }
        if (!isset($filters['visible'])) {
            $where[] = 'ex.visible=1';
        }
        if (isset($filters['lang'])) {
            $where[] = 'exi18n.lang= "' . $filters['lang'] . '"';
        } else {
            $where[] = 'exi18n.lang= "' . $registry->get('lang') . '"';
        }

        if (!empty($where)) {
            $sql['where'] = 'WHERE ' . (implode(' AND ', $where));
        }

        $query = implode("\n", $sql);

        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } else {
            $sanitize = false;
        }
        $exports_types = $db->GetAll($query);
        $exports_types = self::createModels($registry, $exports_types, self::$modelName, $sanitize);

        return $exports_types;
    }

    /**
     * log export
     */
    public static function exportLog(&$registry, $logdata = array()) {

        $db = $registry['db'];

        $set = array();
        $set['export_type'] = sprintf('export_type="%s"', $registry['export_type']);
        $set['file_name'] =  sprintf('file_name="%s"', $logdata['file_name']);
        $log = '';
        foreach ($logdata['log'] as $key => $val) {
            if (is_array($val)) {
                $log .= $key . ':' . implode(',', $val) . "\n";
            } else {
                $log .= $key . ':' . $val;
            }
        }
        $set['log'] = sprintf('log="%s"', General::slashesEscape($log));
        $set['added'] = 'exported=now()';
        $set['added_by'] = sprintf("exported_by=%d", $registry['currentUser']->get('id'));

        $query = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . "\n" .
                 'SET ' . implode(",\n", $set) . "\n";

        $result = $db->Execute($query);

        return $result;
    }

    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

}
?>