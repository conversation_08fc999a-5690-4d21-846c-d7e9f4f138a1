<?php

class Helps_Dropdown extends Dropdown {

    /**
     * Get all actions that help texts can be added for for all modules(+controllers)
     *
     * @param array $params - params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getModulesActions($params) {
        $registry = $params[0];

        $modules = Helps::getHelpModules($registry);

        $optgroups = array();

        foreach ($modules as $module => $actions) {
            $module_label = $registry['translater']->translate('menu_' . $module);
            $optgroups[$module_label] = array();

            foreach ($actions as $key => $action) {
                $optgroups[$module_label][$key] = array(
                    'option_value' => $module . '_' . $action,
                    'label' => Helps::getActionLabel($registry, $module, $action)
                );
            }

            usort($optgroups[$module_label], array('Helps', '_labelSort'));
        }

        ksort($optgroups);

        $optgroups['contain_optgroups'] = 1;

        return $optgroups;
    }
}

?>
