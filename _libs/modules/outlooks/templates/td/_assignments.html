        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td class="t_border"<list>{if !(is_array($single->get('[alternative_field_name]')) && $single->get('[alternative_field_name]')|@count gt 3)}{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', '[alternative_field_name]'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer;" title="{$smarty.config.$title_label}"{/if}{/if}</list>>
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('[alternative_field_name]')}
              {foreach from=$single->get('[alternative_field_name]') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
<export>
              {$long_text}
</export>
<list>
              {if is_array($single->get('[alternative_field_name]')) && $single->get('[alternative_field_name]')|@count gt 3}
                <div id="[alternative_field_name]_part_{$smarty.foreach.i.iteration}">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', '[alternative_field_name]'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$short_text}
                  </div>
                  {capture assign='show_all_label'}{$_module}_show_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_down.png" border="0" alt="{$smarty.config.$show_all_label|escape}" title="{$smarty.config.$show_all_label|escape}" onclick="toggleContent('[alternative_field_name]', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
                <div id="[alternative_field_name]_full_{$smarty.foreach.i.iteration}" style="display: none;">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', '[alternative_field_name]'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$long_text}
                  </div>
                  {capture assign='hide_all_label'}{$_module}_hide_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_up.png" border="0" alt="{$smarty.config.$hide_all_label|escape}" title="{$smarty.config.$hide_all_label|escape}" onclick="toggleContent('[alternative_field_name]', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
              {else}
                {$short_text}
              {/if}
</list>
            {else}
              &nbsp;
            {/if}
        </td>
