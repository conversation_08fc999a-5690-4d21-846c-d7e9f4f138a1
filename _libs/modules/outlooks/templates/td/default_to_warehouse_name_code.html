          <td class="t_border {$sort.to_warehouse.isSorted}">{if $single->get('to_warehouse')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=warehouses&amp;warehouses=view&amp;view={$single->get('to_warehouse')}" title="{#view#|escape}: {$single->get('to_warehouse_name')|escape}">&#91;{$single->get('to_warehouse_code')|escape|default:"&nbsp;"}&#93; {$single->get('to_warehouse_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
