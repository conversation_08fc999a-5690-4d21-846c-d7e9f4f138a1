<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=documents&amp;documents=filter&amp;{if $generate_system_task}generate_system_task={$generate_system_task}&amp;{/if}{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="documents" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents" method="post" enctype="multipart/form-data">
      {if $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
          {if !$autocomplete_params || $autocomplete_params.select_multiple}
            {include file="`$theme->templatesDir`_select_items.html"
              pages=$pagination.pages
              total=$pagination.total
              session_param=$session_param|default:$pagination.session_param
            }
          {else}
            {assign var='hide_selection_stats' value=true}
          {/if}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.full_num.link}">{#documents_full_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.custom_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.custom_num.link}">{#documents_custom_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#documents_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#documents_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{#documents_customer#|escape}</div></td>
          <td class="t_caption t_border {$sort.department.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.department.link}">{#documents_department#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#documents_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#documents_added#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$documents item='document'}
      {strip}
      {capture assign='info'}
        <strong><u>{#documents_full_num#|escape}:</u></strong> {$document->get('full_num')|numerate:$document->get('direction')}<br />
        <strong>{#documents_name#|escape}:</strong> {$document->get('name')|mb_truncate:$smarty.const.PH_MAX_TRUNCATE_ABOUT|escape}<br />
        <strong>{#documents_type#|escape}:</strong> {$document->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$document->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$document->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$document->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$document->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$document->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$document->get('status_modified_by_name')|escape}<br />
        {if $document->isDeleted()}<strong>{#deleted#|escape}:</strong> {$document->get('deleted')|date_format:#date_mid#|escape}{if $document->get('deleted_by_name')} {#by#|escape} {$document->get('deleted_by_name')|escape}{/if}<br />{/if}
        {if $document->get('archived_by')}<strong>{#archived#|escape}:</strong> {$document->get('archived')|date_format:#date_mid#|escape}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$document->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $document->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='document_status'}
        {if $document->get('status') eq 'opened'}
          {#help_documents_status_opened#}
        {elseif $document->get('status') eq 'locked'}
          {#help_documents_status_locked#}
        {elseif $document->get('status') eq 'closed'}
          {#help_documents_status_closed#}
        {/if}
        {if $document->get('substatus_name')}
          <br />
          {#help_documents_substatus#}{$document->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$document->get('active')} t_inactive{/if}{if $document->get('archived_by')} attention{/if}{if $document->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
          {if $autocomplete_params && !$autocomplete_params.select_multiple}
            <input type="checkbox" name='items[]' value="{$document->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
          {else}
            <input type="checkbox"
                   onclick="setCheckAllBox(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                           {rdelim});"
                   name='items[]'
                   value="{$document->get('id')}"
                   title="{#check_to_include#|escape}" />
            {/if}
          </td>
          <td class="t_border hright" nowrap="nowrap">
          {if $document->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=attachments&amp;attachments={$document->get('id')}{if $document->get('archived_by')}&amp;archive=1{/if}">
                <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, 'documents', 'documents', {$document->get('id')}{if $document->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
          {/if}
          {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.full_num.isSorted}">{$document->get('full_num')|numerate:$document->get('direction')}</td>
          <td class="t_border {$sort.custom_num.isSorted}">{$document->get('custom_num')|default:"&nbsp;"}</td>
          <td class="t_border {$sort.name.isSorted}">
            {$document->get('name')|mb_truncate:$smarty.const.PH_MAX_TRUNCATE_ABOUT|escape|default:"&nbsp;"}
            <div id="rf{$document->get('id')}" style="display: none">{$document->get('full_num')|numerate:$document->get('direction')} {$document->get('name')|escape|default:"&nbsp;"}</div>
          </td>
          <td class="t_border {$sort.type.isSorted}">{$document->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.customer.isSorted}">{$document->get('customer_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.department.isSorted}">{$document->get('department_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$document_status|escape caption=#help_documents_status#|escape width=250}{if $document->checkPermissions('setstatus')} onclick="changeStatus({$document->get('id')}, 'documents')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='document_expired'}
            {if $document->get('status') != 'closed' && $document->get('deadline') && $document->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#documents_expired_legend#}: <strong>{$document->get('deadline')|date_format:#date_mid#}</strong>!
            {/if}
            {if $document->get('status') != 'closed' && $document->get('validity_term') && $document->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {$documents_expired} {#documents_expired_validity_legend#}: <strong>{$document->get('validity_term')|date_format:#date_mid#}</strong>!
            {/if}
          {/capture}
          {if $document->get('status') != 'closed' && (($document->get('deadline') && $document->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#) || ($document->get('validity_term') && $document->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
            <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=$document_expired|escape caption=#documents_expired#|escape width=250} />
          {/if}
          {if $document->get('substatus_name')}
            {if $document->get('icon_name')}
              <img src="{$smarty.const.PH_DOCUMENTS_STATUSES_URL}{$document->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              <img src="{$theme->imagesUrl}documents_{$document->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            <span {$popup_and_onclick}>{$document->get('substatus_name')}</span>
          {else}
            <img src="{$theme->imagesUrl}documents_{$document->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {capture assign='status_param'}documents_status_{$document->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          <td class="t_border {$sort.added.isSorted}" nowrap="nowrap">{$document->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$document exclude='edit,delete,view'}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="11">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
      <br />
      <br />
      <table border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td>
          {if $smarty.request.autocomplete_filter}
            {if $autocomplete_params.select_multiple}
              <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
            {/if}
            <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
          {else}
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 0);{rdelim}, this, '{#confirm_link_documents#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_documents#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 1);{rdelim}, this, '{#confirm_link_documents#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_documents#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
          {/if}
          </td>
        </tr>
      </table>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
