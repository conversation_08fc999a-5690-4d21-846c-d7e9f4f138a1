{if $var.group_by}
  <div style="float: left;">
    <div class="t_caption2_title" style="margin: 0 0 0 5px;">{#documents_group_by#|escape}:</div>
    <table border="0" cellpadding="0" cellspacing="0">
      <tr>
        <td>
          <select name="group_by_{$var.grouping}_{$var.new_type}" class="selbox" style="width:120px;">
            <option value="">-</option>
            {foreach key='key' from=$var.group_by item='label' key='key'}
              <option value="{$key}"{if $key eq $var.transform_group_by} selected="selected"{/if}>{$label}</option>
            {/foreach}
          </select>
        </td>
      </tr>
    </table>
  </div>
{/if}
{if $var.transform_definitions}
<div style="float: left; margin-right: 10px;{if $var.group_by} border-left: 1px solid #CCCCCC{/if}">
  <div class="t_caption2_title" style="padding: 0 0 0 5px;">{#filters#}:</div>
  <table id="filter_{$var.grouping}_{$var.new_type}" border="0" cellpadding="0" cellspacing="0" style="float: left;">
    {if $var.transform_filters}
    {foreach name='i' from=$var.transform_filters item='filter'}
    <tr>
      <td>
        <select name="transform_definitions_{$var.grouping}_{$var.new_type}[]" class="selbox" style="width:120px;">
          <option value="">-</option>
          {if $var.transform_definitions eq "all"}
            {foreach from=$var.labels item='label' key='key'}
            {if !$var.hidden[$key]} 
            <option value="{$var.names[$key]}"{if $var.names[$key] eq $filter.0} selected="selected"{/if}>{$label}</option>
            {/if}
            {/foreach}
          {else}
            {foreach from=$var.transform_definitions item='name'}
            {foreach from=$var.labels item='label' key='key'}
              {if $name eq $var.names[$key]} 
              <option value="{$name}"{if $name eq $filter.0} selected="selected"{/if}>{$label}</option>
              {/if}
            {/foreach}
            {/foreach}
          {/if}
        </select>
      </td>
      <td>
        <select name="compare_{$var.grouping}_{$var.new_type}[]" class="selbox" style="width:120px;">
          <option value="eq"{if "eq" eq $filter.1} selected="selected"{/if}>{#eq#}</option>
          <option value="ne"{if "ne" eq $filter.1} selected="selected"{/if}>{#ne#}</option>
          <option value="lt"{if "lt" eq $filter.1} selected="selected"{/if}>{#lt#}</option>
          <option value="gt"{if "gt" eq $filter.1} selected="selected"{/if}>{#gt#}</option>
          <option value="le"{if "le" eq $filter.1} selected="selected"{/if}>{#le#}</option>
          <option value="ge"{if "ge" eq $filter.1} selected="selected"{/if}>{#ge#}</option>
          <option value="like"{if "like" eq $filter.1} selected="selected"{/if}>{#like#}</option>
          <option value="starts"{if "starts" eq $filter.1} selected="selected"{/if}>{#starts_with#}</option>
          <option value="ends"{if "ends" eq $filter.1} selected="selected"{/if}>{#ends_with#}</option>
        </select>
      </td>
      <td><input type="text" class="txtbox" style="width:120px;" name="compare_value_{$var.grouping}_{$var.new_type}[]" value="{$filter.2|escape}" />
      </td>
      <td{if $smarty.foreach.i.last} style="display:none"{/if}>
        <select name="and_or_{$var.grouping}_{$var.new_type}[]" class="selbox" style="width:43px;">
          <option value="and"{if "eq" eq $filter.3} selected="selected"{/if}>{#and#}</option>
          <option value="or"{if "eq" eq $filter.3} selected="selected"{/if}>{#or#}</option>
        </select>
      </td>
   {/foreach}
    </tr>
   {else}
      <tr>
      <td>
        <select name="transform_definitions_{$var.grouping}_{$var.new_type}[]" class="selbox" style="width:120px;">
          <option value="">-</option>
          {if $var.transform_definitions eq "all"}
          {foreach from=$var.labels item='label' key='key'}
            {if !$var.hidden[$key]} 
            <option value="{$var.names[$key]}">{$label}</option>
            {/if}
          {/foreach}
          {else}
          {foreach from=$var.transform_definitions item='name'}
          {foreach from=$var.labels item='label' key='key'}
            {if $name eq $var.names[$key]} 
            <option value="{$name}">{$label}</option>
            {/if}
          {/foreach}
          {/foreach}
          {/if}
        </select>
      </td>
      <td>
        <select name="compare_{$var.grouping}_{$var.new_type}[]" class="selbox" style="width:120px;">
          <option value="eq">{#eq#}</option>
          <option value="ne">{#ne#}</option>
          <option value="lt">{#lt#}</option>
          <option value="gt">{#gt#}</option>
          <option value="le">{#le#}</option>
          <option value="ge">{#ge#}</option>
          <option value="like">{#like#}</option>
          <option value="starts">{#starts_with#}</option>
          <option value="ends">{#ends_with#}</option>
        </select>
      </td>
      <td>
        <input type="text" class="txtbox" style="width:120px;" name="compare_value_{$var.grouping}_{$var.new_type}[]" />
      </td>
      <td style="display:none">
        <select name="and_or_{$var.grouping}_{$var.new_type}[]" class="selbox" style="width:43px;">
          <option value="and">{#and#}</option>
          <option value="or">{#or#}</option>
        </select>
      </td>
      </tr>
    {/if}
  </table>
  <div style="min-height: 20px; margin-top: 5px; float: left">
    {strip}
    <div class="t_buttons">
      <div id="filter_{$var.grouping}_{$var.new_type}_plusButton" onclick="addFilterRow('filter_{$var.grouping}_{$var.new_type}')" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
      <div {if empty($var.transform_filters) || count($var.transform_filters) le 1} class="disabled"{/if} id="filter_{$var.grouping}_{$var.new_type}_minusButton" onclick="removeFilterRow('filter_{$var.grouping}_{$var.new_type}')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
    </div>
    {/strip}
  </div>
</div>
{/if}
<div class="clear"></div>
{if $var.group_by || $var.transform_definitions}
  <button type="button" onclick="prepareTransform(this.form,'group_table_{$var.grouping}_{$var.new_type}', '{$module}', {$var.grouping}, {$var.new_type}, {$smarty.request.transform});" class="button" style="margin-left: 5px;">{#update#|escape}</button>
{elseif $set_group_by}
  <input type="hidden" name="group_by_{$var.grouping}_{$var.new_type}" value="{$group_by}" />
{/if}
<input type="hidden" name="included_{$var.grouping}_{$var.new_type}" id="included_{$var.grouping}_{$var.new_type}" value="1" />
