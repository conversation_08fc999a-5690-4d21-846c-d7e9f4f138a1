<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='counters_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {mb_truncate_overlib text=$documents_counter->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_next_number'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $documents_counter->get('next_number')}{$documents_counter->get('next_number')}{else}1{/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_formula'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$documents_counter->get('formula')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_medial_number_index'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {capture assign='medial_number_index_name'}documents_counters_medial_number_index_{$documents_counter->get('medial_number_index')}{/capture}
            {$smarty.config.$medial_number_index_name|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_medial_number_delimiter'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$documents_counter->get('medial_number_delimiter')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_medial_number_index_position'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {capture assign='medial_number_index_position_name'}documents_counters_medial_number_index_position_{$documents_counter->get('medial_number_index_position')}{/capture}
            {$smarty.config.$medial_number_index_position_name|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$documents_counter->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_count_documents'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$documents_counter->get('count_documents')|escape|default:0}
            <input type="hidden" name="count_documents" id="count_documents" value="{$documents_counter->get('count_documents')|escape}" />
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_types_used'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $documents_counter->get('types')}
              {foreach name='i' from=$documents_counter->get('types') item='doctype_name' key='doctype_id'}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=types&amp;types=view&amp;view={$doctype_id}" target="_blank">{$smarty.foreach.i.iteration}. {$doctype_name}</a><br />
              {/foreach}
            {else}
              <span class="error">{#error_no_types_used#|escape}</span>
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_counter}
</div>
