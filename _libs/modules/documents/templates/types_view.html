<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {* SECTION: Basic settings *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#documents_types_basic_settings#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_name'}</td>
          <td class="required">{#required#}</td>
          <td>
            {mb_truncate_overlib text=$documents_type->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_name_plural'}</td>
          <td class="required">{#required#}</td>
          <td>
            {$documents_type->get('name_plural')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_code'}</td>
          <td class="required">{#required#}</td>
          <td>
            {$documents_type->get('code')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_inheritance'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('inheritance') eq 0} {#documents_types_inheritance_primary#|mb_lower}{/if}
            {if $documents_type->get('inheritance') eq 1} {#documents_types_inheritance_secondary#|mb_lower}{/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_direction'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('direction') eq $smarty.const.PH_DOCUMENTS_INCOMING}
              <span class="incoming">{#documents_incoming_sg#|escape}</span>
            {elseif $documents_type->get('direction') eq $smarty.const.PH_DOCUMENTS_OUTGOING}
              <span class="outgoing">{#documents_outgoing_sg#|escape}</span>
            {elseif $documents_type->get('direction') eq $smarty.const.PH_DOCUMENTS_INTERNAL}
              <span class="internal">{#documents_internal_sg#|escape}</span>
            {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='type_section'}</td>
          <td class="required">{#required#}</td>
          <td>
            {$documents_type->get('section_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_description'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$documents_type->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_assignment_types'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {foreach from=$documents_type->get('assignment_types') item='a_type' name='a'}
              {$a_type|escape|default:'&nbsp;'}{if !$smarty.foreach.a.last}<br />{/if}
            {/foreach}
          </td>
        </tr>

        {* SECTION: Counter settings *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#documents_types_counter_settings#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_counter'}</td>
          <td class="required">{#required#}</td>
          <td>
            {if $documents_type->get('counter')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=counters&amp;counters=view&amp;view={$documents_type->get('counter')}" target="_blank">{$counter_name|escape}</a> - {$counter_formula|escape}
            {/if}
          </td>
        </tr>

        {* SECTION: Additional settings of fields *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#documents_types_additional_settings_of_fields#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_validate'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {foreach from=$documents_type->get('validate') item='field' name='f'}
              {$field|escape|default:'&nbsp;'}{if !$smarty.foreach.f.last}<br />{/if}
            {/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_validate_unique'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('validate_unique_current_year')}
              <span style="position: absolute; left: 450px;">{#documents_types_validate_unique_current_year#|escape}</span>
            {/if}
            {foreach from=$documents_type->get('validate_unique') item='field' name='f'}
              {$field|escape|default:'&nbsp;'}{if !$smarty.foreach.f.last}<br />{/if}
            {/foreach}
          </td>
        </tr>
        {if !empty($customers_types)}
        <tr>
          <td class="labelbox">{help label='types_related_customers_types'}</td>
          <td class="required">{#required#}</td>
          <td>
            {include file='view_checkbox_group.html'
                    standalone=true
                    name='related_customers_types'
                    options=$customers_types
                    value=$documents_type->get('related_customers_types')}
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='types_gt2'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>{if $gt2_layout}{#yes#}{else}{#no#}{/if}</td>
        </tr>
        {if $gt2_layout}
        <tr>
          <td class="labelbox">{help label='types_gt2_layout'}</td>
          <td class="unrequired">&nbsp;</td>
          <td><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=layouts&amp;layouts=view&amp;view={$documents_type->getGT2Layout()}" target="_blank">{$gt2_layout}</a></td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_VAT'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('include_VAT')}
              {#documents_types_include_VAT#|escape}, {#documents_types_default_VAT#|escape}: {$documents_type->get('default_VAT')} %
            {else}
              {#documents_types_no_VAT#|escape}
            {/if}
          </td>
        </tr>
        <tr id="calc_price_row"{if 1 || !$documents_type->get('gt2') && !$gt2_layout} style="display:none"{/if}>
          <td class="labelbox">{help label='types_calculated_price'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('calculated_price') eq 'price' || !$documents_type->get('calculated_price')}
              {#documents_types_gt2_price#}
            {/if}
            {if $documents_type->get('calculated_price') eq 'last_delivery_price'}
              {#documents_types_gt2_last_delivery_price#}
            {/if}
          </td>
        </tr>
        {/if}
        {if $layouts_search_url}
        <tr>
          <td class="labelbox">{help label_content=#menu_layouts#}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="{$layouts_search_url}">{#help_menu_layouts#|escape}</a>
          </td>
        </tr>
        {/if}

        {* SECTION: Tasks and comments *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#documents_types_tasks_and_comments#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_opened_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td>{capture assign='current_opened_requires_comment_label'}required_statuses_option_{$documents_type->get('opened_requires_comment')}{/capture}
            {$smarty.config.$current_opened_requires_comment_label}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_locked_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td>{capture assign='current_locked_requires_comment_label'}required_statuses_option_{$documents_type->get('locked_requires_comment')}{/capture}
            {$smarty.config.$current_locked_requires_comment_label}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_closed_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td>{capture assign='current_closed_requires_comment_label'}required_statuses_option_{$documents_type->get('closed_requires_comment')}{/capture}
            {$smarty.config.$current_closed_requires_comment_label}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_generate_system_task'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('generate_system_task')}{#yes#}{else}{#no#}{/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_requires_completed_minitasks'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('requires_completed_minitasks')}{#yes#}{else}{#no#}{/if}
          </td>
        </tr>

        {* SECTION: Archive settings *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#archive#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label_content=$smarty.config.after|mb_ucfirst}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('archive_interval_count') && $documents_type->get('archive_interval_type')}
              {$documents_type->get('archive_interval_count')} 
              {capture assign='interval_type_label'}date_{$documents_type->get('archive_interval_type')|lower}s{/capture}{$smarty.config.$interval_type_label|escape}
            {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label_content=#field#}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('archive_field')}
              {foreach from=$archive_field_options item='og'}
                {foreach from=$og item='opt'}
                  {if $documents_type->get('archive_field') eq $opt.option_value}
                    {$opt.label|escape}
                  {/if}
                {/foreach}
              {/foreach}
            {/if}
          </td>
        </tr>


        {* SECTION: Purge settings *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#purge#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label_content=$smarty.config.after|mb_ucfirst}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('purge_interval_count') && $documents_type->get('purge_interval_type')}
              {$documents_type->get('purge_interval_count')}
              {capture assign='interval_type_label'}date_{$documents_type->get('purge_interval_type')|lower}s{/capture}{$smarty.config.$interval_type_label|escape}
            {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label_content=#field#}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_type->get('purge_field')}
              {foreach from=$purge_field_options item='og'}
                {foreach from=$og item='opt'}
                  {if $documents_type->get('purge_field') eq $opt.option_value}
                    {$opt.label|escape}
                  {/if}
                {/foreach}
              {/foreach}
            {/if}
          </td>
        </tr>

        {* SECTION: Default settings *}
        <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text">{#documents_types_default_settings#|escape}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_default_name'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$documents_type->get('default_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_default_customer'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>{$default_customer|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label_content=#trademark#}</td>
          <td class="unrequired">&nbsp;</td>
          <td>{$default_trademark|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_media'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>{$default_media|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_pattern'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>{$default_pattern|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_department'}</td>
          <td class="required">{#required#}</td>
          <td>{$default_department|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_group'}</td>
          <td class="required">{#required#}</td>
          <td>{$default_group|escape}</td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_type}
</div>

<br /><br />
{include file=`$templatesDir`_types_transform_list.html}
<br />
<br />
{include file=`$templatesDir`_types_vars_list.html title=#patternvars_additional_list# list=$document->get('vars')}
