  {foreach from=$document->getLayoutsDetails() key='lkey' item='layout'}
    {if $layout.info_header_visibility && $layout.view}
      {if $lkey eq 'type'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$document->get('type_name')|escape}
          </td>
        </tr>
      {elseif $lkey eq 'full_num'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$document->get('full_num')|numerate:$document->get('direction')}
          </td>
        </tr>
      {elseif $lkey eq 'custom_num'}
          <tr>
            <td class="labelbox">{help label_content=$layout.name}</td>
            <td class="unrequired">&nbsp;</td>
            <td>
              {$document->get('custom_num')|default:"&nbsp;"}
            </td>
          </tr>
      {elseif $lkey eq 'date'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('date')}
              {$document->get('date')|date_format:#date_short#}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'name'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {include file=`$templatesDir`_info.html assign='info'}
            <span {popup text=$info|escape caption=#system_info#|escape width=250}>
              <img src="{if $document->get('icon_name')}{$smarty.const.PH_DOCUMENTS_STATUSES_URL}{$document->get('icon_name')}{else}{$theme->imagesUrl}documents_{$document->get('status')}.png{/if}" class="t_info_image" alt="" title="" />
              {mb_truncate_overlib text=$document->get('name')|escape|default:"&nbsp;"}
            </span>
          </td>
        </tr>
      {elseif $lkey eq 'customer'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$document->get('customer')}" title="{#view#|escape}: {$document->get('customer_name')|escape}">{$document->get('customer_name')|escape|default:"&nbsp;"}</a>
            {if $document->get('branch')}
              <span class="labelbox">{help label_content=$document->getBranchLabels('documents_branch')|escape}</span> {$document->get('branch_name')|escape}
            {/if}
            {if $document->get('contact_person')}
              <span class="labelbox">{help label_content=$document->getBranchLabels('documents_contact_person')|escape}</span> {$document->get('contact_person_name')|escape}
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'trademark'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('trademark')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$document->get('trademark')}" title="{#view#|escape}: {$document->get('trademark_name')|escape}">{$document->get('trademark_name')|escape|default:"&nbsp;"}</a>
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'contract'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('contract')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$document->get('contract')}" title="{#view#|escape}: {$document->get('contract_custom_label')|escape}">{$document->get('contract_custom_label')|escape|default:"&nbsp;"}</a>
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'project'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('project')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$document->get('project')}" title="{#view#|escape}: {$document->get('project_name')|escape}">{$document->get('project_name')|escape|default:"&nbsp;"}</a>
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'office'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('office')}
              {$document->get('office_name')|escape|default:"&nbsp;"}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'employee'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('employee')}
              {$document->get('employee_name')|escape|default:"&nbsp;"}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'media'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('media')}
              {$document->get('media_name')|escape|default:"&nbsp;"}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'deadline'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('deadline')}
              {if $document->get('status') != 'closed' && $document->get('deadline') && $document->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
                {capture assign='document_expired'}
                  {#documents_expired_legend#}: <strong>{$document->get('deadline')|date_format:#date_mid#}</strong>
                {/capture}
                <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" class="t_info_image" alt="{#expired#|escape}" {popup text=$document_expired|escape caption=#documents_expired#|escape} />
              {/if}
              {$document->get('deadline')|date_format:#date_mid#}
            {else}
              {#documents_no_deadline#|escape}
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'validity_term'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('validity_term')}
              {if $document->get('status') != 'closed' && $document->get('validity_term') && $document->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
                {capture assign='document_expired_validity_term'}
                  {#documents_expired_validity_legend#}: <strong>{$document->get('validity_term')|date_format:#date_mid#}</strong>
                {/capture}
                <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" class="t_info_image" alt="{#expired#|escape}" {popup text=$document_expired_validity_term|escape caption=#documents_validity_term_expired#|escape} />
              {/if}
              {$document->get('validity_term')|date_format:#date_mid#}
            {else}
              {#documents_no_validity_term#|escape}
            {/if}
          </td>
        </tr>
      {elseif $lkey eq 'description'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$document->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
          </td>
        </tr>
      {elseif $lkey eq 'notes'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$document->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
          </td>
        </tr>
      {elseif $lkey eq 'department'}
        <tr>
          <td class="labelbox">{help label_content=$layout.name}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $document->get('department')}
              {$document->get('department_name')|escape|default:"&nbsp;"}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {/if}
    {/if}
  {/foreach}