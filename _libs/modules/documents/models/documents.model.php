<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

require_once 'documents.validator.php';
require_once 'documents.dropdown.php';

/**
 * Documents model class
 */
class Document extends Model {
    use BelongsToTrait;

    public $modelName = 'Document';

    public $counter;

    //flag defining whether to use status to define permissions
    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('full_num', 'customer_name', 'name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
        if ($this->get('id') && $registry->get('getAssignments')) {
            $this->getAssignments();
            $this->getAssignments('responsible');
            $this->getAssignments('observer');
            $this->getAssignments('decision');
        }
        if ($this->get('direction')) {
            $direction_name = '';
            if ($this->get('direction') == PH_DOCUMENTS_INCOMING) {
                $direction_name = $this->i18n('documents_incoming_sg');
            } elseif ($this->get('direction') == PH_DOCUMENTS_OUTGOING) {
                $direction_name = $this->i18n('documents_outgoing_sg');
            } elseif ($this->get('direction') == PH_DOCUMENTS_INTERNAL) {
                $direction_name = $this->i18n('documents_internal_sg');
            }
            $this->set('direction_name', $direction_name, true);
        }

        //prepare a custom contract label
        if ($this->get('contract')) {
            $contract_custom_label = $registry['config']->getParam('documents', 'contract_custom_label');
            if (!empty($contract_custom_label) && preg_match('/(custom_num|num|name)/', $contract_custom_label)) {
                $contract_custom_label = str_replace('custom_num', $this->get('contract_custom_num'), $contract_custom_label);
                $contract_custom_label = str_replace('num',        $this->get('contract_num'),        $contract_custom_label);
                $contract_custom_label = str_replace('name',       $this->get('contract_name'),       $contract_custom_label);
            } else {
                $contract_custom_label = '[' . $this->get('contract_num') . '] ' . $this->get('contract_name');
            }
            $this->set('contract_custom_label', $contract_custom_label, true);
        }
    }

    /**
     * Custom logics to check if model belongs to user
     *
     * @param int $userId
     * @param string $action
     * @return bool
     * @throws Exception
     */
    public function belongsToUserCustom(int $userId, string $action): bool
    {
        return $this->isUserAssignedAs($userId, ['responsible', 'decision', 'owner']);
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'documents', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hit for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Document->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Document ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        //special permissions defined by the status of the document (activated/deactivated)
        if (!$this->isActivated() && !$this->get('num')) {
            //do not allow transform, multitransform and clone of initially deactivated documents (documents without numbers)
            if ($action == 'transform' || $action == 'multitransform' || $action == 'clone') {
                return false;
            }
        }

        if ($action == 'purge' && !($this->isDeleted() || $this->isArchived())) {
            return false;
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('archived_by')) {
                //archived model
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    //adding timesheets is forbidden for archived documents
                    return false;
                    break;
                case 'viewtimesheets':
                    //check if the document type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    }

                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }
                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the document
                    if (array_key_exists($current_user_id, $this->get('assignments_owner')) ||
                        array_key_exists($current_user_id, $this->get('assignments_responsible')) ||
                        array_key_exists($current_user_id, $this->get('assignments_decision')) ||
                        array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                //forbidden actions
                case 'edit':
                case 'multiedit':
                case 'translate':
                /*case 'assign':*/
                case 'observer':
                case 'clone':
                case 'transform':
                case 'multitransform':
                case 'delete':
                case 'restore':
                case 'setstatus':
                case 'generate':
                case 'print':
                case 'multiprint':
                case 'remind':
                case 'events':
                case 'tasks':
                case 'activate':
                case 'deactivate':
                case 'comments_add':
                case 'emails_add':
                case 'tags_edit':
                case 'multitag':
                case 'delete_file':
                case 'edit_file':
                    return false;
                    break;
                //allowed actions
                case 'manage_outlooks':
                case 'assign':
                case 'attachments':
                case 'view':
                case 'comments':
                case 'emails':
                case 'minitasks':
                case 'tag':
                case 'tags_view':
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
            if ($this->get('status') == 'locked') {
                //locked status
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }

                    //check if the document type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    } else {
                        // check for non-finished system task
                        $system_task_id = Documents::getSystemTask($registry, $this->get('id'));
                        $task = '';
                        if ($system_task_id) {
                            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                            $task = Tasks::searchOne($registry,
                                                     array('where' => array('t.id = ' . $system_task_id,
                                                                            't.type = ' . PH_TASK_SYSTEM_TYPE,
                                                                            't.status = \'progress\''),
                                                           'sanitize' => true));
                        }
                        if (empty($task)) {
                            return false;
                        }
                        unset($task);
                    }

                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets']) &&
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets'] == 'none') {
                        return false;
                    }

                    //check if the permission for adding timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the document
                    if (array_key_exists($current_user_id, $this->get('assignments_owner')) ||
                        array_key_exists($current_user_id, $this->get('assignments_responsible')) ||
                        array_key_exists($current_user_id, $this->get('assignments_decision')) ||
                        array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                case 'viewtimesheets':
                    //check if the document type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    }

                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }
                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the document
                    if (array_key_exists($current_user_id, $this->get('assignments_owner')) ||
                        array_key_exists($current_user_id, $this->get('assignments_responsible')) ||
                        array_key_exists($current_user_id, $this->get('assignments_decision')) ||
                        array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                //forbidden actions
                case 'edit':
                case 'multiedit':
                    return false;
                    break;
                //allowed actions
                case 'view':
                case 'assign':
                case 'attachments':
                case 'clone':
                case 'transform':
                case 'multitransform':
                case 'generate':
                case 'print':
                case 'multiprint':
                case 'translate':
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } elseif ($this->get('status') == 'closed') {
                //closed status
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    //adding timesheets is forbidden for closed documents
                    return false;
                    break;
                case 'viewtimesheets':
                    //check if the document type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    }

                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }
                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the document
                    if (array_key_exists($current_user_id, $this->get('assignments_owner')) ||
                        array_key_exists($current_user_id, $this->get('assignments_responsible')) ||
                        array_key_exists($current_user_id, $this->get('assignments_decision')) ||
                        array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                //forbidden actions
                case 'edit':
                case 'multiedit':
                case 'translate':
                case 'observer':
                    return false;
                    break;
                //allowed actions
                case 'attachments':
                case 'view':
                case 'assign':
                case 'clone':
                case 'transform':
                case 'multitransform':
                case 'generate':
                case 'print':
                case 'multiprint':
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } else {
                //opened status
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }

                    //check if the document type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    } else {
                        // check for non-finished system task
                        $system_task_id = Documents::getSystemTask($registry, $this->get('id'));
                        $task = '';
                        if ($system_task_id) {
                            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                            $task = Tasks::searchOne($registry,
                                                     array('where' => array('t.id = ' . $system_task_id,
                                                                            't.type = ' . PH_TASK_SYSTEM_TYPE,
                                                                            't.status = \'progress\''),
                                                           'sanitize' => true));
                        }
                        if (empty($task)) {
                            return false;
                        }
                        unset($task);
                    }

                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets']) &&
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets'] == 'none') {
                        return false;
                    }

                    //check if the permission for adding timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the document
                    if (array_key_exists($current_user_id, $this->get('assignments_owner')) ||
                        array_key_exists($current_user_id, $this->get('assignments_responsible')) ||
                        array_key_exists($current_user_id, $this->get('assignments_decision')) ||
                        array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;

                    break;
                case 'viewtimesheets':
                    //check if the document type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    }

                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }
                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                    $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the document
                    if (array_key_exists($current_user_id, $this->get('assignments_owner')) ||
                        array_key_exists($current_user_id, $this->get('assignments_responsible')) ||
                        array_key_exists($current_user_id, $this->get('assignments_decision')) ||
                        array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;

                    break;
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
        } else {
            return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            );
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - action with the model
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        parent::validate($action);
        if ((!$this->get('id') || $this->isDefined('name')) && !$this->get('name')) {
            $this->raiseError('error_no_name', 'name', null, array($this->getLayoutName('name', false)));
        }

        if (!$this->get('id') && !$this->get('type')) {
            $this->raiseError('error_no_type', 'type');
        }

        if ($this->get('medial_document_insert') && ($this->isDefined('active') && !$this->get('active'))) {
            $this->raiseError('error_not_allowed_medial_document_add_as_inactive', 'active');
        }

        //set counter
        $this->getCounter();

        if (! $this->counter) {
            $this->raiseError('error_no_counter_for_this_document_type', 'type');
        }

        if ($this->isDefined('customer') && !$this->get('customer')) {
            $this->raiseError('error_no_customer', 'customer', null, array($this->getLayoutName('customer')));
        }

        if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }

        if ($this->get('deadline') && !Validator::validDateTime($this->get('deadline'))) {
            $this->raiseError('error_invalid_deadline', 'deadline', null, array($this->getLayoutName('deadline')));
        }

        if ($this->get('validity_term') && !Validator::validDateTime($this->get('validity_term'))) {
            $this->raiseError('error_invalid_validity_term', 'validity_term', null, array($this->getLayoutName('validity_term')));
        }

        //check if the project is defined
        if ((($action == 'add' || $action == 'edit') && $this->isActivated()) && ($this->counter && $this->counter->get('project_code')) && !$this->get('project')) {
            $this->raiseError('error_invalid_project', 'project', null, array($this->getLayoutName('project')));
        }

        //check if the office is defined
        if ((($action == 'add' || $action == 'edit') && $this->isActivated()) &&
            ($this->counter && ($this->counter->get('office_code') || $this->counter->get('office_num'))) &&
            !$this->get('office')) {
            $this->raiseError('error_invalid_office', 'office', null, array($this->getLayoutName('office')));
        }

        //check if the office is defined
        if ((($action == 'add' || $action == 'edit') && $this->isActivated()) &&
            ($this->counter && ($this->counter->get('trademark_code') || $this->counter->get('trademark_num'))) &&
            !$this->get('trademark')) {
            $this->raiseError('error_no_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }

        // validate additional vars
        if ($action != 'translate') {
            $this->validateVars();
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($action == 'translate') {
                $action = 'edit';
            }
            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']                = sprintf("added=now()");
        $set['added_by']             = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified']      = sprintf("`status_modified`=now()");
        $set['status_modified_by']   = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        $set['type']                 = sprintf("type=%d", $this->get('type'));
        if ($set['active'] == 'active=1') {
            if ($this->get('medial_document_insert') && $this->get('medial_document_date_add')) {
                if (!$this->getDocMedialNum()) {
                    $db->FailTrans();
                    return false;
                }
                $num = $this->get('num');
                $full_num = $this->get('full_num');
                $set['added'] = sprintf("added='%s'", $this->get('medial_document_date_add'));
                $set['medial_index'] = sprintf("medial_index='%s'", $this->get('medial_index'));
            } else {
                $num = $this->getDocNum();
                $full_num = $this->getDocFullNum();
            }
            $set['num']      = sprintf("num=%d", $num);
            $set['full_num'] = sprintf("full_num='%s'", $full_num);
        } else {
            $set['num']      = sprintf("num=%d", '');
            $set['full_num'] = sprintf("full_num='%s'", '');
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new document base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N('add');

        //UPDATE THE RELATIVES TABLE
        if ($this->get('update_relatives')) {
            $this->updateRelatives(false);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //increment the counter
        if ($set['active'] == 'active=1' && !isset($set['medial_index'])) {
            $this->counter->increment();
        }

        //if create from event, update event relatives
        if ($this->get('event_id')) {
            $origin = 'document';
            $link_type = 'child';
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                      'SET parent_id=' . $this->get('event_id') . "\n" .
                      ', link_to=' . $this->get('id') . "\n" .
                      ', origin="' . $origin . '"' . "\n" .
                      ', link_type="' . $link_type . '"' . "\n" .
                      ', added=now()' . "\n";
            $db->Execute($query);
        }

        // if create from task, update task relatives
        if ($this->get('task_id')) {
            $origin = 'document';
            $link_type = 'child';
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_TASKS_RELATIVES . "\n" .
                      'SET parent_id=' . $this->get('task_id') . "\n" .
                      ', link_to=' . $this->get('id') . "\n" .
                      ', origin="' . $origin . '"' . "\n" .
                      ', link_type="' . $link_type . '"' . "\n" .
                      ', added=now()' . "\n";
            $db->Execute($query);
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if ($this->replaceVars()) {
                // replace temporary model_id with real id of model
                $this->updateModelIdAfterAdd();
                $this->updateModelFiles();
            } else {
                $db->FailTrans();
            }
        }

        // generate system task if necessary
        if ($this->get('generate_system_task')) {
            $this->slashesStrip();
            if (!$this->createSystemTask()) {
                $db->FailTrans();
            }
        }

        if ($this->registry['currentUser']->get('is_portal') && !$db->HasFailedTrans()) {
            // do not fail transaction on file upload error
            $this->_addAttachmentsPortal();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // If the transaction has failed
        if ($dbTransError) {
            // Remove the id
            $this->set('id', '', true);
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        if (($this->get('num') == 0) && ($set['active'] == 'active=1')) {
            $this->set('added', '', true);
            $set['num']      = sprintf("num=%d", ($this->getDocNum()));
            $set['full_num'] = sprintf("full_num='%s'", $this->getDocFullNum());
            $this->counter->increment();
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        // update relations
        if ($this->get('update_relatives')) {
            $this->updateRelatives();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if ($this->replaceVars()) {
                $this->updateModelFiles();
            } else {
                $db->FailTrans();
            }
        }

        if ($this->registry['currentUser']->get('is_portal') && !$db->HasFailedTrans()) {
            // do not fail transaction on file upload error
            $this->_addAttachmentsPortal();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Add attachments on document add/edit by portal user
     * @return boolean - result of the operation
     */
    private function _addAttachmentsPortal() {
        $request = $this->registry['request'];

        $added_files = array();
        $additional_descriptions = $request->get('a_file_descriptions');
        $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

        $result = true;
        if (!empty($additional_files)) {
            // prepare model for file save
            $model = clone $this;
            $model->sanitize();

            //save attachments
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';
            foreach ($additional_files['name'] as $idx => $name) {
                if ($additional_files['tmp_name'][$idx]) {
                    $file = array(
                        'name'      => $additional_files['name'][$idx],
                        'type'      => $additional_files['type'][$idx],
                        'tmp_name'  => $additional_files['tmp_name'][$idx],
                        'error'     => $additional_files['error'][$idx],
                        'size'      => $additional_files['size'][$idx]
                    );
                } else {
                    $file = array();
                }
                $params = array(
                    'name'          => $additional_files['name'][$idx] ?? '',
                    'description'   => $additional_descriptions[$idx] ?? '',
                    'revision'      => '',
                    'permission'    => 'all'
                );

                if (!empty($file) || $params['name']) {
                    if (!Files::attachFile($this->registry, $file, $params, $model)) {
                        $error_type = '';
                        if (empty($file)) {
                            $error_type = $error_type . $this->i18n('error_attachments_file');
                        }
                        if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                        if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                        $erred_added_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));
                    } else {
                        $success_added_files[] = $idx;
                    }
                }

                $added_files[$idx] = $params;
            }
            // unset model used for file save
            unset($model);
        }

        if ($added_files && !empty($erred_added_files)) {
            $result = false;
        }

        return $result;
    }

    /**
     * Change status/substatus
     *
     * @return bool - result of the operation
     */
    public function setStatus() {
        $flag_error = false;
        $flag_error_substatus = false;

        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];
        //start transaction
        $db->StartTrans();

        $permission_unlock = $this->checkPermissions('setstatus_unlock');

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            $current_status = Documents::getDocumentStatus($this->registry, $this->get('id'));
            $status_info = explode ('_', $this->get('status'));
            $status_name = $status_info[0];
            if ($current_status == 'locked' && $status_name == 'opened') {
                if (! $permission_unlock) {
                    $flag_error = true;
                }
            } else if ($current_status == 'closed' && ($status_name == 'opened' || $status_name == 'locked')) {
                $flag_error = true;
            }

            //takes the status properties based
            //if the status is defined in a dropdown it's a single
            // value containing the main status and the id of the substatus if such is defined
            @ $status_properties = explode('_', $this->get('status'));
            $new_status = $status_properties[0];

            if ($this->get('substatus')) {
                $substatus_properties = explode('_', $this->get('substatus'));
                if ($substatus_properties[0] != $new_status) {
                    $flag_error_substatus = true;
                } else {
                    $new_substatus = $substatus_properties[1];
                }
            } elseif (isset($status_properties[1])) {
                $new_substatus = $status_properties[1];
            }
        }

        if ($flag_error || $flag_error_substatus) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if (isset($current_substatus)) {
                $this->set('substatus', $current_substatus, true);
            }
            $multistatus = $this->registry['request']->get('multistatusSelect');
            if ($flag_error && !$multistatus) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            if ($flag_error_substatus && !$multistatus) {
                $this->raiseError('error_invalid_substatus_change', 'substatus', -3);
            }
            $db->CompleteTrans();
            if (!empty($sanitize_after)) {
                $this->sanitize();
            }
            return false;
        }

        $set['status'] = sprintf("`status`='%s'", $new_status);
        if (isset($new_substatus)) {
            $set['substatus'] = sprintf("`substatus`=%d", $new_substatus);
        } else {
            $set['substatus'] = "`substatus`=0";
        }

        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($new_status != 'opened' && $current_status == 'opened') {
            $this->setAvailableQuantities();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->isDefined('custom_num')) {
            $set['custom_num'] = sprintf("custom_num='%s'", $this->get('custom_num'));
        }
        if ($this->isDefined('office')) {
            if ($this->get('office')) {
                $office = $this->getDocOffice();
            } else {
                $office = 0;
            }
            $set['office'] = sprintf("office=%d", $office);
        }
        if ($this->isDefined('employee')) {
            $set['employee'] = sprintf("employee=%d", $this->get('employee'));
        }
        if ($this->isDefined('media')) {
            if ($this->get('media')) {
                $media = $this->getDocMedia();
            } else {
                $media = 0;
            }
            $set['media'] = sprintf("media=%d", $media);
        }

        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("customer=%d", $this->get('customer'));
        }
        if ($this->isDefined('branch')) {
            $set['branch'] = sprintf("branch=%d", $this->get('branch'));
        }
        if ($this->isDefined('contact_person')) {
            $set['contact_person'] = sprintf("contact_person=%d", $this->get('contact_person'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('contract')) {
            $set['contract'] = sprintf("contract=%d", $this->get('contract'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('deadline')) {
            if ($this->get('deadline')) {
                $set['deadline'] = sprintf("deadline='%s'", $this->get('deadline'));
            } else {
                $set['deadline'] = sprintf("deadline=%s", 'null');
            }
        }
        if ($this->isDefined('validity_term')) {
            if ($this->get('validity_term')) {
                $set['validity_term'] = sprintf("validity_term='%s'", $this->get('validity_term'));
            } else {
                $set['validity_term'] = sprintf("validity_term=%s", 'null');
            }
        }
        if ($this->isDefined('date')) {
            $set['date'] = sprintf("`date`='%s'", $this->get('date'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        $set['modified']        = sprintf("modified=now()");
        $set['modified_by']     = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N($action = '') {
        $db = $this->registry['db'];
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
        require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';

        if ($action == 'add' || $action == 'transform') {
            //compose extender
            $extender = new Extender;

            if (preg_match('#\[document_num\]#', ($this->get('name') . $this->get('description') . $this->get('notes')))) {
                if ($this->isActivated()) {
                    $extender->add('document_num', $this->getDocFullNum());
                } else {
                    $extender->add('document_num', '[document_num]');
                }
            }

            //get customer details
            if (preg_match('#\[customer_name\]#', ($this->get('name') . $this->get('description') . $this->get('notes')))) {
                if ($this->isActivated()) {
                    $filters = array('where' => array ('c.id = ' . $this->get('customer')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitaze' => true);
                    $customer = Customers::searchOne($this->registry, $filters);
                    $extender->add('customer_name', General::slashesEscape(trim($customer->get('name') . ' ' . $customer->get('lastname'))));
                } else {
                    $extender->add('customer_name', '[customer_name]');
                }
            }

            //get project details
            if ($this->get('project') && (preg_match('#\[project_name\]#', ($this->get('name') . $this->get('description') . $this->get('notes'))))) {
                if ($this->isActivated()) {
                    $filters = array('where' => array('p.id = ' . $this->get('project')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);
                    $extender->add('project_name', General::slashesEscape(trim($project->get('name'))));
                } else {
                    $extender->add('project_name', '[project_name]');
                }
            }

            //get office details
            if ($this->get('office') && (preg_match('#\[office_name\]#', ($this->get('name') . $this->get('description') . $this->get('notes'))))) {
                if ($this->isActivated()) {
                    $filters = array('where' => array('o.id = ' . $this->get('office')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitize' => true);
                    $office = Offices::searchOne($this->registry, $filters);
                    $extender->add('office_name', General::slashesEscape(trim($office->get('name'))));
                } else {
                    $extender->add('office_name', '[office_name]');
                }
            }
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            if ($action == 'add' || $action == 'transform') {
                $name = $extender->expand($this->get('name'), false);
            } else {
                $name = $this->get('name');
            }
            $update['name'] = sprintf("name='%s'", $name);
        }
        if ($this->isDefined('description')) {
            if ($action == 'add' || $action == 'transform') {
                $description = $extender->expand($this->get('description'), false);
            } else {
                $description = $this->get('description');
            }
            $update['description']  = sprintf("description='%s'", $description);
        }
        if ($this->isDefined('notes')) {
            if ($action == 'add' || $action == 'transform') {
                $notes = $extender->expand($this->get('notes'), false);
            } else {
                $notes = $this->get('notes');
            }
            $update['notes']  = sprintf("notes='%s'", $notes);
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing document i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Update contract of the model
     *
     * @return bool
     */
    public function updateContract($contract_id) {

        $db = $this->registry['db'];

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET contract=' . $contract_id . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Prepares dropdown optgroups (grouped by destination module) for
     * available transformations for current model
     *
     * @param string $display - display mode: panel or button (optional)
     * @return array[] - dropdown optgroups
     */
    public function getTransformOptgroups($display = '') {

        $sanitize_after = $this->isSanitized();
        if ($sanitize_after) {
            $this->unsanitize();
        }

        $filters = array(
            'where' => array(
                "t.source_model = '{$this->modelName}'",
                "t.source_type = '{$this->get('type')}'",
                $display ? "t.display IN ('$display', 'both')" : 't.display IS NOT NULL',
            ),
            'sort' => array('t.position ASC'),
            'model_lang' => $this->get('model_lang'),
            'sanitize' => true,
        );
        require_once PH_MODULES_DIR . 'transformations/models/transformations.factory.php';
        $transformations = Transformations::search($this->registry, $filters);

        $_transform_optgroups = array();
        foreach ($transformations as $transformation) {
            $destinations = explode(',', $transformation->get('destination_type'));
            $destination_model = $transformation->get('destination_model');
            $destination_mc = strtolower(General::singular2plural($destination_model));
            $show = true;

            foreach ($destinations as $destination) {
                // permission check
                if (
                    $this->registry['currentUser'] &&
                    !$this->registry['currentUser']->checkRights(
                        $destination_mc . $destination,
                        'add'
                    )
                ) {
                    $show = false;
                    break;
                }

                //do not allow transformation to model of disabled or deleted type
                if ($destination_model == 'Document') {
                    $filters = array(
                        'where' => array(
                            'dt.id = ' . $destination,
                            'dt.active = 1'
                        ),
                        'sanitize' => true
                    );
                    require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                    $type = Documents_Types::searchOne($this->registry, $filters);
                } elseif ($destination_model == 'Contract') {
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.types.factory.php';
                    $filters = array(
                        'where' => array(
                            'cot.id = ' . $destination,
                            'cot.active = 1'
                        ),
                        'sanitize' => true
                    );
                    $type = Contracts_Types::searchOne($this->registry, $filters);
                } elseif (preg_match('#^finance_#i', $destination_model)) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                    $filters = array(
                        'where' => array(
                            'fdt.id = ' . $destination,
                            'fdt.active = 1'
                        ),
                        'sanitize' => true
                    );
                    $type = Finance_Documents_Types::searchOne($this->registry, $filters);
                }
                if (empty($type)) {
                    $show = false;
                    break;
                }

            }
            if ($show) {
                $optgroup = $this->registry['translater']->translate('menu_' . $destination_mc);
                $_transform_optgroups[$optgroup][] = array(
                    'label'        => $transformation->get('name'),
                    'option_value' => $transformation->get('id'),
                    'settings'     => $transformation->getSettings()
                );
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $_transform_optgroups;
    }

    /**
     * Transform document
     *
     * @return bool - result of the operation
     */
    public function transform() {
        $db = $this->registry['db'];

        $this->slashesEscape();

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        if ($this->get('origin_model') == $this->modelName) {
            $this->getDocTransformNum();
            $this->set('transform_full_num', $this->getDocTransformFullNum(), true);
        }

        $set = $this->prepareMainData();
        $set['transform_num']       = sprintf("transform_num=%d", $this->get('transform_num'));
        $set['transform_full_num']  = sprintf("transform_full_num='%s'", $this->get('transform_full_num'));
        $set['added']               = sprintf("added=now()");
        $set['added_by']            = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified']     = sprintf("`status_modified`=now()");
        $set['status_modified_by']  = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        $set['type']                = sprintf("type=%d", $this->get('type'));
        $set['ownership']           = sprintf("ownership='%s'", $this->get('ownership'));
        $set['num']                 = sprintf("num=%d", $this->getDocNum(true, true));
        $set['full_num']            = sprintf("full_num='%s'", $this->getDocFullNum(true, true));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        $db->Execute($query1);
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N('transform');
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE RELATIVES TABLE
        $this->insertTransformedRelatives();

        //save additional variables
        $this->replaceVars();
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //copy franky bb variables
        if ($this->get('copy_bb_configs')) {
            $copy_bb_configs = $this->get('copy_bb_configs');
            foreach ($copy_bb_configs as $new_config_num => $bb_configs) {
                $this->copyConfigBBVars($bb_configs, $new_config_num);
            }
        }

        //copy franky variables
        if ($this->get('franky_vars')) {
            $franky_vars = $this->get('franky_vars');
            foreach ($franky_vars as $new_config_num => $config_num) {
                $this->copyConfigVars($config_num, $new_config_num);
            }
        }

        //copy bb variables
        if ($this->get('copy_bb')) {
            $this->copyBBVars($this->get('bb_ids'), $this->get('meta_ids'));
        }

        //generate system task
        if ($id && $this->get('generate_system_task')) {
            $this->createSystemTask();
        }

        //increment the counter
        $this->counter->increment();

        //set status
        if ($this->get('status')) {
            $substatus = '0';
            // set property for status change
            if ($this->get('substatus')) {
                $substatus = $this->get('substatus');
                $this->set('substatus', $this->get('status') . '_' . $substatus, true);
            }
            $result = $this->setStatus();
            if (!$result) {
                $db->FailTrans();
            }
            // restore property for history
            if ($substatus) {
                $this->set('substatus', $substatus, true);
            }
        } else {
            // set default values to model for history
            $this->set('status', 'opened', true);
            $this->set('substatus', '0', true);
        }

        if ($this->get('rows_links')) {
            //update row links
            $this->updateReasonsRelatives();
        }

        // insert default assignments + assignments from transformation settings
        // perform check if transaction has failed because assignments send mails
        if (!$db->HasFailedTrans()) {
            $this->defaultAssign();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Clone document
     *
     * @return bool - result of the operation
     */
    public function cloneModel() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        if (!$this->get('custom_num_is_set')) {
            $this->set('custom_num', '', true);
        }

        $this->slashesEscape();

        $set = $this->prepareMainData();
        $set['transform_num']  = sprintf("transform_num=%d", 0);
        $set['added']          = sprintf("added=now()");
        $set['added_by']       = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        $set['type']           = sprintf("type=%d", $this->get('type'));
        $set['ownership']      = sprintf("ownership='%s'", $this->get('ownership'));
        if ($this->isActivated()) {
            $set['num']            = sprintf("num=%d", $this->getDocNum());
            $set['full_num']       = sprintf("full_num='%s'", $this->getDocFullNum());
            $this->counter->increment();
        } else {
            $set['num']      = sprintf("num=%d", '');
            $set['full_num'] = sprintf("full_num='%s'", '');
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
            $this->set('added', General::strftime($this->i18n('date_iso')), true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE RELATIVES TABLE
        $this->insertRelatives();
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        $this->slashesStrip();

        // create the system task if necessary
        if ($this->get('id') && $this->get('generate_system_task')) {
            $this->createSystemTask();
        }

        //copy variables
        $this->copyVars();
        $this->cloneBBVars();

        // insert default assignments
        $this->defaultAssign();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Copy variables when cloning a document
     * Copies data from orig_vars into vars
     *
     * @param string $lang - when set, only multilang vars should be copied in current model lang
     * @return bool - result of the operation
     */
    public function copyVars($lang = '') {
        //gets vars assigned from the source (original) document
        $orig_vars = $this->get('orig_vars');
        // no additional variables
        if (!$orig_vars) {
            return true;
        }
        $assoc_vars = array();
        //convert variables array to associative
        foreach ($orig_vars as $var) {
            if (empty($lang) || $var['multilang']) {
                $assoc_vars[$var['name']] = $var;
            }
        }

        //gets vars of the destination document
        $this->unsetVars();
        $this->getVars();

        //compares variable names and removes the unnecessary variables
        $new_vars = $this->get('vars');

        foreach ($new_vars as $k => $var) {
            if ($var['bb']) {
                // bb data is copied and processed in a separate method, skip it here
                unset($new_vars[$k]);
            } elseif ($var['type'] == 'config' && $var['configurator'] && !empty($var['source']) &&
            preg_match('#\bfields\s*:=#', $var['source']) && isset($assoc_vars[$var['name']])) {
                // copy Franky to Franky values
                $this->copyConfigVars($assoc_vars[$var['name']]['configurator'], $var['configurator']);
            } elseif ($var['type'] == 'gt2') {
                if (!$lang) {
                    $new_vars[$k] = $assoc_vars[$var['name']];
                    //remove the ids of the rows and save the GT2 vars in the new model
                    $new_vars[$k]['rows'] = array();
                } else {
                    // on translate copy the values of multilang variables
                    // into the rows of the new model which have already been saved
                    $new_gt2_values = array_values($new_vars[$k]['values']);
                    $old_gt2_values = array_values($assoc_vars[$var['name']]['values']);
                    foreach ($new_gt2_values as $idx => $row) {
                        if (empty($row) || empty($old_gt2_values[$idx])) {
                            continue;
                        }
                        foreach ($new_vars[$k]['vars'] as $gt2_var_name => $gt2_var) {
                            if ($gt2_var['multilang']) {
                                $new_gt2_values[$idx][$gt2_var_name] = $old_gt2_values[$idx][$gt2_var_name];
                            }
                        }
                    }
                    $new_vars[$k]['values'] = array_combine($new_vars[$k]['rows'], $new_gt2_values);
                    $new_vars[$k]['plain_values']['total_no_vat_reason_text'] = $assoc_vars[$var['name']]['plain_values']['total_no_vat_reason_text'];
                }
                $this->registry['request']->set('gt2_requested', true, true);
            } elseif (isset($assoc_vars[$var['name']]) && !empty($assoc_vars[$var['name']]['model_id'])) {
                $new_vars[$k]['value'] = $assoc_vars[$var['name']]['value'];
            } else {
                unset($new_vars[$k]);
            }
        }

        //store new vars in the DB
        if (!empty($new_vars)) {
            //assign newly defined vars
            $this->set('vars', $new_vars, true);
            $this->registry->set('edit_all', true, true);
            if ($lang) {
                // save vars as if action is translate (only multilang ones and only in current model lang)
                $this->set('translate_multilang', true, true);
                // do not autotranslate GT2, use values that are set in the var
                $this->set('translation_values_are_set', true, true);
            }
            return $this->replaceVars();
        } else {
            return true;
        }
    }

    /**
     * Copy additional config variables (Franky to Franky) on clone or transform
     *
     * @deprecated Franky configurator is not in use any more
     * @param int $config_num - value of "configurator" field of the config variable for original model
     * @param int $new_config_num - value of "configurator" field of the config variable for current model
     * @return bool
     */
    public function copyConfigVars($config_num, $new_config_num) {
        $query = "INSERT INTO " . DB_TABLE_CONFIGURATOR
                 . " (name, model, model_type, model_id, config_num, params, added, added_by) SELECT CONCAT(id, ' ', '"
                 . date('Y-m-d H:i:s') . "'), model, " . $this->get('type'). "," . $this->get('id')
                 . ", " . $new_config_num . ", params, now(), "
                 . $this->registry['currentUser']->get('id')
                 . " FROM " . DB_TABLE_CONFIGURATOR
                 . " WHERE config_num=$config_num AND model_id=" . $this->get('origin_id');
        $this->registry['db']->Execute($query);
        return !$this->registry['db']->HasFailedTrans();
    }

    /**
     * Copy additional bb config variables (transformation of configurator in bb to Franky configurator)
     *
     * @deprecated Franky configurator is not in use any more
     * @return bool
     */
    public function copyConfigBBVars($bb_configs, $new_config_num) {
        foreach ($bb_configs as $k=>$bb_config) {
            $query = "SELECT CONCAT(id, ' ', '"
                     . date('Y-m-d H:i:s') . "') as name, model, params "
                     . " FROM " . DB_TABLE_BB
                     . " WHERE id=" . $bb_config['bb_id'];
            $record = $this->registry['db']->GetRow($query);
            $record['params'] = unserialize($record['params']);
            $record['params'][$bb_config['bb_name']] = $bb_config['bb_name_value'];
            $record['params'] = General::slashesEscape(serialize($record['params']));

            $query = "INSERT INTO " . DB_TABLE_CONFIGURATOR
                     . " (name, model, model_type, model_id, config_num, params, added, added_by) VALUES( '"
                     . mt_rand() . "','"
                     . $record['model'] . "','"
                     . $this->get('type'). "','"
                     . $this->get('id') . "','"
                     . $new_config_num . "','"
                     . $record['params'] . "', now(),"
                     . $this->registry['currentUser']->get('id'). ")";
            $this->registry['db']->Execute($query);
        }
        if ($this->registry['db']->ErrorMsg()) {
            $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
        }

        return !$this->registry['db']->HasFailedTrans();
    }

    /**
     * Copy all bb data of original model to current model (used when cloning a model)
     *
     * @return bool - result of the operation
     */
    public function cloneBBVars() {
        // get bb variables for caption rows from parent model
        $id = $this->get('id');
        $this->set('id', $this->get('origin_id'), true);
        $add_bb_vars = $this->getBBFields();
        $this->set('id', $id, true);

        // no bb vars for document or no bb data saved - nothing to copy
        if (!$add_bb_vars) {
            return true;
        }

        $ids = array();
        //get ids of bb fields as 'bb_name', 'bb_quantity' ...
        foreach ($add_bb_vars as $key => $var) {
            $ids[] = $var['id'];
        }
        //get saved bb configs for the original document
        $query = "SELECT * "
                 . " FROM " . DB_TABLE_BB
                 . " WHERE model='Document' AND model_id=" . $this->get('origin_id');
        $records = $this->registry['db']->GetAll($query);

        foreach ($records as $rec) {
            //insert new bb row for new document
            $query = "INSERT INTO " . DB_TABLE_BB
                 . " (model, model_type, model_id, bb_num, meta_id, params, added, added_by) VALUES ("
                 . "'" . $rec['model'] . "', '" . $rec['model_type'] . "', "
                 . $this->get('id')
                 . "," . $rec['bb_num'] . ", " . $rec['meta_id'] . ", '" . General::slashesEscape($rec['params']) . "', now(),"
                 . $this->registry['currentUser']->get('id') . ')';
            $this->registry['db']->Execute($query);

            $id = $this->registry['db']->Insert_Id();
            //insert bb fields values for inserted bb row
            $query = "INSERT INTO " . DB_TABLE_DOCUMENTS_CSTM . "\n"
                     . " (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) SELECT " . "\n"
                     . $this->get('id') . ", var_id, " . $id . " , value, now(), " . "\n"
                     . $this->registry['currentUser']->get('id') . ", now(), " . "\n"
                     . $this->registry['currentUser']->get('id') . ", lang " . "\n"
                     . " FROM " . DB_TABLE_DOCUMENTS_CSTM . "\n"
                     . " WHERE num=" . $rec['id'] . "\n"
                     . " AND model_id=" . $this->get('origin_id') . "\n"
                     . " AND var_id in (" . implode(',', $ids) . ")";
            $this->registry['db']->Execute($query);

            // clone cstm relatives for both main and inner bb vars
            $query = 'INSERT INTO ' . DB_TABLE_CSTM_RELATIVES . ' (model, model_id, cstm_model, cstm_model_id, var_id, num, lang)' . "\n" .
                     'SELECT cr.model, ' . $this->get('id') . ', cr.cstm_model, cr.cstm_model_id, cr.var_id, ' . $id . ', lang' . "\n" .
                     'FROM ' . DB_TABLE_CSTM_RELATIVES . ' AS cr' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     '  ON cr.var_id=fm.id' . "\n" .
                     'WHERE cr.model=\'' . $this->modelName . '\'' . "\n" .
                     '  AND cr.model_id=\'' . $this->get('origin_id') . '\'' . "\n" .
                     '  AND fm.bb=\'' . $rec['bb_num'] . '\'' . "\n" .
                     '  AND cr.num=\'' . $rec['id'] . '\'';
            $this->registry['db']->Execute($query);
        }

        return !$this->registry['db']->HasFailedTrans();
    }

    /**
     * Copy some bb variables (used for transformation 'transformBBtoBB')
     *
     * @param array $bb_ids - ids of bb rows of source document in `bb` table
     * @param array $meta_ids - mapping of ids of bb elements (group/gt2/config variables) in `_fields_meta`,
     *                          key is id for source document type, value is id for destination document type
     * @return bool - result of the operation
     */
    public function copyBBVars($bb_ids = array(), $meta_ids = array()) {
        if (empty($bb_ids) || empty($meta_ids)) {
            return true;
        }

        $add_bb_vars = $this->get('add_bb_vars');
        $ids = array();
        //get ids of bb fields as 'bb_name', 'bb_quantity' ...
        foreach ($add_bb_vars as $key => $var) {
            $ids[] = $var['origin_add_bb_id'];
        }

        //get saved bb configs for the original document
        $query = "SELECT * "
                 . " FROM " . DB_TABLE_BB
                 . " WHERE model='Document' AND model_id=" . $this->get('origin_id')
                 . " AND id in (" . implode(',', $bb_ids) . ") ORDER BY id ASC";
        $records = $this->registry['db']->GetAll($query);

        // get mapping of ids of both main and inner vars that cstm relations
        // have to be created for (there are saved values for transformed rows)
        $cstm_model_vars = array();
        $var_id_clause = '0';
        if ($records) {
            $query = 'SELECT DISTINCT fms.id AS idx, fmd.id' . "\n" .
                     'FROM ' . DB_TABLE_FIELDS_META . ' AS fms' . "\n" .
                     'JOIN ' . DB_TABLE_CSTM_RELATIVES . ' AS crs' . "\n" .
                     '  ON fms.id=crs.var_id' . "\n" .
                     '    AND crs.model=\'' . $this->get('origin_model') . '\'' . "\n" .
                     '    AND crs.model_id=\'' . $this->get('origin_id') . '\'' . "\n" .
                     '    AND crs.num IN (\'' . implode('\', \'', $bb_ids) . '\')' . "\n" .
                     '    AND fms.bb!=0' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' AS fmd' . "\n" .
                     '  ON fmd.model=\'' . $this->modelName . '\'' . "\n" .
                     '    AND fmd.model_type=\'' . $this->get('type') . '\'' . "\n" .
                     '    AND fmd.bb!=0' . "\n" .
                     '    AND fms.name=fmd.name';
            $cstm_model_vars = $this->registry['db']->GetAssoc($query);

            if ($cstm_model_vars) {
                $var_id_clause = 'CASE var_id ';
                foreach ($cstm_model_vars as $src => $dest) {
                    $var_id_clause .= 'WHEN \'' . $src . '\' THEN \'' . $dest . '\' ';
                }
                $var_id_clause .= 'END';
            }
        }

        foreach ($records as $rec) {
            //insert new bb row for new document
            if (isset($meta_ids[$rec['meta_id']])) {
                $query = "INSERT INTO " . DB_TABLE_BB
                     . " (model, model_type, model_id, bb_num, meta_id, params, added, added_by) VALUES ("
                     . "'" . $rec['model'] . "', '" . $this->get('type') . "', "
                     . $this->get('id')
                     . "," . $rec['bb_num'] . ", " . $meta_ids[$rec['meta_id']] . ", '" . General::slashesEscape($rec['params']) . "', now(),"
                     . $this->registry['currentUser']->get('id') . ')';
                $this->registry['db']->Execute($query);

                $id = $this->registry['db']->Insert_Id();
                //insert bb fields values for inserted bb row
                $query = "INSERT INTO " . DB_TABLE_DOCUMENTS_CSTM . "\n"
                         . " (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) SELECT " . "\n"
                         . $this->get('id') . ", var_id, " . $id . " , value, now(), " . "\n"
                         . $this->registry['currentUser']->get('id') . ", now(), " . "\n"
                         . $this->registry['currentUser']->get('id') . ", lang " . "\n"
                         . " FROM " . DB_TABLE_DOCUMENTS_CSTM . "\n"
                         . " WHERE num = " . $rec['id'] . "\n"
                         . " AND model_id=" . $this->get('origin_id') . "\n"
                         . " AND var_id in (" . implode(',', $ids) . ")";
                $this->registry['db']->Execute($query);

                //update var_id's
                foreach ($add_bb_vars as $key => $var) {
                    $query = "UPDATE " . DB_TABLE_DOCUMENTS_CSTM . "\n"
                         . " SET var_id=" . $var['id'] . "\n"
                         . " WHERE num = " . $id . "\n"
                         . " AND model_id=" . $this->get('id') . "\n"
                         . " AND var_id=" . $var['origin_add_bb_id'];
                    $this->registry['db']->Execute($query);
                }

                // add cstm relations
                if ($cstm_model_vars) {
                    $query = 'INSERT INTO ' . DB_TABLE_CSTM_RELATIVES . ' (model, model_id, cstm_model, cstm_model_id, var_id, num, lang)' . "\n" .
                             'SELECT model, ' . $this->get('id') . ', cstm_model, cstm_model_id, ' . $var_id_clause . ', ' . $id . ', lang' . "\n" .
                             'FROM ' . DB_TABLE_CSTM_RELATIVES . "\n" .
                             'WHERE model=\'' . $this->get('origin_model') . '\'' . "\n" .
                             '  AND model_id=\'' . $this->get('origin_id') . '\'' . "\n" .
                             '  AND var_id IN (\'' . implode('\', \'', array_keys($cstm_model_vars)) . '\')' . "\n" .
                             '  AND num=\'' . $rec['id'] . '\'';
                    $this->registry['db']->Execute($query);
                }
            }
        }

        return !$this->registry['db']->HasFailedTrans();
    }

    /**
     * Update relatives table of the model
     *
     * @param bool $delete - whether to delete old relations first
     * @return bool - result of operation
     */
    public function updateRelatives($delete = true) {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $old_referers = $db->GetCol("
            SELECT parent_id
            FROM " . DB_TABLE_DOCUMENTS_RELATIVES . "
            WHERE link_to = '{$this->get('id')}'
              AND link_to_model_name = 'Document'
              AND parent_model_name = 'Document'
              AND origin = 'inherited'") ?: array();

        if ($delete) {
            // delete old relations
            $query3 = 'DELETE FROM `' . DB_TABLE_DOCUMENTS_RELATIVES . '`' . "\n" .
                      'WHERE link_to=' . $this->get('id') . "\n" .
                      '  AND link_to_model_name="Document" AND origin="inherited"';
            $db->Execute($query3);
            $query3 = preg_replace('#`([^`]+)`#', '`archive_$1`', $query3);
            $db->Execute($query3);
        }

        // add new relations
        $tmp = array();
        if (is_array($this->get('referers')) && count($this->get('referers')) > 0) {
            // get parents and children of current document
            $tree_p = $tree_c = array();
            $this->getParentsTree($this->get('id'), 0, $tree_p);
            $this->getChildrenTree($this->get('id'), 0, $tree_c);

            foreach ($this->get('referers') as $ref) {
                $insert = true;
                //check if referer is an ancestor of current model
                foreach ($tree_p as $rec) {
                    if ($rec['id'] == $ref && $rec['model'] == 'Document') {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_documents_relative_child'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                //check if referer is a direct descendant (transformed, cloned) of current model
                foreach ($tree_c as $rec) {
                    if ($rec['id'] == $ref && $rec['model'] == 'Document' && $rec['level'] == 0 && $rec['origin'] != 'inherited') {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_documents_relative_child'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                if ($insert && $this->get('id') != $ref) {
                    $tmp[$ref] = '(' . $ref . ', ' . $this->get('id') . ', "Document", "inherited")';
                }
            }
            $query4 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_RELATIVES . ' (parent_id, link_to, link_to_model_name, origin) VALUES ' . "\n" .
                      implode(', ' . "\n", $tmp);
        }

        if (count($tmp)) {
            $db->Execute($query4);
        }

        // save history
        if (!$db->HasFailedTrans()) {
            $this->set('old_relatives', $old_referers && $this->getParentNames($old_referers) ? $this->get('referers') : array(), true);
            $this->set('new_relatives', $tmp && $this->getParentNames(array_keys($tmp)) ? $this->get('referers') : array(), true);
            $this->set('referers', array_keys($tmp), true);
            $this->set('relation', 'parent', true);

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $this,
                    'action_type' => 'relatives',
                ));

            // save history for every opposite model that was added or removed
            $added = array_diff_key($this->get('new_relatives'), $this->get('old_relatives'));
            $removed = array_diff_key($this->get('old_relatives'), $this->get('new_relatives'));
            foreach (array('added' => $added, 'removed' => $removed) as $diff_key => $diff) {
                if ($diff) {
                    if (empty($ref_model)) {
                        $ref_model = new Document(
                            $this->registry,
                            array(
                                'model_lang' => $this->get('model_lang'),
                                'relation' => 'child',
                            )
                        );
                        $diff_data = array(
                            $this->get('id') => array(
                                'full_num' => $this->get('full_num'),
                                'name' => $this->get('name'),
                            )
                        );
                    }
                    foreach ($diff as $ref => $ref_data) {
                        $ref_model->set('id', $ref, true);
                        foreach ($ref_data as $prop => $val) {
                            $ref_model->set($prop, $val, true);
                        }
                        $ref_model->set('new_relatives', ($diff_key == 'added' ? $diff_data : array()), true);
                        $ref_model->set('old_relatives', ($diff_key == 'added' ? array() : $diff_data), true);

                        Documents_History::saveData(
                            $this->registry,
                            array(
                                'model' => $ref_model,
                                'action_type' => 'relatives',
                            ));
                    }
                }
            }

            $this->unsetProperty('old_relatives', true);
            $this->unsetProperty('new_relatives', true);
            $this->unsetProperty('relation', true);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Insert relatives table of the model clone, transform
     *
     * @return bool - result of the operation
     */
    public function insertTransformedRelatives($multi = 0, $group_index = '') {
        $db = $this->registry['db'];

        if ($this->get('multi')) {
            $multi = $this->get('multi');
        } elseif ($this->get('group_index')) {
            $group_index = $this->get('group_index');
        }

        $this->set('link_to', $this->get('origin_id'), true);
        $this->set('link_to_model_name', $this->get('origin_model'), true);

        $query4 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_RELATIVES .
                    ' (parent_id, link_to, link_to_model_name, origin, multi_index, group_index) VALUES (' .
                    $this->get('id') . ', ' . $this->get('origin_id'). ', "' . $this->get('origin_model') . '", ' .
                    "'" . $this->get('clone_transform'). "', " . $multi . ", '$group_index')";

        if (!empty($query4)) {
            $db->Execute($query4);
            if ($this->get('origin_model') == 'Contract') {
                $query = 'INSERT INTO ' . DB_TABLE_CONTRACTS_RELATIVES .
                    ' (parent_id, parent_model_name, link_to, link_to_model_name, origin, multi_index, group_index) VALUES (' .
                    $this->get('id') . ', "Document", ' . $this->get('origin_id') . ', "' . $this->get('origin_model'). '", ' .
                    "'" . $this->get('clone_transform') . "', " . $multi . ", '$group_index')";
                $db->Execute($query);
            }
            return !$db->HasFailedTrans();
        } else {
            return true;
        }
    }

    /**
     * update reasons relatives
     *
     * @return bool
     */
    public function updateReasonsRelatives() {
        $db = $this->registry['db'];

        $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE parent_id=' . $this->get('id') . ' AND parent_model_name="Document"' . "\n";
        $records = $db->GetAll($query);

        $new_rows_links = $this->get('rows_links');

        if (!empty($records) && !empty($new_rows_links['deleted'])) {
            //invoice edit - remove deleted rows form the links
            if (!empty($new_rows_links['deleted'])) {
                foreach ($records as $record) {
                    foreach ($new_rows_links['deleted'] as $deleted) {
                        if (preg_match('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', $record['rows_links'])) {
                            $record['rows_links'] = preg_replace('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', "\n", $record['rows_links']);
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                                     ' SET rows_links = \'' . $record['rows_links'] . "'\n" .
                                     'WHERE parent_id = ' . $record['parent_id'] .
                                     '  AND parent_model_name="' . $record['parent_model_name'] . '" ' . "\n" .
                                     '  AND link_to= ' . $record['link_to'] . "\n" .
                                     '  AND link_to_model_name="' . $record['link_to_model_name'] . '" ';
                            $db->Execute($query);
                        }
                    }
                }
            }
        } elseif (!empty($new_rows_links['added'])) {
            //document adding
            foreach ($new_rows_links['added'] as $new_row => $old_row) {
                $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
            }

            if ($this->get('link_to_model_name')) {
                $link_to_model_name = $this->get('link_to_model_name');
            } else {
                $link_to_model_name = "Document";
            }

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     ' SET parent_id = ' . $this->get('id') . ",\n" .
                     '     parent_model_name = "Document",' . "\n" .
                     '     link_to = ' . $this->get('link_to') . ",\n" .
                     '     link_to_model_name = "' . $link_to_model_name . '"' . ",\n" .
                     '     rows_links = \'' . implode("\n", $new_rows_links['added']) . '\'';
            $db->Execute($query);

        }
    }

    /**
     * Update the relations of the current document to the destination document.
     *
     * @param int $mergeDestinationID - id of the document to merge from
     */
    public function moveRelations($mergeDestinationID):void {
        $query = "UPDATE " . DB_TABLE_DOCUMENTS_RELATIVES .
                 " SET link_to={$mergeDestinationID}
                   WHERE link_to_model_name='Document'
                       AND link_to={$this->get('id')}";
        $this->registry['db']->Execute($query);

        $query = "UPDATE " . DB_TABLE_DOCUMENTS_RELATIVES .
                 " SET parent_id={$mergeDestinationID}
                   WHERE parent_model_name='Document'
                       AND parent_id={$this->get('id')}";
        $this->registry['db']->Execute($query);
    }

    /**
     * Insert relatives table of the model clone, transform
     *
     * @return bool
     */
    public function insertRelatives($multi = 0, $group_index = '') {
        // If it's set to skip the relation between the parent record and the current one
        if ($this->get('skip_relatives')) {
            // Skip the relation
            return true;
        }

        $db = $this->registry['db'];

        if (!empty($group_index)) {
            $multi = 0;
        }
        $query4 = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_RELATIVES .
                    ' (parent_id, link_to, link_to_model_name, origin, multi_index, group_index) VALUES (' .
                    $this->get('id') . ', ' . $this->get('origin_id') . ', "' . $this->get('origin_model') . '", ' .
                    "'" . $this->get('clone_transform') . "', " . $multi . ", '$group_index')";

        if (!empty($query4)) {
            $db->Execute($query4);
            return !$db->HasFailedTrans();
        } else {
            return true;
        }
    }

    /**
     * Checks permitted layouts
     *
     * @param string $mode - action name
     * @param string $model - model name, not used in overwrite method
     * @return array - 'view' layouts or 'edit' layouts or array of both
     */
    public function getPermittedLayouts($mode = '', $model = '') {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        //if validLogin or automation user for crontab
        if ($this->registry['validLogin'] || ($this->registry['currentUser'] && $this->registry['currentUser']->get('id') == PH_AUTOMATION_USER)) {
            if (!$this->isDefined('layouts_view')) {
                $groups = $this->registry['currentUser']->getGroups();
                if (count($groups)) {
                    // check if current user is assigned as observer of records of this type
                    $module = strtolower(General::singular2plural($this->modelName));
                    $set_observer =
                        in_array('observer', $this->registry['config']->getParamAsArray($module, 'assignment_types_' . $this->get('type'))) &&
                        $this->registry['currentUser']->getPersonalSettings($module, 'set_observer');

                    // make sure model has all types of assignments
                    if ($this->get('id')) {
                        if (!$this->isDefined('assignments_responsible')) {
                            $this->getAssignments('responsible');
                        }
                        if (!$this->isDefined('assignments_decision')) {
                            $this->getAssignments('decision');
                        }
                        if (!$this->isDefined('assignments_observer')) {
                            $this->getAssignments('observer');
                        }
                        if (!$this->isDefined('assignments_owner')) {
                            $this->getAssignments();
                        }
                    }

                    //get rights for layouts view
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="view")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND (l.model_type="' . intval($this->get('type')) . '" OR l.model_type=0) AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="view"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_view_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_view = array();
                    foreach ($layouts_view_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_view[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_view[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_view[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_view', $layouts_view);

                    //get rights for layouts edit
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="edit")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND (l.model_type="' . intval($this->get('type')) . '" OR l.model_type=0) AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="edit"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_edit_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_edit = array();
                    foreach ($layouts_edit_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_edit[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_edit[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_edit[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_edit', $layouts_edit);
                } else {
                    $this->set('layouts_view', array());
                    $this->set('layouts_edit', array());
                }
            }
        } else {
            $this->set('layouts_view', array());
            $this->set('layouts_edit', array());
        }

        if ($sanitized) {
            $this->sanitize();
        }

        if ($mode) {
            return $this->get('layouts_' . $mode);
        } else {
            return array($this->get('layouts_view'), $this->get('layouts_edit'));
        }
    }

    /**
     * Get parents from database
     *
     * @return bool - result of the operation
     */
    public function getParents() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = '(SELECT dr.parent_id as idx, d.num, d.full_num, d.archived_by, dt.direction as direction, di18n.name, dr.parent_id as id, dti18n.name as type_name ' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt ' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n ' . "\n" .
                 '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $lang . '") ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n ' . "\n" .
                 '  ON (dr.parent_id=di18n.parent_id AND di18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') . "\n" .
                 '  AND dr.origin="inherited" AND dr.link_to_model_name="Document")' . "\n" .
                 'UNION' . "\n" .
                 '(SELECT dr.parent_id as idx, d.num, d.full_num, d.archived_by, dt.direction as direction, di18n.name, dr.parent_id as id, dti18n.name as type_name ' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . '` AS dr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt ' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n ' . "\n" .
                 '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $lang . '") ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n ' . "\n" .
                 '  ON (dr.parent_id=di18n.parent_id AND di18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') . "\n" .
                 '  AND dr.origin="inherited" AND dr.link_to_model_name="Document")' . "\n" .
                 'UNION' . "\n" .
                 '(SELECT dr.parent_id as idx, d.num, d.full_num, d.archived_by, dt.direction as direction, di18n.name, dr.parent_id as id, dti18n.name as type_name ' . "\n" .
                 'FROM `' . DB_TABLE_DOCUMENTS_RELATIVES . '` AS dr ' . "\n" .
                 'LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS . '` AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt ' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n ' . "\n" .
                 '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $lang . '") ' . "\n" .
                 'LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS_I18N . '` AS di18n ' . "\n" .
                 '  ON (dr.parent_id=di18n.parent_id AND di18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') . "\n" .
                 '  AND dr.origin="inherited" AND dr.link_to_model_name="Document")' . "\n" .
                 'UNION' . "\n" .
                 '(SELECT dr.parent_id as idx, d.num, d.full_num, d.archived_by, dt.direction as direction, di18n.name, dr.parent_id as id, dti18n.name as type_name ' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . '` AS dr ' . "\n" .
                 'LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS . '` AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt ' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n ' . "\n" .
                 '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $lang . '") ' . "\n" .
                 'LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS_I18N . '` AS di18n ' . "\n" .
                 '  ON (dr.parent_id=di18n.parent_id AND di18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') . "\n" .
                 '  AND dr.origin="inherited" AND dr.link_to_model_name="Document")' . "\n" .
                 'ORDER BY direction';
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('referers', $records, true);
    }

    /**
     * Get relative documents names
     *
     * @param array $filter - document ids
     * @return bool - result of the operation
     */
    public function getParentNames($filter) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT d.id as idx, d.num, d.full_num, d.archived_by, dt.direction, di18n.name, d.id ' . "\n" .
                 'FROM `' . DB_TABLE_DOCUMENTS . '` AS d ' . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt ' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0) ' . "\n" .
                 'LEFT JOIN `' . DB_TABLE_DOCUMENTS_I18N . '` AS di18n ' . "\n" .
                 '  ON (d.id=di18n.parent_id AND di18n.lang="' . $lang . '")' . "\n" .
                 'WHERE d.id in (' . implode(',', $filter) . ')';
        // get both archived and non-archived relatives of current document
        $query = implode("\n" . 'UNION' . "\n",
                         array($query, preg_replace('#`([^`\s]+)`#', '`archive_$1`', $query)));
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('referers', $records, true);
    }

    /**
     * Get documents files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        if ($this->get('archived_by')) {
            $f1 = DB_TABLE_ARCHIVE_FILES;
            $f2 = DB_TABLE_ARCHIVE_FILES_I18N;
        } else {
            $f1 = DB_TABLE_FILES;
            $f2 = DB_TABLE_FILES_I18N;
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM `' . $f1 . '` AS f' . "\n" .
                       'LEFT JOIN `' . $f2 . '` AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . $f1 . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . $f1 . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get document attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Document\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted = 0'),
                         'model_lang' => $this->get('model_lang'),
                         'archive' => $this->get('archived_by'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Document\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted = 0'),
                         'archive' => $this->get('archived_by'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }

    /**
     * Get documents num
     *
     * @return int
     */
    public function getDocNum($force = false, $transform = false) {
        if (! $this->get('num') || !$this->get('full_num') || $force) {

            //get the counter assigned to the document type
            $this->getCounter();

            //define some the counter's fomula components
            $formula = $this->counter->get('formula');
            $prefix = $this->counter->get('prefix');
            $delimiter = $this->counter->get('delimiter');
            $zeroes = $this->counter->get('leading_zeroes');
            $date_format = $this->counter->get('date_format');
            $customer_year = $this->counter->get('customer_year');
            $office_year = $this->counter->get('office_year');
            $trademark_year = $this->counter->get('trademark_year');

            //create extender to expand the formula components
            $extender = new Extender;

            //set document number
            if ($this->counter->get('customer_num') && $this->get('customer')) {
                //get number of documents for document customer
                $query = 'SELECT (SELECT COUNT(d.id) ' . "\n" .
                         '  FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                         '  JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                         '    ON d.type=dt.id AND dt.active=1 AND dt.deleted=0 AND dt.counter=' . $this->counter->get('id') . "\n" .
                         '      AND d.customer="' . $this->get('customer') . '" AND d.num > 0 AND d.medial_index IS NULL' .
                         ($customer_year ? ' AND YEAR(d.added)=YEAR(CURDATE())' : '') . ') +' . "\n" .
                         '(SELECT COUNT(d.id) ' . "\n" .
                         '  FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                         '  JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                         '    ON d.type=dt.id AND dt.active=1 AND dt.deleted=0 AND dt.counter=' . $this->counter->get('id') . "\n" .
                         '      AND d.customer="' . $this->get('customer') . '" AND d.num > 0 AND d.medial_index IS NULL' .
                         ($customer_year ? ' AND YEAR(d.added)=YEAR(CURDATE())' : '') . ') + 1';
                $num = sprintf('%0' . $zeroes . 'd', $this->registry['db']->GetOne($query));
                $extender->add('customer_num', $num);
            } else {
                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_DOCUMENTS_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('document_num', $num);
            }

            if ($this->counter->get('prefix_used')) {
                //add this component to the extender
                $extender->add('prefix', $prefix);
            }

            if ($this->counter->get('customer_code') && $this->get('customer')) {
                //get customer code
                $query = 'SELECT code FROM ' . DB_TABLE_CUSTOMERS . ' WHERE id=' . $this->get('customer');
                $customer_code = $this->registry['db']->GetOne($query);

                //add this component to the extender
                $extender->add('customer_code', $customer_code);
            }

            if ($this->counter->get('project_code') && $this->get('project')) {
                //get project code
                $query = 'SELECT code FROM ' . DB_TABLE_PROJECTS . ' WHERE id=' . $this->get('project');
                $project_code = $this->registry['db']->GetOne($query);

                //add this component to the extender
                $extender->add('project_code', $project_code);
            }

            if ($this->counter->get('office_code') && $this->get('office')) {
                //get office code
                $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                $office_code = $this->registry['db']->GetOne($query);

                //add this component to the extender
                $extender->add('office_code', $office_code);
            }

            //set office number
            if ($this->counter->get('office_num') && $this->get('office')) {
                //get number of documents for document office
                $query = 'SELECT (SELECT COUNT(d.id) ' . "\n" .
                         '  FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                         '  JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                         '    ON d.type=dt.id AND dt.active=1 AND dt.deleted=0 AND dt.counter=' . $this->counter->get('id') . "\n" .
                         '      AND d.office="' . $this->get('office') . '" AND d.num > 0 AND d.medial_index IS NULL' .
                         ($office_year ? ' AND YEAR(d.added)=YEAR(CURDATE())' : '') . ') +' . "\n" .
                         '(SELECT COUNT(d.id) ' . "\n" .
                         '  FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                         '  JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                         '    ON d.type=dt.id AND dt.active=1 AND dt.deleted=0 AND dt.counter=' . $this->counter->get('id') . "\n" .
                         '      AND d.office="' . $this->get('office') . '" AND d.num > 0 AND d.medial_index IS NULL' .
                         ($office_year ? ' AND YEAR(d.added)=YEAR(CURDATE())' : '') . ') + 1';
                $num = sprintf('%0' . $zeroes . 'd', $this->registry['db']->GetOne($query));
                $extender->add('office_num', $num);
            }

            if ($this->counter->get('user_code')) {
                //get user code
                //add this component to the extender
                $extender->add('user_code', $this->registry['currentUser']->get('code'));
            }

            if ($this->counter->get('document_type_code')) {
                //get document type code
                //add this component to the extender
                $extender->add('document_type_code', $this->get('type_code'));
            }

            if ($this->counter->get('transform_subnum') && $transform) {
                //get transform subnumber
                //$extender->add('transform_subnum', $this->get('transform_full_num'));
                $extender->add('transform_subnum', sprintf('%02d', $this->get('transform_num')));
            }

            if ($this->counter->get('parent_doc_num') && $transform) {
                //get the full num from the parent doc
                $extender->add('parent_doc_num', $this->get('parent_doc_num'));
            }

            if ($this->counter->get('document_added')) {
                //replace the date
                $date = ($this->get('added')) ? General::strftime($date_format, strtotime($this->get('added'))) : General::strftime($date_format);

                //add this component to the extender
                $extender->add('document_added', $date);
            }

            if ($this->counter->get('trademark_code') && $this->get('trademark')) {
                //add this component to the extender
                //get trademark code
                $query = 'SELECT code FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE id=' . $this->get('trademark');
                $trademark_code = $this->registry['db']->GetOne($query);
                $extender->add('trademark_code', $trademark_code);
            }

            //set trademark number
            if ($this->counter->get('trademark_num') && $this->get('trademark')) {
                //get number of documents for document office
                $query = 'SELECT (SELECT COUNT(d.id) ' . "\n" .
                         '  FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                         '  JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                         '    ON d.type=dt.id AND dt.active=1 AND dt.deleted=0 AND dt.counter=' . $this->counter->get('id') . "\n" .
                         '      AND d.trademark="' . $this->get('trademark') . '" AND d.num > 0 AND d.medial_index IS NULL' .
                         ($trademark_year ? ' AND YEAR(d.added)=YEAR(CURDATE())' : '') . ') +' . "\n" .
                         '(SELECT COUNT(d.id) ' . "\n" .
                         '  FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                         '  JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                         '    ON d.type=dt.id AND dt.active=1 AND dt.deleted=0 AND dt.counter=' . $this->counter->get('id') . "\n" .
                         '      AND d.trademark="' . $this->get('trademark') . '" AND d.num > 0 AND d.medial_index IS NULL' .
                         ($trademark_year ? ' AND YEAR(d.added)=YEAR(CURDATE())' : '') . ') + 1';
                $num = sprintf('%0' . $zeroes . 'd', $this->registry['db']->GetOne($query));
                $extender->add('trademark_num', $num);
            }


            $full_num = $extender->expand($formula);
            if ($delimiter) {
                //remove repeating delimiters
                $full_num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $full_num);
                $full_num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $full_num);
                $full_num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $full_num);
            }

            if ($this->slashesEscaped) {
                $full_num = General::slashesEscape($full_num);
            }

            $this->set('num', $num, true);
            $this->set('full_num', $full_num, true);
        }

        return $this->get('num');
    }

    /**
     * Get documents full number
     *
     * @return string
     */
    public function getDocFullNum($force = false, $transform = false) {
        $this->getDocNum($force, $transform);

        return $this->get('full_num');
    }

    /**
     * Get documents transform_num
     *
     * @return int
     */
    public function getDocTransformNum() {
        $db = $this->registry['db'];

        $subquery = "SELECT MAX(d.transform_num) AS max_transformed
                    FROM `" . DB_TABLE_DOCUMENTS . "` AS d, `" . DB_TABLE_DOCUMENTS_RELATIVES . "` AS dr
                    WHERE d.id = dr.parent_id
                         AND dr.link_to = " . $this->get('origin_id') . "
                         AND dr.link_to_model_name = '" . $this->get('origin_model') . "'
                         AND dr.origin = 'transformed'
                    GROUP BY dr.link_to";

        $query = "SELECT MAX(max_transformed) AS max_transformed FROM (\n" .
                 implode("\nUNION ALL\n",
                         array($subquery,
                               // both archive tables
                               preg_replace('#`([^`\s]+)`#', '`archive_$1`', $subquery),
                               // document is archived but relatives record is not
                               preg_replace('#`(' . DB_TABLE_DOCUMENTS . ')`#', '`archive_$1`', $subquery))) . "\n" .
                 ") AS subQuery";

        $transform_num = intval($db->GetOne($query)) + 1;
        $this->set('transform_num', $transform_num, true);
        return $transform_num;
    }

    /**
     * Get documents custom_num
     *
     * @return string
     */
    public function getDocTransformFullNum() {
        $db = $this->registry['db'];
        $type = $this->get('type');
        require_once 'documents.types.factory.php';
        $filters = array('where' => array('dt.id = ' . $type,
                                          'dt.deleted IS NOT NULL'),
                         'model_lang' => $this->get('model_lang'));
        $type_obj = Documents_Types::searchOne($this->registry, $filters);

        $query = "SELECT IF(d.transform_num = 0 or d.transform_full_num = '' or d.transform_full_num is null,'',d.transform_full_num )
                    AS 'transform_full_num',
                    dt.inheritance as parent_inheritance
                    FROM " . DB_TABLE_DOCUMENTS . " AS d, " . DB_TABLE_DOCUMENTS_TYPES . " AS dt
                    WHERE d.type = dt.id AND dt.active=1 AND dt.deleted=0 AND d.id = " . $this->get('origin_id');
        list($res) = $db->GetAll($query);

        $counter = $this->getCounter();
        $delimiter = $counter->get('delimiter');

        //check if the document is primary (not secondary)
        //if ($type_obj->get('inheritance')) {
            if (!preg_match("/\./", $res['transform_full_num'])) {
                $transform_full_num = sprintf('%s%02d', $res['transform_full_num'], $this->get('transform_num'));
            } else {
                $transform_full_num = sprintf('%02d', $this->get('transform_num'));
            }
        //} else {
            //$transform_full_num = '';
        //}

        return $transform_full_num;
    }

    /**
     * Check if there is a previous document to base medial number on
     *
     * @return bool
     */
    public function checkMedialDocNum() {
        //get the last document from the required type

        $query = '(SELECT id, num, full_num, medial_index ' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS . "\n" .
                 'WHERE DATE_FORMAT(added, "%Y-%m-%d")<="' . $this->get('medial_document_date') . '" AND type="' . $this->get('type') . '")' . "\n" .
                 'UNION' . "\n" .
                 '(SELECT id, num, full_num, medial_index ' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS . "`\n" .
                 'WHERE DATE_FORMAT(added, "%Y-%m-%d")<="' . $this->get('medial_document_date') . '" AND type="' . $this->get('type') . '")' . "\n" .
                 'ORDER BY id DESC' . "\n" .
                 'LIMIT 0,1';

        $result = $this->registry['db']->GetOne($query);

        return !empty($result);
    }

    /**
     * Construct the full num of a medial document
     *
     * @return bool
     */
    public function getDocMedialNum () {

        //get the counter assigned to the document type
        $this->getCounter();

        $additional_where = '';
        if (preg_match('#\[customer_num\]#', $this->counter->get('formula'))) {
            // if number is specified by customer
            $additional_where = ' AND customer="' . $this->get('customer') . '"';
        }
        //take the previous document
        $query = '(SELECT id, num, medial_index, added, DATE_FORMAT(added, "%Y-%m-%d") as formated_added_date ' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS . "\n" .
                 'WHERE DATE_FORMAT(added, "%Y-%m-%d")<="' . $this->get('medial_document_date_add') . '" AND type="' . $this->get('type') . '" AND num!=0' . $additional_where . ')' . "\n" .
                 'UNION' . "\n" .
                 '(SELECT id, num, medial_index, added, DATE_FORMAT(added, "%Y-%m-%d") as formated_added_date ' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS . "`\n" .
                 'WHERE DATE_FORMAT(added, "%Y-%m-%d")<="' . $this->get('medial_document_date_add') . '" AND type="' . $this->get('type') . '" AND num!=0' . $additional_where . ')' . "\n" .
                 'ORDER BY DATE_FORMAT(added, "%Y-%m-%d") DESC, id DESC' . "\n" .
                 'LIMIT 0,1';
        $result = $this->registry['db']->GetAll($query);

        if (empty($result)) {
            $this->registry['messages']->setError($this->i18n('error_no_previous_document_of_this_type' . ($additional_where ? '_and_customer' : '')));
            $this->registry['messages']->insertInSession($this->registry);
            $this->registry['db']->FailTrans();
            return false;
        }

        $previous_document = $result[0];
        if ($previous_document['formated_added_date']<$this->get('medial_document_date_add')) {
            $new_date = $this->get('medial_document_date_add') . ' 00:00:00';
        } else {
            $new_date = $previous_document['added'];
        }
        $this->set('medial_document_date_add', $new_date, true);

        //get the document type
        require_once 'documents.types.factory.php';
        $filters = array('where' => array('dt.id = ' . $this->get('type'),
                                          'dt.deleted IS NOT NULL'),
                         'sanitize' => true);
        $type = Documents_Types::searchOne($this->registry, $filters);

        //define some the counter's fomula components
        $formula = $this->counter->get('formula');
        $prefix = $this->counter->get('prefix');
        $delimiter = $this->counter->get('delimiter');
        $zeroes = $this->counter->get('leading_zeroes');
        $date_format = $this->counter->get('date_format');

        $previous_index = '';
        if ($previous_document['medial_index']) {
            $previous_index = $previous_document['medial_index'];
        }

        if ($previous_index) {
            if ($this->counter->get('medial_number_index') == 'number') {
                $current_index = (sprintf('%d', $previous_index)) + 1;
            } else if ($this->counter->get('medial_number_index') == 'latin_small_letters' || $this->counter->get('medial_number_index') == 'latin_capital_letters') {
                $current_index = chr(ord($previous_index)+1);
            } else if ($this->counter->get('medial_number_index') == 'cyrilic_small_letters' || $this->counter->get('medial_number_index') == 'cyrilic_capital_letters') {
                $current_index = mb_convert_encoding('&#' . intval($this->uniord($previous_document['medial_index'])+1) . ';', 'UTF-8', 'HTML-ENTITIES');
            }
        } else {
            if ($this->counter->get('medial_number_index') == 'number') {
                $current_index = 1;
            } else if ($this->counter->get('medial_number_index') == 'latin_capital_letters') {
                $current_index = 'A';
            } else if ($this->counter->get('medial_number_index') == 'latin_small_letters') {
                $current_index = 'a';
            } else if ($this->counter->get('medial_number_index') == 'cyrilic_capital_letters') {
                $current_index = $this->i18n('documents_medial_first_cyrilic_capital_letter');
            } else if ($this->counter->get('medial_number_index') == 'cyrilic_small_letters') {
                $current_index = $this->i18n('documents_medial_first_cyrilic_small_letter');
            }
        }

        $this->set('medial_index', $current_index, true);

        //create extender to expand the formula components
        $extender = new Extender;

        //set document number
        $num = sprintf('%0' . $zeroes . 'd', $previous_document['num']);
        $number_with_medial_index = '';
        if ($this->counter->get('medial_number_index_position') == 'prefix') {
            $number_with_medial_index = $current_index . $this->counter->get('medial_number_delimiter') . $num;
        } else {
            $number_with_medial_index = $num . $this->counter->get('medial_number_delimiter') . $current_index;
        }
        // formula can contain only one of the numbers
        if ($this->counter->get('customer_num')) {
            $extender->add('customer_num', $number_with_medial_index);
        } else {
            $extender->add('document_num', $number_with_medial_index);
        }

        if ($this->counter->get('prefix_used')) {
            //add this component to the extender
            $extender->add('prefix', $prefix);
        }

        if ($this->counter->get('customer_code') && $this->get('customer')) {
            //get customer code
            $query = 'SELECT code FROM ' . DB_TABLE_CUSTOMERS . ' WHERE id=' . $this->get('customer');
            $customer_code = $this->registry['db']->GetOne($query);

            //add this component to the extender
            $extender->add('customer_code', $customer_code);
        }

        if ($this->counter->get('project_code') && $this->get('project')) {
            //get project code
            $query = 'SELECT code FROM ' . DB_TABLE_PROJECTS . ' WHERE id=' . $this->get('project');
            $project_code = $this->registry['db']->GetOne($query);

            //add this component to the extender
            $extender->add('project_code', $project_code);
        }

        if ($this->counter->get('office_code') && $this->get('office')) {
            //get office code
            $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
            $office_code = $this->registry['db']->GetOne($query);

            //add this component to the extender
            $extender->add('office_code', $office_code);
        }

        if ($this->counter->get('user_code')) {
            //get user code
            //add this component to the extender
            $extender->add('user_code', $this->registry['currentUser']->get('code'));
        }

        if ($this->counter->get('document_type_code')) {
            //get document type code
            //add this component to the extender
            $extender->add('document_type_code', $type->get('code'));
        }

        if ($this->counter->get('document_added')) {
            //replace the date
            $date = ($this->get('medial_document_date_add')) ? General::strftime($date_format, strtotime($this->get('medial_document_date_add'))) : General::strftime($date_format);

            //add this component to the extender
            $extender->add('document_added', $date);
        }

        $full_num = $extender->expand($formula);
        if ($delimiter) {
            //remove repeating delimiters
            $full_num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $full_num);
            $full_num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $full_num);
            $full_num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $full_num);
        }

        // no number in formula
        if (! $this->counter->get('document_num') && !$this->counter->get('customer_num')) {
            $full_num .= $this->counter->get('medial_number_delimiter') . $current_index;
        }

        $this->set('num', $num, true);
        $this->set('full_num', $full_num, true);

        return true;
    }

    /**
     * Get documents office
     *
     * @return office id
     */
    public function getDocOffice() {
        $office = $this->get('office');
        if (is_numeric($office)) {
            $query = 'SELECT * FROM ' . DB_TABLE_OFFICES . "\n" .
                     'WHERE id=' . $office . "\n";
            $records = $this->registry['db']->GetAll($query);
            if (count($records)) {
                return $office;
            }
        }

        require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'));
        $new_office = Offices::buildModel($this->registry, $filters);
        $new_office->set('name', $office, true);
        $new_office->set('description', '', true);
        $new_office->set('id', '', true);
        $new_office->setRandomCode();

        $id = false;
        if ($new_office->save()) {
            $this->set('office', $new_office->get('id'), true);
            $id = $new_office->get('id');
        }

        unset($new_office);

        return $id;
    }

    /**
     * Get documents media
     *
     * @return media id
     */
    public function getDocMedia() {
        $media = $this->get('media');
        if (is_numeric($media)) {
            $query = 'SELECT * FROM ' . DB_TABLE_DOCUMENTS_MEDIAS . "\n" .
                     'WHERE id=' . $media . "\n";
            $records = $this->registry['db']->GetAll($query);
            if (count($records)) {
                return $media;
            }
        }

        require_once 'documents.medias.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'));
        $new_media = Documents_Medias::buildModel($this->registry, $filters);
        $new_media->set('name', $media, true);
        $new_media->set('description', '', true);
        $new_media->set('id', '', true);

        $id = false;
        if ($new_media->save()) {
            $this->set('media', $new_media->get('id'), true);
            $id = $new_media->get('id');
        }

        unset($new_media);

        return $id;
    }

    /**
     * Get all patterns variables - basic/system, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Document", "Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        $t_customer = array();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
            $pattern_format = ($pattern_format == 'docx2pdf') ? 'docx' : $pattern_format;
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize' => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                $this->set('contact_person_lastname', $contactperson->get('lastname'), true);
                $this->set('contact_person_salutation', $contactperson->get('salutation'), true);
                $this->set('contact_person_email', $contactperson->get('email'), true);
                $this->set('contact_person_phone', $contactperson->get('phone'), true);
                $this->set('contact_person_fax', $contactperson->get('fax'), true);
                $this->set('contact_person_gsm', $contactperson->get('gsm'), true);
                $this->set('contact_person_web', $contactperson->get('web'), true);
            }
        }

        //prepare customer branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize' => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }
        $translations = $this->getTranslations();
        $t_document = $t_a_vars = array();

        //save the previous registry lang
        $registry_lang_old = $this->registry['lang'];

        foreach ($translations as $t_lang) {
            if ($t_lang != $this->get('model_lang')) {
                $this->registry->set('lang', $t_lang, true);
                $this->registry['translater']->reloadFiles($t_lang);

                $filters = array('model_lang' => $t_lang,
                                 'where' => array('d.id = ' . $this->get('id')));
                $t_document[$t_lang] = Documents::searchOne($this->registry, $filters);
                $t_document[$t_lang]->getVarsForTemplate();
                $t_a_vars[$t_lang] = $t_document[$t_lang]->get('vars');
                if ($this->get('contact_person')) {
                    $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                               'c.subtype = \'contact\''),
                                              'sanitize' => true,
                                              'model_lang' => $t_lang);
                    $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                    if ($contactperson) {
                        $t_document[$t_lang]->set('contact_person_name',
                            $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                    }
                }
                if ($this->get('branch')) {
                    $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                             'c.subtype = \'branch\''),
                                            'sanitize' => true,
                                            'model_lang' => $t_lang);
                    $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                    if ($branch) {
                        $t_document[$t_lang]->set('branch_name', $branch->get('name'), true);
                    }
                }
            }
        }

        $this->registry->set('lang', $registry_lang_old, true);
        $this->registry['translater']->reloadFiles($registry_lang_old);

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Document') {
                //document variables
                    if (!$placeholder->get('multilang')) {
                        if ($placeholder->get('source') == 'num') {
                            $vars[$placeholder->get('varname')] = sprintf(PH_DOCUMENTS_NUM_FORMAT, $this->get('num'));
                        } elseif ($placeholder->get('source') == 'contact_person_salutation') {
                            $salutation = '';
                            $vars[$placeholder->get('varname') . '_formal'] = '';

                            if ($this->get('contact_person_salutation')) {
                                $salutation = $this->get('contact_person_salutation');
                                $vars[$placeholder->get('varname') . '_formal'] =
                                    $salutation ? ($salutation == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f')) : '';
                                $salutation = $this->i18n('salutation_vocative_' . $salutation) . ' ';
                            }
                            $salutation .= $this->get('contact_person_lastname') ?
                                $this->get('contact_person_lastname') : $this->get('contact_person_name');

                            $vars[$placeholder->get('varname')] = $salutation;
                        } else {
                            $vars[$placeholder->get('varname')] = $this->get($placeholder->get('source'));
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                $vars[ $t_document[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_document[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $this->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $this->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        if ($placeholder->get('source') == 'salutation') {
                            $salutation = '';
                            $vars[$placeholder->get('varname') . '_formal'] = '';

                            if ($customer->get('salutation')) {
                                $salutation = $customer->get('salutation');
                                $vars[$placeholder->get('varname') . '_formal'] =
                                    $salutation ? ($salutation == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f')) : '';
                                $salutation = $this->i18n('salutation_vocative_' . $salutation) . ' ';
                            }
                            $salutation .= $customer->get('lastname') ? $customer->get('lastname') : $customer->get('name');

                            $vars[$placeholder->get('varname')] = $salutation;
                        } else {
                            $vars[$placeholder->get('varname')] = $customer->get($placeholder->get('source'));
                        }
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_customer[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $customer->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $customer->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $user->get($placeholder->get('source'));
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_user[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $user->get($placeholder->get('source'));
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($placeholder->get('source'), '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $placeholder->get('source'));
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$placeholder->get('varname')] = $res;
                } else {
                    $vars[$placeholder->get('varname')] = $placeholder->get('source');
                }
            }
        }

        //prepare additional variables
        $this->getVarsForTemplate(false);

        //prepare BB variables
        $additional_vars = $this->get('vars');

        $bb_vars = $this->getBB(array('model_id' => $this->get('id')));

        $bb_elements = array();
        foreach ($additional_vars as $key => $var) {
            if (isset($var['bb']) && ($var['bb']) > 0 &&
                ($var['type'] == 'grouping' || $var['type'] == 'config' || $var['type'] == 'gt2')) {
                if ($var['type']!='gt2') {
                    $var['width'] = $var['width_print'];
                }
                $bb_elements[$var['id']] = $var;
                unset($additional_vars[$key]);
            }
        }

        // set additional vars back to model without the bb elements vars
        $this->set('vars', $additional_vars, true);

        $add_bb_vars = $this->getBBFields();
        foreach ($add_bb_vars as $bb_var_name => $bb_var_defs) {
            $add_bb_vars[$bb_var_name]['width'] = $bb_var_defs['width_print'];

            if ($bb_var_defs['type'] == 'file_upload') {
                if (!empty($bb_var_defs['value'])) {
                    foreach ($bb_var_defs['value'] as $bb_id => $file) {
                        // display thumbnail or file name
                        if (!empty($file) && is_object($file) && !$file->get('not_exist') && !$file->get('deleted_by')) {
                            $file = $this->getFileUploadForPrint($file, $bb_var_defs);
                        } else {
                            $file = '';
                        }
                        $add_bb_vars[$bb_var_name]['value'][$bb_id] = $file;
                    }
                }
            }
        }
        $this->set('add_bb_vars', $add_bb_vars, true);

        foreach ($bb_vars as $index => $var) {
            if (isset($bb_elements[$var['meta_id']])) {
                $bb_vars[$index] = $bb_elements[$var['meta_id']];
                $bb_vars[$index]['id'] = $var['id'];
                $bb_vars[$index]['meta_id'] = $var['meta_id'];
                $this->prepareBbVarValues($bb_vars[$index], $var['params'], true);

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (isset($bb_elements[$var['meta_id']]['names'])) {
                    foreach ($bb_elements[$var['meta_id']]['names'] as $bb_sub_elements) {
                        if (isset($bb_elements[$var['meta_id']][$bb_sub_elements]) && !empty($bb_elements[$var['meta_id']][$bb_sub_elements]['source'])) {
                            // parse the params
                            $source_fields = General::parseSettings($bb_elements[$var['meta_id']][$bb_sub_elements]['source']);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if (!empty($bb_vars[$index]['values'][$replaced_var])) {
                                    $bb_vars[$index]['values'][$bb_sub_elements] =
                                        str_replace('[a_' . $replaced_var . ']',
                                                    $bb_vars[$index]['values'][$bb_sub_elements],
                                                    $source_fields['replace_value']);

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if ($bb_vars[$index][$bb_sub_elements]['type'] == 'dropdown' || $bb_vars[$index][$bb_sub_elements]['type'] == 'radio' || $bb_vars[$index][$bb_sub_elements]['type'] == 'checkbox') {
                                        $bb_vars[$index][$bb_sub_elements]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        $this->set('bb_vars', $bb_vars, true);
        //end of preparation of BB variables


        $additional_vars = $this->get('vars');

        foreach ($additional_vars as $k => $a_var) {
            if (isset($a_var['type']) && !in_array($a_var['type'], array('bb', 'grouping', 'config', 'table', 'gt2'))) {
                if (!$a_var['multilang']) {
                    if (isset($a_var['value']) && $a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['options'] as $opt) {
                            if ($opt['option_value'] == $a_var['value']) {
                                $vars['a_' . $a_var['name']] =
                                    ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                break;
                            }
                        }
                    } elseif (isset($a_var['value']) && $a_var['value'] !== '' && is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['value'] as $val) {
                            foreach ($a_var['options'] as $opt) {
                                if ($opt['option_value'] == $val) {
                                    $vars['a_' . $a_var['name']][] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        }
                    } elseif ($a_var['type'] == 'textarea') {
                        $vars['a_' . $a_var['name']] = nl2br($a_var['value']);
                    } elseif ($a_var['type'] == 'file_upload') {
                        if (!empty($a_var['value']) && is_object($a_var['value']) && !$a_var['value']->get('not_exist') && !$a_var['value']->get('deleted_by')) {
                            if (isset($a_var['view_mode']) && $a_var['view_mode'] == 'thumbnail' && $a_var['value']->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['value']->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($a_var['thumb_width']) ? ("&maxwidth=" . $a_var['thumb_width']) : ''),
                                    (!empty($a_var['thumb_height']) ? ("&maxheight=" . $a_var['thumb_height']) : '')
                                );
                            } else {
                                $value = $a_var['value']->get('name');
                            }
                        } else {
                            $value = '';
                        }

                        $vars['a_' . $a_var['name']] = $value;
                    } else {
                        $vars['a_' . $a_var['name']] = isset($a_var['value']) ? $a_var['value'] : '';
                    }
                } else {
                    foreach ($translations as $t_lang) {
                        if ($t_lang != $this->get('model_lang')) {
                            if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                $t_a_vars[$t_lang][$k]['value'] = '';
                            }
                            if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                            && isset($t_a_vars[$t_lang][$k]['options'])) {
                                foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$t_document[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$t_document[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = nl2br($t_a_vars[$t_lang][$k]['value']);
                            } else {
                                $vars[$t_document[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = $t_a_vars[$t_lang][$k]['value'];
                            }
                        } else {
                            if ($a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                                foreach ($a_var['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                nl2br($a_var['value']);
                            } else {
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                $a_var['value'];
                            }
                        }
                    }
                }

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (!empty($a_var['source'])) {
                    // parse the params
                    $source_fields = General::parseSettings($a_var['source']);

                    // check if there is a var with the required name and replace it
                    if (!empty($source_fields['replace_value'])) {
                        if (!$a_var['multilang']) {
                            $vars['a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars['a_' . $a_var['name']], $source_fields['replace_value']);
                        } else {
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars[$this->get('model_lang') . '_a_' . $a_var['name']], $source_fields['replace_value']);
                        }
                    }
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'config' && isset($a_var['names']) && is_array($a_var['names'])) {
                //add containing variables to the list of replaceable variables
                $a_var['width'] = $a_var['width_print'];

                foreach ($a_var['names'] as $var_name) {
                    $ac_var = $a_var[$var_name];
                    $ac_var['value'] = isset($a_var['values'][$var_name]) ? $a_var['values'][$var_name] : '';

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a configurator (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } elseif ($ac_var['type'] == 'file_upload') {
                            if (!empty($ac_var['value']) && is_object($ac_var['value']) && !$ac_var['value']->get('not_exist') && !$ac_var['value']->get('deleted_by')) {
                                if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $ac_var['value']->isImage()) {
                                    $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                        $_SERVER['SCRIPT_NAME'],
                                        $this->registry['module_param'],
                                        rawurlencode(General::encrypt($ac_var['value']->get('id'), '_viewfile_', 'xtea')),
                                        (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                        (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                    );
                                } else {
                                    $value = $ac_var['value']->get('name');
                                }
                            } else {
                                $value = '';
                            }
                            $a_var['values'][$var_name] = $vars['a_'.$ac_var['name']] = $value;
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                $t_ac_var = $t_a_vars[$t_lang][$k][$var_name];
                                $t_ac_var['value'] = isset( $t_a_vars[$t_lang][$k]['values'][$var_name]) ?  $t_a_vars[$t_lang][$k]['values'][$var_name] : '';
                                if ($t_ac_var['value'] !== '' && !is_array($t_ac_var['value']) && isset($t_ac_var['options'])) {
                                    foreach ($t_ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $t_ac_var['value']) {
                                            $vars[$t_lang . '_a_'.$t_ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($t_ac_var['value'] !== '' && is_array($t_ac_var['value']) && isset($t_ac_var['options'])) {
                                    foreach ($t_ac_var['value'] as $val) {
                                        foreach ($t_ac_var['options'] as $opt) {
                                            if ($opt['option_value'] == $val) {
                                                $vars[$t_lang . '_a_'.$t_ac_var['name']][] =
                                                    ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                                break;
                                            }
                                        }
                                    }
                                    //special behaviour for checkboxes in a configurator (used as independent placeholders)
                                    if ($t_ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$t_ac_var['name']]) && is_array($vars['a_'.$t_ac_var['name']])) {
                                        if (!empty($t_ac_var['options_align']) && $t_ac_var['options_align'] == 'horizontal') {
                                            //separator is a space when options are aligned horizontally (options_align := horizontal)
                                            $vars[$t_lang . '_a_'.$t_ac_var['name']] = implode('&nbsp;', $vars['a_'.$t_ac_var['name']]);
                                        } else {
                                            //separator is a break when options are aligned vertically (default)
                                            $vars[$t_lang . '_a_'.$t_ac_var['name']] = implode('<br />', $vars['a_'.$t_ac_var['name']]);
                                        }
                                    }
                                } elseif ($t_ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_lang . '_a_'.$t_ac_var['name']] = nl2br($t_ac_var['value']);
                                } elseif ($t_ac_var['type'] == 'file_upload') {
                                    if (!empty($t_ac_var['value']) && is_object($t_ac_var['value']) && !$t_ac_var['value']->get('not_exist') && !$t_ac_var['value']->get('deleted_by')) {
                                        if (isset($t_ac_var['view_mode']) && $t_ac_var['view_mode'] == 'thumbnail' && $t_ac_var['value']->isImage()) {
                                            $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                                $_SERVER['SCRIPT_NAME'],
                                                $this->registry['module_param'],
                                                rawurlencode(General::encrypt($t_ac_var['value']->get('id'), '_viewfile_', 'xtea')),
                                                (!empty($t_ac_var['thumb_width']) ? ("&maxwidth=" . $t_ac_var['thumb_width']) : ''),
                                                (!empty($t_ac_var['thumb_height']) ? ("&maxheight=" . $t_ac_var['thumb_height']) : '')
                                            );
                                        } else {
                                            $value = $t_ac_var['value']->get('name');
                                        }
                                    } else {
                                        $value = '';
                                    }
                                    $a_var['values'][$var_name] = $vars['a_'.$t_ac_var['name']] = $value;
                                } else {
                                    $vars[$t_lang . '_a_'.$t_ac_var['name']] = $t_ac_var['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }
                }

                $configViewer = new Viewer($this->registry);
                if (isset($a_var['frankenstein'])) {
                    $a_var['frankenstein']['columns'] = @$a_var['columns'];
                    $configViewer->data['var'] = $a_var['frankenstein'];
                    $configViewer->data['pattern_id'] = $pattern_id;

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_vars.html');
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_configs.html');
                    $vars['a_'.@$a_var['name'].'_configs'] = $configViewer->fetch();
                } else {
                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'config_vars.html');
                    $configViewer->data['var'] = $a_var;
                    $configViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();
                }
                //ToDo - add multilang config variables

            } elseif (isset($a_var['type']) && $a_var['type'] == 'table' && isset($a_var['names']) && is_array($a_var['names'])) {
                // Skip this table if it's empty
                if (empty($a_var['values']) || count(array_filter($a_var['values'], function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                //add containing variables to the list of replaceable variables
                foreach ($a_var['names'] as $key => $var_name) {
                    $ac_var = $a_var[$var_name];
                    if ($ac_var['type'] == 'file_upload') {
                        if (!empty($a_var['values'][$key]) && is_object($a_var['values'][$key]) && !$a_var['values'][$key]->get('not_exist') && !$a_var['values'][$key]->get('deleted_by')) {
                            if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $a_var['values'][$key]->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['values'][$key]->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                    (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                );
                                $a_var['values'][$key] = $value;
                            } else {
                                $a_var['values'][$key] = $a_var['values'][$key]->get('name');
                                $value = $a_var['values'][$key];
                            }
                        } else {
                            $value = '';
                        }
                    } else {
                        $value = isset($a_var['values'][$key]) ? $a_var['values'][$key] : '';
                    }
                    $ac_var['value'] = $value;
                    $a_var['values'][$key] = $value;

                    $a_var['width'] = $a_var['width_print'];

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a table (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                $t_ac_var = $t_a_vars[$t_lang][$k][$var_name];
                                if ($t_ac_var['value'] !== '' && !is_array($t_ac_var['value']) && isset($t_ac_var['options'])) {
                                    foreach ($t_ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $t_ac_var['value']) {
                                            $vars[$t_lang . '_a_'.$t_ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($t_ac_var['value'] !== '' && is_array($t_ac_var['value']) && isset($t_ac_var['options'])) {
                                    foreach ($t_ac_var['value'] as $val) {
                                        foreach ($t_ac_var['options'] as $opt) {
                                            if ($opt['option_value'] == $val) {
                                                $vars[$t_lang . '_a_'.$t_ac_var['name']][] =
                                                    ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                                break;
                                            }
                                        }
                                    }
                                    //special behaviour for checkboxes in a configurator (used as independent placeholders)
                                    if ($t_ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$t_ac_var['name']]) && is_array($vars['a_'.$t_ac_var['name']])) {
                                        if (!empty($t_ac_var['options_align']) && $t_ac_var['options_align'] == 'horizontal') {
                                            //separator is a space when options are aligned horizontally (options_align := horizontal)
                                            $vars[$t_lang . '_a_'.$t_ac_var['name']] = implode('&nbsp;', $vars['a_'.$t_ac_var['name']]);
                                        } else {
                                            //separator is a break when options are aligned vertically (default)
                                            $vars[$t_lang . '_a_'.$t_ac_var['name']] = implode('<br />', $vars['a_'.$t_ac_var['name']]);
                                        }
                                    }
                                } elseif ($t_ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_lang . '_a_'.$t_ac_var['name']] = nl2br($t_ac_var['value']);
                                } elseif ($t_ac_var['type'] == 'file_upload') {
                                    if (!empty($t_ac_var['value']) && is_object($t_ac_var['value']) && !$t_ac_var['value']->get('not_exist') && !$t_ac_var['value']->get('deleted_by')) {
                                        if (isset($t_ac_var['view_mode']) && $t_ac_var['view_mode'] == 'thumbnail' && $t_ac_var['value']->isImage()) {
                                            $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                                $_SERVER['SCRIPT_NAME'],
                                                $this->registry['module_param'],
                                                rawurlencode(General::encrypt($t_ac_var['value']->get('id'), '_viewfile_', 'xtea')),
                                                (!empty($t_ac_var['thumb_width']) ? ("&maxwidth=" . $t_ac_var['thumb_width']) : ''),
                                                (!empty($t_ac_var['thumb_height']) ? ("&maxheight=" . $t_ac_var['thumb_height']) : '')
                                            );
                                        } else {
                                            $value = $t_ac_var['value']->get('name');
                                        }
                                    } else {
                                        $value = '';
                                    }
                                    $a_var['values'][$var_name] = $vars['a_'.$t_ac_var['name']] = $value;
                                } else {
                                    $vars[$t_lang . '_a_'.$t_ac_var['name']] = $t_ac_var['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    if (!empty($a_var['source'][$var_name])) {
                        // parse the params
                        $source_fields = General::parseSettings($a_var['source'][$var_name]);

                        // checks if 'replace_value' param is set
                        if (!empty($source_fields['replace_value'])) {
                            // find the replacement variable name
                            $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                            // check if there is a var with the required name and replace it
                            if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                $additional_var_name = '[a_' . $replaced_var . ']';
                                $column_key_idx = array_search($replaced_var, $a_var['names']);
                                foreach ($a_var['values'] as $col_index => $col_value) {
                                    if ($col_index == $column_key_idx) {
                                        $new_value = str_replace($additional_var_name, $col_value, $source_fields['replace_value']);
                                        $a_var['values'][$col_index] = $new_value;
                                        $a_var[$replaced_var]['value'] = $new_value;
                                        if (!$ac_var['multilang']) {
                                            $vars['a_' . $replaced_var] = $new_value;
                                        } else {
                                            $vars[$this->get('model_lang') . '_a_' . $replaced_var] = $new_value;
                                        }

                                        // sets the option to overwrite a value and use it
                                        // but not searching for its corresponding label
                                        if ($ac_var['type'] == 'dropdown' || $ac_var['type'] == 'radio' || $ac_var['type'] == 'checkbox') {
                                            $a_var[$var_name]['overwrite_value'] = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                $template_file = 'table_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if (!empty($a_var['multilang'])) {
                    foreach ($translations as $t_lang) {
                        $tableViewer = new Viewer($this->registry);
                        $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $tableViewer->data['pattern_id'] = $pattern_id;
                        $replacement = $tableViewer->fetch();
                        if ($pattern_format == 'docx') {
                            //remove whitespaces
                            $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                        }
                        if ($t_lang != $this->get('model_lang')) {
                            $tableViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_document[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        } else {
                            $tableViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        }
                    }
                } else {
                    $tableViewer = new Viewer($this->registry);
                    $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                    $tableViewer->data['var'] = $a_var;
                    $tableViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $tableViewer->fetch();
                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_'.$a_var['name']] = $replacement;
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'grouping') {
                // Skip this grouping table if it has no rows or it has only one row which is empty
                if (empty($a_var['values']) || count($a_var['values']) == 1 && count(array_filter(reset($a_var['values']), function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                $template_file = 'grouping_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $groupingViewer = new Viewer($this->registry);
                        $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $groupingViewer->data['pattern_id'] = $pattern_id;
                        if ($t_lang != $this->get('model_lang')) {
                            $groupingViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_document[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                            = $groupingViewer->fetch();
                        } else {
                            $groupingViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                        }
                    }
                } else {
                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    foreach ($a_var['names'] as $idx_var => $var_name) {
                        $a_var['width'] = $a_var['width_print'];

                        if (!empty($a_var['source'][$var_name])) {
                            // parse the params
                            $source_fields = General::parseSettings($a_var['source'][$var_name]);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                    $column_key_idx = array_search($replaced_var, $a_var['names']);
                                    foreach ($a_var['values'] as $row => $row_content) {
                                        $a_var['values'][$row][$column_key_idx] =
                                            str_replace('[a_' . $replaced_var . ']', $row_content[$column_key_idx], $source_fields['replace_value']);
                                    }

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if (in_array($a_var['types'][$column_key_idx], array('dropdown', 'radio', 'checkbox'))) {
                                        $a_var[$var_name]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }

                    //check if there are empty rows in the table and remove them
                    if (isset($a_var['values']) && is_array($a_var['values'])) {
                        $row_is_empty = true;
                        foreach ($a_var['values'] as $row_index => $row_content) {
                            foreach ($row_content as $cell_index => $cell_content) {
                                if ($cell_content || $cell_content === '0') $row_is_empty = false;
                            }
                            if ($row_is_empty) {
                                unset($a_var['values'][$row_index]);
                            } else {
                                $row_is_empty = true;
                            }
                        }
                    }

                    foreach ($a_var['types'] as $key_column => $var_type) {
                        if ($var_type == 'file_upload') {
                            $group_var_name = $a_var['names'][$key_column];
                            if (!empty($a_var['values'])) {
                                foreach ($a_var['values'] as $row => $row_values) {
                                    if (!empty($row_values[$key_column]) && is_object($row_values[$key_column]) && !$row_values[$key_column]->get('not_exist') && !$row_values[$key_column]->get('deleted_by')) {
                                        $file = $row_values[$key_column];
                                        if (isset($a_var[$group_var_name]['view_mode']) && $a_var[$group_var_name]['view_mode'] == 'thumbnail' && $row_values[$key_column]->isImage()) {
                                            $file_upload_properties = $a_var[$group_var_name];
                                            $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                                $_SERVER['SCRIPT_NAME'],
                                                $this->registry['module_param'],
                                                rawurlencode(General::encrypt($file->get('id'), '_viewfile_', 'xtea')),
                                                (!empty($file_upload_properties['thumb_width']) ? ("&maxwidth=" . $file_upload_properties['thumb_width']) : ''),
                                                (!empty($file_upload_properties['thumb_height']) ? ("&maxheight=" . $file_upload_properties['thumb_height']) : '')
                                            );
                                            $a_var['values'][$row][$key_column] = $value;
                                        } else {
                                            $a_var['values'][$row][$key_column] = $file->get('name');
                                        }
                                    } else {
                                        $a_var['values'][$row][$key_column] = '';
                                    }
                                }
                            }
                        }
                    }

                    $groupingViewer->data['var'] = $a_var;
                    $groupingViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $groupingViewer->fetch();

                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_' . $a_var['name']] = $replacement;
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'gt2') {
                // Skip the gt2 if it's empty (GT2 row could not be saved with empty row. The empty GT2 table has an empty array the $a_var['values'][0])
                if (empty($a_var['values']) || count(array_filter($a_var['values'])) == 0) {
                    continue;
                }

                //get print settings for the 2nd type grouping table
                $print_properties = $this->getGT2PrintSettings($pattern_id);

                if ($a_var['multilang']) {
                    $a_var_idx = array();
                    $a_var_name = $a_var['name'];
                    foreach ($translations as $t_lang) {
                        if ($t_lang != $this->get('model_lang')) {
                            // get index of gt2 variable in each set of variables
                            $a_var_idx[$t_lang] = array_keys(array_filter($t_a_vars[$t_lang], function($tv) use ($a_var_name) { return $tv['type'] == 'gt2' && $tv['name'] == $a_var_name; }));
                            if ($a_var_idx[$t_lang]) {
                                $a_var_idx[$t_lang] = reset($a_var_idx[$t_lang]);
                            } else {
                                $a_var_idx[$t_lang] = false;
                                continue;
                            }
                            $table = $t_a_vars[$t_lang][$a_var_idx[$t_lang]];
                        } else {
                            $table = $a_var;
                        }

                        //prepare files in GT2
                        if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                            foreach ($table['values'] as $ridx => $row) {
                                foreach ($row as $rkey => $rval) {
                                    if (!empty($rval) && is_object($rval)) {
                                        $file = $rval;
                                        if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                            $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                                        } else {
                                            $file = '';
                                        }
                                        $table['values'][$ridx][$rkey] = $file;
                                    }
                                }
                            }
                        }

                        $table_ordered = $table;
                        $table_ordered['vars'] = array();
                        $styles_for_template = array();
                        $styles_for_docx_template = array();

                        foreach ($print_properties as $key => $property) {
                            // style properties
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];

                                if ($pattern_format == 'docx') {
                                    //prepare styles for DOCX word template
                                    $style_rows = preg_split('(\n|\r)', $property['style']);
                                    $styles = array('hide' => false);
                                    foreach ($style_rows as $sr) {
                                        list($attribute, $attribute_value) = preg_split('#\s*:\s*#', $sr);
                                        $attribute_value = str_replace('!important', '', $attribute_value);
                                        $attribute_value = str_replace(';', '', $attribute_value);
                                        switch ($attribute) {
                                            case 'font-family':
                                                //get the first font in the family
                                                $styles['font'] = preg_replace('#([^,]),.*#', '\1', $attribute_value);
                                                break;
                                            case 'font-size':
                                                //convert pixels to points (it's all in the modifier convert2docx_fontsize)
                                                $styles['size'] = $attribute_value;
                                                break;
                                            case 'color':
                                                //remove the hash for hex number
                                                $styles['color'] = str_replace('#', '', $attribute_value);
                                                break;
                                            case 'font-style':
                                                $styles['italic'] = strpos($attribute_value, 'italic') !== false;
                                                break;
                                            case 'font-weight':
                                                $styles['bold'] = strpos($attribute_value, 'bold') !== false;
                                                break;
                                            case 'width':
                                                // (it's all in the modifier convert2docx_width)
                                                $styles['width'] = $attribute_value;
                                                break;
                                            case 'text-align':
                                                $styles['align'] = $attribute_value;
                                                break;
                                            case 'display':
                                                $styles['hide'] = true;
                                                break;
                                        }
                                    }
                                    $styles_for_docx_template[$key] = $styles;
                                }
                            }
                            // label for table caption
                            if ($key == 'var_' . $table['id']) {
                                if (isset($property['labels'][$t_lang])) {
                                    $table_ordered['label'] = $property['labels'][$t_lang];
                                }
                                continue;
                            }
                            foreach ($table['vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    $table_ordered['vars'][$idx] = $var;
                                    // label for field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                                        } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                            unset($table_ordered['vars'][$idx]['agregate']);
                                        }
                                    }
                                    continue 2;
                                }
                            }
                            foreach ($table['plain_vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    // label for total field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    continue 2;
                                }
                            }
                        }

                        // calculate aggregates in GT2 table
                        $table_ordered = $this->calculateGT2Agregates($table_ordered);

                        if ($pattern_format != 'docx') {
                            //prepare GT2 for HTML/PDF
                            $groupingViewer = new Viewer($this->registry);
                            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');
                            $groupingViewer->data['styles'] = $styles_for_template;
                            $groupingViewer->data['table'] = $table_ordered;
                            $groupingViewer->data['pattern_id'] = $pattern_id;
                            $vars[$t_lang . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                        } else {
                            //prepare GT2 for DOCX
                            $groupingViewer = new Viewer($this->registry);
                            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars_docx.html');
                            $groupingViewer->data['styles'] = $styles_for_docx_template;
                            $groupingViewer->data['table'] = $table_ordered;
                            $groupingViewer->data['pattern_id'] = $pattern_id;
                            $replacement = $groupingViewer->fetch();

                            //remove whitespaces
                            $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                            $vars[$t_lang . '_a_' . $a_var['name']] = $replacement;
                        }
                    }
                }

                //get the plain vars of the GT2
                $plain_values = $a_var['plain_values'];
                foreach ($plain_values as $plain_var => $plain_value) {
                    switch ($plain_var) {
                    case 'total_no_vat_reason_text':
                        if ($a_var['plain_vars'][$plain_var]['multilang']) {
                            foreach ($translations as $t_lang) {
                                $vars[$t_lang . '_a_' . $plain_var] =
                                    $a_var['multilang'] && $t_lang != $this->get('model_lang') ?
                                    ($a_var_idx[$t_lang] !== false ? $t_a_vars[$t_lang][$a_var_idx[$t_lang]]['plain_values'][$plain_var] : '') :
                                    $plain_value;
                            }
                        } else {
                            $vars['a_' . $plain_var] = $plain_value;
                        }
                        break;
                    case 'total_vat_rate':
                        $vars['a_' . $plain_var] = $plain_value . ' %';
                        break;
                    default:
                        $vars['a_' . $plain_var] = $plain_value;
                    }
                }

            } elseif (isset($a_var['type']) && $a_var['type'] == 'bb') {
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $bbViewer = new Viewer($this->registry);
                        $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');
                        //get print settings for the 2nd type grouping table
                        $print_properties = $this->getGT2PrintSettings($pattern_id);

                        $styles_for_template = array();

                        foreach ($print_properties as $key => $property) {
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];
                            }
                        }

                        $bbViewer->data['styles'] = $styles_for_template;
                        $bbViewer->data['pattern_id'] = $pattern_id;
                        $bbViewer->data['document'] = $this;

                        // complete the labels from the printing properties
                        $new_bb_vars = $this->get('bb_vars');
                        foreach ($new_bb_vars as $bb_idx => $bb_details) {
                            // if the variable is GT2
                            if ($bb_details['type'] == 'gt2') {
                                foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    if (array_key_exists($print_properties_key, $print_properties)) {
                                        $property = $print_properties[$print_properties_key];
                                        // label
                                        if (!empty($property['labels'][$t_lang])) {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = $property['labels'][$t_lang];
                                        }
                                        // aggregates
                                        if (isset($property['agregate'])) {
                                            if ($property['agregate'] != 'none') {
                                                $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                            } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                                unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                            }
                                        }
                                    }
                                }
                                foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    // label
                                    if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'][$t_lang])) {
                                        $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = $print_properties[$print_properties_key]['labels'][$t_lang];
                                    }
                                }
                                // calculate aggregates in GT2 table
                                $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                            }
                        }
                        $bbViewer->data['document']->set('bb_vars', $new_bb_vars, true);

                        if ($t_lang != $this->get('model_lang')) {
                            $bbViewer->data['var'] = @$t_a_vars[$t_lang][$k];
                            $vars[$t_document[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        } else {
                            $bbViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        }
                    }
                } else {
                    $bbViewer = new Viewer($this->registry);
                    $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');
                    $bbViewer->data['document'] = $this;
                    $bbViewer->data['var'] = $a_var;

                    //get print settings for the 2nd type grouping table
                    $print_properties = $this->getGT2PrintSettings($pattern_id);

                    // complete the labels from the printing properties
                    $new_bb_vars = $this->get('bb_vars');
                    foreach ($new_bb_vars as $bb_idx => $bb_details) {
                        // if the variable is GT2
                        if ($bb_details['type'] == 'gt2') {
                            foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                if (array_key_exists($print_properties_key, $print_properties)) {
                                    $property = $print_properties[$print_properties_key];
                                    // label
                                    if (!empty($property['labels'])) {
                                        $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = reset($property['labels']);
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                        } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                            unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                        }
                                    }
                                }
                            }
                            foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                // label
                                if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'])) {
                                    $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = reset($print_properties[$print_properties_key]['labels']);
                                }
                            }
                            // calculate aggregates in GT2 table
                            $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                        }
                    }
                    $bbViewer->data['document']->set('bb_vars', $new_bb_vars, true);

                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                    }
                    $bbViewer->data['styles'] = $styles_for_template;
                    $bbViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_' . $a_var['name']] = $bbViewer->fetch();
                }
            }
        }

        return $vars;
    }

    public function getChildrenTree($parent, $level, &$tree) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // retrieve all children of $parent
        $query = 'SELECT dr.parent_id, dr.origin, parent_model_name AS model' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                 'WHERE dr.link_to = "' . $parent . '" AND dr.link_to_model_name = "Document"' . "\n" .
                 'UNION' . "\n" .
                 'SELECT dr.parent_id, dr.origin, parent_model_name AS model' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . '` AS dr' . "\n" .
                 'WHERE dr.link_to = "' . $parent . '" AND dr.link_to_model_name = "Document"';
        $records = $this->registry['db']->GetAll($query);

        // retrieve all children of $parent
        $query = 'SELECT parent_id, parent_model_name as model, "transformed" as origin' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE link_to = "' . $parent . '" AND link_to_model_name = "Document" AND parent_model_name NOT IN ("Document", "Contract")';

        $records = array_merge($records, $this->registry['db']->GetAll($query));

        // display each child
        foreach ($records as $k => $rec) {
            $p_model = $rec['model'];
            $tree[] = array('id' => $rec['parent_id'], 'level' => $level,
                            'origin' => $rec['origin'], 'model' => $rec['model']);
            if ($rec['model'] == 'Document' && $level < PH_MAX_TREE_LEVEL) {
                $this->getChildrenTree($rec['parent_id'], $level+1, $tree);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
    }

    public function getParentsTree($child, $level, &$tree) {
        // retrieve all parents of child
        $query = 'SELECT dr.link_to, dr.link_to_model_name AS model, dr.origin' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                 'WHERE dr.parent_id = "' . $child . '" AND dr.parent_model_name = "Document"' . "\n" .
                 '  AND NOT (dr.link_to = "' . $child . '" AND dr.link_to_model_name = "Document") AND dr.link_to_model_name != "Finance_Invoices_Template"' . "\n" .
                 'UNION' . "\n" .
                 'SELECT dr.link_to, dr.link_to_model_name AS model, dr.origin' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . '` AS dr ' . "\n" .
                 'WHERE dr.parent_id = "' . $child . '" AND dr.parent_model_name = "Document"' . "\n" .
                 '  AND NOT (dr.link_to = "' . $child . '" AND dr.link_to_model_name = "Document") AND dr.link_to_model_name != "Finance_Invoices_Template"';

        $records = $this->registry['db']->GetAll($query);

        // display each parent
        foreach ($records as $k => $rec) {
            $tree[] = array('id' => $rec['link_to'], 'level' => $level,
                            'origin' => $rec['origin'], 'model' => $rec['model']);
            if ($rec['model'] == 'Document' && $level < PH_MAX_TREE_LEVEL) {
                $this->getParentsTree($rec['link_to'], $level+1, $tree);
            } elseif ($rec['model'] == 'Contract' && $level < PH_MAX_TREE_LEVEL) {
                require_once PH_MODULES_DIR . 'contracts/models/contracts.model.php';
                $c = new Contract($this->registry);
                $c->getParentsTree($rec['link_to'], $level+1, $tree);
                unset($c);
            }
        }
    }

    /**
     * Get records that document has been transformed to
     *
     * @return array - records data
     */
    public function getTransformedTo() {
        // retrieve transformed to documents
        $query = 'SELECT dr.parent_id, parent_model_name AS model,' . "\n" .
                 '    IF(d.id IS NOT NULL AND d.id != "", d.type, ad.type) AS type,' . "\n" .
                 '    0 AS archive' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                 '    ON dr.parent_id = d.id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS ad' . "\n" .
                 '    ON dr.parent_id = ad.id' . "\n" .
                 'WHERE dr.link_to = "' . $this->get('id') . '" AND dr.link_to_model_name = "Document"' . "\n" .
                 '    AND dr.parent_model_name = "Document" AND dr.origin = "transformed"' . "\n" .
                 'UNION' . "\n" .
                 'SELECT dr.parent_id, parent_model_name AS model,' . "\n" .
                 '    IF(d.id IS NOT NULL AND d.id != "", d.type, ad.type) AS type,' . "\n" .
                 '    1 AS archive' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . '` AS dr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                 '    ON dr.parent_id = d.id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS ad' . "\n" .
                 '    ON dr.parent_id = ad.id' . "\n" .
                 'WHERE dr.link_to = "' . $this->get('id') . '" AND dr.link_to_model_name = "Document"' . "\n" .
                 '    AND dr.parent_model_name = "Document" AND dr.origin = "transformed"';
        $records = $this->registry['db']->GetAll($query);

        // retrieve transformed to contracts
        $query = 'SELECT dr.parent_id, parent_model_name AS model,' . "\n" .
                 '    c.type' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                 '    ON dr.parent_id = c.id' . "\n" .
                 'WHERE dr.link_to = "' . $this->get('id') . '" AND dr.link_to_model_name = "Document"' . "\n" .
                 '    AND dr.parent_model_name = "Contract" AND dr.origin = "transformed"' . "\n" .
                 'UNION' . "\n" .
                 'SELECT dr.parent_id, parent_model_name AS model,' . "\n" .
                 '    c.type' . "\n" .
                 'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . '` AS dr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                 '    ON dr.parent_id = c.id' . "\n" .
                 'WHERE dr.link_to = "' . $this->get('id') . '" AND dr.link_to_model_name = "Document"' . "\n" .
                 '    AND dr.parent_model_name = "Contract" AND dr.origin = "transformed"';
        $records = array_merge($records, $this->registry['db']->GetAll($query));

        // retrieve all incomes reasons
        $query = 'SELECT frr.parent_id, frr.parent_model_name as model, fir.type' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '    ON frr.parent_id = fir.id' . "\n" .
                 'WHERE link_to = "' . $this->get('id') . '" AND link_to_model_name = "Document" AND parent_model_name="Finance_Incomes_Reason"';

        $records = array_merge($records, $this->registry['db']->GetAll($query));

        // retrieve all expenses reasons
        $query = 'SELECT frr.parent_id, frr.parent_model_name as model, fer.type' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                 '    ON frr.parent_id = fer.id' . "\n" .
                 'WHERE link_to = "' . $this->get('id') . '" AND link_to_model_name = "Document" AND parent_model_name="Finance_Expenses_Reason"';

        $records = array_merge($records, $this->registry['db']->GetAll($query));

        return $records;
    }

    /**
     * Function for taking the child/parent documents from first level
     * It is used from custom outlooks and front page dashlets
     *
     * @param string $relation - relation type of other records, may be 'child' or 'parent'
     * @return array $document_relations - all related documents from first level
     */
    public function getFirstLevelRelatedDocuments($relation) {
        $this->unsanitize();

        // retrieve all children from first level or retrieve parent + related documents
        $query = 'SELECT ' . (($relation == 'child') ? ('dr.parent_id') : ('dr.link_to')) . ' AS related_to, dr.origin ' . "\n" .
                 '  FROM `' . DB_TABLE_DOCUMENTS_RELATIVES . '` AS dr ' . "\n" .
                 '  WHERE dr.' . ($relation == 'child' ? 'link_to' : 'parent_id') . ' = "' . $this->get('id') . '"' .
                 '  AND dr.parent_model_name = "Document"' .
                 '  AND dr.link_to_model_name = "Document"';

        // get both archived and non-archived relatives of current document
        $query = implode("\n" . 'UNION' . "\n",
                         array($query, preg_replace('#`([^`\s]+)`#', '`archive_$1`', $query)));

        $records = $this->registry['db']->GetAll($query);

        $related_doc_ids = array();

        foreach ($records as $record) {
            if (!in_array($record['related_to'], $related_doc_ids)) {
                $related_doc_ids[] = $record['related_to'];
            }
        }

        $document_relations = array();
        if (!empty($related_doc_ids)) {
            $sql = array();
            $sql['select'] = 'SELECT d.id, d.type, d.full_num, dt.direction, di18n.name, d.archived_by ';
            $sql['from']   = 'FROM `' . DB_TABLE_DOCUMENTS . '` AS d' . "\n" .
                             'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                             '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' .
                             'LEFT JOIN `' . DB_TABLE_DOCUMENTS_I18N . '` AS di18n' . "\n" .
                             '  ON (di18n.parent_id=d.id AND di18n.lang="' . $this->get('model_lang') . '")' . "\n";
            $where = array();
            $where[] = 'd.id IN (' . implode(', ', $related_doc_ids) . ')';
            $where[] = 'd.deleted IS NOT NULL';

            $sql['where'] = 'WHERE ' . implode(' AND ', $where);

            //search basic details with current lang parameters
            $query = implode("\n", $sql);

            // get both archived and non-archived relatives of current document
            $query = implode("\n" . 'UNION' . "\n",
                             array($query, preg_replace('#`([^`\s]+)`#', '`archive_$1`', $query)));

            $document_relations = $this->registry['db']->GetAll($query);
        }
        $this->sanitize();

        return $document_relations;
    }

    /**
     * Gets the sum of the reported time in the timesheets
     *
     * @param bool $force - force to always get the reported time from the database
     * @return int $minutes - the total of minutes reported in the timesheets
     */
    public function getTimesheetTime($force = false) {

        // If the model has no registry
        $sanitize_after = false;
        if ($this->isSanitized()) {
            // Set the registry to the model
            $this->unsanitize();
            $sanitize_after = true;
        }

        if ($this->isDefined('timesheet_time') && !$force) {
            $minutes = $this->get('timesheet_time');
        } else {
            $minutes = 0;

            // If this model has id
            if ($this->get('id')) {
                $system_task_id = Documents::getSystemTask($this->registry, $this->get('id'));

                if ($system_task_id) {
                    $query = 'SELECT SUM(duration) FROM ' . DB_TABLE_TASKS_TIMESHEETS .
                             ' WHERE task_id="' . $system_task_id . '"';
                    if ($this->get('event_id')) {
                        $query .= ' AND event_id=' . $this->get('event_id');
                    }
                    $minutes = $this->registry['db']->GetOne($query);
                }

                $this->set('system_task_id', $system_task_id, true);
            }

            $this->set('timesheet_time', $minutes, true);
        }

        // prepare formatted value
        $this->set('timesheet_time_formatted', General::minutes2Human($this->registry, $minutes, false, true), true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $minutes;
    }

    /**
     * Sends notification e-mail.
     *
     * @param string $template - template name
     * @param string $email - recipient e-mail
     * @param string $user_name - recipient name and last name
     */
    function sendNotification($template, $email, $user_name) {
        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //send email
        $mailer = new Mailer($this->registry, $template, $this);
        $mailer->placeholder->add('document_id', $this->get('id'));
        $mailer->placeholder->add('document_num', $this->getDocFullNum());
        $mailer->placeholder->add('document_name', $this->get('name'));
        $mailer->placeholder->add('document_type', $this->get('type_name'));
        $mailer->placeholder->add('document_added_by', $this->get('added_by_name'));
        $mailer->placeholder->add('customer_name', $this->getCustomerName());
        $mailer->placeholder->add('user_name', $user_name);

        $document_view_url = sprintf('%s/index.php?%s=documents&documents=view&view=%d',
                                     $this->registry['config']->getParam('crontab', 'base_host'),
                                     $this->registry['module_param'], $this->get('id'));
        $mailer->placeholder->add('document_view_url', $document_view_url);

        $add_comment_url = sprintf('%s/index.php?%s=documents&documents=communications&communications=%d&communication_type=comments#comments_add_form',
                                   $this->registry['config']->getParam('crontab', 'base_host'),
                                   $this->registry['module_param'], $this->get('id'));
        $mailer->placeholder->add('document_add_comment_url', $add_comment_url);

        $mailer->placeholder->add('to_email', $email);
        $mailer->placeholder->add('user_lastname', $this->registry['currentUser']->get('lastname'));
        $mailer->placeholder->add('user_firstname', $this->registry['currentUser']->get('firstname'));

        $mailer->template['model_name'] = $this->modelName;
        $mailer->template['model_id'] = $this->get('id');

        $result = $mailer->send();
        if (!@in_array($email, $result['erred'])) {
            $notify_for = $this->i18n('documents_' . $template . '_notify');
            if ($this->registry['sent_email'] != 1) {
                $this->registry['messages']->setMessage($this->i18n('documents_email_sent_success', array($notify_for)));
                $this->registry->set('sent_email', 1, true);
            }
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            if ($this->registry['err_sent_email'] != 1) {
                $this->registry['messages']->setWarning($this->i18n('error_documents_send_email', array($notify_for)), '', 10);
                $this->registry->set('err_sent_email', 1, true);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Gets name of customer of document - from property if set, from db otherwise.
     */
    public function getCustomerName() {
        if ($this->get('customer_name')) {
            return $this->get('customer_name');
        }

        if ($this->get('customer')) {
            $customer_id = $this->get('customer');
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name') . (!$customer->get('is_company') ? ' ' . $customer->get('lastname') : '');
                $this->set('customer_name', $customer_name, true);
                return $this->get('customer_name');
            }
        }

        return false;
    }

    /**
     * Translates some i18n fields
     *
     * @return bool - result of the operation
     */
    public function getDefaultTranslations() {
        $db = $this->registry['db'];
        $model_lang = $this->registry['lang'];

        //select clause
        $sql['select'] = 'SELECT d.*, di18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  dti18n.name as type_name, dt.direction as direction, ' . "\n" .
                         '  gi18n.name as group_name, ' . "\n" .
                         '  dm.name as media_name, pi18n.name as project_name ' . "\n";
       //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                        //relate to document types
                       'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                       '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                       '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to document group
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (d.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to customers
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to medias
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_MEDIAS . ' AS dm' . "\n" .
                       '  ON (d.media=dm.id AND dm.lang="' . $model_lang . '")' . "\n" .
                        //relate to document types i18n
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                       '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to document types i18n
                       'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                       '  ON (d.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        $sql['where'] = 'WHERE d.id=' . $this->get('id') . "\n";

        $query = implode("\n", $sql);
        $records = $db->GetRow($query);

        $this->set('default_translations', $records, true);

        return $records;
    }

    /**
     * get num multitransform models
     *
     * @return int - result of the operation
     */
    public function getTransformMultiCount($new_type) {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $query = 'SELECT multi_index, count(*) as multi_count FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') .
                 ' AND dr.link_to_model_name="Document"' .
                 ' AND d.type=' . $new_type . "\n" .
                 ' AND multi_index > 0' .
                 ' GROUP BY multi_index';
        $records = $this->registry['db']->GetAssoc($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;

    }

    /**
     * get num multitransform models
     *
     * @return int - result of the operation
     */
    public function getTransformMultiGroupCount($new_type) {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $query = 'SELECT group_index, count(*) as group_count FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') .
                 ' AND dr.link_to_model_name="Document"' .
                 ' AND d.type=' . $new_type . "\n" .
                 ' AND group_index != ""' .
                 ' GROUP BY group_index';
        $records = $this->registry['db']->GetAssoc($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;

    }

    /**
     * get num grouptransform models
     *
     * @return int - result of the operation
     */
    public function getGroupTransformCount($new_type, $group_index) {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $query = 'SELECT count(*) FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d ' . "\n" .
                 '  ON (dr.parent_id=d.id) ' . "\n" .
                 'WHERE dr.link_to=' . $this->get('id') .
                 ' AND dr.link_to_model_name="Document"' .
                 ' AND d.type=' . $new_type . "\n" .
                 ' AND group_index="' . General::slashesEscape($group_index) . '"' . "\n";
        $record = $this->registry['db']->GetOne($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * get counter for this document
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            // check if the model is sanitized
            if ($this->sanitized) {
                $sanitized = true;
                $this->unsanitize();
            } else {
                $sanitized = false;
            }

            //get directly the counter id and and type code
            $query = 'SELECT counter, code FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' WHERE id = \'' . $this->get('type') . '\'';
            $type_data = $this->registry['db']->GetRow($query);

            if ($type_data) {
                //the type code might be used by the counter
                $this->set('type_code', $type_data['code'], true);

                require_once 'documents.counters.factory.php';
                $filters = array('where' => array('dc.id = ' . $type_data['counter'],
                                                  'dc.deleted IS NOT NULL'));
                $this->counter = Documents_Counters::searchOne($this->registry, $filters);
            }

            if ($sanitized) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * sets number to the document
     *
     * @return int - result of the operation
     */
    public function setNumber() {
        $this->set('added', '', true);

        $set['num']      = sprintf("num=%d", ($this->getDocNum()));
        $set['full_num'] = sprintf("full_num='%s'", $this->getDocFullNum());
        $this->counter->increment();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $result = $this->registry['db']->Execute($query1);

        //update the i18n variables and replace the placeholders in them with values (name, notes, description)
        $this->updateI18N('add');

        return $result;
    }

    /**
     * Convert franky to grouping table
     *
     * @param array $source_additional_vars - additional vars of model
     * @return array - additional vars of model after modification
     */
    public function convertFrankyToGrouping($source_additional_vars) {
        if (is_array($this->get('config_vars')) && count($this->get('config_vars'))) {
            //copy franky variables in orig_vars
            foreach ($this->get('config_vars') as $var) {
                // frankenstein
                if (!empty($var['source'])) {
                    // parse the params
                    $show_fields = General::parseSettings($var['source'], '#^fields$#');
                    if (!empty($show_fields)) {
                        $show_fields = reset($show_fields);
                        $show_fields = preg_split('/\s*,\s*/', $show_fields);
                        $franky = $this->getFrankyValues(array('fields' => $show_fields, 'config_num' => $var['configurator']));
                        foreach ($franky as $fr) {
                            $new_index = 0;
                            if (isset($fr['value'])) {
                                foreach ($fr['value'] as $conf_i => $conf_val) {
                                    $new_index++;
                                    unset($fr['value'][$conf_i]);
                                    $fr['value'][$new_index] = $conf_val;
                                    $fr['t_ids'][$new_index] = $conf_i;
                                }
                                $fr['model_id'] = $this->get('id');
                                $fr['grouping'] = $fr['configurator'];
                                $source_additional_vars[$fr['name']] = $fr;
                            }
                        }
                    }
                }
            }
        }

        return $source_additional_vars;
    }

    /**
     * create system tasks if the document's type requires it
     */
    public function createSystemTask () {
        require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
        $task = Tasks::buildModel($this->registry);
        $task->set('id', null, true);
        $task->set('name', ($this->i18n('documents_system_task_type') . ' ' . $this->get('name')), true);
        if ($this->get('customer')) {
            $task->set('customer', $this->get('customer'), true);
        }
        if ($this->isDefined('trademark')) {
            $task->set('trademark', $this->get('trademark'), true);
        }
        if ($this->isDefined('project')) {
            $task->set('project', $this->get('project'), true);
        }
        if ($this->get('status') == 'closed') {
            $task->set('status', 'finished', true);
        } else {
            $task->set('status', 'progress', true);
        }

        $task->set('planned_start_date', General::strftime($this->i18n('date_iso')), true);
        $task->unsetProperty('planned_finish_date', true);

        //set type task
        $task->set('type', PH_TASK_SYSTEM_TYPE, true);
        $task->set('active', 1, true);
        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini');
        if ($task->save()) {
            $task->set('referers', array($this->get('id')), true);
            if ($task->updateAllRelatives()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets records related to the model
     * (get only some for Relatives action, get everything for side panel)
     *
     * @return array - array with related records
     */
    public function getRelatedRecords() {

        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $related = array();
        $registry = &$this->registry;
        $db = &$registry['db'];

        $rights = $registry['currentUser']->get('rights');

        if ($this->checkPermissions('relatives')) {

            // all links of related records lead to Relatives tab
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=documents&amp;';
            $link .= 'documents=relatives&amp;relatives=' . $this->get('id');
            if ($this->get('archived_by')) {
                $link .= '&amp;archive=1';
            }

            //get related documents
            $query = 'SELECT DISTINCT(IF(dr.parent_id = ' . $this->get('id') . ', dr.link_to,' . "\n" .
                     '       IF(dr.link_to = ' . $this->get('id') . ', dr.parent_id, NULL))) as id,' . "\n" .
                     '       0 AS archive' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS . " AS d\n" .
                     '  ON dr.parent_id = d.id OR dr.link_to = d.id' . "\n" .
                     'WHERE d.deleted_by = 0 AND dr.link_to_model_name="Document" AND dr.parent_model_name="Document"' . "\n" .
                     '  AND (dr.parent_id = ' . $this->get('id') . ' OR dr.link_to = ' . $this->get('id') . ')' . "\n" .
                     'UNION' . "\n" .
                     'SELECT DISTINCT(IF(dr.parent_id = ' . $this->get('id') . ', dr.link_to,' . "\n" .
                     '       IF(dr.link_to = ' . $this->get('id') . ', dr.parent_id, NULL))) as id,' . "\n" .
                     '       1 AS archive' . "\n" .
                     'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . "` AS dr\n" .
                     'LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS . "` AS d\n" .
                     '  ON dr.parent_id = d.id OR dr.link_to = d.id' . "\n" .
                     'WHERE d.deleted_by = 0 AND dr.link_to_model_name="Document" AND dr.parent_model_name="Document"' . "\n" .
                     '  AND (dr.parent_id = ' . $this->get('id') . ' OR dr.link_to = ' . $this->get('id') . ')';
            $result = $db->GetAssoc($query);

            $related['documents'] = array('name' => 'documents',
                                          'label' => $this->i18n('menu_documents'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());

            if (!empty($rights['contracts']['_access_']) && $rights['contracts']['_access_'] != 'none') {
                //get related contracts
                $query = 'SELECT DISTINCT(IF(dr.parent_id = ' . $this->get('id') . ', dr.link_to,' . "\n" .
                         '       IF(dr.link_to = ' . $this->get('id') . ', dr.parent_id, NULL))) as id,' . "\n" .
                         '       0 AS archive' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr\n" .
                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS . " AS d\n" .
                         '  ON dr.parent_id = d.id OR dr.link_to = d.id' . "\n" .
                         'WHERE d.deleted_by = 0 AND ' . "\n" .
                         '  (dr.parent_model_name="Document" AND dr.parent_id = ' . $this->get('id') . ' AND dr.link_to_model_name="Contract" OR ' . "\n" .
                         '   dr.link_to_model_name="Document" AND dr.link_to = ' . $this->get('id') . ' AND dr.parent_model_name="Contract")' . "\n" .
                         'UNION' . "\n" .
                         'SELECT DISTINCT(IF(dr.parent_id = ' . $this->get('id') . ', dr.link_to,' . "\n" .
                         '       IF(dr.link_to = ' . $this->get('id') . ', dr.parent_id, NULL))) as id,' . "\n" .
                         '       1 AS archive' . "\n" .
                         'FROM `' . DB_TABLE_ARCHIVE_DOCUMENTS_RELATIVES . "` AS dr\n" .
                         'LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS . "` AS d\n" .
                         '  ON dr.parent_id = d.id OR dr.link_to = d.id' . "\n" .
                         'WHERE d.deleted_by = 0 AND ' . "\n" .
                         '  (dr.parent_model_name="Document" AND dr.parent_id = ' . $this->get('id') . ' AND dr.link_to_model_name="Contract" OR ' . "\n" .
                         '   dr.link_to_model_name="Document" AND dr.link_to = ' . $this->get('id') . ' AND dr.parent_model_name="Contract")';
                $result = $db->GetAssoc($query);

                $related['contracts'] = array('name' => 'contracts',
                                              'label' => $this->i18n('menu_contracts'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if (!empty($rights['tasks']['_access_']) && $rights['tasks']['_access_'] != 'none') {
                //get related tasks
                $query = 'SELECT tr.parent_id' . "\n" .
                         'FROM ' . DB_TABLE_TASKS_RELATIVES . " AS tr\n" .
                         'LEFT JOIN ' . DB_TABLE_TASKS . " AS t\n" .
                         '  ON t.id = tr.parent_id' . "\n" .
                         'WHERE t.deleted_by = 0 AND t.type != ' . PH_TASK_SYSTEM_TYPE . "\n" .
                         '  AND tr.origin = \'document\'' . "\n" .
                         '  AND tr.link_to = ' . $this->get('id');
                $result = $db->GetCol($query);

                $related['tasks'] = array('name' => 'tasks',
                                          'label' => $this->i18n('menu_tasks'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
            }

            if (!empty($rights['events']['_access_']) && $rights['events']['_access_'] != 'none') {
                //get related events
                $query = 'SELECT er.parent_id' . "\n" .
                         'FROM ' . DB_TABLE_EVENTS_RELATIVES . " AS er\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS . " AS e" .
                         '  ON er.parent_id=e.id' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES . " AS et" .
                         '  ON e.type=et.id AND et.active=1 AND et.deleted=0' . "\n" .
                         'WHERE e.deleted_by = 0 AND er.origin = \'document\'' . "\n" .
                         '  AND et.keyword != "reminder" AND er.link_to = ' . $this->get('id');
                $result = $db->GetCol($query);

                $related['events'] = array('name' => 'events',
                                           'label' => $this->i18n('menu_events'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
            }

            if (!empty($rights['finance_incomes_reasons']['_access_']) && $rights['finance_incomes_reasons']['_access_'] != 'none') {
                // get related finance incomes reasons
                $query = 'SELECT frr.parent_id' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                         '  ON frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id AND fir.active=1' . "\n" .
                         'WHERE frr.link_to_model_name="Document" AND frr.link_to="' . $this->get('id') . '"';
                $result = $db->GetCol($query);

                $related['finance_incomes_reasons'] = array('name' => 'finance_incomes_reasons',
                                                            'label' => $this->i18n('menu_finance_incomes_reasons'),
                                                            'link' => $link,
                                                            'ids' => is_array($result) ? $result : array());
            }

            if (!empty($rights['finance_expenses_reasons']['_access_']) && $rights['finance_expenses_reasons']['_access_'] != 'none') {
                // get related finance expenses reasons
                $query = 'SELECT frr.parent_id' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                         '  ON frr.parent_model_name="Finance_Expenses_Reason" AND frr.parent_id=fer.id AND fer.active=1' . "\n" .
                         'WHERE frr.link_to_model_name="Document" AND frr.link_to="' . $this->get('id') . '"';
                $result = $db->GetCol($query);

                $related['finance_expenses_reasons'] = array('name' => 'finance_expenses_reasons',
                                                             'label' => $this->i18n('menu_finance_expenses_reasons'),
                                                             'link' => $link,
                                                             'ids' => is_array($result) ? $result : array());
            }
        }

        if (isset($rights[$this->module]['communications']) && $rights[$this->module]['communications'] != 'none' && $this->checkPermissions('communications')) {
            if (isset($rights[$this->module]['comments']) && $rights[$this->module]['comments'] != 'none' && $this->checkPermissions('comments')) {
                //get related comments
                if ($this->get('archived_by')) {
                    $table = DB_TABLE_ARCHIVE_COMMENTS;
                } else {
                    $table = DB_TABLE_COMMENTS;
                }
                $query = 'SELECT id' . "\n" .
                         'FROM `' . $table . "`\n" .
                         'WHERE deleted_by = 0 AND model = \'Document\' AND model_id = \'' . $this->get('id') . '\'' . ($registry['currentUser']->get('is_portal') ? ' AND is_portal = \'1\'' : '');

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=documents&amp;';
                $link .= 'documents=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=comments';
                if ($this->get('archived_by')) {
                    $link .= '&amp;archive=1';
                }
                $related['comments'] = array('name' => 'comments',
                                              'label' => $this->i18n('documents_comments'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if (isset($rights[$this->module]['emails']) && $rights[$this->module]['emails'] != 'none' && $this->checkPermissions('emails')) {
                //get related e-mails
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model = \'Document\' AND model_id = ' . $this->get('id') . ' AND `system`=\'0\'' . "\n" .
                         'GROUP BY code';

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=documents&amp;';
                $link .= 'documents=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=emails';
                if ($this->get('archived_by')) {
                    $link .= '&amp;archive=1';
                }
                $related['emails'] = array('name' => 'email',
                                              'label' => $this->i18n('documents_emails'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if ($this->checkPermissions('minitasks')) {
                //get related mini tasks
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_MINITASKS . ' AS m' . "\n" .
                         'WHERE model = \'Document\' AND model_id=' . $this->get('id');

                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $current_right = isset($rights['minitasks']['list']) ? $rights['minitasks']['list'] : '';
                unset($rights);
                if ($current_right == 'mine') {
                    $query .= " AND m.assigned_to=$current_user_id ";
                } elseif ($current_right == 'group') {
                    $query .= " AND (m.added_by=$current_user_id OR m.assigned_to=$current_user_id) ";
                } elseif ($current_right != 'all') {
                    $query .= ' AND 0';
                }

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=documents&amp;';
                $link .= 'documents=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=minitasks';
                if ($this->get('archived_by')) {
                    $link .= '&amp;archive=1';
                }
                $related['minitasks'] = array('name' => 'minitasks',
                                              'label' => $this->i18n('menu_minitasks'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }
        }

        if ($this->checkPermissions('attachments')) {
            //get related files
            if ($this->get('archived_by')) {
                $table = DB_TABLE_ARCHIVE_FILES;
            } else {
                $table = DB_TABLE_FILES;
            }
            $query = 'SELECT f.id' . "\n" .
                     'FROM `' . $table . '` AS f ' . "\n" .
                     'WHERE f.deleted_by = 0 AND f.model = \'Document\' AND f.model_id = ' . $this->get('id');
            //check access permissions of files
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $query .= Files::getAdditionalWhere($registry);

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=documents&amp;';
            $link .= 'documents=attachments&amp;attachments=' . $this->get('id');
            if ($this->get('archived_by')) {
                $link .= '&amp;archive=1';
            }
            $related['files'] = array('name' => 'attachments',
                                          'label' => $this->i18n('attachments'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
        }

        if ($this->checkPermissions('viewtimesheets')) {
            //get related timesheets
            $query = 'SELECT tt.id' . "\n" .
                     'FROM ' . DB_TABLE_TASKS_TIMESHEETS . " AS tt\n" .
                     'JOIN ' . DB_TABLE_TASKS . " AS t\n" .
                     '  ON t.id = tt.task_id' . "\n" .
                     'JOIN ' . DB_TABLE_TASKS_RELATIVES . " AS tr\n" .
                     '  ON tr.parent_id = tt.task_id' . "\n" .
                     'WHERE t.deleted_by = 0 AND t.type = ' . PH_TASK_SYSTEM_TYPE . "\n" .
                     '  AND tr.origin = \'document\'' . "\n" .
                     '  AND tr.link_to = ' . $this->get('id');

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=documents&amp;';
            $link .= 'documents=timesheets&amp;timesheets=' . $this->get('id');
            if ($this->get('archived_by')) {
                $link .= '&amp;archive=1';
            }
            $related['timesheets'] = array('name' => 'timesheets',
                                           'label' => $this->i18n('timesheets'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $related;
    }

    /**
     * Prepares recipient placeholders data of by search in db or from request
     *
     * @param string $mail - recipient e-mail address from request
     * @param string $name - recipient name from request
     * @return multitype:|array - array with names and values of placeholders for recipient data
     */
    function prepareRecipientPlaceholders($mail, $name) {

        $placeholders = array('recipient_firstname' => '',
                              'recipient_lastname' => '',
                              'recipient_salutation' => '',
                              'recipient_salutation_formal' => '');

        if (!$name) {
            return $placeholders;
        }

        $model_lang = $this->get('model_lang');
        $db = $this->registry['db'];

        //search for customer persons or contact persons with exact match of names and e-mail
        //NOTE: e-mail must start at beginning of field or have a new line (\n) before it;
        //      e-mail must end at end of field or have a new line (\n) or a pipe (|) character after it.
        $query = 'SELECT c.salutation AS recipient_salutation, c.subtype, c.is_company, ' . "\n" .
                 'TRIM(ci18n.name) AS recipient_firstname, TRIM(ci18n.lastname) AS recipient_lastname ' . "\n" .
                 '  FROM ' . DB_TABLE_CUSTOMERS . ' AS c ' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n ' . "\n" .
                 '  ON c.id=ci18n.parent_id AND lang="' . $model_lang . '"' . "\n" .
                 '  WHERE TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) = "' . General::slashesEscape($name) . '" AND c.email REGEXP \'(^|\n)' . $mail . '(\\\\||\n|$)\'' . "\n" .
                 '  ORDER BY id DESC';
        $results = $db->getAll($query);

        if ($results) {
            foreach ($results as $result) {
                //matching recipient with salutation - get first found
                if ($result['recipient_salutation'] && ($result['subtype'] == 'contact' || $result['subtype'] == 'normal' && !$result['is_company'])) {
                    unset($result['is_company']);
                    unset($result['subtype']);
                    $placeholders = $result;
                    $placeholders['recipient_salutation_formal'] = ($placeholders['recipient_salutation'] == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f'));
                    $placeholders['recipient_salutation'] = $this->i18n('salutation_vocative_' . $placeholders['recipient_salutation']);
                    break;
                }
            }
            //matching recipient without salutation - get first found
            if (!$placeholders['recipient_salutation']) {
                $result = $results[0];
                unset($result['is_company']);
                unset($result['subtype']);
                $placeholders = $result;
            }
        } else {
            $name = trim($name);
            //try to guess recipient first and last name (assuming text chunk after last interval is last name)
            preg_match('#^(.+)\s+([^\s]+)$#', $name, $matches);
            if ($matches) {
                $placeholders['recipient_firstname'] = $matches[1];
                $placeholders['recipient_lastname'] = $matches[2];
            } else {
                $placeholders['recipient_firstname'] = $name;
            }
        }

        return $placeholders;
    }

    /*
     * UTF-8 version of ord
     */
    function uniord($c) {
        $h = ord($c[0]);
        if ($h <= 0x7F) {
            return $h;
        } else if ($h < 0xC2) {
            return false;
        } else if ($h <= 0xDF) {
            return ($h & 0x1F) << 6 | (ord($c[1]) & 0x3F);
        } else if ($h <= 0xEF) {
            return ($h & 0x0F) << 12 | (ord($c[1]) & 0x3F) << 6
                                     | (ord($c[2]) & 0x3F);
        } else if ($h <= 0xF4) {
            return ($h & 0x0F) << 18 | (ord($c[1]) & 0x3F) << 12
                                     | (ord($c[2]) & 0x3F) << 6
                                     | (ord($c[3]) & 0x3F);
        } else {
            return false;
        }
    }

    /**
     * Merge model copying its files, comments, emails, minitasks and write history
     * This method calls parent method and saves relatives
     *
     * @return int $mergeDestinationID - id of the record to merge to
     * @return bool - result of the operation
     */
    public function merge($mergeDestinationID)
    {
        parent::merge($mergeDestinationID);

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $this->moveRelations($mergeDestinationID);

        $this->set('origin_id', $mergeDestinationID, true);
        $this->set('origin_model', $this->modelName, true);
        $this->set('clone_transform', 'transformed', true);
        $result = $this->insertRelatives();

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Checks if document is archived or not
     *
     * @return bool - true - archived, false not deleted
     */
    public function isArchived() {
        $archived    = $this->get('archived');
        $archived_by = $this->get('archived_by');

        if ($archived_by && $archived && $archived != '0000-00-00 00:00:00') {
            return true;
        }

        return false;
    }

    /**
     * Gets value of basic variable for export with explicit arguments
     *
     * @param string $field_name - name of the field
     * @return string - the value of the variable
     */
    public function getExportBasicVarValue(string $field_name): string
    {
        switch($field_name) {
            case 'status':
                $suvstatus = $this->get('substatus_name');
                if ($suvstatus) {
                    return $suvstatus;
                }

                $module = strtolower(General::singular2plural($this->modelName));

                return $this->i18n("{$module}_status_{$this->get('status')}");

            /* simple text fields */
            case "name":
            case "notes":
            case "description":
            case "full_num":
                return $this->get($field_name);

            case "name_full_num":
                return "[{$this->get('full_num')}] {$this->get('name')}";

            /* XXX_name_code fields */
            case 'customer_name_code':
                $code = $this->get('customer_code');
                return $code ? "[{$code}] {$this->get('customer_name')}" : '';
            case "trademark_name_code":
                $code = $this->get('trademark_code');
                return $code ? "[{$code}] {$this->get('trademark_name')}" : '';
            case "contract_name_num":
                $code = $this->get('contract_num');
                return $code ? "[{$code}] {$this->get('contract_name')}" : '';
            case "project_name_code":
                $code = $this->get('project_code');
                return $code ? "[{$code}] {$this->get('project_name')}" : '';

            /* simple {field}_name types */
            case "type":
            case "customer":
            case "trademark":
            case "office":
            case "department":
            case "media":
            case "group":
            case "project":
            case "added_by":
            case "modified_by":
            case "deleted_by":
                return $this->get("{$field_name}_name");

            case "contract":
                return $this->get('contract_custom_label');

            case "employee":
                $employee = $this->get('employee_name');
                if (is_array($employee)) {
                    return $employee['value']??'';
                }
                return $employee;

            case "relatives_parent":
                $relatives = $this->getFirstLevelRelatedDocuments('parent');
                $relValues = [];
                foreach ($relatives as $relative) {
                    $relValues[] = "[{$relative['full_num']}] {$relative['name']}";
                }
                return implode(', ', $relValues);

            //case "emails": break;
            //case "email": break;
            //case "comments": break;
            //case "history_activity": break;

            case "timesheet_time":
                return $this->get('timesheet_time_formatted');

            // date fields handled in Model
            case 'added':
            case 'created':
            case 'modified':
            case 'deleted':
            case 'date':
            // datetime fields handled in Model
            case 'deadline':
            case 'validity_term':
                $value = $this->get($field_name);
                if (preg_match('#^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$#', $value)) {
                    return General::strftime('%d.%m.%Y %H:%M', $value);
                }

                if (preg_match('#^\d{4}-\d{2}-\d{2}$#', $value)) {
                    return General::strftime('%d.%m.%Y', $value);
                }

                return '';
        }

        return parent::getExportBasicVarValue($field_name);
    }

    public function getExportBasicVarType(string $field_name): string
    {
        switch($field_name) {
            /*case 'status':
            case "name":
            case "notes":
            case "description":
            case "full_num":
            case "name_full_num":
            case 'customer_name_code':
            case "trademark_name_code":
            case "contract_name_num":
            case "project_name_code":
            case "type":
            case "customer":
            case "trademark":
            case "office":
            case "department":
            case "media":
            case "group":
            case "project":
            case "added_by":
            case "modified_by":
            case "deleted_by":
            case "contract":
            case "employee":
            case "relatives_parent":
            case "emails":
            case "email":
            case "comments":
            case "history_activity":
                return 'string';*/

            case "timesheet_time":
            case 'added':
            case 'created':
            case 'modified':
            case 'deleted':
            case 'date':
                return 'date';

            case 'deadline':
            case 'validity_term':
                return 'datetime';
        }


        return parent::getExportBasicVarType($field_name);
    }
}

?>
