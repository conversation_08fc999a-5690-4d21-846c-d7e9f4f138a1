<?php

/**
 * NIK ADD CONTRACT plugin custom model class
 */
Class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name = 'nik_add_contract';
    public $type_id = '';

    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        return true;
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\n|\r|\r\n)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);

            // set them as properties of model in the usual way
            $this->set($key, $value, true);
        }
    }

    /**
     * Prepare all necessary custom options for dashlet
     *
     * @return array - array with arrays of options
     */
    public function prepareCustomOptions() {

        $options = array();

        $db = &$this->registry['db'];
        $lang = $this->registry['lang'];

        // customer autocompleter
        $customer_autocomplete = array(
            'type'          => 'customers',
            'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers'),
            'filters'       => array(
                '<type>'               => $this->get('customer_type_client')
            ),
            'add'           => 1,
            'addquick_type' => $this->get('customer_type_client'),
            'clear'         => 1
        );
        $options['customer_autocomplete'] = $customer_autocomplete;

        // service autocompleter
        $service_autocomplete = array(
            'type'          => 'nomenclatures',
            'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'nomenclatures', 'nomenclatures'),
            'search'        => array('<code>', '<name>'),
            'suggestions'   => '[<code>] <name>',
            'fill_options'  => array(
                '$service_name => [<code>] <name>',
                '$service_name_oldvalue => [<code>] <name>',
                '$service => <id>'
            ),
            'filters'       => array(
                '<type>'               => $this->get('nom_type_service')
            ),
            'buttons_hide'  => 'search',
            'clear'         => 1,
            'execute_after' => 'updateDropdowns(autocomplete, data);changePriceFilter'
        );
        $options['service_autocomplete'] = $service_autocomplete;

        // electronic invoice (yes/no)
        $options['electronic_invoice'] = Dropdown::getYesNo(array($this->registry));
        usort($options['electronic_invoice'], function ($a, $b) { return $a['option_value'] > $b['option_value'] ? 1 : -1; });

        return $options;
    }

    /**
     * Prepare available option for machine type and period when value of
     * service autocompleter is changed
     */
    public function updateDropdowns() {
        $request = $this->registry['request'];
        $db = &$this->registry['db'];

        $result = array('machine_type' => array(), 'period' => array());

        if ($request->get('service')) {
            // get machine types
            $query = 'SELECT nmt.id AS option_value, nmti.name AS label' . "\n" .
                     'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc' . "\n" .
                     '  ON fm.model="Nomenclature" AND fm.model_type="' . $this->get('nom_type_service') . '"' . "\n" .
                     '    AND fm.name="' . $this->get('service_machine_type_var') . '"' . "\n" .
                     '    AND nc.model_id = \'' . $request->get('service') . '\' AND nc.var_id=fm.id' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nmt' . "\n" .
                     '  ON nc.value=nmt.id AND nmt.active=1 AND nmt.deleted_by=0' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nmti' . "\n" .
                     '  ON nmt.id=nmti.parent_id AND nmti.lang="' . $this->registry['lang'] . '"' . "\n" .
                     'ORDER BY label, option_value';
            $result['machine_type'] = $db->GetAll($query);
        }

        $services = $request->get('services') ?: array();
        foreach ($services as $k => $v) {
            if ($v == 2) {
                unset($services[$k]);
            }
        }
        $services = array_unique(array_filter($services));
        if ($services) {
            // get common periods for all services
            $query = 'SELECT fo.option_value, fo.label' . "\n" .
                     'FROM ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     '  ON fm.model="Nomenclature" AND fm.model_type="' . $this->get('nom_type_service') . '"' . "\n" .
                     '    AND fm.name="' . $this->get('service_reporting_period_var') . '"' . "\n" .
                     '    AND fm.name=fo.parent_name AND fo.active_option=1 AND fo.lang="' . $this->registry['lang'] . '"' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc' . "\n" .
                     '  ON nc.model_id IN (\'' . implode('\', \'', $services) . '\')' . "\n" .
                     '    AND nc.var_id=fm.id AND nc.value=fo.option_value' . "\n" .
                     'GROUP BY fo.option_value' . "\n" .
                     'HAVING COUNT(nc.model_id)=' . count($services) . "\n" .
                     'ORDER BY fo.position, fo.label';
            $result['period'] = $db->GetAll($query);
        }

        return 'var result = ' . json_encode($result) . ';';
    }

    /**
     * Construct GT2 table of model from specified data from request and database
     *
     * @param Contract $contract - Contract model
     * @return string|boolean - result of the operation - true on success or JSON-encoded error messages on error
     */
    private function constructGT2(Contract $contract) {

        $request = $this->registry['request'];
        $db = &$this->registry['db'];
        $lang = $this->registry['lang'];

        $service = $request->get('service');
        $service_name = $request->get('service_name');
        $machine_type = $request->get('machine_type');
        $num = $request->get('num');
        $price = $request->get('price');
        $period = $request->get('period');

        $result = array('errors' => array());
        foreach ($service as $k => $v) {
            if ($v == 2) {
                unset($machine_type[$k]);
                // check for negative price
                if (empty ($price[$k]) || $price[$k] >= 0) {
                    $result['errors'][] = $this->i18n('error_plugin_nik_add_contract_negative_price_required');
                }
            }
        }

        $prec = $this->registry['config']->getParam('precision', 'gt2_rows');
        $is_empty = function($a) { return !intval($a); };
        $is_empty_price = function($a) use ($prec) { return round(floatval($a), $prec) <= 0; };
        // validation of request
        if (!$period ||
            !is_array($service) || array_filter($service, $is_empty) ||
            !is_array($machine_type) || array_filter($machine_type, $is_empty) ||
            !is_array($num) || array_filter($num, $is_empty) ||
            !is_array($price) || count(array_filter($price, $is_empty_price)) == count($price)) {
            $result['errors'][] = $this->i18n('error_plugin_nik_add_contract_preview_missing_data');
        }

        $gt2 = array();
        $gt2_var_idx = -1;
        $vars = $contract->get('vars');
        foreach ($vars as $var_idx => $var) {
            if ($var['type'] == 'gt2') {
                $gt2 = $var;
                $gt2_var_idx = $var_idx;
                break;
            }
        }

        // prepare values
        $values = array();
        $service_unique = array();
        $ukey_format = '%d-%d-%.' . $prec . 'f';
        foreach ($service as $idx => $s) {
            if (empty($s)) continue;
            $mt = isset($machine_type[$idx]) ? $machine_type[$idx] : 0;
            $p = isset($price[$idx]) ? $price[$idx] : 0;
            $ukey = sprintf($ukey_format, $s, $mt, $p);
            if (!array_key_exists($ukey, $service_unique)) {
                $service_unique[$ukey] = array(
                    'id' => $s,
                    'machine_type' => $mt,
                    'price' => $p,
                    'num' => isset($num[$idx]) ? intval($num[$idx]) : 0,
                    'name' => isset($service_name[$idx]) ? $service_name[$idx] : '',
                    'code' => ''
                );
                if (preg_match('#^\[([^\]]*)\]\s*(.*)$#', $service_unique[$ukey]['name'], $matches)) {
                    $service_unique[$ukey]['code'] = $matches[1];
                    $service_unique[$ukey]['name'] = $matches[2];
                }
            } else {
                $service_unique[$ukey]['num'] += isset($num[$idx]) ? intval($num[$idx]) : 0;
            }
        }

        // prepare values and calculate GT2
        $row_idx = -1;
        foreach ($service_unique as $ukey => $article) {
            $values[$row_idx--] = array(
                'article_id' => $article['id'],
                'article_code' => $article['code'],
                'article_name' => $article['name'],
                'quantity' => $article['num'],
                $gt2['calculated_price'] => $article['price'],
                'article_measure_name' => '1', // br.
                'current' => '1', // periodical
                'free_field1' => $period, // reporting period
                'free_field2' => $article['machine_type'] // machine type
            );
        }
        $gt2['values'] = $values;

        $contract->set('grouping_table_2', $gt2, true);
        $contract->calculateGT2();
        $gt2 = $contract->get('grouping_table_2');
        if ($gt2['plain_values']['total_with_vat'] < 0) {
            $result['errors'][] = $this->i18n('error_plugin_nik_add_contract_negative_total');
        }
        if (!empty($result['errors'])) {
            return 'var result = ' . json_encode($result) . ';';
        }

        if ($gt2_var_idx > -1) {
            $vars[$gt2_var_idx] = $gt2;
            $contract->set('vars', $vars, true);
            $contract->set('grouping_table_2', 1, true);
        }

        return true;
    }

    /**
     * Preview topic (GT2 table) of contract to be added
     */
    public function previewTopic() {

        $request = $this->registry['request'];
        $db = &$this->registry['db'];
        $lang = $this->registry['lang'];

        $result = array();

        // create an empty contract and get its GT2 table
        require_once PH_MODULES_DIR . 'contracts/models/contracts.model.php';
        $params = array(
            'type' => $this->get('contract_type_contract'),
            'company' => $this->get('contract_company')
        );
        $contract = new Contract($this->registry, $params);

        // get only variables from "Topic" tab
        $contract->set('layout_place_from', PH_CONTRACTS_TAB1_FROM, true);
        $contract->set('layout_place_to', PH_CONTRACTS_TAB1_TO, true);

        // change registry action to get the default values for GT2
        $reg_action = $this->registry['action'];
        $this->registry->set('action', 'add', true);

        // prepare additional variables
        $contract->getVarsForTemplate(false);

        $this->registry->set('action', $reg_action, true);

        // construct GT2 table
        $res = $this->constructGT2($contract);
        if ($res !== true) {
            // return error messages
            return $res;
        }

        // some processing of the GT2 table for display in dashlet
        $gt2 = array();
        $vars = $contract->get('vars');
        foreach ($vars as $var_idx => $var) {
            if ($var['type'] == 'gt2') {
                $gt2 = $var;
                break;
            }
        }
        unset($vars);

        $visible_vars = array(
            'article_name', 'free_field1', 'free_field2',
            'quantity', $gt2['calculated_price'], 'subtotal_with_discount'
        );
        foreach ($gt2['vars'] as $var_name => $var) {
            $gt2['vars'][$var_name]['hidden'] = !in_array($var_name, $visible_vars);
        }
        $visible_vars = array('total', 'total_vat', 'total_with_vat');
        foreach ($gt2['plain_vars'] as $var_name => $var) {
            $gt2['plain_vars'][$var_name]['hidden'] = !in_array($var_name, $visible_vars);
            if (!$gt2['plain_vars'][$var_name]['hidden']) {
                $gt2['plain_values'][$var_name] .= ' ' . $gt2['plain_values']['currency'];
            }
        }
        $gt2['show_totals_inwords'] = 0;
        $gt2['totals_texts_colspan'] = 5;
        $gt2['totals_texts_rowspan'] = 3;
        $gt2['width'] = '94%';
        foreach ($gt2['values'] as $idx => $row) {
            $gt2['values'][$idx]['quantity'] = sprintf('%d', $row['quantity']);
            $gt2['values'][$idx][$gt2['calculated_price']] .= ' ' . $gt2['plain_values']['currency'];
            $gt2['values'][$idx]['subtotal_with_discount'] .= ' ' . $gt2['plain_values']['currency'];
        }

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('_gt2_view.html');
        $viewer->data['table'] = $gt2;
        $result['rows'] = $viewer->fetch();

        return 'var result = ' . json_encode($result) . ';';
    }

    /**
     * Create contract, finish it, automatically create invoices templates
     *
     * @return boolean - result of the operation
     */
    public function createContract() {

        $request = $this->registry['request'];
        $db = &$this->registry['db'];
        $lang = $this->registry['lang'];

        $result = array();

        // load i18n files for contracts
        $lang_file_contract = sprintf('%s%s%s%s', PH_MODULES_DIR, 'contracts/i18n/', $lang, '/contracts.ini');
        $this->loadI18NFiles($lang_file_contract);

        // check user permission to add contracts
        if (!$this->registry['currentUser']->checkRights('contracts' . $this->get('contract_type_contract'), 'add')) {
            $result['errors'] = array(
                $this->i18n('error_contracts_add_failed', array($this->i18n('contracts_sg'))),
                $this->i18n('error_no_access_to_module_action')
            );
            return 'var result = ' . json_encode($result) . ';';
        }

        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        require_once PH_MODULES_DIR . 'contracts/models/contracts.types.factory.php';

        $db->StartTrans();

        $filters_type = array('where' => array('cot.id = "' . $this->get('contract_type_contract') . '"',
                                               'cot.active = 1',
                                               'cot.inheritance = 0'),
                              'sanitize'   => true,
                              'model_lang' => $request->get('model_lang'));
        $contract_type = Contracts_Types::searchOne($this->registry, $filters_type);

        if (!$contract_type) {
            $result['errors'] = array(
                $this->i18n('error_contracts_add_failed', array($this->i18n('contracts_sg'))),
                $this->i18n('error_invalid_type')
            );
            return 'var result = ' . json_encode($result) . ';';
        }

        $params = array(
            'type' => $this->get('contract_type_contract'),
            'type_name' => $contract_type->get('name') ?: $this->i18n('contract'),
            'name' => $contract_type->get('default_name') ?: $this->i18n('contract'),
            'customer' => $request->get('customer'),
            'company' => $this->get('contract_company'),
            'office' => $this->get('contract_office'),
            'employee' => $this->registry['currentUser']->get('employee'),
            'employee_name' => $this->registry['currentUser']->get('employee_name'),
            'change_formula_date_sign' => 'date_sign',
            'date_sign' => $request->get('date_start'),
            'change_formula_date_start' => 'date_start',
            'date_start' => $request->get('date_start'),
            'change_formula_date_validity' => 'date_validity',
            'date_validity' => $request->get('date_validity'),
            'department' => $this->get('contract_department'),
            'group' => PH_ROOT_GROUP,
            'active' => 1
        );
        $contract = new Contract($this->registry, $params);

        // change registry action to get some default plain values for GT2
        $reg_action = $this->registry['action'];
        $this->registry->set('action', 'add', true);

        $this->registry->set('get_old_vars', false, true);
        $contract->set('plain_vars', null, true);
        $contract->getVars();

        // construct GT2 table
        $res = $this->constructGT2($contract);
        if ($res !== true) {
            // return the error messages
            return $res;
        }

        // restore registry action
        $this->registry->set('action', $reg_action, true);

        $vars = $contract->get('vars');
        foreach ($vars as $var_idx => $var) {
            if ($var['name'] == $this->get('invoice_firstdate_var')) {
                $vars[$var_idx]['value'] = $request->get('invoice_firstdate');
            } elseif ($var['name'] == $this->get('invoice_auto_var')) {
                $vars[$var_idx]['value'] = $this->get('issue_invoice_auto_no');
            }
        }
        $contract->set('vars', $vars, true);

        //this param is used to allow the save of GT2 using the model's saveVars method
        $request->set('gt2_requested', true, 'all', true);
        //this param defines that the GT2 is already prepared for save (no need to get it from the DB)
        $contract->set('table_values_are_set', true, true);
        // allow edit of all layouts
        $this->registry->set('edit_all', true, true);

        if ($contract->save()) {
            // save history
            $filters = array('where' => array('co.id = ' . $contract->get('id')),
                             'model_lang' => $contract->get('model_lang'));
            $new_contract = Contracts::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_contract->getVars();
            $old_contract = new Contract($this->registry);
            $old_contract->set('type', $new_contract->get('type'), true);
            $old_contract->set('company', $new_contract->get('company'), true);
            $old_contract->getVars();
            $this->registry->set('get_old_vars', false, true);
            $old_contract->sanitize();

            require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
            $audit_parent = Contracts_History::saveData($this->registry,
                                                        array('model' => $contract,
                                                              'action_type' => 'add',
                                                              'new_model' => $new_contract,
                                                              'old_model' => $old_contract));

            // use the model from DB from here on
            $contract = $new_contract;

            // save parties
            if ($request->get('electronic_invoice') && $request->get('email')) {

                $cstm_fin_email = '';
                $cstm_fin_email_cc = array();
                foreach ($request->get('email') as $email) {
                    if ($email && Validator::validEmail($email) && $email != $cstm_fin_email && !in_array($email, $cstm_fin_email_cc)) {
                        if (!$cstm_fin_email) {
                            $cstm_fin_email = $email;
                        } else {
                            $cstm_fin_email_cc[] = $email;
                        }
                    }
                }

                if ($cstm_fin_email) {

                    $request->set('cstm_fin_email', $cstm_fin_email, 'all', true);

                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                    $filters = array('sanitize' => true,
                                     'model_lang' => $contract->get('lang'),
                                     'where' => array('c.id = ' . $contract->get('customer')));
                    $customer = Customers::searchOne($this->registry, $filters);

                    if ($customer->get('is_company')) {
                        //the customer is a company, check if contact person with the email exists
                        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                        $filters = array('where' => array('c.subtype = \'contact\'',
                                                          'c.email LIKE "%' . $cstm_fin_email . '%"',
                                                          'c.active = 1'),
                                         'sort' => array('c.is_main DESC'),
                                         'sanitize' => true,
                                         'skip_permissions' => true);
                        $contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                        $financial_contact_label = $this->i18n('plugin_nik_add_contract_financial_contact');

                        if ($contact_person) {
                            //set the id of the contact person
                            $request->set('cstm_financial', $contact_person->get('id'), 'all', true);
                        } else {
                            //now set the contact person details as if they are written in combo
                            $request->set('cstm_financial', $financial_contact_label, 'all', true);
                            $request->set('cstm_financial_isCustom', 1, 'all', true);
                            $request->set('cstm_fin_email_isCustom', 1, 'all', true);
                        }

                        // copies of financial contact
                        if ($cstm_fin_email_cc) {

                            $request->set('cstm_fin_email_cc', $cstm_fin_email_cc, 'all', true);

                            $cstm_financial_cc = $cstm_financial_cc_isCustom = $cstm_fin_email_cc_isCustom = array();

                            foreach ($cstm_fin_email_cc as $idx => $cc_email) {
                                // check if contact person with the email exists
                                $filters = array('where' => array('c.subtype = \'contact\'',
                                                                  'c.email LIKE "%' . $cc_email . '%"',
                                                                  'c.active = 1'),
                                                 'sort' => array('c.is_main DESC'),
                                                 'sanitize' => true,
                                                 'skip_permissions' => true);
                                $contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                                if ($contact_person) {
                                    // set the id of the contract person
                                    $cstm_financial_cc[$idx] = $contact_person->get('id');
                                } else {
                                    // prepare contact person details as if they are written in combo
                                    $cstm_financial_cc[$idx] = $financial_contact_label;
                                    $cstm_financial_cc_isCustom[$idx] = 1;
                                    $cstm_fin_email_cc_isCustom[$idx] = 1;
                                }
                            }

                            $request->set('cstm_financial_cc', $cstm_financial_cc, 'all', true);
                            if ($cstm_financial_cc_isCustom && $cstm_fin_email_cc_isCustom) {
                                $request->set('cstm_financial_cc_isCustom', $cstm_financial_cc_isCustom, 'all', true);
                                $request->set('cstm_fin_email_cc_isCustom', $cstm_fin_email_cc_isCustom, 'all', true);
                            }
                        }
                    } else {
                        //the customer is person, check if such email has been added to person
                        $request->set('cstm_financial', $customer->get('id'), 'all', true);

                        //check if the person has this email, add it if needed
                        $emails = $customer->get('email');

                        // collect all data to save in 'email' field of customer
                        $email_data = array();

                        if (empty($emails) || !in_array($cstm_fin_email, $emails)) {
                            if ($emails) {
                                $email_notes = $customer->get('email_note');
                                foreach($emails as $idx => $email) {
                                    $email_data[] = sprintf("%s%s", $email, (!empty($email_notes[$idx])) ? '|' . $email_notes[$idx] : '');
                                }
                            }
                            $email_data[] = $cstm_fin_email;
                        }

                        // copies of financial contact
                        if ($cstm_fin_email_cc) {

                            $request->set('cstm_fin_email_cc', $cstm_fin_email_cc, 'all', true);
                            $request->set('cstm_financial_cc', array_fill(0, count($cstm_fin_email_cc), $customer->get('id')), 'all', true);

                            foreach ($cstm_fin_email_cc as $idx => $cc_email) {
                                if (empty($emails) || !in_array($cc_email, $emails)) {
                                    $email_data[] = $cc_email;
                                }
                            }
                        }

                        // add all new emails to the customer
                        if ($email_data) {
                            $old_customer = clone $customer;
                            $old_customer->sanitize();

                            $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . ' SET email="' . General::slashesEscape(implode("\n", $email_data)) . '" WHERE id=' . $contract->get('customer');
                            $db->Execute($query);

                            // write history for the customer
                            require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                            require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';

                            $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                             'model_lang' => $customer->get('model_lang'),
                                             'sanitize' => true);
                            $new_customer = Customers::searchOne($this->registry, $filters);

                            Customers_History::saveData($this->registry,
                                                        array('model' => $customer,
                                                              'action_type' => 'edit',
                                                              'new_model' => $new_customer,
                                                              'old_model' => $old_customer));
                        }
                    }

                    // set default financial contact for own company (because field is required)
                    $request->set('self_financial', '[default_financial_contact_person]', 'all', true);

                    $old_contract = clone $contract;
                    $old_contract->getContactCc();
                    $old_contract->sanitize();

                    if ($contract->savePartiesData()) {
                        $filters = array('where' => array('co.id = ' . $contract->get('id')));
                        $new_contract = Contracts::searchOne($this->registry, $filters);
                        $new_contract->getContactCc();

                        //save history
                        $audit_parent = Contracts_History::saveData($this->registry,
                                                                    array('model' => $contract,
                                                                          'action_type' => 'parties',
                                                                          'new_model' => $new_contract,
                                                                          'old_model' => $old_contract
                                                                    ));

                        // use the model from DB from here on
                        $contract = $new_contract;
                    } else {
                        $db->FailTrans();
                        $this->registry['messages']->setError($this->i18n('error_contracts_add_failed', array($contract->getModelTypeName())), '', -2);
                        $result['errors'] = $this->registry['messages']->getErrors();
                    }
                }
            }

            if (!$db->HasFailedTrans()) {

                // set temporary routing parameters while performing automations
                $real_module = $this->registry['module'];
                $real_controller = $this->registry['controller'];
                $real_action = $this->registry['action'];

                $this->registry->set('module', 'contracts', true);
                $this->registry->set('controller', 'contracts', true);
                $this->registry->set('action', 'setstatus', true);

                $request->set('status', 'closed', 'true', true);

                require_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
                $automation = new Automations_Controller($this->registry);
                // automation has to be executed before action
                $automation->before_action = true;
                $automation_result = $automation->performAutomation($contract, $contract);

                // restore routing parameters
                $this->registry->set('module', $real_module, true);
                $this->registry->set('controller', $real_controller, true);
                $this->registry->set('action', $real_action, true);

                $request->remove('status');

                // change status to closed
                $old_contract = clone $contract;
                $old_contract->sanitize();

                $contract->set('status', 'closed', true);

                if ($automation_result && $contract->setStatus()) {
                    // save history
                    $filters = array('where'      => array('co.id = ' . $contract->get('id')),
                                     'model_lang' => $contract->get('model_lang'));
                    $new_contract = Contracts::searchOne($this->registry, $filters);

                    Contracts_History::saveData($this->registry,
                                                array('model' => $contract,
                                                      'action_type' => 'status',
                                                      'new_model' => $new_contract,
                                                      'old_model' => $old_contract));
                } else {
                    $db->FailTrans();
                    $this->registry['messages']->setError($this->i18n('error_contracts_add_failed', array($contract->getModelTypeName())), '', -2);
                    $result['errors'] = $this->registry['messages']->getErrors();
                    $this->registry['messages']->removeFromSession($this->registry);
                }

                if (!$db->HasFailedTrans()) {
                    $contract_url = sprintf('<a href="%s?%s=contracts&amp;contracts=view&amp;view=%s" target="_blank" style="vertical-align: bottom;">%s %s</a>',
                                            $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                            $contract->get('id'), $contract->getModelTypeName(), $contract->get('num'));
                    $result['messages'] = array($this->i18n('message_contracts_add_success', array($contract_url)));
                }
            }
        } else {
            $db->FailTrans();
            $this->registry['messages']->setError($this->i18n('error_contracts_add_failed', array($contract->getModelTypeName())), '', -1);
            $result['errors'] = $this->registry['messages']->getErrors();
        }

        // the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        return 'var result = ' . json_encode($result) . ';';
    }
}

?>
