<div class="load_unload_complete_box">
  <div>
    <div class="load_unload_complete_box_cell">
      <label class="load_unload_complete_box_cell_title">{#plugin_ictvp_loading_unloading_truck#|escape}:</label>
      <label class="load_unload_complete_box_cell_text">{$load_unload_hours.truck_num|escape}</label>
    </div>
    <div class="load_unload_complete_box_cell">
      <label class="load_unload_complete_box_cell_title">{#plugin_ictvp_loading_unloading_trailer#|escape}:</label>
      <label class="load_unload_complete_box_cell_text">{$load_unload_hours.trailer_num|escape}</label>
    </div>
  </div>
  {foreach from=$load_unload_hours.planned item=planned name=plan}
    <div class="load_unload_complete_box_plan">
      <div class="load_unload_complete_box_cell">
        <label class="load_unload_complete_box_plan_cell_title">{#plugin_ictvp_loading_unloading_address#|escape}:</label>
        <label class="load_unload_complete_box_plan_cell_text">{$planned.address|escape}</label>
      </div>
    </div>
    <div>
      <div class="load_unload_complete_box_cell">
        <label class="load_unload_complete_box_plan_cell_title">{$title_hour|escape}:</label>
      </div>
      <div class="load_unload_complete_box_cell">
        <label class="load_unload_complete_box_plan_cell_title">{$title_planned_hour|escape}:</label>
      </div>
    </div>
    <div>
      <div class="load_unload_complete_box_cell">
        {include file='input_datetime.html'
                 index=$planned.index
                 name='load_unload_date_time'
                 standalone=true
                 width=130
        }
      </div>
      <div class="load_unload_complete_box_cell">
        <label class="load_unload_complete_box_plan_cell_text">{$planned.planned|date_format:#date_mid#|escape}</label>
      </div>
    </div>
    <div class="load_unload_button_container">
      <div>
        <button class="button load_unload_confirm" onclick="confirmSelectedHour('{$planned.index}');return false;">{#plugin_ictvp_loading_unloading_confirm#|escape}</button>
      </div>
    </div>
  {/foreach}
</div>