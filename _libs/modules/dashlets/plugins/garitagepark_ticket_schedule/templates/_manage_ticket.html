{**
  * MANAGE TICKET - MULTIPLE EVENTS
  *}
<form name="manage_ticket_form" class="garitagepark_ticket_schedule_form" method="post" action="" onsubmit="return false;">
  <input type="hidden" name="ticket" id="ticket" value="{$document->get('id')|default:''}" />
  <input type="hidden" name="ticket_name" id="ticket_name" value="{$document->get('name')|escape|default:''}" />
  <input type="hidden" name="customer" id="customer" value="{$document->get('customer')|default:''}" />
  <input type="hidden" name="customer_name" id="customer_name" value="{$document->get('customer_name')|escape|default:''}" />
  <input type="hidden" name="branch" id="branch" value="{$document->get('branch')|default:''}" />
  <input type="hidden" name="contact_person" id="contact_person" value="{$document->get('contact_person')|default:''}" />
  <input type="hidden" name="location" id="location" value="{$document->get('object_name')|escape|default:''}, {$document->get('service_priority_name')|escape}" />
  <input type="hidden" name="description" id="description" value="{$document->get('service_description')|escape|default:''}" />
  <table class="t_table t_layout_table" style="margin: auto; width: auto;">
    <tr>
      <td>
        {array assign='var'
                grouping='scheduled_work'
                dont_copy_values='1'
                values=$events
        }
        {array assign='var'
                upon_table_rows_update="Garitagepark_Ticket_Schedule.clearAssignmentsTable('var_group_`$var.grouping`', this);"
        }
        {array assign='datetime_js_methods'
                onblur="Garitagepark_Ticket_Schedule.onChangeDatetime(this);"
                disallow_date_before=$smarty.now|date_format:#date_iso_short#
                disallow_date_after=$document->get('deadline')|default:''
        }
        <table id="var_group_{$var.grouping}" class="t_grouping_table t_table{if $var.dont_copy_values} dont_copy_values{/if}" style="margin: auto; float: none;">
          <tr>
            <td class="t_panel_caption t_panel_caption_title">{#num#|escape}</td>
            <td class="t_panel_caption t_panel_caption_title" style="width: 225px;">{#plugin_scheduled_time#|escape}</td>
            <td class="t_panel_caption t_panel_caption_title">
              <div class="floatl">
                {#plugin_participants#|escape}
              </div>
              <div class="t_buttons">
              {strip}
                <div id="var_group_{$var.grouping}_plusButton"{if !empty($var.values) && is_array($var.values) && $var.values|@count >= $smarty.const.PH_MAX_GROUP_ROWS} class="disabled"{/if} onclick="addField('var_group_{$var.grouping}'{if $var.dont_copy_values},'','','{$var.dont_copy_values}'{/if}){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                <div id="var_group_{$var.grouping}_minusButton"{if empty($var.values) || !is_array($var.values) || $var.values|@count le 1} class="disabled"{/if} onclick="removeField('var_group_{$var.grouping}'){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
              {/strip}
              </div>
            </td>
          </tr>
          {foreach from=$events item='event' key='eid' name='i'}
          {assign var='event_array' value=$event->getAll()}
          <tr id="var_group_{$var.grouping}_{$smarty.foreach.i.iteration}">
            <td class="hright nowrap">
              <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if empty($var.values) || !is_array($var.values) || $var.values|@count le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function(el) {ldelim} hideField('var_group_{$var.grouping}','{$smarty.foreach.i.iteration}'){if $var.upon_table_rows_update};{$var.upon_table_rows_update|replace:'this':'el'}{/if}; {rdelim}, this);" />&nbsp;<span class="group_table_tow_num" onclick="disableField('var_group_{$var.grouping}','{$smarty.foreach.i.iteration}'){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}">{$smarty.foreach.i.iteration}</span>
            </td>
            <td>
              {include file="input_hidden.html"
                  standalone=true
                  name='id'
                  index=$smarty.foreach.i.iteration
                  value=$event_array.id|default:''
              }
              {include file="input_hidden.html"
                  standalone=true
                  name='name'
                  index=$smarty.foreach.i.iteration
                  value=$event_array.name|default:''
              }
              <a id="error_event_start_{$smarty.foreach.i.iteration}" class="floatl"></a>
              {include file="input_datetime.html"
                  standalone=true
                  name='event_start'
                  index=$smarty.foreach.i.iteration
                  value=$event_array.event_start|default:''
                  width='100'
                  label=#events_start_date#
                  js_methods=$datetime_js_methods
              }
              <span class="floatl" style="padding: 0 5px;">
                {#to#|mb_lower}
              </span>
              <a id="error_event_end_{$smarty.foreach.i.iteration}" class="floatl"></a>
              {include file="input_datetime.html"
                  standalone=true
                  name='event_end'
                  index=$smarty.foreach.i.iteration
                  value=$event_array.event_end|default:''
                  width='100'
                  label=#events_event_end#
                  js_methods=$datetime_js_methods
              }
            </td>
            <td>
              <a id="error_participants_{$smarty.foreach.i.iteration}"></a>
              {include file="input_autocompleter.html"
                  standalone=true
                  name='participants'
                  index=$smarty.foreach.i.iteration
                  autocomplete=$participants_autocomplete
                  autocomplete_var_type='basic'
                  width='300'
                  label=#autocomplete_search_users#
                  show_placeholder='label'
              }
              <br />
              <br />
              {include file=`$templatesDir`_assign_participants.html
                  index=$smarty.foreach.i.iteration
                  user_intervals=$event->get('user_intervals')
                  availability_events=$event->get('availability_events')
                  before_width=$event->get('before_width')
                  event_width=$event->get('event_width')
                  end_width=$event->get('end_width')
                  event_hours=$event->get('event_hours')
                  start_hour=$event->get('start_hour')
              }
            </td>
          </tr>
          {/foreach}
        </table>
      </td>
    </tr>
    <tr>
      <td class="hright">
        {include file=`$theme->templatesDir`cancel_button.html}
        <button name="btn_save" type="button" class="button" onclick="Garitagepark_Ticket_Schedule.multiSave(this.form);">{#plugin_save_scheduled_time#|escape}</button>
      </td>
    </tr>
  </table>
</form>
