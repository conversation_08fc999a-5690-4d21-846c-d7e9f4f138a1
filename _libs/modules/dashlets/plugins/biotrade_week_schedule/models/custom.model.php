<?php
/**
 * Biotrade week schedule plugin custom model class
 */
Class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name = 'biotrade_week_schedule';

    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        return true;
    }

    /**
     * Defining filters for the search
     */
    function prepareFilters() {
        // $filters - array containing description of all filters
        $filters = array();

        $week_options = array();
        $today_date = General::strftime('%Y-%m-%d');
        $current_date_day_of_week = General::strftime('%w', strtotime($today_date));
        if ($current_date_day_of_week == 0) {
            $current_date_day_of_week = 7;
        }
        $current_week_start = General::strftime('%Y-%m-%d', strtotime('-' . ($current_date_day_of_week-1) . ' day', strtotime($today_date)));

        $week_count = 10;
        for ($i = -$week_count; $i <= $week_count; $i++) {
            $selected_week_start = General::strftime('%Y-%m-%d', strtotime(($i < 0 ? '' : '+') . $i . ' week', strtotime($current_week_start)));
            $selected_week_end = General::strftime('%Y-%m-%d', strtotime('+1 week -1 day', strtotime($selected_week_start)));

            $selected_week_number = General::strftime('%Y_%V',
                strtotime(substr($selected_week_start, 0, 4) != substr($selected_week_end, 0, 4) && General::strftime('%V', strtotime($selected_week_end)) != 1 ?
                    $selected_week_start : $selected_week_end));
            $week_options[$selected_week_number] = array(
                'label'        => sprintf('%s %d (%s-%s)', $this->i18n('plugin_biotrade_week_week'), General::strftime('%V', strtotime($selected_week_start)), General::strftime('%d.%m.%Y', $selected_week_start), General::strftime('%d.%m.%Y', $selected_week_end)),
                'option_value' => $selected_week_start
            );
        }

        //prepare filters
        $filter = array (
            'custom_id' => 'show_week',
            'name'      => 'show_week',
            'width'     => 250,
            'value'     => $current_week_start,
            'label'     => $this->i18n('plugin_biotrade_week_show_week'),
            'help'      => $this->i18n('plugin_biotrade_week_show_week'),
            'options'   => $week_options
        );
        $filters['show_week'] = $filter;

        return $filters;
    }

    public function prepareCustomData(&$model) {
        //prepare custom filters
        $filters = array();
        if ($model->get('title_template')) {
            $filters['title_template'] = $model->get('title_template');
            $model->unsetProperty('title_template', true);
        }

        if ($model->get('employees')) {
            $filters['employees'] = $model->get('employees');
            $model->unsetProperty('employees', true);
        }

        $model->set('filters', $filters, true);

        return $model;
    }

    /**
     * Function to parse the settings for the current plugin and
     * to set them as properties to the model
     */
    public function parseDashletPluginSettings($dashlet_settings) {
        $settings_rows = preg_split('/(\n|\r|\r\n)/', $dashlet_settings);
        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);
            $this->$key = $value;
        }
    }

    /**
     * Function to build the week schedule
     */
    public function buildSchedule($date) {
        // define and load custom plugin lang files
        $files = array(PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $this->registry['lang'] . '/custom.ini');
        $this->loadI18NFiles($files);

        $week_end = General::strftime('%Y-%m-%d', strtotime('+1 week -1 day', strtotime($date)));
        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
        $working_days = Calendars_Calendar::getWorkingDays($this->registry, $date, $week_end);
        $country_code = Calendars_Calendar::getCountryCode($this->registry);

        $query = 'SELECT `date` FROM ' . DB_TABLE_COUNTRY_NONWORKDAYS . "\n" .
                 '  WHERE `date`>="' . $date . '" AND `date`<="' . $week_end . '" AND country="' . $country_code . '"';
        $nonworking_days = $this->registry['db']->getCol($query);

        $current_date = $date;
        $dates_list = array();
        while ($current_date <= $week_end) {
            if (!in_array($current_date, $nonworking_days)) {
                $dates_list[] = $current_date;
            }
            $current_date = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($current_date)));
        }

        $today_date = date('Y-m-d');
        $dates_data = array();
        foreach ($dates_list as $dat) {
            $dates_data[$dat] = array(
                'date_iso'       => $dat,
                'date_formatted' => General::strftime('%d.%m.%Y', strtotime($dat)),
                'day_name'       => General::strftime('%A', strtotime($dat)),
                'ray_name'       => '',
                'schedule_id'    => '',
                'meetings'       => array(),
                'future_date'    => (($dat>$today_date) ? 1 : 0),
                'current_date'   => (($dat==$today_date) ? 1 : 0)
            );
        }
        if (!empty($dates_list) && $this->registry['currentUser']->get('employee')) {
            // find additional vars ids
            $sql_for_add_vars = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                                'WHERE (fm.model="Document" AND fm.model_type="' . $this->daily_plan_type_id . '" AND fm.name="' . $this->daily_plan_ray . '")';
            $daily_ray_id = $this->registry['db']->GetOne($sql_for_add_vars);

            // find the data for the daily plans
            $query_docs = 'SELECT d.id, d_cstm_ray.value as ray_id, nomi18n.name as ray_name, DATE_FORMAT(`date`, "%Y-%m-%d") as date_schedule,' . "\n" .
                          '  gt2.article_id as customer_id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, c.type as customer_type, "0" as issued_documents, ' . "\n" .
                          '  gt2.id as row_id, gt2.free_field2 as address, ' . "\n" .
                          '  IF(gt2.article_height, (SELECT IF(fir1.active, id, 0) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir1 WHERE fir1.id=gt2.article_height), 0) as meeting,' . "\n" .
                          '  IF(gt2.article_width,  (SELECT IF(fir2.active, id, 0) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir2 WHERE fir2.id=gt2.article_width),  0) as sell,' . "\n" .
                          '  IF(gt2.article_weight, (SELECT IF(d.active,    id, 0) FROM ' . DB_TABLE_DOCUMENTS               . ' as d    WHERE d.id=gt2.article_weight),    0) as request' . "\n" .
                          'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                          'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                          '  ON (gt2.model = "Document" AND gt2.model_id=d.id AND gt2.article_id IS NOT NULL AND gt2.article_id != "")' . "\n" .
                          'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ray' . "\n" .
                          '  ON (d.id = d_cstm_ray.model_id AND d_cstm_ray.var_id="' . $daily_ray_id . '" AND d_cstm_ray.value IS NOT NULL AND d_cstm_ray.value != "")' . "\n" .
                          'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                          '  ON (d_cstm_ray.value=nomi18n.parent_id AND nomi18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                          'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                          '  ON (gt2.article_id=c.id)' . "\n" .
                          'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                          '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                          'WHERE d.type="' . $this->daily_plan_type_id . '" AND d.active=1 AND d.deleted_by=0 AND DATE_FORMAT(`date`, "%Y-%m-%d") IN ("' . implode('","', $dates_list) . '") AND d.employee="' . $this->registry['currentUser']->get('employee') . '" AND gt2.free_field1!="' . $this->daily_plan_meeting_failed . '"' . "\n" .
                          'ORDER BY d.id ASC, CONVERT(gt2.free_field5, SIGNED INT) ASC, gt2.id ASC';
            $schedule_docs = $this->registry['db']->getAll($query_docs);

            foreach ($schedule_docs as $sch_doc) {
                if (isset($dates_data[$sch_doc['date_schedule']])) {
                    if (empty($dates_data[$sch_doc['date_schedule']]['ray_name'])) {
                        $dates_data[$sch_doc['date_schedule']]['ray_name'] = $sch_doc['ray_name'];
                    }
                    if (empty($dates_data[$sch_doc['date_schedule']]['schedule_id'])) {
                        $dates_data[$sch_doc['date_schedule']]['schedule_id'] = $sch_doc['id'];
                    }
                    $sch_doc['meeting'] = sprintf('%d', $sch_doc['meeting']);
                    $sch_doc['sell'] = sprintf('%d', $sch_doc['sell']);
                    $sch_doc['request'] = sprintf('%d', $sch_doc['request']);
                    if ($sch_doc['meeting'] || $sch_doc['sell'] || $sch_doc['request']) {
                        $sch_doc['issued_documents'] = 1;
                    }
                    $sch_doc['encoded_data'] = base64_encode(json_encode($sch_doc));
                    $dates_data[$sch_doc['date_schedule']]['meetings'][] = $sch_doc;
                }
            }
        }

        // Return the template
        return $dates_data;
    }

    /**
     * Function to prepare the template with schedule for the selected week
     */
    public function prepare_schedule_template($schedule_data) {
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = '_dates_schedule.html';
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'dashlets/plugins/',
            $this->plugin_name,
            '/i18n/',
            $this->registry['lang'],
            '/custom.ini');
        $viewer->loadCustomI18NFiles($i18n_file);

        $viewer->data['related_report'] = $this->related_report;
        $viewer->data['dates_data'] = $schedule_data;
        $viewer->data['sell_available'] = (isset($this->sell_type) ? $this->sell_type : '');
        $viewer->data['request_available'] = (isset($this->request_type) ? $this->request_type : '');
        $viewer->data['meetings_available'] = (isset($this->visit_type) ? $this->visit_type : '');
        $viewer->data['templatesUrl'] = PH_MODULES_URL . 'dashlets/plugins/' . $this->plugin_name . '/templates/';

        $template = $viewer->fetch();

        return $template;
    }

    /**
     * Function to prepare the template with schedule for the selected week
     */
    public function prepare_selected_week_schedule() {
        $selected_date = $this->registry['request']->get('show_week');
        $dates_data = $this->buildSchedule($selected_date);

        $template = $this->prepare_schedule_template($dates_data);
        return $template;
    }

    /**
     * Function to mark meeting as failed
     */
    public function fail_meeting() {

        // get the row which has to be changed
        $row_id = $this->registry['request']->get('row_id');

        if (!$this->registry['request']->get('fail_reason')) {
            //we have just to show lightbox with field for the reason for the fail
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
            $viewer->template = '_fail_reason.html';
            $i18n_file = sprintf('%s%s%s%s%s%s',
                    PH_MODULES_DIR,
                    'dashlets/plugins/',
                    $this->plugin_name,
                    '/i18n/',
                    $this->registry['lang'],
                    '/custom.ini');
            $viewer->loadCustomI18NFiles($i18n_file);
            $viewer->data['row_id'] = $row_id;
            echo 'result = ' . json_encode(array('template' => $viewer->fetch(), 'title' => $this->i18n('plugin_biotrade_week_comment'))) . ';';
            exit;
        }

        // get the document related to the selected row
        $query = 'SELECT `model_id`' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . "\n" .
                 'WHERE `id`="' . $row_id . '"' . "\n";
        $document_id = $this->registry['db']->GetOne($query);

        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

        // GET THE SEARCHED DOCUMENT
        $filters = array('where'      => array('d.id="' . $document_id . '"'),
                         'model_lang' => $this->registry['lang']
        );
        $document = Documents::searchOne($this->registry, $filters);

        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $this->registry->set('get_old_vars', false, true);

        $old_document = clone $document;
        $old_document->sanitize();

        // get all the vars
        $vars_list = $document->get('vars');
        $gt2_vars = '';
        $gt2_var_idx = '';

        // find the needed var
        foreach ($vars_list as $var_idx => $vl) {
            if ($vl['type'] == 'gt2') {
                $gt2_vars = $vl;
                $gt2_var_idx = $var_idx;
                break;
            }
        }

        // complete the selected row with the needed status
        foreach ($gt2_vars['values'] as $row_idx => $row_values) {
            if ($row_idx == $row_id) {
                $gt2_vars['values'][$row_idx]['free_field1'] = $this->daily_plan_meeting_failed;
                /**
                 * NOTE: use value from 'get' and not 'all' of request
                 * @see Request::_fixEnc()
                 */
                $gt2_vars['values'][$row_idx]['article_description'] = General::slashesEscape($this->registry['request']->get('fail_reason', 'get'));
            }
        }

        // set the new values in the document
        $document->set('grouping_table_2', $gt2_vars, true);
        $document->calculateGT2();
        $document->set('table_values_are_set', true, true);
        $document->saveGT2Vars($gt2_vars);

        // GET THE CHANGED DOCUMENT
        $filters = array('where'      => array('d.id="' . $document->get('id') . '"'),
                         'model_lang' => $this->registry['lang']
        );
        $new_document = Documents::searchOne($this->registry, $filters);

        $this->registry->set('get_old_vars', true, true);
        $new_document->getVars();
        $this->registry->set('get_old_vars', false, true);

        Documents_History::saveData($this->registry, array('model' => $new_document, 'action_type' => 'edit', 'new_model' => $new_document, 'old_model' => $old_document));

        $selected_date = $this->registry['request']->get('show_week');
        $dates_data = $this->buildSchedule($selected_date);

        $template = $this->prepare_schedule_template($dates_data);
        return $template;
    }

    /**
     * Function to prepare the lightbox form for adding a new sell document
     */
    public function prepare_sell_lightbox() {
        // include finance classes
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.model.php';

        $sell_document = new Finance_Incomes_Reason($this->registry);
        $sell_document->set('type', $this->sell_type, true);
        $sell_document->set('company', $this->registry['currentUser']->get('default_company'), true);

        //prepare additional variables
        $this->registry->set('get_old_vars', true, true);
        $gt2_var = $sell_document->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        $gt2_var['values'][] = array();
        $sell_document->set('grouping_table_2', $gt2_var, true);

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = '_add_sell.html';
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'dashlets/plugins/',
            $this->plugin_name,
            '/i18n/',
            $this->registry['lang'],
            '/custom.ini');
        $viewer->loadCustomI18NFiles($i18n_file);

        $viewer->data['document_model'] = $sell_document;
        $viewer->data['additional_data'] = $this->registry['request']->get('additional_data');

        $container_data = sprintf('%d_%d_%s_%d', $this->registry['currentUser']->get('default_company'), $this->registry['currentUser']->get('office'), 'cash', $this->registry['currentUser']->get('default_cashbox'));
        $viewer->data['company_data'] = $container_data;

        $template = $viewer->fetch();
        $label_var = 'label_sell_lightbox_' . $this->registry['lang'];
        $lightbox_label = '';

        if (!empty($this->$label_var)) {
            $lightbox_label = $this->$label_var;
        } else {
            $lightbox_label = $this->i18n('plugin_biotrade_week_add_sell_title');
        }

        $lightbox_data = array(
            'title'    => $lightbox_label,
            'template' => $template
        );

        return json_encode($lightbox_data);
        exit;
    }

    /**
     * Function to prepare the lightbox form for adding a new visit document
     */
    public function prepare_visit_lightbox() {
        // include finance classes
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.model.php';

        $visit_document = new Finance_Incomes_Reason($this->registry);
        $visit_document->set('type', $this->visit_type, true);
        $visit_document->set('company', $this->registry['currentUser']->get('default_company'), true);

        //prepare additional variables
        $this->registry->set('get_old_vars', true, true);
        $gt2_var = $visit_document->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        $gt2_var['values'][] = array();
        $visit_document->set('grouping_table_2', $gt2_var, true);

        $additional_data = json_decode(base64_decode($this->registry['request']->get('additional_data')), true);

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = '_add_visit.html';
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'dashlets/plugins/',
            $this->plugin_name,
            '/i18n/',
            $this->registry['lang'],
            '/custom.ini');
        $viewer->loadCustomI18NFiles($i18n_file);

        $viewer->data['document_model'] = $visit_document;
        $viewer->data['additional_data'] = $this->registry['request']->get('additional_data');

        $container_data = sprintf('%d_%d_%s_%d', $this->registry['currentUser']->get('default_company'), $this->registry['currentUser']->get('office'), 'cash', $this->registry['currentUser']->get('default_cashbox'));
        $viewer->data['company_data'] = $container_data;
        $viewer->data['visit_time_start'] = date('Y-m-d H:i');

        $customer_types_show_tags = preg_split('#\s*,\s*#', $this->customer_types_show_tags);

        if (in_array($additional_data['customer_type'], $customer_types_show_tags)) {
            $tags_groups = $this->getSelectedTagsGroups($additional_data['customer_type']);

            //get current pharmacy tags
            $query = 'SELECT tag_id' . "\n" .
                     'FROM ' . DB_TABLE_TAGS_MODELS . "\n" .
                     'WHERE model=\'Customer\' AND model_id="' . $additional_data['customer_id'] . '"' . "\n" .
                     'ORDER BY tag_id';
            $current_tags = $this->registry['db']->GetCol($query);

            foreach ($tags_groups as $idx_grp => $group) {
                if (empty($group['tags'])) {
                    unset($tags_groups[$idx_grp]);
                    continue;
                }
                foreach ($group['tags'] as $idx_tag => $tag) {
                    //the first checked class tag should be checked (take a look at: Bug 3926, comment 7, item 3.)
                    if (in_array($tag['id'], $current_tags)) {
                        $tags_groups[$idx_grp]['tags'][$idx_tag]['checked'] = true;
                        break;
                    }
                }
            }

            $viewer->data['tags_groups'] = $tags_groups;
        }

        $template = $viewer->fetch();
        $label_var = 'label_visit_lightbox_' . $this->registry['lang'];
        $lightbox_label = '';

        if (!empty($this->$label_var)) {
            $lightbox_label = $this->$label_var;
        } else {
            $lightbox_label = $this->i18n('plugin_biotrade_week_add_visit_title');
        }

        $lightbox_data = array(
            'title'    => $lightbox_label,
            'template' => $template
        );

        return json_encode($lightbox_data);
        exit;
    }

    /**
     * Function to prepare the lightbox form for adding a new request document
     */
    public function prepare_request_lightbox() {
        // include documents classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.model.php';

        $request_document = new Document($this->registry);
        $request_document->set('type', $this->request_type, true);

        //prepare additional variables
        $this->registry->set('get_old_vars', true, true);
        $gt2_var = $request_document->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        $gt2_var['values'][] = array();
        $request_document->set('grouping_table_2', $gt2_var, true);

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = '_add_request.html';
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'dashlets/plugins/',
            $this->plugin_name,
            '/i18n/',
            $this->registry['lang'],
            '/custom.ini');
        $viewer->loadCustomI18NFiles($i18n_file);

        $viewer->data['document_model'] = $request_document;
        $viewer->data['additional_data'] = $this->registry['request']->get('additional_data');

        $container_data = sprintf('%d_%d_%s_%d', $this->registry['currentUser']->get('default_company'), $this->registry['currentUser']->get('office'), 'cash', $this->registry['currentUser']->get('default_cashbox'));
        $viewer->data['company_data'] = $container_data;

        $template = $viewer->fetch();
        $label_var = 'label_request_lightbox_' . $this->registry['lang'];
        $lightbox_label = '';

        if (!empty($this->$label_var)) {
            $lightbox_label = $this->$label_var;
        } else {
            $lightbox_label = $this->i18n('plugin_biotrade_week_add_request_title');
        }

        $lightbox_data = array(
            'title'    => $lightbox_label,
            'template' => $template
        );

        return json_encode($lightbox_data);
        exit;
    }

    /**
     * Function to add a new sell
     */
    public function add_sell() {
        // include finance classes
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.model.php';

        // load finance i18n files
        $i18n_files_dir = PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/';
        $files_list = FilesLib::readDir($i18n_files_dir, false, '', '', true);
        $this->loadI18NFiles($files_list);

        $messages = array();
        $operation_result = false;

        // build an empty model
        $sell_document = new Finance_Incomes_Reason($this->registry);
        // set both type + company to get default settings for VAT rate and currency
        $sell_document->set('type', $this->sell_type, true);
        $sell_document->set('company', $this->registry['currentUser']->get('default_company'), true);

        $gt2_table = $sell_document->getGT2Vars();

        $sell_document->set('description', $this->registry['request']->get('comment'), true);
        $additional_data = json_decode(base64_decode($this->registry['request']->get('meeting_data')), true);
        $sell_document->set('customer', $additional_data['customer_id'], true);

        $container_data = sprintf('%d_%d_%s_%d', $this->registry['currentUser']->get('default_company'), $this->registry['currentUser']->get('office'), 'cash', $this->registry['currentUser']->get('default_cashbox'));

        $sell_document->set('company_data', $container_data, true);
        $sell_document->set('office', $this->registry['currentUser']->get('office'), true);
        $sell_document->set('payment_type', 'cash', true);
        $sell_document->set('container_id', $this->registry['currentUser']->get('default_cashbox'), true);

        $sell_document->set('issue_date', $additional_data['date_schedule'], true);
        $sell_document->set('employee', $this->registry['currentUser']->get('employee'), true);

        if ($this->registry['request']->get('paid')) {
            $sell_document->set('status', 'finished', true);
            if ($gt2_table['plain_values']['total_with_vat'] > 0) {
                $sell_document->set('finance_after_action', 'payment', true);
                $sell_document->set('currency', $gt2_table['plain_values']['currency'], true);
                $sell_document->set('payment_container', $container_data, true);
                $sell_document->set('payment_date', $additional_data['date_schedule'], true);
                $sell_document->set('payment_reason', '', true);
                $sell_document->set('payment_container_amount', $gt2_table['plain_values']['total_with_vat'], true);
                $sell_document->set('container_rate', 1, true);
                $sell_document->set('container_currency', $gt2_table['plain_values']['currency'], true);
            } else {
                $sell_document->set('finance_after_action', 'finish', true);
            }
        }

        // get the financial document type
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $filters = array(
            'where' => array(
                'fdt.active = 1',
                'fdt.id = ' . $this->sell_type
            ),
            'sanitize' => true
        );
        $sell_document_type = Finance_Documents_Types::searchOne($this->registry, $filters);

        $sell_document->set('name', $sell_document_type->get('name'), true);
        $sell_document->set('date_of_payment_count', $sell_document_type->get('default_date_of_payment_count'), true);
        $sell_document->set('date_of_payment_period_type', $sell_document_type->get('default_date_of_payment_period_type'), true);
        $sell_document->set('date_of_payment_point', $sell_document_type->get('default_date_of_payment_point'), true);
        $sell_document->set('type_name', $sell_document_type->get('name'), true);
        $sell_document->setDepartment(false, $sell_document_type->get('default_department'));
        $sell_document->setGroup(false, $sell_document_type->get('default_group'));

        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                 'WHERE parent_id=' . $sell_document->get('company') . ' AND lang="' . $sell_document->get('model_lang') . '"';
        $sell_document->set('company_name', $this->registry['db']->GetOne($query), true);

        $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                 'WHERE parent_id=' . $sell_document->get('office') . ' AND lang="' . $sell_document->get('model_lang') . '"';
        $sell_document->set('office_name', $this->registry['db']->GetOne($query), true);

        $this->registry['db']->StartTrans();
        if ($sell_document->save()) {
            //write history for the sell
            $filters = array('where' => array('fir.id = ' . $sell_document->get('id')));
            $new_sell_document = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_sell_document->getGT2Vars();
            $this->registry->set('get_old_vars', false, true);

            $old_model = new Finance_Incomes_Reason($this->registry);
            $old_model->set('type', $new_sell_document->get('type'), true);
            $old_model->set('company', $new_sell_document->get('company'), true);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $this->registry->set('get_old_vars', false, true);
            $old_model->sanitize();

            require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
            Finance_Incomes_Reasons_History::saveData($this->registry,
                                                      array('action_type' => 'add',
                                                            'new_model' => $new_sell_document,
                                                            'old_model' => $old_model
                                                      ));
            $automations_controller = new Automations_Controller($this->registry);
            $automations_controller->executeActionAutomations($old_model, $new_sell_document, 'add');

            if ($sell_document->get('finance_after_action') == 'payment') {
                Finance_Incomes_Reasons_History::saveData($this->registry,
                                                          array('action_type' => 'addpayment',
                                                                'new_model' => $new_sell_document,
                                                                'old_model' => $new_sell_document
                                                          ));
            }

            // record the relation in document
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            $filters = array('where' => array('d.id = ' . $additional_data['id']));

            $schedule_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $schedule_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            $old_schedule_document = clone $schedule_document;
            $old_schedule_document->sanitize();

            // get all the vars
            $vars_list = $schedule_document->get('vars');
            $gt2_vars = '';
            $gt2_var_idx = '';

            // find the needed var
            foreach ($vars_list as $var_idx => $vl) {
                if ($vl['type'] == 'gt2') {
                    $gt2_vars = $vl;
                    $gt2_var_idx = $var_idx;
                    break;
                }
            }

            // complete the selected row with the needed status
            foreach ($gt2_vars['values'] as $row_idx => $row_values) {
                if ($row_idx == $additional_data['row_id']) {
                    $gt2_vars['values'][$row_idx]['free_field1'] = $this->daily_plan_meeting_marked;
                    $gt2_vars['values'][$row_idx]['article_width'] = $new_sell_document->get('id');
                }
            }

            // set the new values in the document
            $schedule_document->set('grouping_table_2', $gt2_vars, true);
            $schedule_document->calculateGT2();
            $schedule_document->set('table_values_are_set', true, true);
            $schedule_document->saveGT2Vars($gt2_vars);

            // GET THE CHANGED DOCUMENT
            $filters = array('where'      => array('d.id="' . $schedule_document->get('id') . '"'),
                             'model_lang' => $this->registry['lang']
            );
            $new_schedule_document = Documents::searchOne($this->registry, $filters);

            $this->registry->set('get_old_vars', true, true);
            $new_schedule_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            Documents_History::saveData($this->registry, array('model' => $schedule_document, 'action_type' => 'edit', 'new_model' => $new_schedule_document, 'old_model' => $old_schedule_document));

            if ($this->registry['request']->get('paid')) {
                //check available quantities of the requested articles
                $messages = $this->checkAddOutgoingHandover($new_sell_document);

                if (!empty($messages)) {
                    // error occurred when defining warehouse quantities
                    $this->registry['db']->FailTrans();
                    $this->registry['db']->CompleteTrans();
                    return json_encode(array('result' => $operation_result, 'messages' => $messages));
                }

                if (!$new_sell_document->get('do_not_create_handover')) {
                    //add outgoing handover
                    $messages = $this->addOutgoingHandover($new_sell_document);
                    if (!empty($messages)) {
                        //fail transaction in case of erred procedure of adding handover
                        $this->registry['db']->FailTrans();
                    }
                }
            }
        } else {
            $messages[] = $this->i18n('plugin_biotrade_week_failed_to_save_sell');
        }

        $err_messages = $this->registry['messages']->getErrors();
        foreach ($err_messages as $err) {
            $messages[] = $err;
        }

            if (empty($messages) && !$this->registry['db']->HasFailedTrans()) {
            $operation_result = true;
        } else {
            $this->registry['db']->FailTrans();
        }

        $this->registry['db']->CompleteTrans();

        return json_encode(array('result' => $operation_result, 'messages' => $messages));
    }

    /**
     * Function to add a new visit
     */
    public function add_visit() {
        // include finance classes
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.model.php';

        // load finance i18n files
        $i18n_files_dir = PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/';
        $files_list = FilesLib::readDir($i18n_files_dir, false, '', '', true);
        $this->loadI18NFiles($files_list);

        $messages = array();
        $operation_result = false;

        // collect tags from the request if available
        $tags_groups_ids = preg_split('#\s*,\s*#', $this->customer_show_tags_groups);
        $new_tags = array();
        $reasons_tags = array();
        foreach ($this->registry['request']->getAll() as $req_key => $req_tag) {
            if (preg_match('#^tag_grp_([0-9]*)$#', $req_key)) {
                if (in_array(preg_replace('#^tag_grp_([0-9]*)$#', '$1', $req_key), $tags_groups_ids)) {
                    // customer tags
                    $new_tags[] = $req_tag;
                } else {
                    if (is_array($req_tag)) {
                        $reasons_tags = array_merge($reasons_tags, $req_tag);
                    } else {
                        $reasons_tags[] = $req_tag;
                    }
                }
            }
        }

        // build empty model
        $visit_document = new Finance_Incomes_Reason($this->registry);
        // set both type + company to get default settings for VAT rate and currency
        $visit_document->set('type', $this->visit_type, true);
        $visit_document->set('company', $this->registry['currentUser']->get('default_company'), true);
        $visit_document->getGT2Vars();

        $visit_document->set('description', $this->registry['request']->get('comment'), true);
        $additional_data = json_decode(base64_decode($this->registry['request']->get('meeting_data')), true);
        $visit_document->set('customer', $additional_data['customer_id'], true);

        $container_data = sprintf('%d_%d_%s_%d', $this->registry['currentUser']->get('default_company'), $this->registry['currentUser']->get('office'), 'cash', $this->registry['currentUser']->get('default_cashbox'));

        $visit_document->set('company_data', $container_data, true);
        $visit_document->set('office', $this->registry['currentUser']->get('office'), true);
        $visit_document->set('payment_type', 'cash', true);
        $visit_document->set('container_id', $this->registry['currentUser']->get('default_cashbox'), true);

        $visit_document->set('issue_date', $additional_data['date_schedule'], true);
        //$visit_document->set('status', 'finished', true);
        $visit_document->set('employee', $this->registry['currentUser']->get('employee'), true);

        // get the financial document type
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $filters = array(
            'where' => array(
                'fdt.active = 1',
                'fdt.id = ' . $this->visit_type
            ),
            'sanitize' => true
        );
        $visit_document_type = Finance_Documents_Types::searchOne($this->registry, $filters);

        $visit_document->set('name', $visit_document_type->get('name'), true);
        $visit_document->set('date_of_payment_count', $visit_document_type->get('default_date_of_payment_count'), true);
        $visit_document->set('date_of_payment_period_type', $visit_document_type->get('default_date_of_payment_period_type'), true);
        $visit_document->set('date_of_payment_point', $visit_document_type->get('default_date_of_payment_point'), true);
        $visit_document->set('type_name', $visit_document_type->get('name'), true);
        $visit_document->set('fin_field_1', $this->registry['request']->get('visit_time_start'), true);
        $visit_document->set('fin_field_2', date('Y-m-d H:i'), true);
        $visit_document->setDepartment(false, $visit_document_type->get('default_department'));
        $visit_document->setGroup(false, $visit_document_type->get('default_group'));

        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                 'WHERE parent_id=' . $visit_document->get('company') . ' AND lang="' . $visit_document->get('model_lang') . '"';
        $visit_document->set('company_name', $this->registry['db']->GetOne($query), true);

        $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                 'WHERE parent_id=' . $visit_document->get('office') . ' AND lang="' . $visit_document->get('model_lang') . '"';
        $visit_document->set('office_name', $this->registry['db']->GetOne($query), true);

        $this->registry['db']->StartTrans();
        if ($visit_document->save()) {
            //write history for the visit
            $filters = array('where' => array('fir.id = ' . $visit_document->get('id')));
            $new_visit_document = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_visit_document->getGT2Vars();
            $this->registry->set('get_old_vars', false, true);

            $old_model = new Finance_Incomes_Reason($this->registry);
            $old_model->set('type', $new_visit_document->get('type'), true);
            $old_model->set('company', $new_visit_document->get('company'), true);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $this->registry->set('get_old_vars', false, true);
            $old_model->sanitize();

            require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
            Finance_Incomes_Reasons_History::saveData($this->registry,
                array('action_type' => 'add',
                      'new_model' => $new_visit_document,
                      'old_model' => $old_model
                )
            );

            $automations_controller = new Automations_Controller($this->registry);
            $automations_controller->executeActionAutomations($old_model, $new_visit_document, 'add');

            if (!empty($reasons_tags)) {
                $old_model = clone $new_visit_document;
                $old_model->getModelTagsForAudit();
                $old_model->sanitize();

                $new_visit_document->set('tags', $reasons_tags, true);
                if ($new_visit_document->updateTags(array('skip_permissions' => true))) {
                    // get the new customer
                    $filters = array('where'      => array('fir.id="' . $new_visit_document->get('id') . '"'),
                                     'model_lang' => $this->registry['lang']);
                    $new_visit_document = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                    $new_visit_document->getModelTagsForAudit();
                    $new_visit_document->sanitize();
                    Finance_Incomes_Reasons_History::saveData($this->registry, array('model' => $new_visit_document, 'action_type' => 'tag', 'new_model' => $new_visit_document, 'old_model' => $old_model));
                }
            }

            // record the relation in document
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            $filters = array('where' => array('d.id = ' . $additional_data['id']));

            $schedule_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $schedule_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            $old_schedule_document = clone $schedule_document;
            $old_schedule_document->sanitize();

            // get all the vars
            $vars_list = $schedule_document->get('vars');
            $gt2_vars = '';
            $gt2_var_idx = '';

            // find the needed var
            foreach ($vars_list as $var_idx => $vl) {
                if ($vl['type'] == 'gt2') {
                    $gt2_vars = $vl;
                    $gt2_var_idx = $var_idx;
                    break;
                }
            }

            // complete the selected row with the needed status
            foreach ($gt2_vars['values'] as $row_idx => $row_values) {
                if ($row_idx == $additional_data['row_id']) {
                    $gt2_vars['values'][$row_idx]['free_field1'] = $this->daily_plan_meeting_marked;
                    // IMPORTANT!!! when changing this, change the report: biotrade_made_failed_visits
                    $gt2_vars['values'][$row_idx]['article_height'] = $new_visit_document->get('id');
                }
            }

            // set the new values in the document
            $schedule_document->set('grouping_table_2', $gt2_vars, true);
            $schedule_document->calculateGT2();
            $schedule_document->set('table_values_are_set', true, true);
            $schedule_document->saveGT2Vars($gt2_vars);

            // GET THE CHANGED DOCUMENT
            $filters = array('where'      => array('d.id="' . $schedule_document->get('id') . '"'),
                             'model_lang' => $this->registry['lang']
            );
            $new_schedule_document = Documents::searchOne($this->registry, $filters);

            $this->registry->set('get_old_vars', true, true);
            $new_schedule_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            Documents_History::saveData($this->registry, array('model' => $schedule_document, 'action_type' => 'edit', 'new_model' => $new_schedule_document, 'old_model' => $old_schedule_document));

            // check if the customer should be tagged
            $types_to_show_tags = preg_split('#\s*,\s*#', $this->customer_types_show_tags);

            if (in_array($additional_data['customer_type'], $types_to_show_tags)) {
                // get the current tags of the selected pharmacy
                $query = 'SELECT tag_id ' . "\n" .
                         'FROM ' . DB_TABLE_TAGS_MODELS . "\n" .
                         'WHERE model=\'Customer\' AND model_id="' . $additional_data['customer_id'] . '"';
                $current_tags = $this->registry['db']->GetCol($query);

                $groups_tags = $this->getSelectedTagsGroups($additional_data['customer_type']);
                $settings_tags = array();
                foreach ($groups_tags as $gt) {
                    $settings_tags = array_merge($settings_tags, array_keys($gt['tags']));
                }

                $removed_tags = array();
                foreach ($current_tags as $tag) {
                    //remove all classes tags, the class tag should be only one
                    if (!in_array($tag, $settings_tags)) {
                        $new_tags[] = $tag;
                    } else {
                        $removed_tags[] = $tag;
                    }
                }

                $diff_tags = array_diff($new_tags, $current_tags);
                if (!empty($diff_tags) || count($removed_tags) > 1) {
                    // include required class for editing customers
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';

                    // get the old customer
                    $filters = array('where'      => array('c.id="' . $additional_data['customer_id'] . '"'),
                                     'model_lang' => $this->registry['lang']);
                    $customer = Customers::searchOne($this->registry, $filters);
                    $old_customer = clone $customer;
                    $old_customer->getModelTagsForAudit();
                    $old_customer->sanitize();

                    $customer->set('tags', $new_tags, true);

                    if ($customer->updateTags(array('skip_permissions' => true))) {
                        // get the new customer
                        $filters = array('where'      => array('c.id="' . $additional_data['customer_id'] . '"'),
                                         'model_lang' => $this->registry['lang']);
                        $new_customer = Customers::searchOne($this->registry, $filters);
                        $new_customer->getModelTagsForAudit();
                        $new_customer->sanitize();

                        Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'tag', 'new_model' => $new_customer, 'old_model' => $old_customer));
                    }
                }
            }
        } else {
            $messages[] = $this->i18n('plugin_biotrade_week_failed_to_save_visit');
        }

        $err_messages = $this->registry['messages']->getErrors();
        foreach ($err_messages as $err) {
            $messages[] = $err;
        }

        if (empty($messages) && !$this->registry['db']->HasFailedTrans()) {
            $operation_result = true;
        } else {
            $this->registry['db']->FailTrans();
        }

        $this->registry['db']->CompleteTrans();

        return json_encode(array('result' => $operation_result, 'messages' => $messages));
    }

    /**
     * Function to add a new request
     */
    public function add_request() {
        // include documents classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.model.php';

        // load document i18n files
        $i18n_files_dir = PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/';
        $files_list = FilesLib::readDir($i18n_files_dir, false, '', '', true);
        $this->loadI18NFiles($files_list);

        $messages = array();
        $operation_result = false;

        // build an empty model
        $request_document = new Document($this->registry);
        $request_document->set('type', $this->request_type, true);
        $request_document->set('description', $this->registry['request']->get('comment'), true);
        $additional_data = json_decode(base64_decode($this->registry['request']->get('meeting_data')), true);
        $request_document->set('customer', $additional_data['customer_id'], true);
        $request_document->set('date', $additional_data['date_schedule'], true);
        $request_document->set('employee', $this->registry['currentUser']->get('employee'), true);

        // add main branch if customer is a company and has such
        $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS . "\n" .
                 'WHERE subtype=\'branch\' AND is_main=1 AND parent_customer=\'' . $additional_data['customer_id'] . '\'';
        $request_document->set('branch', $this->registry['db']->GetOne($query), true);

        // get the document type
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
        $filters = array(
            'where' => array(
                'dt.active = 1',
                'dt.id = ' . $this->request_type
            ),
            'sanitize' => true
        );
        $request_document_type = Documents_Types::searchOne($this->registry, $filters);

        $request_document->set('name', $request_document_type->get('default_name'), true);
        $request_document->set('group', $request_document_type->getDefaultGroup(), true);
        $request_document->set('department', $request_document_type->getDefaultDepartment(), true);

        // all the GT2 data comes from POST into the variable
        $this->registry->set('get_old_vars', false, true);
        $request_document->getVars();

        $this->registry['db']->StartTrans();
        if ($request_document->save()) {
            //write history for the request
            $filters = array('where' => array('d.id = ' . $request_document->get('id')));
            $new_request_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_request_document->getVars();

            $old_model = new Document($this->registry);
            $old_model->getVars();
            $old_model->sanitize();

            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            Documents_History::saveData(
                $this->registry,
                array('action_type' => 'add',
                      'new_model' => $new_request_document,
                      'old_model' => $old_model
                )
            );

            $automations_controller = new Automations_Controller($this->registry);
            $automations_controller->executeActionAutomations($old_model, $new_request_document, 'add');

            // record the relation in schedule document
            $filters = array('where' => array('d.id = ' . $additional_data['id']));

            $schedule_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $schedule_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            $old_schedule_document = clone $schedule_document;
            $old_schedule_document->sanitize();

            // get all the vars
            $vars_list = $schedule_document->get('vars');
            $gt2_vars = '';
            $gt2_var_idx = '';

            // find the needed var
            foreach ($vars_list as $var_idx => $vl) {
                if ($vl['type'] == 'gt2') {
                    $gt2_vars = $vl;
                    $gt2_var_idx = $var_idx;
                    break;
                }
            }

            // complete the selected row with the needed status
            foreach ($gt2_vars['values'] as $row_idx => $row_values) {
                if ($row_idx == $additional_data['row_id']) {
                    $gt2_vars['values'][$row_idx]['free_field1'] = $this->daily_plan_meeting_marked;
                    $gt2_vars['values'][$row_idx]['article_weight'] = $new_request_document->get('id');
                }
            }

            // set the new values in the document
            $schedule_document->set('grouping_table_2', $gt2_vars, true);
            $schedule_document->calculateGT2();
            $schedule_document->set('table_values_are_set', true, true);
            $schedule_document->saveGT2Vars($gt2_vars);

            // GET THE CHANGED DOCUMENT
            $filters = array('where'      => array('d.id="' . $schedule_document->get('id') . '"'),
                             'model_lang' => $this->registry['lang']
            );
            $new_schedule_document = Documents::searchOne($this->registry, $filters);

            $this->registry->set('get_old_vars', true, true);
            $new_schedule_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            Documents_History::saveData($this->registry, array('model' => $schedule_document, 'action_type' => 'edit', 'new_model' => $new_schedule_document, 'old_model' => $old_schedule_document));
        } else {
            $messages[] = $this->i18n('plugin_biotrade_week_failed_to_save_request');
        }

        $err_messages = $this->registry['messages']->getErrors();
        foreach ($err_messages as $err) {
            $messages[] = $err;
        }

            if (empty($messages) && !$this->registry['db']->HasFailedTrans()) {
            $operation_result = true;
        } else {
            $this->registry['db']->FailTrans();
        }

        $this->registry['db']->CompleteTrans();

        return json_encode(array('result' => $operation_result, 'messages' => $messages));
    }

    /**
     * Function to check if outgoing handover can be added
     *
     * @param object $fir - finance incomes reasons model
     * @return array $messages - array of messages if the handover could not be added
     */
    public function checkAddOutgoingHandover(&$fir) {
        $messages = array();
        $f = clone $fir;
        if ($f->isDefined('grouping_table_2')) {
            $gt2_table = $f->get('grouping_table_2');
        } else {
            $gt2_table = $f->getGT2Vars();
        }

        // define the warehouse of the current user employee
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        $filters = array(
            'where' => array(
                'fwh.active = 1',
                'fwh.employees = "' . $this->registry['currentUser']->get('employee') . '"'
            )
        );
        $warehouse = Finance_Warehouses::searchOne($this->registry, $filters);

        if (empty($warehouse)) {
            $messages[] = $this->i18n('plugin_biotrade_no_warehouse_for_the_selected_user');
            return $messages;
        }

        // find quantities of the selected nomenclatures
        $warehouse_availabilities = array();

        $prec = $this->registry['config']->getSectionParams('precision');

        // shows what quantity from each batch has to be taken
        $this->batch_quantities = $articles_erred = $warehouse_availabilities = array();

        $idx = 0;
        foreach ($gt2_table['values'] as $row_id => $row_data) {
            $idx++;
            //do some validation of articles and quantities
            if (!empty($row_data['article_id']) && !empty($row_data['quantity'])) {
                // we will not merge quantities as if we have one and the same article
                //into 2 or more rows...this will broke handover add procedure
                /*
                if (!array_key_exists($row_data['article_id'], $nomenclatures_quantities_list)) {
                    $nomenclatures_quantities_list[$row_data['article_id']]['quantity'] = 0;
                    $nomenclatures_quantities_list[$row_data['article_id']]['name'] = $row_data['article_name'];
                }
                $nomenclatures_quantities_list[$row_data['article_id']]['quantity'] += $row_data['quantity'];
                */
                if (!isset($warehouse_availabilities[$row_data['article_id']])) {
                    $warehouse_availabilities[$row_data['article_id']] = $warehouse->getAvailableQuantity(array('nom_id' => $row_data['article_id']));
                    $warehouse_availabilities[$row_data['article_id']] = reset($warehouse_availabilities[$row_data['article_id']]);
                }
                // check if the quantity is available
                if (empty($warehouse_availabilities[$row_data['article_id']]['quantity']) || ($warehouse_availabilities[$row_data['article_id']]['quantity'] - $row_data['quantity']) < 0) {
                    unset($gt2_table['values'][$row_id]);
                    if (empty($articles_erred[$row_data['article_id']])) {
                        $messages[] = sprintf($this->i18n('plugin_biotrade_no_warehouse_quantity_from_the_selected_nomenclature'), $row_data['article_name']);
                        $articles_erred[$row_data['article_id']] = true;
                    }
                } else {
                    // separate the quantity by batches in FIFO style (the user cannot select articles and their batches)
                    $left_article_quantity = $row_data['quantity'];
                    foreach ($warehouse_availabilities[$row_data['article_id']]['batch_data'] as $bd => $batch_data) {
                        if ($left_article_quantity > 0) {
                            $current_batch_quantity = 0;
                            if ($batch_data['quantity'] < $left_article_quantity) {
                                // transfer the entire quantity
                                $current_batch_quantity = $batch_data['quantity'];
                                unset($warehouse_availabilities[$row_data['article_id']]['batch_data'][$bd]);
                            } else {
                                // transfer only the needed quantity
                                $current_batch_quantity = $left_article_quantity;
                                $warehouse_availabilities[$row_data['article_id']]['batch_data'][$bd]['quantity'] -= $left_article_quantity;
                            }
                            // reduce the total quantity as well so that check if the quantity is available works correctly
                            $warehouse_availabilities[$row_data['article_id']]['quantity'] -= $current_batch_quantity;
                            $batch_data['quantity'] = $current_batch_quantity;
                            $this->batch_quantities[$row_id][] = $batch_data;
                            $left_article_quantity = round($left_article_quantity - $current_batch_quantity, $prec['gt2_quantity']);
                        } else {
                            // if there is no quantity to be searched, step out of the cycle
                            break;
                        }
                    }
                }
            } elseif (empty($row_data['article_id']) && empty($row_data['quantity'])) {
                unset($gt2_table['values'][$row_id]);
                continue;
            } elseif (empty($row_data['article_id'])) {
                $messages[] = sprintf($this->i18n('plugin_biotrade_no_article_selected'), $idx);
            } elseif (empty($row_data['quantity'])) {
                $messages[] = sprintf($this->i18n('plugin_biotrade_no_quantity_inserted'), $idx);
            }
        }

        if (empty($gt2_table['values'])) {
            $fir->set('do_not_create_handover', true, true);
            if (!empty($this->visit_type) && $fir->get('type') == $this->visit_type) {
                //allow adding document without any articles in it
            } else {
                $messages[] = $this->i18n('plugin_biotrade_no_articles_selected');
            }
        }

        return $messages;
    }

    /**
     * Function to add outgoing handover
     *
     * @param object $fir - finance incomes reasons model
     * @return array $messages - array of errors if the handover could not be added, empty array if no errors
     */
    public function addOutgoingHandover($fir) {
        $messages = array();

        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
        //load finance warehouses documents i18n file
        $this->registry['translater']->loadFile(PH_MODULES_DIR  . 'finance/i18n/' . $this->registry['lang'] . '/finance_warehouses_documents.ini');


        $employee = $this->registry['currentUser']->get('employee');
        $is_portal = $this->registry['currentUser']->get('is_portal');
        $today = General::strftime($this->i18n('date_iso_short'));
        $request = &$this->registry['request'];

        // define the warehouse of the current user employee
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        $filters = array(
            'where' => array(
                'fwh.active = 1',
                'fwh.employees = "' . $this->registry['currentUser']->get('employee') . '"'
            )
        );
        $warehouse = Finance_Warehouses::searchOne($this->registry, $filters);
        $wid = $warehouse->get('id');

        // clear temp data from POST
        if ($request->isPost()) {
            $tmp_post = $request->getAll('post');
            foreach ($tmp_post as $k => $v) {
                $request->remove($k);
            }
        }

        // create outgoing handover, set all necessary data in POST
        $request_params = array(
            'handover_direction' => 'outgoing',
            'name' => '',
            'gt2_requested' => 1,
            'department' => $this->department_all,
            'group' => PH_ROOT_GROUP,
            'is_portal' => $is_portal,
            'active' => 1
        );
        foreach ($request_params as $k => $v) {
            $request->set($k, $v, 'post', true);
        }
        $request_params_wh = array(
            'from' => $employee,
            'to' => $fir->get('customer_name'),
            'location' => $this->i18n('finance_warehouses_documents_warehouse'),
            'date' => $today,
            'description' => '',
            'notes' => '',
            'status' => 'finished'
        );
        foreach ($request_params_wh as $k => $v) {
            $request->set($k, array($wid => $v), 'post', true);
        }
        $gt2 = $fir->get('grouping_table_2');
        $values_post = array();
        if (empty($gt2['values'])) {
            return $messages;
        }
        foreach ($gt2['values'] as $row_idx => $row) {
            if (empty($row) || empty($row['quantity'])) {
                continue;
            }
            $row['warehouse' . $wid . '_quantity'] = $row['total_quantity'] = $row['quantity'];
            //$row['warehouse' . $wid . '_available'] = $row['total_available'] = '';
            if (empty($values_post)) {
                $values_post = array_fill_keys(array_keys($row), array());
            }
            foreach ($row as $k => $v) {
                $values_post[$k][$row_idx] = $v;
            }
            if (!empty($this->batch_quantities[$row_idx])) {
                // prepare batch data for row
                $request_params_batch = array_fill_keys(array('wh', 'batch', 'quantity', 'expire', 'custom', 'delivery_price', 'currency'), array());
                foreach ($this->batch_quantities[$row_idx] as $bidx => $bd) {
                    $request_params_batch['wh'][] = $wid;
                    $request_params_batch['batch'][] = $bd['batch'];
                    $request_params_batch['quantity'][] = $bd['quantity'];
                    $request_params_batch['expire'][] = $bd['expire'];
                    $request_params_batch['custom'][] = $bd['custom'];
                    $request_params_batch['delivery_price'][] = $bd['delivery_price'];
                    $request_params_batch['currency'][] = $bd['currency'];
                }
                foreach ($request_params_batch as $k => $v) {
                    $request->set('article_' . $row_idx . '_' . $k, $v, 'post', true);
                }
            }
        }

        foreach ($values_post as $k => $v) {
            if (in_array($k, array('id', 'model', 'model_id', 'current', 'translated', 'translated_by', 'lang', 'parent_id', 'added', 'added_by', 'modified', 'modified_by', 'date_from', 'date_to')) ||
            preg_match('/index|formula/', $k)) {
                continue;
            }
            $request->set($k, $v, 'post', true);
        }
        foreach ($gt2['plain_values'] as $k => $v) {
            $request->set($k, $v, 'post', true);
        }
        $params = array(
            'model' => $fir,
            'type' => PH_FINANCE_TYPE_HANDOVER
        );
        $result = Finance_Warehouses_Documents::addMultipleDocuments($this->registry, $params);
        if (!$result['status']) {
            $messages[] = $this->i18n('plugin_biotrade_week_failed_to_save_outgoing_handover');
        }

        return $messages;
    }

    /**
     * Function to get the tags from the options as groups
     */
    public function getSelectedTagsGroups($record_type, $record_model='customers') {
        $tags_groups = array();

        $tags_groups_ids = preg_split('#\s*,\s*#', $this->customer_show_tags_groups);
        $tags_ids = preg_split('#\s*,\s*#', $this->customer_show_tags_ids);
        $required_tags_groups = preg_split('#\s*,\s*#', $this->required_tags_groups);
        $record_type = array($record_type, $this->visit_type);
        $tags_groups_models = array('customers');
        foreach ($tags_groups_ids as $t_idx => $t) {
            if (!preg_match('#^([a-z_])*([0-9]+)$#', $t)) {
                unset($tags_groups_ids[$t_idx]);
            } else {
                $tags_groups_ids[$t_idx] = preg_replace('#^(([a-z_]*)_)?([0-9]+)$#', '$3', $t);
                $tags_groups_models[] = preg_replace('#^(([a-z_]*)_)?([0-9]+)$#', '$2', $t);
                preg_match_all('#^(([a-z_]*)_)?([0-9]+)$#', $t, $matches);

            }
        }
        $tags_groups_models = array_filter($tags_groups_models);

        foreach ($tags_ids as $t_idx => $t) {
            if (!preg_match('#^[0-9]+$#', $t)) {
                unset($tags_ids[$t_idx]);
            }
        }

        if (empty($tags_groups_ids) && empty($tags_ids)) {
            return $tags_groups;
        }

        //get the tags
        $query = 'SELECT t.id, ti18n.name as label, t.section as tag_group, tsi18n.name as tag_group_name, t.model' . "\n" .
                 'FROM ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                 'INNER JOIN ' . DB_TABLE_TAGS_TYPES . ' AS tt' . "\n" .
                 '  ON (tt.tag_id=t.id AND tt.type_id IN ("' . implode('","', $record_type) . '"))' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n' . "\n" .
                 '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $this->registry['lang'] . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TAGS_SECTIONS_I18N . ' AS tsi18n' . "\n" .
                 '  ON t.section=tsi18n.parent_id AND tsi18n.lang="' . $this->registry['lang'] . '"' . "\n" .
                 'WHERE t.model IN ("' . implode('","', $tags_groups_models) . '") AND t.active=1 AND t.deleted_by=0';
        if (!empty($tags_ids)) {
            $query .= ' AND t.id IN ("' . implode('","', $tags_ids) . '")';
        }
        if (!empty($tags_groups_ids)) {
            $query .= ' AND t.section IN ("' . implode('","', $tags_groups_ids) . '")';
        }
        $query .= "\n" . 'ORDER BY FIND_IN_SET(tag_group, "' . implode(',', $tags_groups_ids) . '"), t.place';
        $tags = $this->registry['db']->GetAll($query);

        // prepare the tags by groups
        foreach ($tags as $tag) {
            if (!empty($tags_groups_ids)) {
                $current_group_id = $tag['tag_group'];
            } else {
                $current_group_id = 0;
            }

            if (!isset($tags_groups[$current_group_id])) {
                $tags_groups[$current_group_id] = array(
                    'id'   => $tag['tag_group'],
                    'name' => (!empty($tags_groups_ids) ? $tag['tag_group_name'] : ''),
                    'type' => ($tag['model'] == 'customers' ? 'radio' : 'checkbox'),
                    'required' => in_array($current_group_id, $required_tags_groups),
                    'tags' => array()
                );
            }
            $tags_groups[$current_group_id]['tags'][$tag['id']] = array(
                'id' => $tag['id'],
                'label' => $tag['label'],
                'checked' => 0
            );
        }

        return $tags_groups;
    }

    /**
     * Used to call buildSchedule through request with date variable.
     *
     * @return false|int|string
     */
    public function getScheduleBuildByDate()
    {
        $date = $this->registry['request']->get('date');
        $res = [];
        if ($date) {
            $res = $this->buildSchedule($date);
        }

        return json_encode($res);
    }
}

?>
