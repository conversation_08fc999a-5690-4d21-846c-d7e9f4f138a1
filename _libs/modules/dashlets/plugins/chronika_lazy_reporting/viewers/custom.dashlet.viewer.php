<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template   = 'dashlet.html';
        $this->pluginName = 'chronika_lazy_reporting';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir        = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl        = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir     = $this->pluginDir . 'templates/';

        $this->modelsDir        = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir       = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir   = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir          = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir       = $this->pluginDir . 'javascript/';
        $this->scriptsUrl       = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {
        // Prepare the registry
        $registry = &$this->registry;

        $i18nfiles = array();
        $lang_file = sprintf('%s%s%s%s%s',
                PH_MODULES_DIR,
                'calendars/',
                'i18n/',
                $this->registry['lang'],
                '/calendars.ini');
        if (file_exists($lang_file)) {
            $i18n_files[] = $lang_file;
        }

        $registry['translater']->loadFile($i18n_files);
        $this->loadCustomI18NFiles($i18n_files);

        // Set the dashlet into the viewer
        $this->data['dashlet']     = $registry->get('dashlet');
        // Set the scripts URL into the viewer
        $this->data['scripts_url'] = $this->scriptsUrl . 'custom.js';

        // Get the dashlet settings
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $filter_dashlet_plugin = array('get_one' => $this->pluginName);
        $dashlet_plugin        = Dashlets::getPlugins($registry, $filter_dashlet_plugin);
        $dashlet_settings      = $dashlet_plugin[$this->pluginName]['settings'];
        require_once $this->pluginDir . 'models/custom.model.php';
        $custom_dashlet_model = new Custom_Model($dashlet_settings);

        $selected_date = $this->registry['request']->get('date');
        if (empty($selected_date)) {
            $selected_date = date('Y-m-d');
        }

        $first_day = date_create($selected_date)->format('N') - 1;
        $first_day = date_sub(date_create($selected_date), new DateInterval('P' . $first_day . 'D'))->format('Y-m-d');
        $last_day = date_add(date_create($first_day), new DateInterval('P6D'))->format('Y-m-d');
        $week_num = date_create($selected_date)->format('W');
        $show_next = true;
        if ($week_num == date('W')) {
            $show_next = false;
        }

        $days = $works = array();
        for ($i = $first_day; $i <= $last_day; $i = date_add(date_create($i), new DateInterval('P1D'))->format('Y-m-d')) {
            $query = 'SELECT id, work_on FROM ' . DB_TABLE_COUNTRY_NONWORKDAYS . ' WHERE `date` = "' . $i . '"';
            $rec = $registry['db']->GetRow($query);
            if (!empty($rec)) {
                if (!empty($rec['work_on'])) {
                    $works[] = $i;
                }
                $days[$i]['working'] = 0;
            } else {
                $days[$i]['working'] = 1;
            }
            $days[$i]['name'] = $this->i18n('weekday_' . date_create($i)->format('w'));
        }
        foreach ($works as $w) {
            if (isset($days[$w])) {
                $days[$w]['working'] = 1;
            }
        }

        $this->data['days'] = $days;
        $this->data['first_day'] = $first_day;
        $this->data['last_day'] = $last_day;
        $this->data['week_num'] = $week_num;
        $this->data['show_next'] = $show_next;

        $user = $this->registry['currentUser'];
        if ($this->registry['request']->get('user')) {
            $user = Users::searchOne($this->registry, array('where' => array('u.id = ' . $this->registry['request']->get('user'))));
        }

        $settings = $user->getPersonalSettings('calendars');
        if (empty($settings['week_start_hour'])) {
            $settings['week_start_hour'] = 7;
            $settings['week_end_hour'] = 19;
        }
        $this->data['settings'] = $settings;
        $this->data['prev'] = date_sub(date_create($selected_date), new DateInterval('P1W'))->format('Y-m-d');
        $this->data['next'] = date_add(date_create($selected_date), new DateInterval('P1W'))->format('Y-m-d');

        $users = array_values(Dropdown::getUsers(array($this->registry)));
        $this->data['users'] = $users[1];
        foreach ($this->data['users'] as $k => $u) {
            if (!$u['active_option']) {
                unset($this->data['users'][$k]);
            }
        }
        $this->data['not_master'] = true;
        if (!empty($custom_dashlet_model->master_permissions_group)) {
            $gr = array_intersect($registry['currentUser']->getGroups(), $custom_dashlet_model->master_permissions_group);
            if (!empty($gr)) {
                $this->data['not_master'] = false;
            }
        }
        if (!empty($custom_dashlet_model->master_permissions_user)) {
            if (in_array($registry['currentUser']->get('id'), $custom_dashlet_model->master_permissions_user)) {
                $this->data['not_master'] = false;
            }
        }

        $query = "SELECT name, id FROM " . DB_TABLE_FIELDS_META  . "\n" .
                "WHERE model = 'Document' AND model_type IN (22, 23) AND name IN ('shospital_time', 'fhospital_time', 'leave_start_date', 'leave_finish_date')";
        $vars = $this->registry['db']->getAssoc($query);

        $query = "SELECT d.id, d.type, dti18n.name as type_name," . "\n" .
                "IF (d.type = 23, dc1.value, dc3.value) as start,\n" .
                "IF (d.type = 23, dc2.value, dc4.value) as end\n" .
                "FROM " . DB_TABLE_DOCUMENTS . " d\n" .
                "JOIN " . DB_TABLE_DOCUMENTS_TYPES_I18N . ' dti18n' . "\n" .
                "  ON d.type = dti18n.parent_id AND dti18n.lang = '" . $registry['lang'] . "'\n" .
                //hosplital lists
                "LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc1\n" .
                "  ON dc1.model_id = d.id AND dc1.var_id = $vars[shospital_time]" . "\n" .
                "LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc2\n" .
                "  ON dc2.model_id = d.id AND dc2.var_id = $vars[fhospital_time]" . "\n" .
                //holidays
                "LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc3\n" .
                "  ON dc3.model_id = d.id AND dc3.var_id = $vars[leave_start_date]" . "\n" .
                "LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc4\n" .
                "  ON dc4.model_id = d.id AND dc4.var_id = $vars[leave_finish_date]" . "\n" .
                "WHERE d.type = 23 AND d.employee = {$user->get('employee')} AND dc1.value <= '{$last_day}' AND dc2.value >= '{$first_day}'\n" .
                "  OR d.type = 22 AND d.customer = {$user->get('employee')} AND d.substatus = 7 AND dc3.value <= '{$last_day}' AND dc4.value >= '{$first_day}'";
        $records = $this->registry['db']->GetAll($query);
        $this->data['all_day'] = $records;
        $all_day_count = array();
        foreach($records as $r) {
            for ($i = $r['start']; $i <= $r['end']; $i = date_add(date_create($i), new DateInterval('P1D'))->format('Y-m-d')) {
                if (!isset($all_day_count[$i])) {
                    $all_day_count[$i] = 0;
                }
                $all_day_count[$i]++;
            }
        }
        if (!empty($all_day_count)) {
            rsort($all_day_count);
            $this->data['all_day_count'] = array_shift($all_day_count);
        } else {
            $this->data['all_day_count'] = 1;
        }
        //get variables IDs
        $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Nomenclature" AND model_type IN (' . $custom_dashlet_model->team_type . ', ' . $custom_dashlet_model->status_type . ')';
        $vars = $registry['db']->GetAssoc($query);

        $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Document" AND model_type = ' . $custom_dashlet_model->document_type . ' AND `grouping` = 24';
        $dvars = $registry['db']->GetAssoc($query);

        $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS . ' WHERE type = ' . $custom_dashlet_model->self_company_type;
        $self_companies = $registry['db']->GetCol($query);

        // Get all times spent for the user
        $query = 'SELECT d.id as document, d.customer as customer,' . "\n" .
                 '  dc1.value as date, dc2.value as start, dc3.value as end,' . "\n" .
                 '  TIMESTAMPDIFF(MINUTE, TIMESTAMP(CONCAT(dc1.value," ", dc2.value)), TIMESTAMP(CONCAT(dc1.value," ", dc3.value))) as duration,' . "\n" .
                 '  TIMESTAMPDIFF(MINUTE, TIMESTAMP(CONCAT(dc1.value," ", CAST(dc2.value AS SIGNED),":00:00")), TIMESTAMP(CONCAT(dc1.value," ", dc2.value))) as offset,' . "\n" .
                 '  dc4.value as status, dc0.num as gt_row_num, nc7.value as timespent_to' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc0' . "\n" .
                 '  ON dc0.model_id = d.id AND dc0.var_id = ' . $dvars['employee_id'] . ' AND dc0.value = ' . $user->get('employee') . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc1' . "\n" .
                 '  ON dc1.model_id = d.id AND dc1.num = dc0.num AND dc1.var_id = ' . $dvars['reported_date'] . "\n" .
                 '  AND dc1.value <= "' . $last_day . '" AND dc1.value >= "' . $first_day . '"' . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc2' . "\n" .
                 '  ON dc2.model_id = d.id AND dc2.num = dc0.num AND dc2.var_id = ' . $dvars['reported_from'] . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc5' . "\n" .
                 '  ON dc5.model_id = d.id AND dc5.num = dc0.num AND dc5.var_id = ' . $dvars['task_name_ID'] . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc3' . "\n" .
                 '  ON dc3.model_id = d.id AND dc3.num = dc0.num AND dc3.var_id = ' . $dvars['reported_to'] . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc4' . "\n" .
                 '  ON dc4.model_id = d.id AND dc4.num = dc0.num AND dc4.var_id = ' . $dvars['status_at_row'] . "\n" .
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc7' . "\n" .
                 '  ON nc7.model_id = dc4.value AND nc7.var_id = ' . $vars['timespent_to'] . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc1' . "\n" .
                 '  ON nc1.var_id = ' . $vars['team_id'] . ' AND nc1.value = ' . $user->get('employee') . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc2' . "\n" .
                 '  ON nc2.model_id = nc1.model_id AND nc2.var_id = ' . $vars['validity_start'] . ' AND (nc2.value <= "' . $last_day . '" OR nc2.value = "" OR nc2.value = "0000-00-00")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc3' . "\n" .
                 '  ON nc3.model_id = nc1.model_id AND nc3.var_id = ' . $vars['validity_end'] . ' AND (nc3.value >= "' . $first_day . '" OR nc3.value = "" OR nc3.value = "0000-00-00")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc4' . "\n" .
                 '  ON nc4.model_id = nc1.model_id AND nc4.var_id = ' . $vars['client_id']  . ' AND d.customer = nc4.value' . "\n" .
                 'WHERE d.type = ' . $custom_dashlet_model->document_type . "\n" .
                 '  AND (DATE_FORMAT(d.added, "%Y-%m") = "' . date_create($first_day)->format('Y-m') . '" OR DATE_FORMAT(d.added, "%Y-%m") = "' . date_create($last_day)->format('Y-m') . '")' . "\n" .
                 '  AND (nc4.value IS NOT NULL OR d.customer IN (' . implode(',', $self_companies) . ') OR dc5.value > 0)' . "\n" .
                'GROUP BY d.id, dc0.num' . "\n" .
                'ORDER BY duration DESC, offset ASC' . "\n";
        $times = $registry['db']->GetAll($query);

        $total = array();
        foreach($times as $k => $t) {
            $times[$k]['show_duration'] = $t['duration'];
            // cut duration if it is too long
            if ($t['end'] > ($settings['week_end_hour'] + 1) . ':00:00') {
                $tmp = date_diff(date_create(date('Y-m-d ') . ($settings['week_end_hour'] + 1) . ':00:00'), date_create(date('Y-m-d ') . $t['end'] . ':00'));
                $times[$k]['show_duration'] = $t['duration'] - ($tmp->h * 60 + $tmp->i);
                $tmp = date_diff(date_create(date('Y-m-d ') . preg_replace('#^(\d{2}).*#', '$1', $t['start']) . ':00:00'), date_create(date('Y-m-d ') . ($settings['week_end_hour']+1) . ':00:00'));
            } else {
                $tmp = date_diff(date_create(date('Y-m-d ') . preg_replace('#^(\d{2}).*#', '$1', $t['start']) . ':00:00'), date_create(date('Y-m-d ') . $t['end'] . ':00'));
            }
            $times[$k]['show_duration'] += $tmp->h - 2;
            /*if ($tmp->i) {
                $times[$k]['show_duration']++;
            }*/
            switch ($t['timespent_to']) {
                case 2:
                    $qvar = 'time_correction_one';
                    break;
                case 3:
                    $qvar = 'time_correction_two';
                    break;
                case 4:
                    $qvar = 'time_correction_three';
                    break;
                case 5:
                    $qvar = 'time_correction_four';
                    break;
                default:
                    $qvar = 'employee_time';
                    break;
            }

            $query = 'SELECT dc1.value as last_time, ni1.name as business_line, c.code as customer_code, nc2.value as editable,' . "\n" .
                     'ni2.name as action_name, ni3.name as sub_action_name, dc5.value as description, dc6.value as alt_description,' . "\n" .
                     'ci.name as customer_name, nc1.value as color' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' ci' . "\n" .
                     '  ON ci.parent_id = c.id AND ci.lang="' . $registry['lang'] . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc1' . "\n" .
                     ' ON dc1.model_id = "' . $t['document'] . '" AND dc1.num = "' . $t['gt_row_num'] . '" AND dc1.var_id = ' . $dvars[$qvar] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc2' . "\n" .
                     '  ON dc1.model_id = dc2.model_id AND dc1.num = dc2.num AND dc2.var_id = ' . $dvars['business_line'] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc3' . "\n" .
                     '  ON dc3.model_id = dc1.model_id AND dc3.num = dc1.num AND dc3.var_id = ' . $dvars['action_name'] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc4' . "\n" .
                     '  ON dc4.model_id = dc1.model_id AND dc4.num = dc1.num AND dc4.var_id = ' . $dvars['sub_action_name'] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc5' . "\n" .
                     '  ON dc5.model_id = dc1.model_id AND dc5.num = dc1.num AND dc5.var_id = ' . $dvars['action_description'] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc6' . "\n" .
                     '  ON dc6.model_id = dc1.model_id AND dc6.num = dc1.num AND dc6.var_id = ' . $dvars['alternative_desc'] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' ni1' . "\n" .
                     '  ON ni1.parent_id = dc2.value AND ni1.lang = "' . $registry['lang'] .'"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' ni2' . "\n" .
                     '  ON ni2.parent_id = dc3.value AND ni2.lang = "' . $registry['lang'] .'"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' ni3' . "\n" .
                     '  ON ni3.parent_id = dc4.value AND ni3.lang = "' . $registry['lang'] .'"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc1' . "\n" .
                     '  ON nc1.model_id = "' . $t['status'] . '" AND nc1.var_id = ' . $vars['bkg_color'] . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc2' . "\n" .
                     '  ON nc2.model_id = "' . $t['status'] . '" AND nc2.var_id = ' . $vars['editable_from'] . "\n" .
                     'WHERE c.id = "' . $t['customer'] . '"';
            $times[$k] = array_merge($times[$k], $registry['db']->GetRow($query));

            if ($times[$k]['editable'] == 2 && $registry['currentUser']->get('id') == $user->get('id') && $this->data['not_master'] ||
                $times[$k]['editable'] == 1 && $registry['currentUser']->get('id') != $user->get('id') ||
                $times[$k]['editable'] == 3) {

                $times[$k]['editable'] = 0;
            }

            if (!isset($total[$t['date']])) {
                $total[$t['date']] = array(
                    'time' => '00:00',
                    'plus' => '00:00',
                    'minus' => '00:00',
                    'final' => '00:00',
                );
            }
            $total[$t['date']]['time'] = date_add(date_create($t['date'] . ' ' . $total[$t['date']]['time'] . ':00'), new DateInterval('PT' . $t['duration'] . 'M'))->format('H:i');
            $total[$t['date']]['final'] = date_add(date_create($t['date'] . ' ' . $total[$t['date']]['final'] . ':00'), new DateInterval('PT' . $t['duration'] . 'M'))->format('H:i');
            if ($qvar != 'employee_time' && $times[$k]['last_time']) {
                $tmp = explode(':', $times[$k]['last_time']);
                $tmp[0] = intval($tmp[0]);
                $tmp[1] = intval($tmp[1]);
                if ($tmp[0] < 10) {
                    $tmp[0] = '0' . $tmp[0];
                }
                if (empty($tmp[1]) || $tmp[1] == 0) {
                    $tmp[1] = '00';
                } elseif ($tmp[1] < 10) {
                    $tmp[1] = '0' . $tmp[1];
                }
                $times[$k]['last_time'] = $tmp[0] . ':' . $tmp[1] . ':00';
                $tmp = array(floor($t['duration'] / 60), $t['duration'] % 60);
                if ($tmp[0] < 10) {
                    $tmp[0] = '0' . $tmp[0];
                }
                if ($tmp[1] < 10) {
                    $tmp[1] = '0' . $tmp[1];
                }
                $tmp = $tmp[0] . ':' . $tmp[1] . ':00';
                $tmp = date_diff(date_create($t['date'] . ' ' . $tmp), date_create($t['date'] . ' ' . $times[$k]['last_time']));
                if ($tmp->invert) {
                    $tmp->invert = 0;
                    $total[$t['date']]['minus'] = date_add(date_create($t['date'] . ' ' . $total[$t['date']]['minus'] . ':00'), $tmp)->format('H:i');
                    $total[$t['date']]['final'] = date_sub(date_create($t['date'] . ' ' . $total[$t['date']]['final'] . ':00'), $tmp)->format('H:i');
                } else {
                    $total[$t['date']]['plus'] = date_add(date_create($t['date'] . ' ' . $total[$t['date']]['plus'] . ':00'), $tmp)->format('H:i');
                    $total[$t['date']]['final'] = date_add(date_create($t['date'] . ' ' . $total[$t['date']]['final'] . ':00'), $tmp)->format('H:i');
                }
            }
        }
        $this->data['total'] = $total;
        $this->data['times'] = $times;

        $query = 'SELECT n.id as idx, ni.name as label, nc.value as color' . "\n" .
                'FROM ' . DB_TABLE_NOMENCLATURES . ' n' ."\n" .
                'JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' ni' . "\n" .
                '  ON n.id = ni.parent_id AND ni.lang = "' . $this->registry['lang'] .'"' ."\n" .
                'JOIN ' . DB_TABLE_FIELDS_META . ' fm' . "\n" .
                '  ON fm.model = "Nomenclature" AND fm.model_type = n.type AND fm.name="bkg_color"' . "\n" .
                'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' nc' . "\n" .
                '  ON nc.model_id = n.id AND nc.var_id = fm.id' . "\n";
                'WHERE n.type = "' . $custom_dashlet_model->status_type . '"';
        $this->data['legend'] = $registry['db']->GetAssoc($query);
    }
}

?>
