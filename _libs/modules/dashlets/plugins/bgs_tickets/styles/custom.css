.bgs_tickets_container div.container {
    margin: auto;
    display: flex;
    align-items: stretch;
    flex-direction: row;
    justify-content: center;
    /* background-color: #f1f1f1; */
}
.bgs_tickets_container div.container div.content_block {
    padding: 10px;
    overflow: auto;
}
.bgs_tickets_container div.container div.main_column {
    padding: 10px;
    /* flex-basis: 56%; */
}
.bgs_tickets_container div.container div.side_column {
    min-width: 200px;
    flex-basis: 22%;
}
.bgs_tickets_container div.container a[target="_blank"]:after {
    content: url('../../../../../themes/Default/images/external.png');
    padding: 0 3px;
}
.bgs_tickets_container div.details_container {
    display: block;
    border-top: 1px solid #cccccc;
}

/* form layer */
.bgs_tickets_container div.details_container.absolute {
    display: block;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    margin-left: -10px;
    z-index: 1000;
    background-color: transparent;
}
.bgs_tickets_container div.details_container.absolute > div.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    max-width: none;
    max-width: unset;
    background-color: #666666;
    opacity: 0.5;
    filter: alpha(opacity=50);
    background-color: #66666680;
}
.bgs_tickets_container div.details_container.absolute > div.form_container {
    position: relative;
    background-color: #ffffff;
}
.bgs_tickets_container div.details_container.absolute > div.form_header {
    margin: 0 auto;
    overflow: hidden;
    height: 16px;
    position: relative;
    padding: 5px 9px;
}
.bgs_tickets_container div.details_container.absolute > div.form_header div.form_caption {
    position: absolute;
    height: 16px;
    overflow: hidden;
    left: 6px;
    right: 24px;
    float: left;
    text-align: left;
    font-weight: bold;
}
.bgs_tickets_container div.details_container.absolute > div.form_header a.form_close {
    width: 16px;
    float: right;
    padding-bottom: 16px;
    outline: none;
    background: url(../../../../../themes/Default/images/close_window.png) 100% 0% no-repeat;
    margin: 0px;
    margin-right: -4px;
}
.bgs_tickets_container div.details_container.absolute div.form_container table.t_table {
    width: 100%!important;
    border: 0px none!important;
    background-color: unset!important;
    background-image: unset!important;
}
.bgs_tickets_container div.details_container.absolute div.form_container .t_footer {
    background: transparent none;
}
/* form layer end */

.bgs_tickets_container div.details_container > div {
    max-width: 800px;
    padding: 10px;
    margin: auto;
}
.bgs_tickets_container .t_layout_table {
    margin: auto;
}
.bgs_tickets_container .t_layout_table.width_unset {
    width: unset;
}
.bgs_tickets_container div.container div.side_column div.content_block table,
.bgs_tickets_container div.container div.side_column div.content_block > div {
    min-width: 200px;
    max-width: 300px;
}
.bgs_tickets_container .action_tabs {
    background-color: transparent;
    border: 1px solid #cccccc;
    padding: 5px;
    margin-bottom: -15px;
}
.bgs_tickets_container div.container .action_tabs li a img {
    padding: 0 5px;
}

@media (max-width: 1024px) {
    .bgs_tickets_container div.container {
        display: block;
    }
    .bgs_tickets_container div.details_container > div {
        width: 90%;
        overflow: auto;
    }
    .bgs_tickets_container .selbox,
    .bgs_tickets_container .selbox_hov,
    .bgs_tickets_container .txtbox,
    .bgs_tickets_container .txtbox_hov,
    .bgs_tickets_container .areabox,
    .bgs_tickets_container .areabox_hov {
        width: 100%;
    }
    .bgs_tickets_container .datebox, .bgs_tickets_container .datetimebox {
        width: 200px;
    }
}
@media (max-width: 768px) {
    .bgs_tickets_container div.container > div.t_border {
        border-right: 0;
    }
    .bgs_tickets_container div.container div.side_column {
        border-top: 1px solid #cccccc;
    }
    .bgs_tickets_container div.container div.side_column table,
    .bgs_tickets_container div.container div.side_column div.content_block table,
    .bgs_tickets_container div.container div.side_column div.content_block > div {
        margin: auto;
        width: 100%;
        max-width: none;
        max-width: unset;
    }
    .bgs_tickets_container .floatl, .bgs_tickets_container .datebox, .bgs_tickets_container .datetimebox {
        float: none;
    }
    .bgs_tickets_container .floatl > img.calendar_trigger, .bgs_tickets_container td > img.calendar_trigger {
        margin-bottom: -4px;
        margin-left: 0;
    }
}
@media (max-width: 1024px) and (min-width: 768px) {
    .bgs_tickets_container div.container div.main_column.t_border {
        border-right: 0;
    }
    .bgs_tickets_container div.container div.side_column {
        float: left;
        width: 50%;
        border-top: 1px solid #cccccc;
        margin-right: -1px;
    }
}
.bgs_tickets_container .labelbox, .bgs_tickets_container .floatl {
    width: auto!important;
    vertical-align: unset!important;
    vertical-align: auto!important;
}
.bgs_tickets_container table.labelbox {
    width: 100%!important;
}
.bgs_tickets_container .labelbox.width70 {
    width: 70px!important;
}
.bgs_tickets_container label.labelbox.width70 {
    display: inline-block;
}
.bgs_tickets_container select {
    padding: 1px;
}
.bgs_tickets_container input, .bgs_tickets_container textarea {
    padding: 2px 4px;
}
.bgs_tickets_container .combobox_button {
    padding-top: 2px;
    padding-bottom: 1px;
}
.bgs_tickets_container div.content_block table {
    border: 0px none;
    border-spacing: 0;
}
.bgs_tickets_container table.t_grouping_table {
    margin: 0;
}
.bgs_tickets_container table.t_grouping_table.t_borderless th {
    color: #666666;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 11px;
}
.bgs_tickets_container table.t_grouping_table tr td:first-child[align="right"] {
    width: 29px;
}

.bgs_tickets_container .call_answered:before,
.bgs_tickets_container .call_missed:before {
    content: "";
    width: 16px;
    height: 16px;
    margin: -3px 3px -3px 0;
    display: inline-block;
    background-size: 16px 16px;
    background-repeat: no-repeat;
}
.bgs_tickets_container .call_answered:before {
    background-image: url('../images/call_answered.png');
}
.bgs_tickets_container .call_missed:before {
    background-image: url('../images/call_missed.png');
}
