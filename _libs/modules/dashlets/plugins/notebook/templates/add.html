{if $dashlet}
<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="dashlets" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$lang}" />
<input type="hidden" name="module_name" id="module_name" value="{$dashlet->get('module_name')}" />

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="1" cellpadding="3" border="0" class="t_table">
      <!-- FIELDS FOR ALL PLUGINS -->
        <tr>
          <td class="labelbox">{help label='dashlet_for'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
              {$module_name_i18n}
          </td>
        </tr>
        {if $dashlet->get('name')}
          {assign var='name' value=$dashlet->get('name')}
        {else}
          {assign var='name' value=$module_name_i18n}
        {/if}
        {include file=`$theme->templatesDir`input_text.html
          name='name'
          custom_id='name'
          label=#plugin_name#
          help=#help_plugin_name#
          readonly=0
          value=$name
          required=1
        }
        {include file=`$theme->templatesDir`input_textarea.html
          name='description'
          custom_id='description'
          label=#plugin_description#
          help=#help_plugin_description#
          value=$dashlet->get('description')
          required=0
        }
        <tr>
          <td class="labelbox">{help label='default'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {include file="input_checkbox.html"
              standalone=true
              name='default'
              value=$dashlet->get('default')
              option_value=1
            }
          </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>
            {*include file="input_checkbox.html"
                standalone=true
                name='full_width'
                required=0
                custom_id='full_width'
                label=#plugin_full_width#
                help=#help_plugin_full_width#
                value=$dashlet->get('full_width')
                onclick="processDashletColumns(this, $max_columns);"
                option_value=1
            *}
          </td>
        </tr>
        <tr>
          <td colspan="3">
        <!-- END OF FIELDS FOR ALL PLUGINS -->
        <!-- PLUGIN SPECIFIC FIELDS -->
        {include file=`$templatesDir`_plugin_fields.html}
        <!-- END OF PLUGIN SPECIFIC FIELDS -->
          </td>
       </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <div class="t_footer">&nbsp;</div>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`system_settings_box.html object=$dashlet}
<table class="t_table">
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td style="padding-left:20px">
      <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
{/if}