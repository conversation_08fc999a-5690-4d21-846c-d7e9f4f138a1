{if $containers}
  <table id="paymet_for_visit">
    <tr>
      <td colspan="2" style="colspan: 5px;">
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row hcenter">
            <td class="t_border" style="vertical-align: middle;"><div style="width: 160px;">{#plugin_aestheclinic_work_leaving_schedule_container#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 85px;">{#plugin_aestheclinic_work_leaving_schedule_prepaid_sum#|escape}</div></td>
            <td style="vertical-align: middle;"><div style="width: 85px;">{#plugin_aestheclinic_work_leaving_schedule_paid#|escape}</div></td>
          </tr>

          {foreach from=$containers item=container name=cont}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" nowrap="nowrap">
                {$container.name|escape|default:"&nbsp;"}
                {capture assign=container_key}wlf_container_{$container.type}_{$container.id}{/capture}
                <input type="hidden" name="containers_included[]" id="containers_included_{$smarty.foreach.cont.iteration}" value="{$container.type}_{$container.id}" />
              </td>
              <td class="t_border hright">
                <input type="hidden" name="{$container_key}_prepaid" id="{$container_key}_prepaid" value="{$container.prepaid}" />
                {$container.prepaid}
              </td>
              <td class="hright">
                {include file="input_text.html"
                  standalone=true
                  name="`$container_key`_current_paid"
                  custom_id="`$container_key`_current_paid"
                  text_align='right'
                  restrict='insertOnlyFloats'
                  onkeyup='recalculateLeftQuantity(this)'
                  label=#plugin_aestheclinic_work_leaving_schedule_paid#
                  help=#plugin_aestheclinic_work_leaving_schedule_paid#}
              </td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="3"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="2">
        <input type="button" class="button" id="wlf_add_payment_button" onclick="addWorkLeavingFormPayment(this)" style="float: left;" value="{#add#|escape}" />
        <input type="button" class="button" id="wlf_cancel_button" onclick="lb.deactivate();" style="float: right; margin-right: 0;" value="{#cancel#|escape}" />
      </td>
    </tr>
  </table>
{else}
  <span style="color: red">{#error_plugin_aestheclinic_work_leaving_form_no_payments_to_distribute#|escape}</span>
{/if}