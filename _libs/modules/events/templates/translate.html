<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
    <div id="form_container">

      {include file=`$theme->templatesDir`actions_box.html}
      {include file=`$theme->templatesDir`translate_box.html}
      {include file=`$theme->templatesDir`_submenu_actions_box.html}

      <form name="events_translate" action="{$submitLink}" method="post">
      <input type="hidden" name="id" id="id" value="{$event->get('id')}" />
      <input type="hidden" name="type" id="type" value="{$event->get('type')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$event->get('model_lang')|default:$lang}" />
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td class="vtop">
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <tr>
                <td colspan="3">
                  {#message_translatable_items#|escape}
                </td>
                <td class="vtop t_border divider_cell" rowspan="8">&nbsp;</td>
                <td colspan="3">&nbsp;</td>
              </tr>
              <tr>
                <td colspan="2">&nbsp;</td>
                {capture assign='source_lang'}lang_{$event->get('model_lang')}{/capture}
                <td><img src="{$theme->imagesUrl}flags/{$event->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
                <td>&nbsp;</td>
                {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
                <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
              </tr>

            {foreach from=$event->getLayoutsDetails() key='lkey' item='layout'}
              {if $lkey eq 'name'}
              {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
              <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox distinctive" name="name" id="name" value="{if !($layout.view && $layout.edit)}{$base_model->get('name')|escape}{else}{$event->get('name')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
                <td>&nbsp;</td>
                <td class="vtop">
                  <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
                </td>
                <td>
                  <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
                </td>
              </tr>
              {elseif $lkey eq 'location'}
              {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
              <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_location"><label for="location"{if $messages->getErrors('location')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox distinctive" name="location" id="location" value="{if !($layout.view && $layout.edit)}{$base_model->get('location')|escape}{else}{$event->get('location')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
                <td>&nbsp;</td>
                <td class="vtop">
                  <button type="button" name="copy_location" id="copy_location" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
                </td>
                <td>
                  <input type="text" class="txtbox distinctive" name="bm_location" id="bm_location" value="{$base_model->get('location')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
                </td>
              </tr>
              {elseif $lkey eq 'description'}
              {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
              <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
                <td class="labelbox"><label for="description">{help label_content=$layout.name text_content=$layout.description}</label></td>
                <td class="unrequired">&nbsp;</td>
                <td nowrap="nowrap">
                  <textarea class="areabox distinctive" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('description')|escape}{else}{$event->get('description')|escape}{/if}</textarea>
                </td>
                <td>&nbsp;</td>
                <td class="vtop">
                  <button type="button" name="copy_description" id="copy_description" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
                </td>
                <td>
                  <textarea class="areabox distinctive" name="bm_description" id="bm_description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('description')|escape}</textarea>
                </td>
              </tr>
              {/if}
            {/foreach}
              <tr>
                <td colspan="3">&nbsp;</td>
                <td colspan="3">&nbsp;</td>
              </tr>
              <tr>
                <td colspan="3">
                  <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
                </td>
                <td>&nbsp;</td>
                <td colspan="2">
                  {if $translate_fields_count}
                  <button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
                  {/if}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      {include file=`$theme->templatesDir`help_box.html}
      {include file=`$theme->templatesDir`after_actions_box.html}
      </form>
      </div>
    </td>
    <td class="side_panel_container">
      <div id="assignments_info" class="info_extra_panel">
        {include file=`$templatesDir`_assignments_info_panel.html}
      </div>
    </td>
  </tr>
</table>
