<?php

class Roles_Edit_Viewer extends Viewer {
    public $template = 'edit.html';

    public function prepare() {
        $this->model = $this->registry['role'];
        $this->data['role'] = $this->model;

        // Get the last selected section for this role
        $this->data['selected_section_name'] = $this->model->getLastSelectedSection($this->registry);

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);
    }

    public function prepareTitleBar() {
        $title = $this->i18n('roles_edit');
        $this->data['title'] = $title;
    }
}

?>
