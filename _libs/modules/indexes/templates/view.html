<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="indexes" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$varindex->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$varindex->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="subject"{if $messages->getErrors('name')} class="error"{/if}>{help label='name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {mb_truncate_overlib text=$varindex->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="model">{help label='model'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{#indexes_model_contract#}
            <input type="hidden" name="model" id="model" value="Contract" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_model_type"><label for="type"{if $messages->getErrors('model_type')} class="error"{/if}>{help label='model_type'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {foreach from=$contracts_types item=type}
              {if $type.option_value eq $varindex->get('model_type')}{$type.label}{/if}
            {/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="data"{if $messages->getErrors('data')} class="error"{/if}>{help label='data'}</label></td>
          <td class="required">{#required#}</td>
          <td>
            &nbsp;
          </td>
        </tr>
        <tr>
          <td colspan="3" class="nopadding">
            {include file=`$templatesDir`_data.html data=$varindex->get('data')}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            {help label='operation'}<br /><br />
            *&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;{#indexes_multi_legend#}<br />
            +&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;{#indexes_plus_leged#}<br />
            =&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;{#indexes_equal_legend#}<br />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
</form>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$varindex exclude='is_portal groups'}
</div>
