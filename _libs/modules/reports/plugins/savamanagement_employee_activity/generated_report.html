<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row hcenter">
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_num#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_document#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_company#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_from_date#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_jurisdiction#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_service#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_subservice#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_customer#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_value#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_deadline#|escape}</div></td>
    <td style="vertical-align: middle;"><div style="">{#reports_status#|escape}</div></td>
  </tr>
  {foreach from=$reports_results item=order}
    {foreach from=$order.jurisdictions item=jurisdiction name=jur}
      {foreach from=$jurisdiction.services item=service name=serv}
        <tr class="t_even1 t_even2 row_yellow">
          {if $smarty.foreach.serv.first && $smarty.foreach.jur.first}
            <td rowspan="{$order.rowspan}" nowrap="nowrap" class="{if $order.requests}pointer {/if}vmiddle hcenter"{if $order.requests} onclick="toggleSubItems($('switch_inc_{$order.id}'));"{/if}>
              <div class="switch_{if !$order.requests}collapse{else}expand{/if}" id="switch_inc_{$order.id}"></div>
            </td>
            <td class="t_border" rowspan="{$order.rowspan}" nowrap="nowrap" style="vertical-align: middle;">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$order.id}" target="_blank">{#reports_order#|escape} {$order.full_num|escape}</a>
            </td>
            <td class="t_border" rowspan="{$order.rowspan}" nowrap="nowrap" style="vertical-align: middle;">
              {$order.contact|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" rowspan="{$order.rowspan}" nowrap="nowrap" style="vertical-align: middle;">
              {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
          {/if}
          {if $smarty.foreach.serv.first}
            <td class="t_border" rowspan="{$jurisdiction.rowspan}" nowrap="nowrap" style="vertical-align: middle;">
              {$jurisdiction.name|escape|default:"&nbsp;"}
            </td>
          {/if}
          <td class="t_border vmiddle"nowrap="nowrap">
            {$service.service_name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border" nowrap="nowrap">
            {$service.subservice|escape|nl2br|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" nowrap="nowrap">
            {$service.customer|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" nowrap="nowrap">
            {$service.subtotal|string_format:"%.2f"|default:"0.00"} {$service.currency|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" nowrap="nowrap">
            {$service.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          {if $smarty.foreach.serv.first && $smarty.foreach.jur.first}
            <td nowrap="nowrap" rowspan="{$order.rowspan}" style="vertical-align: middle;">
              {if $order.substatus}
                {$order.substatus_name|escape|default:"&nbsp;"}
              {else}
                {capture assign='status_name'}reports_status_{$order.status}{/capture}
                {$smarty.config.$status_name|escape|default:"&nbsp;"}
              {/if}
            </td>
          {/if}
        </tr>
      {/foreach}
      {if $smarty.foreach.serv.last && $smarty.foreach.jur.last}
        {include file=`$templatesDirPlugin``$report_type`/_requests_data.html
          requests=$order.requests
          order_idx=$order.id}
      {/if}
    {foreachelse}
      {if !$order.id}
        <tr class="t_even1 t_even2 row_yellow">
          <td nowrap="nowrap" class="pointer vmiddle hcenter" onclick="toggleSubItems($('switch_inc_{$order.id}'));">
            <div class="switch_expand" id="switch_inc_{$order.id}"></div>
          </td>
          <td nowrap="nowrap" colspan="10" style="vertical-align: middle;">
            <i>{#reports_requests_without_orders#|escape}</i>
          </td>
        </tr>
        {include file=`$templatesDirPlugin``$report_type`/_requests_data.html
            requests=$order.requests
            order_idx=$order.id}
      {/if}
    {/foreach}
  {foreachelse}
    <tr class="t_even">
      <td class="error" colspan="11">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr class="row_blue">
    <td colspan="8" class="t_border hright"><strong>{#reports_orders_total#|escape}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.orders_total|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.orders_currency_total|escape}</strong></td>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr class="row_blue">
    <td colspan="8" class="t_border hright"><strong>{#reports_expected_income#|escape}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.expected_income|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.orders_currency_total|escape}</strong></td>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td class="t_footer" colspan="11"></td>
  </tr>
</table>

<script type="text/javascript">
  {literal}
    function toggleSubItems(element) {
        var items = $$('.order_requests_' + element.id.replace(/switch_inc_/, ''));
        if (element.className.match(/switch_expand/)) {
            removeClass(element, 'switch_expand');
            addClass(element, 'switch_collapse');
            for (var i = 0; i < items.length; i++) {
                items[i].style.display = 'table-row';
            }
        } else {
            removeClass(element, 'switch_collapse');
            addClass(element, 'switch_expand');
            for (var i = 0; i < items.length; i++) {
                items[i].style.display = 'none';
            }
        }
    }
  {/literal}
</script>