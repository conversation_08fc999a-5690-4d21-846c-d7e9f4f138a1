<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
  <table>
    {if $reports_results.main}
      <tr>
        <td>
          <table border="1" cellpadding="0" cellspacing="0">
            <tr>
              <td nowrap="nowrap">{#reports_customer#|escape}</td>
              {if $report_filters.separate_invoices.value eq $report_filters.separate_invoices.option_value}
                <td nowrap="nowrap">{#reports_invoice_num#|escape}</td>
                <td nowrap="nowrap">{#reports_invoice_date#|escape}</td>
                <td nowrap="nowrap">{#reports_invoice_amount#|escape}</td>
              {/if}
              <td nowrap="nowrap">{#reports_total_percent#|escape}</td>
              <td nowrap="nowrap">{#reports_not_paid#|escape}</td>
              <td nowrap="nowrap">{#reports_current#|escape}</td>
              <td nowrap="nowrap">{#reports_expired#|escape}</td>
            </tr>
            {foreach from=$reports_results.main key=k name=i item=result}
              <tr{if $smarty.foreach.i.last} style="background-color: #98BCFF;"{/if}>
                {if $smarty.foreach.i.last}
                  <td nowrap="nowrap" width="25">
                    <strong>{#reports_grand_total#}</strong>
                  </td>
                  {if $report_filters.separate_invoices.value eq $report_filters.separate_invoices.option_value}
                    <td nowrap="nowrap">
                      <strong>-</strong>
                    </td>
                    <td nowrap="nowrap">
                      <strong>-</strong>
                    </td>
                    <td nowrap="nowrap">
                      <strong>-</strong>
                    </td>
                  {/if}
                  <td nowrap="nowrap"  style="mso-number-format: '0\.00';">
                    <strong>100.00</strong>
                  </td>
                  <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                    <strong>{$result.unpaid|default:0|string_format:"%.2F"}</strong>
                  </td>
                  <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                    <strong>{$result.current|default:0|string_format:"%.2F"}</strong>
                  </td>
                  <td style="mso-number-format: '0\.00';">
                    <strong>{$result.expired|default:0|string_format:"%.2F"}</strong>
                  </td>
                {else}
                  <td nowrap="nowrap" width="25">
                    {$result.customer_name}
                  </td>
                  {if $report_filters.separate_invoices.value eq $report_filters.separate_invoices.option_value}
                    <td nowrap="nowrap">
                      {$result.num|default:'-'}
                    </td>
                    <td nowrap="nowrap">
                      {$result.date|default:'-'}
                    </td>
                    <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                      {$result.total|default:0|string_format:"%.2F"}
                    </td>
                  {/if}
                  <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                    {$result.total_percent|default:0|string_format:"%.2F"}
                  </td>
                  <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                    {$result.unpaid|default:0|string_format:"%.2F"}
                  </td>
                  <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                    {$result.current|default:0|string_format:"%.2F"}
                  </td>
                  <td style="mso-number-format: '0\.00';">
                    {$result.expired|default:0|string_format:"%.2F"}
                  </td>
                {/if}
              </tr>
            {foreachelse}
              <tr>
                <td colspan="8">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if $reports_results.prescriptive}
      <tr>
        <td>
          <table border="1" cellpadding="0" cellspacing="0">
          <tr>
            <td nowrap="nowrap">{#reports_article#|escape}</td>
            <td nowrap="nowrap">{#reports_30#|escape}</td>
            <td nowrap="nowrap">{#reports_60#|escape}</td>
            <td nowrap="nowrap">{#reports_90#|escape}</td>
            <td nowrap="nowrap">{#reports_more#|escape}</td>
            <td nowrap="nowrap">{#reports_total#|escape}</td>
          </tr>
          {foreach from=$reports_results.prescriptive key=k name=i item=result}
            <tr{if $smarty.foreach.i.last} style="background-color: #98BCFF;"{/if}>
              {if $smarty.foreach.i.last}
                <td nowrap="nowrap" width="25">
                  <strong>{#reports_grand_total#}</strong>
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  <strong>{$result.30|default:0|string_format:"%.2F"}</strong>
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  <strong>{$result.60|default:0|string_format:"%.2F"}</strong>
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  <strong>{$result.90|default:0|string_format:"%.2F"}</strong>
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  <strong>{$result.more|default:0|string_format:"%.2F"}</strong>
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  <strong>{$result.total|default:0|string_format:"%.2F"}</strong>
                </td>
              {else}
                <td nowrap="nowrap" width="25">
                  {$result.article}
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  {$result.30|default:0|string_format:"%.2F"}
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  {$result.60|default:0|string_format:"%.2F"}
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  {$result.90|default:0|string_format:"%.2F"}
                </td>
                <td nowrap="nowrap" style="mso-number-format: '0\.00';">
                  {$result.more|default:0|string_format:"%.2F"}
                </td>
                <td style="mso-number-format: '0\.00';">
                  {$result.total|default:0|string_format:"%.2F"}
                </td>
              {/if}
            </tr>
          {foreachelse}
            <tr>
              <td colspan="8">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if $reports_results.prescriptive_customer}
    <tr>
      <td>
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td>{#reports_customer#|escape}</td>
          <td>{#reports_30#|escape}</td>
          <td>{#reports_60#|escape}</td>
          <td>{#reports_90#|escape}</td>
          <td>{#reports_more#|escape}</td>
          <td>{#reports_total#|escape}</td>
        </tr>
        {foreach from=$reports_results.prescriptive_customer key=k name=i item=result}
          <tr{if $smarty.foreach.i.last} style="background-color: #98BCFF;"{/if}>
            {if $smarty.foreach.i.last}
              <td>
                <strong>{#reports_grand_total#}</strong>
              </td>
              <td style="mso-number-format: '0\.00';">
                <strong>{$result.30|default:0|string_format:"%.2F"}</strong>
              </td>
              <td style="mso-number-format: '0\.00';">
                <strong>{$result.60|default:0|string_format:"%.2F"}</strong>
              </td>
              <td style="mso-number-format: '0\.00';">
                <strong>{$result.90|default:0|string_format:"%.2F"}</strong>
              </td>
              <td style="mso-number-format: '0\.00';">
                <strong>{$result.more|default:0|string_format:"%.2F"}</strong>
              </td>
              <td style="mso-number-format: '0\.00';">
                <strong>{$result.total|default:0|string_format:"%.2F"}</strong>
              </td>
            {else}
              <td>
                {$result.name}
              </td>
              <td style="mso-number-format: '0\.00';">
                {$result.30|default:0|string_format:"%.2F"}
              </td>
              <td style="mso-number-format: '0\.00';">
                {$result.60|default:0|string_format:"%.2F"}
              </td>
              <td style="mso-number-format: '0\.00';">
                {$result.90|default:0|string_format:"%.2F"}
              </td>
              <td style="mso-number-format: '0\.00';">
                {$result.more|default:0|string_format:"%.2F"}
              </td>
              <td style="mso-number-format: '0\.00';">
                {$result.total|default:0|string_format:"%.2F"}
              </td>
            {/if}
          </tr>
        {foreachelse}
          <tr>
            <td colspan="7">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
      </table>
      </td>
    </tr>
    {/if}
  </table>
</body>
</html>