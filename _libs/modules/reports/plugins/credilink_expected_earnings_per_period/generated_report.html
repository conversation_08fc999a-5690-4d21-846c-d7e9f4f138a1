<h1>{#reports_table_title#} {$report_filters.period_from.value|date_format:#date_short#} - {$report_filters.period_from.additional_filter.value|date_format:#date_short#}</h1>
<table class="reports_table">
  <tr class="reports_title_row hcenter">
    <th rowspan="2" width="20">{#num#}</th>
    <th rowspan="2" width="75">{#reports_th_contract_number_date#}</th>
    <th rowspan="2" width="30">{#reports_th_currency#}</th>
    <th rowspan="2" width="150">{#reports_th_customer_ucn#}</th>
    <th rowspan="2" width="75">{#reports_th_customer_phone#}</th>
    <th colspan="7">{#reports_th_upcoming_installment#}</th>
    <th colspan="9">{#reports_th_overdue_installments#}</th>
    <th rowspan="2" width="80">{#reports_th_total_overdue#}</th>
    <th rowspan="2" width="100">{#reports_th_total_expected_earning#}</th>
    <th rowspan="2" width="100">{#reports_th_percent_overdue_installments#}</th>
  </tr>
  <tr class="reports_title_row hcenter">
    <th width="80">{#reports_th_date_of_payment#}</th>
    <th width="80">{#reports_th_principal#}</th>
    <th width="80">{#reports_th_interest_rate#}</th>
    <th width="80">{#reports_th_guarantor_fee#}</th>
    <th width="80">{#reports_th_tax_management#}</th>
    <th width="80">{#reports_th_tax_angagement#}</th>
    <th width="80">{#reports_th_total#}</th>
    <th width="160">{#reports_th_date_of_payment_amount#}</th>
    <th width="80">{#reports_th_count#}</th>
    <th width="80">{#reports_th_principals#}</th>
    <th width="80">{#reports_th_interest_rates#}</th>
    <th width="80">{#reports_th_guarantor_fees#}</th>
    <th width="80">{#reports_th_tax_managements#}</th>
    <th width="80">{#reports_th_tax_angagements#}</th>
    <th width="80">{#reports_th_punishment_interest_rates#}</th>
    <th width="80">{#reports_th_lpg#}</th>
  </tr>
  {foreach from=$reports_results item='document' key='document_id'}
    {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
    {foreach from=$document.future_installments item='future_installment' name='future_installments'}
      <tr class="{$current_row_class}">
        {if $smarty.foreach.future_installments.first}
          <td class="hcenter" rowspan="{$document.rowspan}">{counter}</td>
          <td class="hcenter" rowspan="{$document.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$document_id}">
              {$document.name|escape}<br />
              ({$document.date|date_format:#date_short#}) ({$document.installments_count|default:"0"})
            </a>
          </td>
          <td class="hcenter" rowspan="{$document.rowspan}">
            {$document.currency|escape}
          </td>
          <td class="hcenter" rowspan="{$document.rowspan}">
            {capture assign='client'}{$document.customer_name|escape} ({$document.customer_eik_ucn}){/capture}
            <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&amp;reports=generate_report&amp;report_type=credilink_customer_file&amp;client={$document.customer_id}&amp;client_autocomplete={$client|escape:'url'}">{$document.customer_name|escape}<br />({$document.customer_eik_ucn})</a>
          </td>
          <td class="hcenter" rowspan="{$document.rowspan}" style="max-width: 75px; word-wrap: break-word;">{$document.customer_gsm|nl2br}</td>
        {/if}
        <td class="hcenter">{$future_installment.date_of_payment|date_format:#date_short#}</td>
        <td class="hright">{$future_installment.principal|number_format:'2':'.':' '}</td>
        <td class="hright">{$future_installment.interest_rate|number_format:'2':'.':' '}</td>
        <td class="hright">{$future_installment.guarantor_fee|number_format:'2':'.':' '}</td>
        <td class="hright">{$future_installment.tax_management|number_format:'2':'.':' '}</td>
        <td class="hright">{$future_installment.tax_angagement|number_format:'2':'.':' '}</td>
        <td class="hright">{$future_installment.amount|number_format:'2':'.':' '}</td>
        {if $smarty.foreach.future_installments.first}
          {if !empty($document.overdue_installments.dates_amounts)}
            <td class="hcenter" rowspan="{$document.rowspan}" style="width: 140px; white-space: nowrap;">
              {foreach from=$document.overdue_installments.dates_amounts item='date_amount' name='overdue_installments_dates_amounts'}
                {$date_amount.date_of_payment|date_format:#date_short#} / {$date_amount.amount|number_format:'2':'.':' '}{if !$smarty.foreach.overdue_installments_dates_amounts.last}<br />{/if}
              {/foreach}
            </td>
            <td class="hcenter" rowspan="{$document.rowspan}">{$document.overdue_installments_count|default:'0'}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.principals|number_format:'2':'.':' '}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.interest_rates|number_format:'2':'.':' '}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.guarantor_fees|number_format:'2':'.':' '}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.tax_management|number_format:'2':'.':' '}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.tax_angagement|number_format:'2':'.':' '}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.punishment_interest_rates|number_format:'2':'.':' '}</td>
            <td class="hright" rowspan="{$document.rowspan}">{$document.overdue_installments.lpg|number_format:'2':'.':' '}</td>
          {else}
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
            <td class="hcenter" rowspan="{$document.rowspan}">-</td>
          {/if}
          <td class="hright" rowspan="{$document.rowspan}">{$document.total_overdue|number_format:'2':'.':' '}</td>
          <td class="hright" rowspan="{$document.rowspan}">{$document.total_expected_earning|number_format:'2':'.':' '}</td>
          <td class="hright" rowspan="{$document.rowspan}">{$document.percent_overdue_installments|string_format:'%.2f'|default:'0.00'} %</td>
        {/if}
      </tr>
    {/foreach}
  {foreachelse}
    <tr>
      <td class="error" colspan="19">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  {if !empty($reports_results)}
    <tr style="background-color: #5371AF; color: white;">
      <th class="hright" colspan="6">{#reports_th_totals#}:</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.principals|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.interest_rates|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.guarantor_fees|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.tax_management|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.tax_angagement|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.amounts|number_format:'2':'.':' '}</th>
      <th class="hright">&nbsp;</th>
      <th class="hright">&nbsp;</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_principals|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_interest_rates|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_guarantor_fees|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_tax_management|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_tax_angagement|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_punishment_interest_rates|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.overdue_lpg|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.total_overdue|number_format:'2':'.':' '}</th>
      <th class="hright" style="white-space: nowrap;">{$reports_additional_options.totals.total_expected_earnings|number_format:'2':'.':' '}</th>
      <th class="hright">&nbsp;</th>
    </tr>
  {/if}
</table>
