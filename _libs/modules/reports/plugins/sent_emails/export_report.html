<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {if is_array($reports_results) && $reports_results|@count > 0}
      <table border="1">
        <tr>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_num#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_sent_date#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_sent_name#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_recipient_name#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_recipient_email#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_sent_status#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#reports_subject#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results item=result name=res}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          {foreach from=$result.copies item=email_copy name=ec}
            <tr>
              {if $smarty.foreach.ec.first}
                <td rowspan="{$result.rowspan}">
                  {counter name='item_counter' print=true}
                </td>
                <td style="mso-number-format:'dd\.mm\.yyyy, hh:mm'" rowspan="{$result.rowspan}">
                  {$result.sent|date_format:#date_mid#|escape|default:"&nbsp;"}
                </td>
                <td style="mso-number-format:\@" rowspan="{$result.rowspan}">
                  {$result.sent_by_name|escape|default:"&nbsp;"}
                </td>
              {/if}
              <td style="mso-number-format:\@">
                {$email_copy.name|escape|default:"&nbsp;"}
              </td>
              <td style="mso-number-format:\@">
                {$email_copy.email|escape|default:"&nbsp;"}
              </td>
              <td style="mso-number-format:\@">
                {capture assign='lang_var'}reports_sent_status_{$email_copy.status}{/capture}
                {$smarty.config.$lang_var|escape|default:"&nbsp;"}
              </td>
              {if $smarty.foreach.ec.first}
                <td style="mso-number-format:\@" rowspan="{$result.rowspan}">
                  {$result.subject|escape}
                </td>
              {/if}
            </tr>
          {/foreach}
        {/foreach}
      </table>
    {else}
      <h1 style="padding: 5px; color: red;">{#error_reports_no_results_to_show#|escape}</h1>
    {/if}
  </body>
</html>