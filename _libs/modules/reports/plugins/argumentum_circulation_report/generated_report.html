<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
  <td>
    <table border="0" cellpadding="3" cellspacing="2">
      <tr>
        <td colspan="3"><strong>{#reports_legend#}</strong></td>
      </tr>
      <tr>
        <td style="background-color: #FFCE37; width: 20px;">&nbsp;</td>
        <td> - </td>
        <td>{#reports_expense#}</td>
      </tr>
    </table>
  </td>
</tr>
<tr>
  <td>&nbsp;</td>
</tr>
<tr>
<td>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_employee#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_document_num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_type#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_paying_reason#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_type_paying#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_paying_value#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_paying_currency#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_take_money#|escape}</div></td>
      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_money_date#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results key=k name=list_res item=result}
      {if ! $smarty.foreach.list_res.last}
          <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}{if $result.color} row_{$result.color}{/if}">
            <td class="t_border hright" nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.employee_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" nowrap="nowrap">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.document_id}">{$result.full_num|numerate:$result.direction}</a>
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.customer|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.type_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.paying_reason|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.type_paying_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$result.paying_value|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border">
              {$result.paying_currency|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.take_money_name|escape|default:"-"}
            </td>
            <td>
              {$result.money_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
          </tr>
      {/if}
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="11">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="11"></td>
    </tr>
  </table>
</td>
</tr>
<tr>
  <td>&nbsp;</td>
</tr>
<tr>
  <td>
    <table border="0" cellpadding="7" cellspacing="0" class="t_table t_list">
      <tr>
        <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">&nbsp;</div></td>
        <td class="t_caption t_border" nowrap="nowrap" width="80"><div class="t_caption_title">BGN</div></td>
        <td class="t_caption" nowrap="nowrap" width="80"><div class="t_caption_title">EUR</div></td>
      </tr>
      <tr class="t_odd">
        <td class="t_border"><strong>{#reports_incomings#}</strong></td>
        <td class="t_border hright">{$reports_results.sums.money_count_BGN|string_format:"%.2f"|default:"0.00"}</td>
        <td class="hright">{$reports_results.sums.money_count_EUR|string_format:"%.2f"|default:"0.00"}</td>
      </tr>
      <tr class="t_even">
        <td class="t_border"><strong>{#reports_bank_incomings#}</strong></td>
        <td class="t_border hright">{$reports_results.sums.money_bank_count_BGN|string_format:"%.2f"|default:"0.00"}</td>
        <td class="hright">{$reports_results.sums.money_bank_count_EUR|string_format:"%.2f"|default:"0.00"}</td>
      </tr>
      <tr class="t_odd">
        <td class="t_border"><strong>{#reports_expenses#}</strong></td>
        <td class="t_border hright">{$reports_results.sums.money_expense_BGN|string_format:"%.2f"|default:"0.00"}</td>
        <td class="hright">{$reports_results.sums.money_expense_EUR|string_format:"%.2f"|default:"0.00"}</td>
      </tr>
      <tr class="t_even">
        <td class="t_border"><strong>{#reports_bank_expenses#}</strong></td>
        <td class="t_border hright">{$reports_results.sums.money_bank_expense_BGN|string_format:"%.2f"|default:"0.00"}</td>
        <td class="hright">{$reports_results.sums.money_bank_expense_EUR|string_format:"%.2f"|default:"0.00"}</td>
      </tr>
      <tr>
        <td class="t_footer" colspan="4"></td>
      </tr>
    </table>
  </td>
</tr>
</table>