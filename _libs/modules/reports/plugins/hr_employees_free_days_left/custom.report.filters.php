<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            //DEFINE REQUESTED BY UNIT FILTER
            $filter = array (
                'custom_id'          => 'department',
                'name'               => 'department',
                'type'               => 'dropdown',
                'skip_please_select' => false,
                'label'              => $this->i18n('reports_department'),
                'help'               => $this->i18n('reports_department'),
                'options'            => array(),
            );
            $filters['department'] = $filter;

            require_once PH_MODULES_DIR . 'reports/plugins/hr_employees_free_days_left/custom.report.query.php';
            $departments_included = Hr_Employees_Free_Days_Left::getResponsibleDepartmentsList($registry);
            $powerroles = array_filter(preg_split('/\s*,\s*/', POWERROLES));

            $params_units = array(
                0       => $registry,
                'department' => 1,
                'active'=> 1
            );
            $records_units = Dropdown::getDepartments($params_units);

            foreach ($records_units as $k => $record) {
                if (!in_array($record['option_value'], $departments_included)) {
                    unset($records_units[$k]);
                }
            }

            $filters['department']['options'] = $records_units;
            if (count($records_units) == 1 || in_array($registry['currentUser']->get('role'), $powerroles)) {
                $filters['department']['skip_please_select'] = true;
            }

            return $filters;
        }
    }
?>
