<?php
    class Colliers_Equipment_Maintenance_Schedule extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            if (!defined('DOCUMENT_TYPE_MAINTENANCE_SCHEDULE_ID')) {
                define('DOCUMENT_TYPE_MAINTENANCE_SCHEDULE_ID', 45);
            }
            if (!defined('EQUIPMENT_GROUP_VAR')) {
                define('EQUIPMENT_GROUP_VAR', 'equipment_group');
            }
            if (!defined('DATE_VALID_FROM_VAR')) {
                define('DATE_VALID_FROM_VAR', 'valid_from');
            }
            if (!defined('EQUIPMENT_ACTIVITY_VAR')) {
                define('EQUIPMENT_ACTIVITY_VAR', 'activity_act');
            }
            if (!defined('ACTIVITY_FREQUENCY_VAR')) {
                define('ACTIVITY_FREQUENCY_VAR', 'freq_schedule');
            }
            if (!defined('ACTIVITY_FREQUENCY_PERIOD_VAR')) {
                define('ACTIVITY_FREQUENCY_PERIOD_VAR', 'fr_period');
            }

            define('ACTIVITY_PERIOD_WEEK', 1);
            define('ACTIVITY_PERIOD_MONTH', 2);
            define('ACTIVITY_PERIOD_QUARTER', 3);
            define('ACTIVITY_PERIOD_YEAR', 4);

            if (!defined('NOMENCLATURE_TYPE_EQUIPMENT_ID')) {
                define('NOMENCLATURE_TYPE_EQUIPMENT_ID', 14);
            }
            define('NOMECLATURE_EQUIPMENT_GROUP', 'equipment_group');
            if (!defined('DOCUMENT_EQUIPMENT_START_FINISH_TYPE_ID')) {
                define('DOCUMENT_EQUIPMENT_START_FINISH_TYPE_ID', 41);
            }
            define('DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION', 'protocol_equip');
            define('DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_DATE', 'date_enter');
            define('DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_EQUIPMENT', 'name_equip_id');
            define('DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_OPTION_START', 'equip_entry');
            define('DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_OPTION_FINISH', 'equip_exit');
            if (!defined('DOCUMENT_WORK_DONE_TYPE_ID')) {
                define('DOCUMENT_WORK_DONE_TYPE_ID', 40);
            }
            define('DOCUMENT_WORK_DONE_EQUIPMENT_ID', 'equipment_id');
            define('DOCUMENT_WORK_DONE_EQUIPMENT_ACTIVITY_ID', 'activity_act_id');
            define('DOCUMENT_WORK_DONE_DATE', 'date');

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            $from_week_index = '';
            $to_week_index = '';
            if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                $from_week_index = General::strftime('%Y', $filters['from_date']) . '_' . General::strftime('%W', $filters['from_date']);
                $to_week_index = General::strftime('%Y', $filters['to_date']) . '_' . General::strftime('%W', $filters['to_date']);
            }

            if (!empty($filters['from_date']) && !empty($filters['to_date']) && $from_week_index == $to_week_index) {
                $from_date = $filters['from_date'];
                $to_date = $filters['to_date'];

                // CALCULATES THE WEEK PERIODS
                $period_week = array();

                $week_date_sec = strtotime($from_date);
                $date_week = General::strftime("%w", strtotime($from_date));
                if ($date_week == 0) {
                    $date_week = 7;
                }
                $week_from = strtotime('-' . ($date_week-1) . ' day', $week_date_sec);
                $week_to = strtotime('+' . (7-$date_week) . ' day -1 week', $week_date_sec);

                $period_week = array(
                    'index'        => $from_week_index,
                    'label'        => General::strftime('%d.%m.%Y', $week_from) . ' - ' . General::strftime('%d.%m.%Y', strtotime('+1 week -1 day', $week_from)),
                    'start_period' => General::strftime('%Y-%m-%d', $week_from),
                    'end_period'   => General::strftime('%Y-%m-%d', strtotime('+1 week -1 day', $week_from))
                );


                // CALCULATES THE MONTH PERIODS
                $period_month = array();

                $month_date_sec = strtotime($from_date);
                $from_filter_date_month = General::strftime("%d", strtotime($from_date));

                $month_from = strtotime('-' . ($from_filter_date_month-1) . ' day', $month_date_sec);
                $month_to = strtotime('+1 month -1 day', $month_from);

                $current_year = General::strftime('%Y', $month_from);
                $period_month = array(
                    'index'         => $current_year . '_' . General::strftime('%m', $month_from),
                    'label'         => sprintf('%s %d', General::strftime('%B', $month_from), $current_year),
                    'start_period'  => General::strftime('%Y-%m-%d', $month_from),
                    'end_period'    => General::strftime('%Y-%m-%d', $month_to)
                );


                // CALCULATES QUARTER PERIODS
                $period_quarter = array();

                $quarter_month = General::strftime('%m', $from_date);
                $quarter_year = General::strftime('%Y', $from_date);

                $quarter_num = 0;
                if ($quarter_month >= 10) {
                    $quarter_from = strtotime($quarter_year . '-10-01');
                    $quarter_num = 4;
                } elseif ($quarter_month >= 7) {
                    $quarter_from = strtotime($quarter_year . '-07-01');
                    $quarter_num = 3;
                } elseif ($quarter_month >= 4) {
                    $quarter_num = 2;
                    $quarter_from = strtotime($quarter_year . '-04-01');
                } elseif ($quarter_month >= 1) {
                    $quarter_num = 1;
                    $quarter_from = strtotime($quarter_year . '-01-01');
                }
                $quarter_to = General::strftime('%Y-%m-%d', strtotime('+3 month -1 day', $quarter_from));

                $period_quarter = array(
                    'index'             => $current_year . '_' . $quarter_num,
                    'label'             => sprintf('%s %s %d', $quarter_num, $registry['translater']->translate('reports_quarter'), $current_year),
                    'start_period'      => General::strftime('%Y-%m-%d', $quarter_from),
                    'end_period'        => General::strftime('%Y-%m-%d', $quarter_to)
                );


                // CALCULATES YEAR PERIODS
                $period_year = array();

                $start_from_year = General::strftime('%Y', $from_date);

                $year_from =  General::strftime('%Y-%m-%d', strtotime(sprintf('%d-01-01', $start_from_year)));
                $year_to = General::strftime('%Y-%m-%d', strtotime('+1 year -1 day', strtotime($year_from)));

                $period_year = array(
                    'index'        => $start_from_year,
                    'label'        => $start_from_year,
                    'start_period' => $year_from,
                    'end_period'   => $year_to
                );


                // SUMMARIZE PERIODS
                $available_periods = array(
                    ACTIVITY_PERIOD_WEEK    => $period_week,
                    ACTIVITY_PERIOD_MONTH   => $period_month,
                    ACTIVITY_PERIOD_QUARTER => $period_quarter,
                    ACTIVITY_PERIOD_YEAR    => $period_year
                );

                $summarized_periods = array();

                if (!empty($available_periods)) {
                    // TAKE ADDITIONAL VARS
                    //additional vars for nomenclature
                    $sql_for_add_vars = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                                        'WHERE fm.model="Nomenclature" AND fm.model_type=' . NOMENCLATURE_TYPE_EQUIPMENT_ID . ' AND fm.name="' . NOMECLATURE_EQUIPMENT_GROUP . '"' . "\n" .
                                        'ORDER BY position ASC';
                    $nomenclature_group_id = $registry['db']->GetOne($sql_for_add_vars);


                    // additional vars for equipment start/finish exploitation
                    $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                                        'WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_EQUIPMENT_START_FINISH_TYPE_ID . ' AND ' . "\n" .
                                        '(fm.name="' . DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION . '" OR fm.name="' . DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_DATE . '" OR fm.name="' . DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_EQUIPMENT . '")' . "\n" .
                                        'ORDER BY position ASC';
                    $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                    $equipment_start_finish_exploitation_id = '';
                    $equipment_start_finish_exploitation_date_id = '';
                    $equipment_equipment_start_finish_exploitation_equipment_id_id = '';

                    //assign the ids to vars
                    foreach ($var_ids as $vars) {
                        if ($vars['name'] == DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION) {
                            $equipment_start_finish_exploitation_id = $vars['id'];
                        } else if ($vars['name'] == DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_DATE) {
                            $equipment_start_finish_exploitation_date_id = $vars['id'];
                        } else if ($vars['name'] == DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_EQUIPMENT) {
                            $equipment_equipment_start_finish_exploitation_equipment_id_id = $vars['id'];
                        }
                    }


                    // additional vars for document for work done
                    $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                                        'WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_WORK_DONE_TYPE_ID . ' AND ' . "\n" .
                                        '(fm.name="' . DOCUMENT_WORK_DONE_EQUIPMENT_ID . '" OR fm.name="' . DOCUMENT_WORK_DONE_DATE . '" OR fm.name="' . DOCUMENT_WORK_DONE_EQUIPMENT_ACTIVITY_ID . '")' . "\n" .
                                        'ORDER BY position ASC';
                    $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                    $work_done_equipment_id_id = '';
                    $work_done_activity_id = '';
                    $work_done_date_id = '';

                    //assign the ids to vars
                    foreach ($var_ids as $vars) {
                        if ($vars['name'] == DOCUMENT_WORK_DONE_EQUIPMENT_ID) {
                            $work_done_equipment_id_id = $vars['id'];
                        } else if ($vars['name'] == DOCUMENT_WORK_DONE_DATE) {
                            $work_done_date_id = $vars['id'];
                        } else if ($vars['name'] == DOCUMENT_WORK_DONE_EQUIPMENT_ACTIVITY_ID) {
                            $work_done_activity_id = $vars['id'];
                        }
                    }


                    //additional vars for maintaince schedule
                    $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                                        'WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_MAINTENANCE_SCHEDULE_ID . ' AND ' . "\n" .
                                        '(fm.name="' . DATE_VALID_FROM_VAR . '" OR fm.name="' . EQUIPMENT_GROUP_VAR . '" OR fm.name="' . EQUIPMENT_ACTIVITY_VAR . '" OR fm.name="' . ACTIVITY_FREQUENCY_VAR . '" OR fm.name="' . ACTIVITY_FREQUENCY_PERIOD_VAR . '")' . "\n" .
                                        'ORDER BY position ASC';
                    $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                    $date_valid_from_id = '';
                    $equipment_group_id = '';
                    $equipment_activity_id = '';
                    $activity_frequency_id = '';
                    $activity_frequency_period_id = '';

                    //assign the ids to vars
                    foreach ($var_ids as $vars) {
                        if ($vars['name'] == DATE_VALID_FROM_VAR) {
                            $date_valid_from_id = $vars['id'];
                        } else if ($vars['name'] == EQUIPMENT_GROUP_VAR) {
                            $equipment_group_id = $vars['id'];
                        } else if ($vars['name'] == EQUIPMENT_ACTIVITY_VAR) {
                            $equipment_activity_id = $vars['id'];
                        } else if ($vars['name'] == ACTIVITY_FREQUENCY_VAR) {
                            $activity_frequency_id = $vars['id'];
                        } else if ($vars['name'] == ACTIVITY_FREQUENCY_PERIOD_VAR) {
                            $activity_frequency_period_id = $vars['id'];
                        }
                    }


                    // PREPARE EQUIPMENT INFORMATION
                    // take all the selected equipment
                    $sql_equipment = 'SELECT nom.id as id_idx, nom.id as id, nom.code as code, nomi18n.name as name, nom_cstm_group.value as group_id' . "\n" .
                                     'FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                     '  ON (nomi18n.parent_id=nom.id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm_group' . "\n" .
                                     '  ON (nom_cstm_group.model_id=nom.id AND nom_cstm_group.var_id="' . $nomenclature_group_id . '")' . "\n" .
                                     'WHERE nom.type="' . NOMENCLATURE_TYPE_EQUIPMENT_ID . '" AND nom.deleted_by="0"' . "\n";

                    if (!empty($filters['equipment'])) {
                        $sql_equipment .= ' AND nom.id IN ("' . implode('","', $filters['equipment']) . '")';
                    }
                    // information for all the searched equipment
                    $records_equipment = $registry['db']->GetAssoc($sql_equipment);

                    // get only the equipment ids
                    $equipment_ids = array_keys($records_equipment);

                    // if there are equipment ids then the system search for exploitation documents
                    if (!empty($equipment_ids)) {
                        // sql to take the exploitation documents
                        $sql_exploitation = 'SELECT doc.id as doc_id, d_cstm_date.value as date, ' . "\n" .
                                            '  d_cstm_equipment.value as equip_id, d_cstm_exploitation_status.value as exploitation_status' . "\n" .
                                            'FROM ' . DB_TABLE_DOCUMENTS . ' AS doc' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
                                            '  ON (doc.id=d_cstm_date.model_id AND d_cstm_date.var_id="' . $equipment_start_finish_exploitation_date_id . '")' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_exploitation_status' . "\n" .
                                            '  ON (doc.id=d_cstm_exploitation_status.model_id AND d_cstm_exploitation_status.var_id="' . $equipment_start_finish_exploitation_id . '")' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_equipment' . "\n" .
                                            '  ON (doc.id=d_cstm_equipment.model_id AND d_cstm_equipment.var_id="' . $equipment_equipment_start_finish_exploitation_equipment_id_id . '")' . "\n" .
                                            'WHERE doc.type="' . DOCUMENT_EQUIPMENT_START_FINISH_TYPE_ID . '" AND doc.deleted_by=0 AND doc.active=1 AND doc.status!="opened" ' . "\n" .
                                            ' AND d_cstm_equipment.value IN (' . implode(',', $equipment_ids) . ')';

                        $records_exploitations = $registry['db']->GetAll($sql_exploitation);

                        // going through exploitation documents and building the timeline
                        //containing where the exploitation has start and where it has ended
                        foreach ($records_exploitations as $rec_exp) {
                            $current_status = '';
                            if ($rec_exp['exploitation_status'] == DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_OPTION_START) {
                                $current_status = 'start';
                            } else if ($rec_exp['exploitation_status'] == DOCUMENT_EQUIPMENT_START_FINISH_EXPLOITATION_OPTION_FINISH) {
                                $current_status = 'end';
                            }

                            if (!empty($current_status)) {
                                if (!isset($records_equipment[$rec_exp['equip_id']]['timeline'])) {
                                    $records_equipment[$rec_exp['equip_id']]['timeline'] = array();
                                }
                                $records_equipment[$rec_exp['equip_id']]['timeline'][$rec_exp['date']] = $current_status;
                                ksort($records_equipment[$rec_exp['equip_id']]['timeline']);
                            }
                        }
                    }

                    // build an array of equipment groups and all equipment assigned to them
                    $equipment_info = array();
                    foreach ($records_equipment as $rec_equip) {
                        // if no timeline defined then the equipment is still not in exploitation
                        //so we don't need it
                        if (isset($rec_equip['timeline'])) {
                            if (!isset($equipment_info[$rec_equip['group_id']])) {
                                $equipment_info[$rec_equip['group_id']] = array();
                            }
                            $equipment_info[$rec_equip['group_id']][$rec_equip['id']] = array(
                                'id'        => $rec_equip['id'],
                                'name'      => $rec_equip['name'],
                                'code'      => $rec_equip['code'],
                                'timeline'  => $rec_equip['timeline']
                            );
                        }
                    }

                    // takes all the equipment groups
                    $available_equipment_groups = array_keys($equipment_info);

                    $error = false;
                    $not_serviced_equipment = array();

                    if (!empty ($available_equipment_groups)) {
                        // BEGIN PREPARING THE SCHEDULE
                        // sql to find the active doc before the beginning of the period
                        $timeline_first_schedule = array();
                        $sql_timeline_schedule_first = 'SELECT doc_active_date.value as key_arr, d.id as id' . "\n" .
                                                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_active_date' . "\n" .
                                                       '  ON (doc_active_date.model_id=d.id AND doc_active_date.var_id="' . $date_valid_from_id . '")' . "\n" .
                                                       'WHERE d.deleted_by=0 AND d.active="1" AND d.status!="opened" AND d.type="' . DOCUMENT_TYPE_MAINTENANCE_SCHEDULE_ID . '" AND doc_active_date.value<="' . $year_from . '"' . "\n" .
                                                       'ORDER BY doc_active_date.value DESC LIMIT 1';

                        $timeline_first_schedule = $registry['db']->GetAssoc($sql_timeline_schedule_first);

                        // sql to find the active docs in the period
                        $timeline_period_schedule = array();
                        $sql_timeline_schedule_period = 'SELECT doc_active_date.value as key_arr, d.id as id' . "\n" .
                                                        'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                        'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_active_date' . "\n" .
                                                        '  ON (doc_active_date.model_id=d.id AND doc_active_date.var_id="' . $date_valid_from_id . '")' . "\n" .
                                                        'WHERE d.deleted_by=0 AND d.active="1" AND d.status!="opened" AND d.type="' . DOCUMENT_TYPE_MAINTENANCE_SCHEDULE_ID . '" AND doc_active_date.value>"' . $year_from . '" AND doc_active_date.value<"' . $year_to . '"' . "\n" .
                                                        'ORDER BY doc_active_date.value ASC';
                        $timeline_period_schedule = $registry['db']->GetAssoc($sql_timeline_schedule_period);

                        $timeline = array_merge($timeline_first_schedule, $timeline_period_schedule);
                        $active_doc_ids = $timeline;
                        ksort($timeline);

                        $dates_array = array_keys($timeline);
                        if (isset($dates_array[0]) && $dates_array[0]>$from_date) {
                            General::injectInArray(array($from_date => ''), $timeline, 'first');
                        }

                        if (!empty($active_doc_ids)) {
                            // sql to take the info for all the needed documents
                            $sql_docs_info = array();
                            $sql_docs_info['select'] = 'SELECT d.id as id, d_cstm_doc_active_date.value as active_date, ' . "\n" .
                                                            '  d_cstm_equipment_group.value as equipment_group, d_cstm_equipment_group_name.label as equipment_group_name, d_cstm_equipment_group_name.position AS equipment_group_position, ' .  "\n" .
                                                            '  d_cstm_activity_act.value as activity_act, nomi18n_1.name as activity_act_name, ' . "\n" .
                                                            '  d_cstm_freq_schedule.value as scheduled_frequency, d_cstm_fr_period.value as frequency_period ' . "\n";

                            $sql_docs_info['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_doc_active_date' . "\n" .
                                                       '  ON (d_cstm_doc_active_date.model_id=d.id AND d_cstm_doc_active_date.var_id="' . $date_valid_from_id . '")' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_equipment_group' . "\n" .
                                                       '  ON (d_cstm_equipment_group.model_id=d.id AND d_cstm_equipment_group.var_id="' . $equipment_group_id . '")' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_equipment_group_name' . "\n" .
                                                       '  ON (d_cstm_equipment_group.value=d_cstm_equipment_group_name.option_value AND d_cstm_equipment_group_name.parent_name="' . EQUIPMENT_GROUP_VAR . '" AND d_cstm_equipment_group_name.lang="' . $model_lang . '")' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_activity_act' . "\n" .
                                                       '  ON (d_cstm_activity_act.model_id=d.id AND d_cstm_activity_act.var_id="' . $equipment_activity_id . '" AND d_cstm_equipment_group.num=d_cstm_activity_act.num)' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n_1' . "\n" .
                                                       '  ON (nomi18n_1.parent_id=d_cstm_activity_act.value AND nomi18n_1.lang="' . $model_lang . '")' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_freq_schedule' . "\n" .
                                                       '  ON (d_cstm_freq_schedule.model_id=d.id AND d_cstm_freq_schedule.var_id="' . $activity_frequency_id . '" AND d_cstm_equipment_group.num=d_cstm_freq_schedule.num)' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_fr_period' . "\n" .
                                                       '  ON (d_cstm_fr_period.model_id=d.id AND d_cstm_fr_period.var_id="' . $activity_frequency_period_id . '" AND d_cstm_equipment_group.num=d_cstm_fr_period.num)' . "\n";

                            $where = array();
                            $where[] = 'd.active=1';
                            $where[] = 'd.deleted_by=0';
                            $where[] = 'd.status!="opened"';
                            $where[] = 'd.id IN (' . implode(',', $active_doc_ids) . ')';
                            $where[] = 'd_cstm_equipment_group.value IN ("' . implode('","', $available_equipment_groups) . '")';
                            $searched_periods = array_keys($available_periods);
                            $where[] = 'd_cstm_fr_period.value IN (' . implode(',', $searched_periods) . ')';

                            $sql_docs_info['where'] = 'WHERE ' . implode(' AND ', $where);

                            $sql_docs_info['order'] = 'ORDER BY d_cstm_doc_active_date.value ASC, d_cstm_equipment_group_name.position ASC, nomi18n_1.name ASC' . "\n";

                            $query_docs = implode("\n", $sql_docs_info);
                            $records_docs = $registry['db']->GetAll($query_docs);

                            $schedules_group = array();
                            foreach ($records_docs as $rec_doc) {
                                if (!isset($schedules_group[$rec_doc['id']])) {
                                    $schedules_group[$rec_doc['id']] = array();
                                }
                                $schedules_group[$rec_doc['id']][] = array(
                                    'equipment_group'      => $rec_doc['equipment_group'],
                                    'equipment_group_name' => $rec_doc['equipment_group_name'],
                                    'equipment_group_position' => $rec_doc['equipment_group_position'],
                                    'activity'             => $rec_doc['activity_act'],
                                    'activity_name'        => $rec_doc['activity_act_name'],
                                    'frequency'            => $rec_doc['scheduled_frequency'],
                                    'period'               => $rec_doc['frequency_period']
                                );
                            }

                            $equipment_activity_searched_ids = array();
                            $activities_searched_ids = array();

                            // find out which periods are active
                            foreach ($available_periods as $key_avb_per => $period_info) {
                                $last_used_schedule = 0;
                                foreach ($timeline as $date_schedule => $schedule_id) {
                                    if ($schedule_id) {
                                        if ($period_info['end_period']>=$date_schedule) {
                                            $last_used_schedule = $schedule_id;
                                        } else {
                                            break;
                                        }
                                    }
                                }
                                if ($last_used_schedule && isset($schedules_group[$last_used_schedule])) {
                                    foreach ($schedules_group[$last_used_schedule] as $scheduled_activity) {
                                        if ($scheduled_activity['period']==$key_avb_per) {
                                            foreach ($equipment_info[$scheduled_activity['equipment_group']] as $key_equipment => $eqp_info) {
                                                $timeline_dates = array_keys($eqp_info['timeline']);
                                                $active_in_period = false;
                                                foreach ($timeline_dates as $key_dat => $timeline_period_start) {
                                                    if (isset($timeline_dates[$key_dat+1])) {
                                                        $timeline_period_end = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($timeline_dates[$key_dat+1])));
                                                    } else {
                                                        $timeline_period_end = General::strftime('%Y-%m-%d', strtotime($to_date));
                                                    }

                                                    if ($period_info['start_period']<=$timeline_period_end && $period_info['end_period']>=$timeline_period_start && $eqp_info['timeline'][$timeline_period_start] == 'start') {
                                                        $active_in_period = true;
                                                        break;
                                                    }
                                                }

                                                if ($active_in_period) {
                                                    // construct key
                                                    if (!isset($summarized_periods[$key_avb_per])) {
                                                        $summarized_periods[$key_avb_per] = array();
                                                    }
                                                    $key_summarize = sprintf('%d-%d', $key_equipment, $scheduled_activity['activity']);
                                                    $summarized_periods[$key_avb_per][$key_summarize] = array(
                                                        'group'         => $scheduled_activity['equipment_group'],
                                                        'group_name'    => $scheduled_activity['equipment_group_name'],
                                                        'group_position'    => $scheduled_activity['equipment_group_position'],
                                                        'equipment'     => $eqp_info['id'],
                                                        'equipment_name'=> $eqp_info['name'],
                                                        'equipment_code'=> $eqp_info['code'],
                                                        'activity'      => $scheduled_activity['activity'],
                                                        'activity_name' => $scheduled_activity['activity_name'],
                                                        'start_period'  => $period_info['start_period'],
                                                        'end_period'    => $period_info['end_period'],
                                                        'frequency'     => $scheduled_activity['frequency'],
                                                        'done'          => 0,
                                                        'not_done'      => $scheduled_activity['frequency']
                                                    );
                                                    if (!isset($equipment_activity_searched_ids[$key_avb_per])) {
                                                        $equipment_activity_searched_ids[$key_avb_per] = array();
                                                    }
                                                    if (!in_array($key_summarize, $equipment_activity_searched_ids)) {
                                                        $equipment_activity_searched_ids[$key_avb_per][] = $key_summarize;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    unset($available_periods[$key_avb_per]);
                                }
                            }

                            $records_maintained_equipment = array();
                            if (!empty($equipment_activity_searched_ids)) {
                                foreach ($equipment_activity_searched_ids as $idx_per => $period_equipment) {
                                    $current_period_info = $available_periods[$idx_per];

                                    $current_period_start = $current_period_info['start_period'];
                                    $current_period_end = $current_period_info['end_period'];

                                    // sql to take all the maintaince documents for the required equipment
                                    //in the required period
                                    $sql_maintaince_documents = 'SELECT d_cstm_maintaince_date.value as date_performed, d_cstm_equipment.value as equipment, ' . "\n" .
                                                                '  d_cstm_activity.value as activity' . "\n" .
                                                                'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_maintaince_date' . "\n" .
                                                                '  ON (d.id=d_cstm_maintaince_date.model_id AND d_cstm_maintaince_date.var_id="' . $work_done_date_id . '")' . "\n" .
                                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_equipment' . "\n" .
                                                                '  ON (d.id=d_cstm_equipment.model_id AND d_cstm_equipment.var_id="' . $work_done_equipment_id_id . '")' . "\n" .
                                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_activity' . "\n" .
                                                                '  ON (d.id=d_cstm_activity.model_id AND d_cstm_activity.var_id="' . $work_done_activity_id . '" AND d_cstm_activity.num=d_cstm_equipment.num)' . "\n";

                                    $where = array();
                                    $where[] = 'd.active=1';
                                    $where[] = 'd.deleted_by=0';
                                    $where[] = 'd.status!="opened"';
                                    $where[] = 'd_cstm_maintaince_date.value>="' . $current_period_start . '"';
                                    $where[] = 'd_cstm_maintaince_date.value<="' . $current_period_end . '"';

                                    // activity and equipment clause
                                    $extra_clauses = array();
                                    foreach ($period_equipment as $eqp_act) {
                                        list($current_equipment, $current_activity) = explode('-', $eqp_act);
                                        $extra_clauses[] = '(d_cstm_equipment.value="' . $current_equipment . '" AND d_cstm_activity.value="' . $current_activity . '")';
                                    }
                                    if (!empty($extra_clauses)) {
                                        $where[] = '(' . implode(' OR ', $extra_clauses) . ')';
                                    }
                                    $sql_maintaince_documents .= 'WHERE ' . implode(' AND ', $where);
                                    $records_maintained_equipment = $registry['db']->GetAll($sql_maintaince_documents);

                                    foreach ($records_maintained_equipment as $rme) {
                                        $full_period_index = sprintf('%d-%d', $rme['equipment'], $rme['activity']);
                                        if (isset($summarized_periods[$idx_per][$full_period_index])) {
                                            $summarized_periods[$idx_per][$full_period_index]['done']++;
                                            $summarized_periods[$idx_per][$full_period_index]['not_done']--;
                                            if ($summarized_periods[$idx_per][$full_period_index]['not_done']<0) {
                                                $summarized_periods[$idx_per][$full_period_index]['not_done']=0;
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            $error = true;
                            $registry['messages']->setWarning($registry['translater']->translate('warning_reports_no_maintenance_schedule'));
                        }
                    }

                    foreach ($summarized_periods as $k_sp => $sp) {
                        if (empty($summarized_periods[$k_sp])) {
                            unset($summarized_periods[$k_sp]);
                        } else {
                            usort($summarized_periods[$k_sp], array('self', 'sortingResultsByNotDone'));
                        }
                    }
                } else {
                    $error = $registry['translater']->translate('error_no_available_periods');
                }

                $final_results = $summarized_periods;
                foreach ($available_periods as $avb_per_k => $period_description) {
                    $table_description_index = 'table_title_description_' . $avb_per_k;
                    $final_results['additional_options'][$table_description_index] = sprintf(' (%s)', $period_description['label']);
                }
                $final_results['additional_options']['error'] = $error;
                if (!empty($filters['prepare_equipment_maintenance_protocol'])) {
                    $final_results['additional_options']['include_create_model'] = 1;
                    $final_results['additional_options']['btn_create_model'] = $registry['translater']->translate('reports_create_equipment_maintenance_protocol');
                }
            } else {
                if (empty($filters['from_date']) || empty($filters['to_date'])) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_select_required_filters'));
                }
                if ($from_week_index != $to_week_index) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_dates_week'));
                }
                $final_results['additional_options']['error'] = true;
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        public static function sortingResultsByNotDone($a, $b) {
            if ($a['not_done'] == 0 && $b['not_done'] > 0) {
                return 1;
            } elseif ($b['not_done'] == 0 && $a['not_done'] > 0) {
                return -1;
            }

            if ($a['start_period'] == $b['start_period']) {
                if ($a['group_position'] == $b['group_position']) {
                    return ($a['activity_name'] > $b['activity_name']) ? 1 : -1;
                }
                return ($a['group_position'] > $b['group_position']) ? 1 : -1;
            }
            return ($a['start_period'] > $b['start_period']) ? 1 : -1;
        }

        /**
         * Prepares data for model to be created
         *
         * @param object $registry The main registry
         * @param array $filters
         * @return array Array with properties for model
         */
        public static function prepareModelData(&$registry, $filters = array()) {
            $db = $registry['db'];
            $lang = $registry['lang'];

            $session = &$registry['session'];

            $report_session_name = 'reports_colliers_equipment_maintenance_schedule_report';

            $session_filters = $session->get($report_session_name);

            $selected_items = array();
            if ($session->get('selected_items', $report_session_name)) {
                $items_array = $session->get('selected_items', $report_session_name);
                if (!empty($items_array[$report_session_name]['ids'])) {
                    $selected_items = $items_array[$report_session_name]['ids'];
                }
            }

            $session->remove($report_session_name, 'selected_items');

            if (empty($selected_items)) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_no_items_selected'));
                return array();
            }

            //additional vars for maintenance schedule document
            if (!defined('DATE_VALID_FROM_VAR')) {
                define('DATE_VALID_FROM_VAR', 'valid_from');
            }
            if (!defined('EQUIPMENT_GROUP_VAR')) {
                define('EQUIPMENT_GROUP_VAR', 'equipment_group');
            }
            if (!defined('EQUIPMENT_ACTIVITY_VAR')) {
                define('EQUIPMENT_ACTIVITY_VAR', 'activity_act');
            }
            if (!defined('ACTIVITY_FREQUENCY_VAR')) {
                define('ACTIVITY_FREQUENCY_VAR', 'freq_schedule');
            }
            if (!defined('ACTIVITY_FREQUENCY_PERIOD_VAR')) {
                define('ACTIVITY_FREQUENCY_PERIOD_VAR', 'fr_period');
            }
            //additional vars for maintenance protocol document
            if (!defined('FIELD_DATE')) {
                define('FIELD_DATE', 'date');
            }
            if (!defined('FIELD_EQUIPMENT_ID')) {
                define('FIELD_EQUIPMENT_ID', 'equipment_id');
            }
            if (!defined('FIELD_EQUIPMENT_NAME')) {
                define('FIELD_EQUIPMENT_NAME', 'equipment_name');
            }
            if (!defined('FIELD_EQUIPMENT_GROUP_ID')) {
                define('FIELD_EQUIPMENT_GROUP_ID', 'equipment_group_id');
            }
            if (!defined('FIELD_EQUIPMENT_GROUP_NAME')) {
                define('FIELD_EQUIPMENT_GROUP_NAME', 'equipment_group_name');
            }
            if (!defined('FIELD_ACTIVITY_ACT_ID')) {
                define('FIELD_ACTIVITY_ACT_ID', 'activity_act_id');
            }
            if (!defined('FIELD_ACTIVITY_ACT_NAME')) {
                define('FIELD_ACTIVITY_ACT_NAME', 'activity_act_name');
            }
            //main + additional vars for equipment nomenclature
            if (!defined('NOM_TYPE_EQUIPMENT')) {
                define('NOM_TYPE_EQUIPMENT', 14);
            }
            if (!defined('FIELD_EQUIPMENT_GROUP')) {
                define('FIELD_EQUIPMENT_GROUP', 'equipment_group');
            }

            self::getReportSettings($registry);
            $results = self::buildQuery($registry, $session_filters);
            unset($results['additional_options']);

            foreach ($results as $per_idx => $per_results) {
                foreach ($per_results as $idx_ea => $value_ea) {
                    $key_ea = $value_ea['equipment'] . '-' . $value_ea['activity'];
                    if (!in_array($key_ea, $selected_items)) {
                        unset($results[$per_idx][$idx_ea]);
                    }
                }
            }

            $vars = array (FIELD_DATE => General::strftime('%Y-%m-%d', time()),
                           FIELD_EQUIPMENT_ID => array(),
                           FIELD_EQUIPMENT_NAME => array(),
                           FIELD_EQUIPMENT_GROUP_ID => array(),
                           FIELD_EQUIPMENT_GROUP_NAME => array(),
                           FIELD_ACTIVITY_ACT_ID => array(),
                           FIELD_ACTIVITY_ACT_NAME => array());

            foreach ($results as $per_idx => $per_results) {
                foreach ($per_results as $idx_ea => $value_ea) {
                    $vars[FIELD_EQUIPMENT_ID][] = $value_ea['equipment'];
                    $vars[FIELD_EQUIPMENT_NAME][] = $value_ea['equipment_name'];
                    $vars[FIELD_EQUIPMENT_GROUP_ID][] = $value_ea['group'];
                    $vars[FIELD_EQUIPMENT_GROUP_NAME][] = $value_ea['group_name'];
                    $vars[FIELD_ACTIVITY_ACT_ID][] = $value_ea['activity'];
                    $vars[FIELD_ACTIVITY_ACT_NAME][] = $value_ea['activity_name'];
                }
            }

            return $vars;
        }
    }

?>
