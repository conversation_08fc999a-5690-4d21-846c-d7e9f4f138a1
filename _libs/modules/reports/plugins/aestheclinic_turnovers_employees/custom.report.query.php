<?php
Class Aestheclinic_Turnovers_Employees Extends Reports {
    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare the array for the final results
        $final_results = array();

        if (!is_array($filters['employees'])) {
            $filters['employees'] = array($filters['employees']);
        }
        $filters['employees'] = array_filter($filters['employees']);
        $filters['procedures'] = array_filter($filters['procedures']);

        // Get the report settings
        $settings = Reports::getReportSettings($registry);
        $settings['incomes_sheet_payment_statuses'] = preg_split('/\s*,\s*/', $settings['incomes_sheet_payment_statuses']);
        $settings['roles_full_rights'] = preg_split('/\s*,\s*/', $settings['roles_full_rights']);

        // Check rights
        if (in_array($registry['currentUser']->get('role'), $settings['roles_full_rights'])) {
            $user_full_rights = true;
        } else {
            $user_full_rights = false;
        }

        // Get the report filters
        $report_filters = $registry->get('report_filters');

        // If the user doesn't have full rights
        if (!$user_full_rights) {
            // Make sure that the selected clinic is only the one allowed for the user
            $filters['clinic'] = isset($report_filters['clinic']['options'][0]['option_value']) ? $report_filters['clinic']['options'][0]['option_value'] : '';
        }

        // If no employees are selected
        // or the user doesn't have full rights
        // then select all employees for the selected clinic (company)
        if (empty($filters['employees']) || !$user_full_rights) {
            $filters['employees'] = array();
            foreach ($report_filters['employees']['options'] as $o) {
                if ($o['company_customer_id'] == $settings["company_{$filters['clinic']}_customer"]) {
                    $filters['employees'][] = $o['option_value'];
                }
            }
        }

        // Check required filters
        if (empty($filters['period_from']) || empty($filters['period_to']) || empty($filters['clinic'])) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_required_filters'));
        } else {
            // Get the lang
            $lang = (!empty($filters['model_lang']) ? $filters['model_lang'] : $registry['lang']);

            // Get the database object
            $db = &$registry['db'];

            // If still no employees are selected
            if (empty($filters['employees'])) {
                // Set error message
                $registry['messages']->setError($registry['translater']->translate('error_reports_no_employees'));
            } else {
                $available_procedures = array();

                // Prepare to filter by procedures and category procedures
                if (!empty($filters['procedures'])) {
                    // Get all subprocedures of category procedures
                    $query = "
                        SELECT id
                          FROM " . DB_TABLE_NOMENCLATURES . " AS n
                          WHERE id IN ('" . implode("', '", $filters['procedures']) . "')
                            AND type = '{$settings['nomenclature_type_category']}'";
                    $categories_nomenclatures = $db->GetCol($query);
                    $available_procedures = array_diff($filters['procedures'], $categories_nomenclatures);
                    if ($categories_nomenclatures) {
                        $query = "
                            SELECT n.id
                              FROM " . DB_TABLE_NOMENCLATURES . " AS n
                              JOIN " . DB_TABLE_FIELDS_META . " AS fm
                                ON (fm.model = 'Nomenclature'
                                  AND fm.model_type = n.type
                                  AND fm.name = '{$settings['field_nom_procedure_cat_name_id']}')
                              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                ON (n.deleted_by = 0
                                  AND n.active = 1
                                  AND n.type = '{$settings['nomenclature_type_procedure']}'
                                  AND nc.model_id = n.id
                                  AND nc.var_id = fm.id
                                  AND nc.num = 1
                                  AND nc.value IN ('" . implode("', '", $categories_nomenclatures) . "'))";
                        $nom_in_cat = $db->GetCol($query);

                        $available_procedures = array_merge($available_procedures, $nom_in_cat);
                        unset($nom_in_cat);
                        $available_procedures = array_unique($available_procedures);
                    }
                }

                // Get the records
                $query = "
                    SELECT g.free_field2         AS employee_id,
                        gi.free_text2            AS employee_name,
                        fir.customer             AS patient_id,
                        TRIM(
                          CONCAT(ci.name, ' ', ci.lastname)
                        )                        AS patient_name,
                        d2.id                    AS visit_id,
                        dc2.value                AS visit_date,
                        fir.id                   AS sheet_id,
                        fir.num                  AS sheet_num,
                        g.id                     AS gt2_id,
                        g.article_id             AS procedure_cosmetics_id,
                        gi.article_name          AS procedure_cosmetics_name,
                        n.type                   AS procedure_cosmetics_type,
                        g.price                  AS price,
                        g.price_with_discount    AS price_with_discount,
                        g.quantity               AS quantity,
                        g.subtotal_with_discount AS subtotal_with_discount
                      FROM " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                      JOIN " . DB_TABLE_GT2_DETAILS . " AS g
                        ON (fir.annulled_by = 0
                          AND fir.active = 1
                          AND fir.type = '{$settings['fin_incomes_type_sheet']}'
                          AND fir.payment_status IN ('" . implode("', '", $settings['incomes_sheet_payment_statuses']) . "')
                          AND fir.issue_date BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}'
                          AND fir.company = '{$filters['clinic']}'
                          AND g.model = 'Finance_Incomes_Reason'
                          AND g.model_id = fir.id
                          AND g.price_with_discount != 0
                          AND g.free_field2 IN ('" . implode("', '", $filters['employees']) . "')" .
                          (!empty($filters['procedures']) ? "
                          AND g.article_id IN ('" . implode("', '", $available_procedures) . "')" : '') . ")
                      JOIN " . DB_TABLE_FINANCE_REASONS_RELATIVES . " AS frr
                        ON (frr.parent_model_name = 'Finance_Incomes_Reason'
                          AND frr.parent_id = fir.id
                          AND frr.link_to_model_name = 'Document')
                      JOIN " . DB_TABLE_DOCUMENTS . " AS d1
                        ON (d1.deleted_by = 0
                          AND d1.active = 1
                          AND d1.type = '{$settings['document_type_sheet']}'
                          AND d1.id = frr.link_to)
                      JOIN " . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr
                        ON (dr.link_to_model_name = 'Document'
                          AND dr.link_to = d1.id
                          AND dr.origin = 'inherited'
                          AND dr.parent_model_name = 'Document')
                      JOIN " . DB_TABLE_DOCUMENTS . " AS d2
                        ON (d2.deleted_by = 0
                          AND d2.active = 1
                          AND d2.type = '{$settings['document_type_visit']}'
                          AND d2.id = dr.parent_id)
                      JOIN " . DB_TABLE_FIELDS_META . " AS fm2
                        ON (fm2.model = 'Document'
                          AND fm2.model_type = d2.type
                          AND fm2.name = '{$settings['field_doc_visit_visit_date']}')
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                        ON (dc2.model_id = d2.id
                          AND dc2.var_id = fm2.id
                          AND dc2.num = 1)
                      JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        ON (n.id = g.article_id
                          AND n.type IN ('{$settings['nomenclature_type_procedure']}', '{$settings['nomenclature_type_cosmetics']}'))
                      LEFT JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gi
                        ON (gi.parent_id = g.id
                          AND gi.lang = '{$lang}')
                      LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                        ON (ci.parent_id = fir.customer
                          AND ci.lang = '{$lang}')";
                $records = $db->GetAll($query);

                // If there are any records
                if (!empty($records)) {
                    // Get the commission percentages for each employee
                    $query = "
                        SELECT id
                          FROM " . DB_TABLE_FIELDS_META . "
                          WHERE model = 'Nomenclature'
                            AND model_type = '{$settings['nomenclature_type_procedure']}'
                            AND name = '{$settings['field_nom_procedure_cat_name_id']}'";
                    $field_nom_procedure_cat_name_id = $db->GetOne($query);
                    $query = "
                        SELECT * FROM (
                          SELECT c.id                     AS customer_id,
                              IF(nc.model_id IS NOT NULL,
                                nc.model_id,
                                cc1.value)                AS nom_id,
                              cc2.value                   AS percentage
                            FROM " . DB_TABLE_CUSTOMERS . " AS c
                            JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                              ON (c.id IN ('" . implode("', '", $filters['employees']) . "')
                                AND fm1.model = 'Customer'
                                AND fm1.model_type = c.type
                                AND fm1.name = '{$settings['field_cus_employee_comm_nom_id']}')
                            JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc1
                              ON (cc1.model_id = c.id
                                AND cc1.var_id = fm1.id
                                AND cc1.value != '')
                            JOIN " . DB_TABLE_FIELDS_META . " AS fm2
                              ON (fm2.model = 'Customer'
                                AND fm2.model_type = c.type
                                AND fm2.name = '{$settings['field_cus_employee_comm_perc_value']}')
                            JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc2
                              ON (cc2.model_id = c.id
                                AND cc2.var_id = fm2.id
                                AND cc2.num = cc1.num
                                AND cc2.value != ''
                                AND cc2.value != 0)
                            LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                              ON (nc.var_id = '{$field_nom_procedure_cat_name_id}'
                                AND nc.value = cc1.value)
                            ORDER BY nc.model_id IS NOT NULL
                          ) AS tmp
                          GROUP BY customer_id, nom_id";
                    $employees_procedures_commissions_raw = $db->GetAll($query);
                    $employees_procedures_commissions = array();
                    foreach ($employees_procedures_commissions_raw as $epc) {
                        $employees_procedures_commissions[$epc['customer_id']][$epc['nom_id']] = $epc['percentage'];
                    }

                    //get the visit counts for each patient
                    $patient_ids = array_column($records, 'patient_id');
                    $patients_visit_counts = array();
                    $new_patients = array();
                    if ($patient_ids) {
                        $query = "
                            SELECT customer, COUNT(d.id)
                            FROM " . DB_TABLE_DOCUMENTS . " as d
                            JOIN " . DB_TABLE_FIELDS_META . " AS fm
                                ON fm.model = 'Document' AND fm.model_type = {$settings['document_type_visit']}
                                    AND fm.name = '{$settings['field_doc_visit_visit_date']}'
                                    AND d.customer IN (" . implode(', ', $patient_ids) . ")
                                    AND d.active = 1
                                    AND d.deleted = 0
                            JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                                ON dc.model_id = d.id AND dc.var_id = fm.id AND dc.num = 1
                                  AND dc.value BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}'
                            GROUP BY d.id";
                        $patients_visit_counts = $db->GetAssoc($query);

                        //new patients
                        $query = "
                            SELECT id
                            FROM " . DB_TABLE_CUSTOMERS . "
                            WHERE DATE(added) BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}'";
                        $new_patients = $db->GetCol($query);
                    }


                    // Process the records
                    $final_results['total_commissions'] = 0;
                    foreach ($records as $r) {
                        if (!empty($employees_procedures_commissions[$r['employee_id']][$r['procedure_cosmetics_id']])) {
                            if (!isset($final_results['employees'][$r['employee_id']])) {
                                $final_results['employees'][$r['employee_id']] = array(
                                    'name'                  => $r['employee_name'],
                                    'commission_procedures' => 0,
                                    'commission_cosmetics'  => 0,
                                    'commission_total'      => 0,
                                    'turnover_procedure'    => 0,
                                    'turnover_cosmetics'    => 0,
                                    'visit_count'           => 0,
                                    'visit_count_new'       => 0,
                                    'rowspan'               => 8,
                                    'sheets'                => array()
                                );
                            }
                            if (!isset($final_results['employees'][$r['employee_id']]['sheets'][$r['sheet_id']])) {
                                $final_results['employees'][$r['employee_id']]['sheets'][$r['sheet_id']] = array(
                                    'num'          => $r['sheet_num'],
                                    'patient_id'   => $r['patient_id'],
                                    'patient_name' => $r['patient_name'],
                                    'visit_id'     => $r['visit_id'],
                                    'visit_date'   => $r['visit_date'],
                                    'visit_count'  => isset($patients_visit_counts[$r['patient_id']]) ? $patients_visit_counts[$r['patient_id']] : 0,
                                    'turnover_procedure'  => 0,
                                    'turnover_cosmetics'  => 0,
                                    'rowspan'      => 0,
                                    'procedures'   => array()
                                );
                            }
                            $commission_sum = $r['subtotal_with_discount'] * ($employees_procedures_commissions[$r['employee_id']][$r['procedure_cosmetics_id']] / 100);
                            $final_results['employees'][$r['employee_id']]['sheets'][$r['sheet_id']]['procedures'][$r['gt2_id']] = array(
                                'name'                  => $r['procedure_cosmetics_name'],
                                'price'                 => $r['price'],
                                'price_with_discount'   => $r['price_with_discount'],
                                'quantity'              => $r['quantity'],
                                'commission_percentage' => $employees_procedures_commissions[$r['employee_id']][$r['procedure_cosmetics_id']],
                                'commission_sum'        => $commission_sum
                            );
                            $commission_type = '';
                            if ($r['procedure_cosmetics_type'] == $settings['nomenclature_type_procedure']) {
                                $commission_type = 'procedures';
                                $final_results['employees'][$r['employee_id']]['sheets'][$r['sheet_id']]['turnover_procedure'] += $r['subtotal_with_discount'];
                            } else if ($r['procedure_cosmetics_type'] == $settings['nomenclature_type_cosmetics']) {
                                $commission_type = 'cosmetics';
                                $final_results['employees'][$r['employee_id']]['sheets'][$r['sheet_id']]['turnover_cosmetics'] += $r['subtotal_with_discount'];
                            }
                            $final_results['employees'][$r['employee_id']]["turnover_procedure"] =
                                array_sum(array_column($final_results['employees'][$r['employee_id']]['sheets'], 'turnover_procedure'));
                            $final_results['employees'][$r['employee_id']]["turnover_cosmetics"] =
                                array_sum(array_column($final_results['employees'][$r['employee_id']]['sheets'], 'turnover_cosmetics'));
                            $final_results['employees'][$r['employee_id']]["visit_count"] =
                                array_sum(array_column($final_results['employees'][$r['employee_id']]['sheets'], 'visit_count'));
                            $final_results['employees'][$r['employee_id']]["new_patients"] = !empty($new_patients) ?
                                count(array_intersect(
                                    $new_patients,
                                    array_column($final_results['employees'][$r['employee_id']]['sheets'], 'patient_id')
                                )) : 0;
                            $final_results['employees'][$r['employee_id']]["commission_{$commission_type}"] += $commission_sum;
                            $final_results['employees'][$r['employee_id']]['commission_total'] += $commission_sum;
                            $final_results['total_commissions'] += $commission_sum;
                            $final_results['employees'][$r['employee_id']]['rowspan']++;
                            $final_results['employees'][$r['employee_id']]['sheets'][$r['sheet_id']]['rowspan']++;
                        }
                    }
                }
            }
        }

        // If there is no data
        if (empty($final_results['employees'])) {
            // Dont show the export button
            $final_results['additional_options']['dont_show_export_button'] = true;
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}
?>