<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('DOCUMENT_TYPE_INCOME_ID', 11);
            define('DOCUMENT_TYPE_EXPENSE_ID', 12);
            define('OFFICE_ID', 4);
            define('TYPE_INCOME', 'type_income');
            define('TYPE_EXPENSE', 'type_expense');
            define('PAYING_REASON', 'paying_reason');
            define('PAYING_TYPE', 'type_paying');
            define('PAYING_VALUE', 'paying_value');
            define('PAYING_CURRENCY', 'paying_currency');
            define('TAKEN_MONEY', 'take_money');
            define('RECEIVING_DATE', 'money_date');
            define('FOR_EMPLOYEE', 'for_empl');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE EMPLOYEE FILTER
            //get employees' options
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_customers = array('model_lang'    => $registry['lang'],
                                       'sanitize'      => true,
                                       'sort'          => array('ci18n.name', 'ci18n.lastname'),
                                       'where'         => array('c.type = ' . PH_CUSTOMER_EMPLOYEE,
                                                                'c.active = 1'));
            $employees = Customers::search($registry, $filters_customers);

            //prepare documents' types groups
            $options_projects = array();

            foreach($employees as $employee) {
                $options_customers[] = array(
                    'label'         => $employee->get('name') . ($employee->get('lastname') ? (' ' . $employee->get('lastname')) : ''),
                    'option_value'  => $employee->get('id')
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'employees',
                'name'      => 'employees',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_employees'),
                'help'      => $this->i18n('reports_employees'),
                'options'   => $options_customers,
            );
            $filters['employees'] = $filter;

            //DEFINE FOR EMPLOYEE FILTER
            $filter = array (
                'custom_id' => 'for_employee',
                'name'      => 'for_employee',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_for_employee'),
                'help'      => $this->i18n('reports_for_employee'),
                'options'   => $options_customers
            );
            $filters['for_employee'] = $filter;

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_customers'),
                'help'              => $this->i18n('reports_customers'),
                'value'             => ''
            );
            $filters['customers'] = $filter;

            //DEFINE DOCUMENT TYPE FILTER
            $options_document_types = array(
                array ( 'label' => $this->i18n('reports_document_type_income'),
                        'option_value' => 'income'
                       ),
                array ( 'label' => $this->i18n('reports_document_type_expense'),
                        'option_value' => 'expense'
                       )
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'document_type',
                'name' => 'document_type',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_document_type'),
                'help' => $this->i18n('reports_document_type'),
                'options' => $options_document_types,
            );
            $filters['document_type'] = $filter;

            //DEFINE PAYMENT TYPE FILTER
            $options_payment_types = array(
                array ( 'label' => $this->i18n('reports_payment_type_cash'),
                        'option_value' => 'cash'
                       ),
                array ( 'label' => $this->i18n('reports_payment_type_bank'),
                        'option_value' => 'bank'
                       ),
                array ( 'label' => $this->i18n('reports_payment_type_card'),
                        'option_value' => 'card'
                       ),
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'payment_type',
                'name' => 'payment_type',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_payment_type'),
                'help' => $this->i18n('reports_payment_type'),
                'options' => $options_payment_types,
            );
            $filters['payment_type'] = $filter;

            //DEFINE CURRENCY FILTER
            $options_currency_types = array(
                array ( 'label' => 'BGN',
                        'option_value' => 'BGN'
                       ),
                array ( 'label' => 'EUR',
                        'option_value' => 'EUR'
                       )
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'currency',
                'name' => 'currency',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_currency'),
                'help' => $this->i18n('reports_currency'),
                'options' => $options_currency_types,
            );
            $filters['currency'] = $filter;

            //DEFINE TYPE INCOME FILTER
            $query = 'SELECT fo.option_value, fo.label FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" . 
                     '  INNER JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo ON (fo.parent_name=fm.name AND lang="' . $registry['lang'] . '")' . "\n" .
                     'WHERE fm.model="Document" AND fm.model_type="' . DOCUMENT_TYPE_INCOME_ID . '" AND fm.name="' . TYPE_INCOME . '"';
            $records_income = $registry['db']->GetAll($query);

            $options_type_income = array();
            foreach ($records_income as $record) {
                    $options_type_income[] = array(
                                        'label'         => $record['label'],
                                        'option_value'  => $record['option_value']
                    );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'type_income',
                'name' => 'type_income',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_type_income'),
                'help' => $this->i18n('reports_type_income'),
                'options' => $options_type_income,
            );
            $filters['type_income'] = $filter;

            //DEFINE TYPE EXPENSES FILTER
            $query = 'SELECT fo.option_value, fo.label FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" . 
                     '  INNER JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo ON (fo.parent_name=fm.name AND lang="' . $registry['lang'] . '")' . "\n" .
                     'WHERE fm.model="Document" AND fm.model_type="' . DOCUMENT_TYPE_EXPENSE_ID . '" AND fm.name="' . TYPE_EXPENSE . '"';
            $records_expence = $registry['db']->GetAll($query);

            $options_type_expense = array();
            foreach ($records_expence as $record) {
                    $options_type_expense[] = array(
                                        'label'         => $record['label'],
                                        'option_value'  => $record['option_value']
                    );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'type_expense',
                'name' => 'type_expense',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_type_expense'),
                'help' => $this->i18n('reports_type_expense'),
                'options' => $options_type_expense,
            );
            $filters['type_expense'] = $filter;

            //DEFINE MONEY GIVEN FILTER
            $options_money_given = array(
                array ( 'label'         => $this->i18n('reports_money_given_yes'),
                        'option_value'  => 'yes'
                       ),
                array ( 'label'         => $this->i18n('reports_money_given_no'),
                        'option_value'  => 'no'
                       )
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'money_given',
                'name' => 'money_given',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_money_given'),
                'help' => $this->i18n('reports_money_given'),
                'options' => $options_money_given,
            );
            $filters['money_given'] = $filter;

            return $filters;
        }
    }
?>
