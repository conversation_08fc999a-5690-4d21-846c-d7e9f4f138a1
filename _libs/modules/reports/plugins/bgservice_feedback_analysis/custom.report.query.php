<?php
    Class Bgservice_Feedback_Analysis Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            $final_results = array();

            if (empty($filters['from_date']) || empty($filters['to_date'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_select_required_filters'));
                return null;
            } elseif ($filters['from_date'] > $filters['to_date']) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_invalid_period'));
                return null;
            }

            // get additional vars
            $add_vars = array(FEEDBACK_DATE, PPP_WORKER, FEEDBACK_PEOPLE, PPP_TYPE_SERVICE, FEEDBACK_WORK, FEEDBACK_TYPE);
            $query_fields_ids = 'SELECT name AS idx, id ' . "\n" .
                                'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                                'WHERE model = "Document" AND model_type = "' . DOC_TYPE_FEEDBACK . '" ' . "\n" .
                                '  AND name IN ("' . implode('","', $add_vars) . '")';
            $fields_ids = $registry['db']->getAssoc($query_fields_ids);

            //get all scores (option_value and label)
            $query_scores = 'SELECT option_value, label' . "\n" .
                            'FROM ' . DB_TABLE_FIELDS_OPTIONS . "\n" .
                            'WHERE parent_name="' . FEEDBACK_PEOPLE . '" AND lang="' . $lang . '"' . "\n" .
                            'ORDER BY option_value ASC';
            $scores = $registry['db']->getAssoc($query_scores);

            $sql_presort = array();
            $sql_presort['select'] = 'SELECT d.id AS id' . "\n";
            $sql_presort['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                   '  ON d.type="' . DOC_TYPE_FEEDBACK . '" AND dcstm_fd.model_id=d.id AND dcstm_fd.var_id="' . (!empty($fields_ids[FEEDBACK_DATE]) ? $fields_ids[FEEDBACK_DATE] : '') . '"' .  "\n" .
                                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_w' . "\n" .
                                   '  ON dcstm_fd.model_id=dcstm_w.model_id AND dcstm_w.var_id="' . (!empty($fields_ids[PPP_WORKER]) ? $fields_ids[PPP_WORKER] : '') . '"' .  "\n";

            if (!empty($filters['feedback_for'])) {
                $sql_presort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS feedback_for' . "\n" .
                                        '  ON dcstm_fd.model_id=feedback_for.model_id AND feedback_for.var_id="' . (!empty($fields_ids[FEEDBACK_TYPE]) ? $fields_ids[FEEDBACK_TYPE] : '') . '" AND feedback_for.value IN ("' . implode('","', $filters['feedback_for']) . '")';
            }
            $where = array();
            $where[] = 'WHERE d.type="' . DOC_TYPE_FEEDBACK . '" AND d.active=1 AND d.deleted_by=0';
            if ($filters['customer']) {
                $where[] = 'd.customer=' . $filters['customer'];
            }
            if ($filters['employee']) {
                $where[] = 'dcstm_w.value=' . $filters['employee'];
            }
            if ($filters['from_date']) {
                $where[] = 'dcstm_fd.value>="' . sprintf('%s 00:00:00', $filters['from_date']) . '"';
            }
            if ($filters['to_date']) {
                $where[] = 'dcstm_fd.value<="' . sprintf('%s 23:59:59', $filters['to_date']) . '"';
            }

            $sql_presort['where'] = implode(' AND ' . "\n", $where);
            $query_presort = implode("\n", $sql_presort);

            $doc_ids = $registry['db']->getCol($query_presort);
            $doc_ids = array_unique($doc_ids);

            $records = array();
            if (!empty($doc_ids)) {
                $sql = array();
                $sql['select'] = 'SELECT d.id AS id, d.full_num, ' . "\n" .
                                 '  d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) AS customer_name, ' . "\n" .
                                 '  dcstm_fd.value AS feedback_date, ' . "\n" .
                                 '  dcstm_w.value AS employee, dcstm_w.num AS employee_num, c2.active AS employee_active, ' . "\n" .
                                 '  CONCAT(ci18n2.name, " ", ci18n2.lastname) AS employee_name, ' . "\n" .
                                 '  dcstm_fp.value AS feedback_people, ' . "\n" .
                                 '  dcstm_ts.value AS type_service, dcstm_ts.num AS type_service_num, ' . "\n" .
                                 '  fo_ts.name AS type_service_name, ' . "\n" .
                                 '  dcstm_fw.value AS feedback_work, dcstm_fw.num as feedback_work_key ';

                $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                               '  ON d.customer=ci18n.parent_id AND ci18n.lang="' . $lang . '"' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                               '  ON d.type="' . DOC_TYPE_FEEDBACK . '" AND dcstm_fd.model_id=d.id AND dcstm_fd.var_id="' . (!empty($fields_ids[FEEDBACK_DATE]) ? $fields_ids[FEEDBACK_DATE] : '') . '"' .  "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_w' . "\n" .
                               '  ON dcstm_fd.model_id=dcstm_w.model_id AND dcstm_w.var_id="' . (!empty($fields_ids[PPP_WORKER]) ? $fields_ids[PPP_WORKER] : '') . '"' .  "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c2' . "\n" .
                               '  ON c2.id=dcstm_w.value AND c2.type=' . PH_CUSTOMER_EMPLOYEE . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n2' . "\n" .
                               '  ON c2.id=ci18n2.parent_id AND ci18n2.lang="' . $lang . '"' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fp' . "\n" .
                               '  ON dcstm_w.model_id=dcstm_fp.model_id AND dcstm_w.num=dcstm_fp.num AND dcstm_fp.var_id="' . (!empty($fields_ids[FEEDBACK_PEOPLE]) ? $fields_ids[FEEDBACK_PEOPLE] : '') . '"' .  "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_ts' . "\n" .
                               '  ON dcstm_fp.model_id=dcstm_ts.model_id AND dcstm_ts.var_id="' . (!empty($fields_ids[PPP_TYPE_SERVICE]) ? $fields_ids[PPP_TYPE_SERVICE] : '') . '"' .  "\n" .
                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS fo_ts' . "\n" .
                               '  ON fo_ts.parent_id=dcstm_ts.value AND fo_ts.lang="' . $lang . '"' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fw' . "\n" .
                               '  ON dcstm_ts.model_id=dcstm_fw.model_id AND dcstm_ts.num=dcstm_fw.num AND dcstm_fw.var_id="' . (!empty($fields_ids[FEEDBACK_WORK]) ? $fields_ids[FEEDBACK_WORK] : '') . '"' .  "\n";

                $sql['where'] = 'WHERE d.id IN ("' . implode('","', $doc_ids) . '")';
                $sql['order'] = 'ORDER BY dcstm_fd.value DESC';

                $query = implode("\n", $sql);

                $records = $registry['db']->getAll($query);
            }

            $feedback_data = array();
            $type_service_avg = array();
            $employee_avg = array();

            foreach ($records as $rec) {
                if (!array_key_exists($rec['id'], $feedback_data)) {
                    $feedback_data[$rec['id']] = array('full_num' => $rec['full_num'],
                                                       'feedback_date' => $rec['feedback_date'],
                                                       'customer' => $rec['customer'],
                                                       'customer_name' => trim($rec['customer_name']));
                }
                $feedback_data[$rec['id']]['employee'][$rec['employee_num']] = $rec['employee'];
                $feedback_data[$rec['id']]['employee_active'][$rec['employee_num']] =
                    ($rec['employee_active'] === '0' ? $rec['employee_active'] : '1');
                $feedback_data[$rec['id']]['employee_name'][$rec['employee_num']] =
                    ($rec['employee'] ? trim($rec['employee_name']) : '-');
                $feedback_data[$rec['id']]['feedback_people'][$rec['employee_num']] = $rec['feedback_people'];
                $feedback_data[$rec['id']]['feedback_people_name'][$rec['employee_num']] =
                    array_key_exists($rec['feedback_people'], $scores) ? $scores[$rec['feedback_people']] : '-';

                $feedback_data[$rec['id']]['type_service'][$rec['type_service_num']] = $rec['type_service'];
                $feedback_data[$rec['id']]['type_service_name'][$rec['type_service_num']] =
                    ($rec['type_service'] ? $rec['type_service_name'] : '-');
                $feedback_data[$rec['id']]['feedback_work'][$rec['type_service_num']] = $rec['feedback_work'];
                $feedback_data[$rec['id']]['feedback_work_name'][$rec['type_service_num']] =
                    array_key_exists($rec['feedback_work'], $scores) ? $scores[$rec['feedback_work']] : '-';

                // collect data for average calculations
                if (($rec['type_service'] || $rec['feedback_work']) && !array_key_exists($rec['type_service'], $type_service_avg)) {
                    $type_service_avg[$rec['type_service']] = array(
                        'name' => ($rec['type_service'] ? $rec['type_service_name'] : '-'),
                        'scores' => array());
                }
                if ($rec['feedback_work']) {
                    $feedback_work_key = sprintf('%d_%d', $rec['id'], $rec['feedback_work_key']);
                    $type_service_avg[$rec['type_service']]['scores'][$feedback_work_key] = $rec['feedback_work'];
                }

                if (($rec['employee'] || $rec['feedback_people']) && !array_key_exists($rec['employee'], $employee_avg)) {
                    $employee_avg[$rec['employee']] = array(
                        'name' => ($rec['employee'] ? $rec['employee_name'] : '-'),
                        'active' => $rec['employee_active'],
                        'scores' => array());
                }
                if ($rec['feedback_people']) {
                    $employee_avg[$rec['employee']]['scores'][] = $rec['feedback_people'];
                }
            }

            $type_service_scores = array();
            foreach ($type_service_avg as $st => $st_data) {
                if ($st_data['scores']) {
                    $score = sprintf('%.2f', round(array_sum($st_data['scores'])/count($st_data['scores']), 2));
                    $score_round = strval(round($score));
                    $type_service_avg[$st]['avg'] = $type_service_scores[] = $score;
                    $type_service_avg[$st]['avg_name'] = sprintf('%s (%s)',
                        (array_key_exists($score_round, $scores) ? $scores[$score_round] : '-'),
                        $score);
                } else {
                    $type_service_avg[$st]['avg_name'] = '-';
                }
                unset($type_service_avg[$st]['scores']);
            }
            if ($type_service_avg) {
                usort($type_service_avg, array('self', 'sortingResultsByName'));
            }

            $employee_scores = array();
            foreach ($employee_avg as $e => $e_data) {
                if ($e_data['scores']) {
                    $score = sprintf('%.2f', round(array_sum($e_data['scores'])/count($e_data['scores']), 2));
                    $score_round = strval(round($score));
                    $employee_avg[$e]['avg'] = $employee_scores[] = $score;
                    $employee_avg[$e]['avg_name'] = sprintf('%s (%s)',
                        (array_key_exists($score_round, $scores) ? $scores[$score_round] : '-'),
                        $score);
                } else {
                    $employee_avg[$e]['avg_name'] = '-';
                }
                unset($employee_avg[$e]['scores']);
            }
            if ($employee_avg) {
                usort($employee_avg, array('self', 'sortingResultsByName'));
            }

            $min_type_services = array();
            $max_type_services = array();
            if ($type_service_scores) {
                $type_service_scores = array_unique($type_service_scores);
                sort($type_service_scores);
                $min_value = reset($type_service_scores);
                $max_value = end($type_service_scores);

                $min_value = reset($type_service_scores);
                $max_value = end($type_service_scores);
                foreach ($type_service_avg as $st => $st_data) {
                    if (!empty($st_data['avg'])) {
                        if ($st_data['avg'] == $min_value) {
                            $min_type_services[] = $st_data['name'];
                        }
                        if ($st_data['avg'] == $max_value) {
                            $max_type_services[] = $st_data['name'];
                        }
                    }
                }
            }
            sort($min_type_services);
            sort($max_type_services);

            $min_employees = array();
            $max_employees = array();
            if ($employee_scores) {
                $employee_scores = array_unique($employee_scores);
                sort($employee_scores);

                $min_value = reset($employee_scores);
                $max_value = end($employee_scores);
                foreach ($employee_avg as $e => $e_data) {
                    if (!empty($e_data['avg'])) {
                        if ($e_data['avg'] == $min_value) {
                            $min_employees[] = array(
                                'name' => $e_data['name'],
                                'active' => $e_data['active']
                            );
                        }
                        if ($e_data['avg'] == $max_value) {
                            $max_employees[] = array(
                                'name' => $e_data['name'],
                                'active' => $e_data['active']
                            );
                        }
                    }
                }
            }
            usort($min_employees, array('self', 'sortingResultsByName'));
            usort($max_employees, array('self', 'sortingResultsByName'));

            $final_results = array(
                'feedback_data'     => $feedback_data,
                'type_service_avg'  => $type_service_avg,
                'employee_avg'      => $employee_avg,
                'min_type_services' => $min_type_services,
                'max_type_services' => $max_type_services,
                'min_employees'     => $min_employees,
                'max_employees'     => $max_employees
            );

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        public static function sortingResultsByName($a, $b) {
            return ($a['name'] > $b['name']) ? 1 : -1;
        }
    }

?>
