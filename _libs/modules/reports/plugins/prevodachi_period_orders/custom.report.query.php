<?php
    Class Prevodachi_Period_Orders Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            // Prepare an array for the final results
            $final_results = array();

            // if relative or absolute date filters are specified
            if ($filters['date_limiter'] || $filters['date_from'] || $filters['date_to']) {

                // Get model lang
                if (empty($filters['model_lang'])) {
                    // Default model language is the interface language
                    $model_lang = $registry['lang'];
                } else {
                    $model_lang = $filters['model_lang'];
                }

                $db = &$registry['db'];

                // defines the period
                $date_from = '';
                $date_to = '';
                if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
                    $date_from = $filters['date_from'];
                    $date_to = $filters['date_to'];
                } elseif (!empty($filters['date_from'])) {
                    $date_from = $filters['date_from'];
                    $date_to = $filters['date_from'];
                } elseif (!empty($filters['date_to'])) {
                    $date_from = $filters['date_to'];
                    $date_to = $filters['date_to'];
                } elseif (!empty($filters['date_limiter'])) {
                    if ($filters['date_limiter'] == 'today') {
                        $date_from = General::strftime('%Y-%m-%d');
                        $date_to = General::strftime('%Y-%m-%d');
                    } elseif ($filters['date_limiter'] == 'tommorow') {
                        $date_from = General::strftime('%Y-%m-%d', strtotime('+1 day'));
                        $date_to = General::strftime('%Y-%m-%d', strtotime('+1 day'));
                    } elseif ($filters['date_limiter'] == 'current_week') {
                        $week_current_day = General::strftime('%w');
                        if ($week_current_day == 0) {
                            $week_current_day = 7;
                        }
                        $date_from = General::strftime('%Y-%m-%d', strtotime('-' . ($week_current_day-1) . ' day'));
                        $date_to = General::strftime('%Y-%m-%d', strtotime('+' . (7-$week_current_day) . ' day'));
                    }
                }

                if ($filters['office'] == 'current_user_office') {
                    $filters['office'] = $registry['currentUser']->get('office');
                }

                $var_names = array(
                    DOCUMENT_TYPE_ORDER => array(ORDER_DATE_TIME_RECEPTION, ORDER_DATE_TIME_DELIVERY),
                    DOCUMENT_TYPE_LEGALIZATION => array(LEGALIZATION_TYPE_ORDER_ID, LEGALIZATION_STRAIN_LEGALIZATION_NAME, LEGALIZATION_ACTION_STEP, LEGALIZATION_INSTITUTION_DAYS, LEGALIZATION_DATE_SUBMISSION, LEGALIZATION_HISTORY_REMOVAL),
                    DOCUMENT_TYPE_TRANSLATION_REQUEST => array(TRANSLATION_REQUEST_TYPE_TRANSLATION_ID, TRANSLATION_REQUEST_LANGUAGE, TRANSLATION_REQUEST_COMPLEXITY, TRANSLATION_REQUEST_FILE, TRANSLATION_REQUEST_NUM_SHEETS)
                );
                $where_clause = array();
                $var_ids = array();
                foreach ($var_names as $doc_type => $vars) {
                    $where_clause[] = 'model_type="' . $doc_type . '" AND name IN ("' . implode('", "', $vars) . '")';
                    $var_ids[$doc_type] = array_combine($vars, array_fill(0, count($vars), 0));
                }
                $sql_add_vars = 'SELECT name, model_type, id' . "\n" .
                                'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                                'WHERE model="Document" AND (' . implode(' OR ', $where_clause) . ')';
                $add_vars = $db->GetAll($sql_add_vars);

                foreach ($add_vars as $var) {
                    $var_ids[$var['model_type']][$var['name']] = $var['id'];
                }

                $sql_data = array();
                $sql_data['select'] =
                    'SELECT d.id, d.full_num, ' . "\n" .
                    'TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) AS customer_name, oi18n.name AS office_name,' . "\n" .
                    'dcstm_rec.value AS date_reception, dcstm_del.value AS date_delivery';
                $sql_data['from'] =
                    'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                    'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_rec' . "\n" .
                    '  ON d.id=dcstm_rec.model_id AND dcstm_rec.var_id="' . $var_ids[DOCUMENT_TYPE_ORDER][ORDER_DATE_TIME_RECEPTION] . '"' . "\n" .
                    'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_del' . "\n" .
                    '  ON dcstm_rec.model_id=dcstm_del.model_id AND dcstm_del.var_id="' . $var_ids[DOCUMENT_TYPE_ORDER][ORDER_DATE_TIME_DELIVERY] . '"' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                    '  ON d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '"' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                    '  ON d.office=oi18n.parent_id AND oi18n.lang="' . $model_lang . '"';

                // construct where
                $where = array();
                $where[] = 'd.type="' . DOCUMENT_TYPE_ORDER . '"';
                $where[] = 'd.active=1';
                $where[] = 'd.deleted_by=0';
                $where[] = 'dcstm_del.value!="" AND DATE(dcstm_del.value)>="' . $date_from . '" AND DATE(dcstm_del.value)<="' . $date_to . '"';
                if (isset($filters['office']) && $filters['office'] !== '') {
                    $where[] = 'd.office="' . $filters['office'] . '"';
                }
                $sql_data['where'] = 'WHERE ' . implode(' AND ', $where);

                $query = implode("\n", $sql_data);

                $orders = $db->getAssoc($query);

                if (empty($filters['service']) || in_array('translation', $filters['service'])) {
                    $final_results['requests'] = array();
                }
                if (empty($filters['service']) || in_array('legalization', $filters['service'])) {
                    $final_results['legalizations'] = array();
                }
                if ($orders) {
                    // get translations
                    if (empty($filters['service']) || in_array('translation', $filters['service'])) {
                        // get var id of "class" for Translation nomenclature
                        $sql_add_vars = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                                        'WHERE model="Nomenclature" AND model_type="' . NOM_TYPE_TRANSLATION . '" AND name="' . TRANSLATION_CLASS . '"';
                        $class_var_id = $db->GetOne($sql_add_vars);

                        // load ini file for documents to get status labels
                        $registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $model_lang . '/documents.ini');

                        // get translation GT2 rows of orders
                        $query = 'SELECT gt2.id AS idx, gt2.model_id AS order_id, ROUND(sum(gt2.' . ORDER_PAGES . '), 0) AS pages,' . "\n" .
                                 '  gt2i18n.' . ORDER_LANGUAGE_NAME . ' AS language_name' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                                 '  ON gt2.id=gt2i18n.parent_id AND gt2i18n.lang="' . $model_lang . '"' . "\n" .
                                 'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                                 '  ON n.id=gt2.' . ORDER_LANGUAGE . ' AND n.type=\'' . NOM_TYPE_LANGUAGE . '\'' . "\n" .
                                 'WHERE gt2.model="Document" AND gt2.model_id IN (' . implode(', ', array_keys($orders)) . ')' . "\n" .
                                 'GROUP BY gt2.model_id, gt2.' . ORDER_LANGUAGE . ', gt2.' . ORDER_CLASS . " \n" .
                                 'ORDER BY gt2.model_id ASC, gt2.id ASC';
                        $translation_rows = $db->GetAssoc($query);

                        foreach ($translation_rows as $row_id => $row) {
                            // add data for GT2 rows of orders
                            if (!isset($final_results['requests'][$row['order_id']])) {
                                $final_results['requests'][$row['order_id']] = $orders[$row['order_id']];
                                $final_results['requests'][$row['order_id']]['rows'] = array();
                                $final_results['requests'][$row['order_id']]['rowspan'] = 0;
                            }
                            $final_results['requests'][$row['order_id']]['rows'][$row_id] = array(
                                'pages'             => $row['pages'],
                                'remaining_pages'   => $row['pages'],
                                'language_name'     => $row['language_name'],
                                'requests'          => array(),
                                'rowspan'           => 0
                            );
                            $final_results['requests'][$row['order_id']]['rowspan']++;
                        }

                        // get related translation requests
                        $query = 'SELECT gt2.model_id AS order_id, gt2.id AS row_id,' . "\n" .
                                 '  TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) AS translator_name,' . "\n" .
                                 '  d.status, d.substatus, ds.name AS substatus_name, d.deadline,' . "\n" .
                                 '  dcstm_file.value AS file, CONVERT(dcstm_pages.value, SIGNED INT) AS pages' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_transl' . "\n" .
                                 '  ON gt2.model="Document" AND gt2.model_id=dcstm_transl.value' . "\n" .
                                 '    AND dcstm_transl.var_id="' . $var_ids[DOCUMENT_TYPE_TRANSLATION_REQUEST][TRANSLATION_REQUEST_TYPE_TRANSLATION_ID] . '"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 '  ON d.id=dcstm_transl.model_id AND d.active=1 AND d.deleted_by=0' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_lang' . "\n" .
                                 '  ON d.id=dcstm_lang.model_id' . "\n" .
                                 '    AND dcstm_lang.var_id="' . $var_ids[DOCUMENT_TYPE_TRANSLATION_REQUEST][TRANSLATION_REQUEST_LANGUAGE] . '"' . "\n" .
                                 '    AND dcstm_lang.value=gt2.' . ORDER_LANGUAGE . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_compl' . "\n" .
                                 '  ON dcstm_lang.model_id=dcstm_compl.model_id' . "\n" .
                                 '    AND dcstm_compl.var_id="' . $var_ids[DOCUMENT_TYPE_TRANSLATION_REQUEST][TRANSLATION_REQUEST_COMPLEXITY] . '"' . "\n" .
                                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_class' . "\n" .
                                 '  ON ncstm_class.model_id=gt2.' . ORDER_CLASS . "\n" .
                                 '    AND ncstm_class.var_id="' . $class_var_id . '"' . "\n" .
                                 '    AND ncstm_class.value=dcstm_compl.value' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_pages' . "\n" .
                                 '  ON dcstm_pages.model_id=dcstm_lang.model_id' . "\n" .
                                 '    AND dcstm_pages.var_id="' . $var_ids[DOCUMENT_TYPE_TRANSLATION_REQUEST][TRANSLATION_REQUEST_NUM_SHEETS] . '"' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_file' . "\n" .
                                 '  ON dcstm_compl.model_id=dcstm_file.model_id' . "\n" .
                                 '    AND dcstm_file.var_id="' . $var_ids[DOCUMENT_TYPE_TRANSLATION_REQUEST][TRANSLATION_REQUEST_FILE] . '"' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                                 '  ON d.substatus=ds.id AND ds.lang="' . $model_lang . '"' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON d.employee=ci18n.parent_id AND ci18n.lang="' . $model_lang . '"' . "\n" .
                                 'WHERE gt2.model_id IN (' . implode(', ', array_keys($orders)) . ')';
                        $requests = $db->GetAll($query);

                        // add data for existing requests for GT2 rows of orders
                        foreach ($requests as $request) {
                            // skip this row if it not exists into the data from the orders
                            // (info: this is posible when there are two indentical rows (indentical by language and class) into the order - if there are
                            //  such rows, then they will be grouped and the pages count will be the sum of the pages for each of the rows)
                            if (!isset($final_results['requests'][$request['order_id']]['rows'][$request['row_id']])) {
                                continue;
                            }

                            if ($request['file']) {
                                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                                $filters_file = array(
                                    'where'         => array('f.id = \'' . $request['file'] . '\''),
                                    'model_lang'    => $model_lang,
                                    'sanitize'      => true
                                );
                                $request['file'] = Files::searchOne($registry, $filters_file);
                            }
                            $final_results['requests'][$request['order_id']]['rows'][$request['row_id']]['requests'][] = array(
                                'translator_name'   => $request['translator_name'],
                                'status'            => $request['substatus'] ? $request['substatus_name'] : $registry['translater']->translate('documents_status_' . $request['status']),
                                'deadline'          => $request['deadline'],
                                'file'              => $request['file']
                            );
                            $final_results['requests'][$request['order_id']]['rows'][$request['row_id']]['remaining_pages'] -= $request['pages'];
                            $final_results['requests'][$request['order_id']]['rows'][$request['row_id']]['rowspan']++;

                            // if there is more than one row
                            if (count($final_results['requests'][$request['order_id']]['rows'][$request['row_id']]['requests']) > 1) {
                                // increase the rowspan of the order row
                                $final_results['requests'][$request['order_id']]['rowspan']++;
                            }
                        }
                        uasort($final_results['requests'], function($a, $b) { return $a['date_delivery'] > $b['date_delivery'] ? 1 : -1; });
                    }

                    // get legalizations
                    if (empty($filters['service']) || in_array('legalization', $filters['service'])) {
                        // get legalization GT2 rows of orders + related legalizations
                        $query = 'SELECT gt2.id AS idx, gt2.model_id AS order_id, ROUND(gt2.' . ORDER_PAGES . ', 0) AS num_docs' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                 'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                                 '  ON n.id=gt2.' . ORDER_LANGUAGE . ' AND n.type=\'' . NOM_TYPE_LEGALIZATION_TYPE . '\'' . "\n" .
                                 'WHERE gt2.model="Document" AND gt2.model_id IN (' . implode(', ', array_keys($orders)) . ')' . "\n" .
                                 'ORDER BY gt2.model_id ASC, gt2.id ASC';
                        $legalization_rows = $db->GetAssoc($query);

                        foreach ($legalization_rows as $row_id => $row) {
                            // add data for GT2 rows of orders
                            if (!isset($final_results['legalizations'][$row['order_id']])) {
                                $final_results['legalizations'][$row['order_id']] = $orders[$row['order_id']];
                                $final_results['legalizations'][$row['order_id']]['rows'] = array();
                                $final_results['legalizations'][$row['order_id']]['rowspan'] = 0;
                            }
                            $final_results['legalizations'][$row['order_id']]['rows'][$row_id] = array(
                                'num_docs'      => $row['num_docs']
                            );
                            $final_results['legalizations'][$row['order_id']]['rowspan']++;
                        }

                        $orders_sql_in = '\'' . implode('\', \'', array_keys($final_results['legalizations'])) . '\'';
                        $query = 'SELECT `d`.`id`   AS `document_id`,' . "\n" .
                                 '    `dc1`.`value` AS `institution_days`' . "\n" .
                                 '  FROM `' . DB_TABLE_DOCUMENTS . '` AS `d`' . "\n" .
                                 '  JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc1`' . "\n" .
                                 '    ON (`dc1`.`model_id` = `d`.`id`' . "\n" .
                                 '      AND `dc1`.`var_id` = \'' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_INSTITUTION_DAYS] . '\'' . "\n" .
                                 '      AND `dc1`.`value`  != \'\')' . "\n" .
                                 '  JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc2`' . "\n" .
                                 '    ON (`dc2`.`model_id` = `d`.`id`' . "\n" .
                                 '      AND `dc2`.`var_id` = \'' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_TYPE_ORDER_ID] . '\'' . "\n" .
                                 '      AND `dc2`.`value`  IN (' . $orders_sql_in . '))' . "\n" .
                                 '  WHERE `d`.`type`       = \'' . DOCUMENT_TYPE_LEGALIZATION . '\'' . "\n" .
                                 '    AND `d`.`active`     = \'1\'' . "\n" .
                                 '    AND `d`.`deleted_by` = \'0\'' . "\n" .
                                 '    ORDER BY `d`.`id` ASC, `dc1`.`num` ASC';
                        $legalizations_steps_arr = $db->GetAll($query);
                        $legalizations_steps = array();
                        foreach ($legalizations_steps_arr as $ls) {
                            $legalizations_steps[$ls['document_id']][] = $ls['institution_days'];
                        }
                        $query = 'SELECT * FROM (' . "\n" .
                                 '  SELECT `d`.`id`   AS `document_id`,' . "\n" .
                                 '      `dc1`.`value` AS `history_removal`' . "\n" .
                                 '    FROM `' . DB_TABLE_DOCUMENTS . '` AS `d`' . "\n" .
                                 '    JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc1`' . "\n" .
                                 '      ON (`dc1`.`model_id` = `d`.`id`' . "\n" .
                                 '        AND `dc1`.`var_id` = \'' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_HISTORY_REMOVAL] . '\'' . "\n" .
                                 '        AND `dc1`.`value`  != \'\')' . "\n" .
                                 '    JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc2`' . "\n" .
                                 '      ON (`dc2`.`model_id` = `d`.`id`' . "\n" .
                                 '        AND `dc2`.`var_id` = \'' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_TYPE_ORDER_ID] . '\'' . "\n" .
                                 '        AND `dc2`.`value`  IN (' . $orders_sql_in . '))' . "\n" .
                                 '    WHERE `d`.`type`       = \'' . DOCUMENT_TYPE_LEGALIZATION . '\'' . "\n" .
                                 '      AND `d`.`active`     = \'1\'' . "\n" .
                                 '      AND `d`.`deleted_by` = \'0\'' . "\n" .
                                 '    ORDER BY `d`.`id` ASC, `dc1`.`num` DESC' . "\n" .
                                 ') AS `tmp`' . "\n" .
                                 '  GROUP BY `document_id`';
                        $legalizations_steps_history = $db->GetAssoc($query);

                        // get related legalizations
                        $query = 'SELECT d.id AS legalization_id, gt2.model_id AS order_id, gt2.id AS row_id,' . "\n" .
                                 '  dcstm_step.value AS step, dcstm_submission.value AS date_submission, dcstm_dtype.value AS doc_type,' . "\n" .
                                 '  d.status, d.substatus, ds.name AS substatus_name, d.deadline' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_leg' . "\n" .
                                 '  ON gt2.model="Document" AND gt2.model_id=dcstm_leg.value' . "\n" .
                                 '    AND dcstm_leg.var_id="' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_TYPE_ORDER_ID] . '"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 '  ON d.id=dcstm_leg.model_id AND d.active=1 AND d.deleted_by=0' . "\n" .
                                 '    AND gt2.' . ORDER_USED_FOR_DOC_FIELD . '=d.id' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_dtype' . "\n" .
                                 '  ON d.id=dcstm_dtype.model_id' . "\n" .
                                 '    AND dcstm_dtype.var_id="' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_STRAIN_LEGALIZATION_NAME] . '"' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_step' . "\n" .
                                 '  ON dcstm_step.model_id=dcstm_dtype.model_id' . "\n" .
                                 '    AND dcstm_step.var_id="' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_ACTION_STEP] . '"' . "\n" .
                                 'LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dcstm_submission`' . "\n" .
                                 '  ON (`dcstm_submission`.`model_id` = `dcstm_dtype`.`model_id`' . "\n" .
                                 '    AND `dcstm_submission`.`var_id` = \'' . $var_ids[DOCUMENT_TYPE_LEGALIZATION][LEGALIZATION_DATE_SUBMISSION] . '\'' . "\n" .
                                 '    AND `dcstm_submission`.`value`  != \'\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                                 '  ON d.substatus=ds.id AND ds.lang="' . $model_lang . '"' . "\n" .
                                 'WHERE gt2.model_id IN (' . implode(', ', array_keys($orders)) . ')';
                        $legalizations = $db->GetAll($query);
                        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                        foreach ($legalizations as $legalization) {
                            $expected_for = '';

                            if (!empty($legalizations_steps[$legalization['legalization_id']])) {
                                $step = 0;
                                $working_days = 0;
                                if (!empty($legalization['step'])) {
                                    preg_match('/(\d)\s*\//', $legalization['step'], $matches_step);
                                    if (!empty($matches_step[1])) {
                                        $step = --$matches_step[1];
                                        foreach ($legalizations_steps[$legalization['legalization_id']] as $ls_key => $ls_days) {
                                            if ($ls_key >= $step) {
                                                $working_days += $ls_days;
                                            }
                                        }
                                    }
                                }

                                // If there is a current step
                                if (!empty($working_days)) {
                                    // Calculate the "expected for" date based on the current step
                                    $expected_for = Calendars_Calendar::calcDateOnWorkingDays($registry, $legalization['date_submission'], ($working_days - 1));
                                } else if (!empty($legalizations_steps_history[$legalization['legalization_id']])) {
                                    // If there is a history data - get the "expected for" date from the history
                                    $expected_for = $legalizations_steps_history[$legalization['legalization_id']];
                                } else {
                                    // Calculate the "expected for" date based on all steps working
                                    $working_days = array_sum($legalizations_steps[$legalization['legalization_id']]);
                                    $expected_for = Calendars_Calendar::calcDateOnWorkingDays($registry, $legalization['date_submission'], ($working_days - 1));
                                }
                            }

                            $final_results['legalizations'][$legalization['order_id']]['rows'][$legalization['row_id']] =
                                array_merge($final_results['legalizations'][$legalization['order_id']]['rows'][$legalization['row_id']],
                                    array(
                                        'doc_type' => $legalization['doc_type'],
                                        'step' => $legalization['step'],
                                        'status' => $legalization['substatus'] ? $legalization['substatus_name'] : $registry['translater']->translate('documents_status_' . $legalization['status']),
                                        'deadline' => $legalization['deadline'],
                                        'expected_for' => $expected_for
                                    ));

                        }
                        uasort($final_results['legalizations'], function($a, $b) { return $a['date_delivery'] > $b['date_delivery'] ? 1 : -1; });
                    }
                }
            } else {
                $error_msg = $registry['translater']->translate('error_reports_complete_at_least_one_date_field');
                if ($registry['action'] == 'dashlet') {
                    $final_results['additional_options'] = array('errors' => array($error_msg));
                } else {
                    $registry['messages']->setError($error_msg);
                }
            }

            // Prepare the results
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }

?>
