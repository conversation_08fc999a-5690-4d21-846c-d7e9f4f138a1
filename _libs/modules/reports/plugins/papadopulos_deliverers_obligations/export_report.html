<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1">
      <tr>
        <td style="vertical-align: middle;" rowspan="2">{#reports_customers#|escape}</td>
        <td style="vertical-align: middle;" rowspan="2">{#reports_value#|escape}</td>
        <td style="vertical-align: middle;" colspan="3">{#reports_warehouse_notice#|escape}</td>
        <td style="vertical-align: middle;" colspan="3">{#reports_expenses_reason#|escape}</td>
        <td style="vertical-align: middle;" colspan="3">{#reports_payments#|escape}</td>
        <td style="vertical-align: middle;" rowspan="2">{#reports_total_paid#|escape}</td>
        <td style="vertical-align: middle;" rowspan="2">{#reports_total_left_to_pay#|escape}</td>
      </tr>
      <tr>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_reason_num_label#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_reason_issue_date#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_reason_issue_sum#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_reason_num_label#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_reason_issue_date#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_reason_issue_sum#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_payment_num#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_payment_date#|escape}</td>
        <td style="vertical-align: middle; background-color: #EFEFEF;">{#reports_payment_sum#|escape}</td>
      </tr>
      {foreach from=$reports_results item=result name=results}
        {foreach from=$result.warehouse_notices item=warehouse_notice name=wn}
          {foreach from=$warehouse_notice.reasons item=reason name=reas}
            {foreach from=$reason.payments item=payment name=paym}
              <tr>
                {if $smarty.foreach.wn.first && $smarty.foreach.reas.first && $smarty.foreach.paym.first}
                  <td style="vertical-align: middle;" rowspan="{$result.rowspan}" width="300">
                    {$result.name|escape|default:"&nbsp;"}
                  </td>
                  <td align="right" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                    {$result.value|string_format:"%.2f"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                {/if}
                {if $smarty.foreach.reas.first && $smarty.foreach.paym.first}
                  {if $warehouse_notice.id}
                    <td style="vertical-align: middle; mso-number-format: \@;" rowspan="{$warehouse_notice.rowspan}">
                      {$warehouse_notice.full_num|escape|default:"&nbsp;"}
                    </td>
                    <td style="vertical-align: middle;" rowspan="{$warehouse_notice.rowspan}">
                      {$warehouse_notice.date|date_format:#date_short#|default:"&nbsp;"}
                    </td>
                    <td align="right" style="vertical-align: middle;" rowspan="{$warehouse_notice.rowspan}" nowrap="nowrap">
                      {$warehouse_notice.sum|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                    </td>
                  {else}
                    <td style="vertical-align: middle; text-align: center;" rowspan="{$warehouse_notice.rowspan}" colspan="3">
                      -
                    </td>
                  {/if}
                {/if}
                {if $smarty.foreach.paym.first}
                  <td style="vertical-align: middle; mso-number-format: \@;" rowspan="{$reason.rowspan}">
                    {$reason.num|escape|default:"&nbsp;"}
                  </td>
                  <td style="vertical-align: middle;" rowspan="{$reason.rowspan}">
                    {$reason.date|date_format:#date_short#|default:"&nbsp;"}
                  </td>
                  <td align="right" style="vertical-align: middle;" rowspan="{$reason.rowspan}" nowrap="nowrap">
                    {$reason.sum|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                {/if}
                <td style="mso-number-format: \@;">
                  {$payment.num|escape|default:"&nbsp;"}
                </td>
                <td>
                  {$payment.date|date_format:#date_short#|default:"&nbsp;"}
                </td>
                <td align="right" nowrap="nowrap">
                  {$payment.sum|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                </td>
                {if $smarty.foreach.wn.first && $smarty.foreach.reas.first && $smarty.foreach.paym.first}
                  <td align="right" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                    {$result.paid|string_format:"%.2f"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                  <td align="right" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                    {$result.remain_to_pay|string_format:"%.2f"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                {/if}
              </tr>
            {foreachelse}
              <tr>
                {if $smarty.foreach.wn.first && $smarty.foreach.reas.first}
                  <td style="vertical-align: middle;" rowspan="{$result.rowspan}" width="300">
                    {$result.name|escape|default:"&nbsp;"}
                  </td>
                  <td align="right" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                    {$result.value|string_format:"%.2f"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                {/if}
                {if $smarty.foreach.reas.first}
                  {if $warehouse_notice.id}
                    <td style="vertical-align: middle; mso-number-format: \@;" rowspan="{$warehouse_notice.rowspan}">
                      {$warehouse_notice.full_num|escape|default:"&nbsp;"}
                    </td>
                    <td style="vertical-align: middle;" rowspan="{$warehouse_notice.rowspan}">
                      {$warehouse_notice.date|date_format:#date_short#|default:"&nbsp;"}
                    </td>
                    <td align="right" style="vertical-align: middle;" rowspan="{$warehouse_notice.rowspan}" nowrap="nowrap">
                      {$warehouse_notice.sum|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                    </td>
                  {else}
                    <td style="vertical-align: middle; text-align: center;" rowspan="{$warehouse_notice.rowspan}" colspan="3">
                      -
                    </td>
                  {/if}
                {/if}
                <td style="vertical-align: middle; mso-number-format: \@;" rowspan="{$reason.rowspan}">
                  {$reason.num|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;" rowspan="{$reason.rowspan}">
                  {$reason.date|date_format:#date_short#|default:"&nbsp;"}
                </td>
                <td align="right" style="vertical-align: middle;" rowspan="{$reason.rowspan}" nowrap="nowrap">
                  {$reason.sum|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                </td>
                <td colspan="3"><span style="color: #888888;">{#reports_no_payments_found#|escape}</span></td>
                {if $smarty.foreach.wn.first && $smarty.foreach.reas.first}
                  <td align="right" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                    {$result.paid|string_format:"%.2f"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                  <td align="right" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                    {$result.remain_to_pay|string_format:"%.2f"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}
                  </td>
                {/if}
              </tr>
            {/foreach}
          {/foreach}
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="13"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
    <br />
    <br />

    <table border="1">
      <tr>
        <td style="padding: 7px;"><strong>{#reports_total_paid_orders#|escape}</strong></td>
        <td style="padding: 7px; background-color:#98BCFF;" align="right"><strong>{$reports_additional_options.full_paid_invoices|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}</strong></td>
      </tr>
      <tr>
        <td style="padding: 7px;"><strong>{#reports_total_partial_paid_orders#|escape}</strong></td>
        <td style="padding: 7px; background-color:#98BCFF;" align="right"><strong>{$reports_additional_options.partial_paid_invoices|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}</strong></td>
      </tr>
      <tr>
        <td style="padding: 7px;"><strong>{#reports_total_unpaid_orders#|escape}</strong></td>
        <td style="padding: 7px; background-color:#98BCFF;" align="right"><strong>{$reports_additional_options.not_paid_invoices|string_format:"%.2f"|default:"0.00"} {$reports_additional_options.main_currency|escape|default:"&nbsp;"}</strong></td>
      </tr>
    </table>
  </body>
</html>