<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {if $reports_results}
      <table border="1" cellpadding="0" cellspacing="0">
        <tr style="font-weight: bold;">
          <td style="width: 270px;">{#reports_nomenclature#|escape}</td>
          <td style="width: 30px;">{#reports_measure#|escape}</td>
          <td style="width: 110px;">{#reports_bought_quantity#|escape}</td>
          <td style="width: 130px;">{#reports_bought_total#|escape}</td>
          <td style="width: 130px;">{#reports_bought_average#|escape}</td>
          <td style="width: 110px;">{#reports_sold_quantity#|escape}</td>
          <td style="width: 130px;">{#reports_sold_total#|escape}</td>
          <td style="width: 130px;">{#reports_sold_average#|escape}</td>
          <td style="width: 110px;">{#reports_available_quantity#|escape}</td>
        </tr>
        {foreach from=$reports_results key='rk' name='ri' item='res'}
          <tr>
            <td>{$res.name|escape|default:"&nbsp;"}</td>
            <td style="text-align: center;">{$res.measure_name|default:"&nbsp;"}</td>
            <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_quantity}';">{$res.bought_quantity|default:0}</td>
            <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_price}';">{$res.bought_total|default:0}</td>
            <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_price}';">{$res.bought_average|default:0}</td>
            <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_quantity}';">{$res.sold_quantity|default:0}</td>
            <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_price}';">{$res.sold_total|default:0}</td>
            <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_price}';">{$res.sold_average|default:0}</td>
            {if $res.subtype eq 'commodity'}
              <td style="text-align: right; mso-number-format: '{$reports_additional_options.export_style_quantity}';">{$res.available_quantity|default:0}</td>
            {else}
              <td>&nbsp;</td>
            {/if}
          </tr>
        {/foreach}
      </table>
    {else}
      <span style="color: #5371AF; font-weight: bold;">{#error_reports_no_results_to_show#|escape}</span>
    {/if}
  </body>
</html>