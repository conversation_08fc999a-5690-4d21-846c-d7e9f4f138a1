<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="0">
      <tr>
        <td>
          <table border="1">
            <tr>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_bank_account_should_received#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_bank_account_received#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_amount#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_to_transfer#|escape}</strong></td>
            </tr>
            {foreach from=$reports_additional_options.incorrectly_transfered_amounts item=ita}
              <tr>
                <td nowrap="nowrap">{$ita.bank_account_should_received|escape|default:"&nbsp;"}</td>
                <td nowrap="nowrap">{$ita.bank_account_received|escape|default:"&nbsp;"}</td>
                <td style="mso-number-format:'0\.00';" nowrap="nowrap">{$ita.amount|string_format:"%.2f"|default:"0.00"}</td>
                <td style="mso-number-format:'0\.00';" nowrap="nowrap">{if $ita.transfered ne '-'}{$ita.transfered|string_format:"%.2f"|default:"-"}{else}-{/if}</td>
              </tr>
            {foreachelse}
              <tr>
                <td colspan="4"><span style="color: red;">{#no_items_found#|escape}</span></td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td>
          <table border="1">
            <tr>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#repotrs_num_payment#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#repotrs_customer#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_amount#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_bank_account_should_received#|escape}</strong></td>
              <td style="text-align: center;" nowrap="nowrap"><strong>{#reports_bank_account_received#|escape}</strong></td>
            </tr>
            {foreach from=$reports_results item=res name=result}
              <tr>
                <td nowrap="nowrap" style="mso-number-format:\@;">{$res.num|escape|default:"&nbsp;"}</td>
                <td nowrap="nowrap">{$res.customer_name|escape|default:"&nbsp;"}</td>
                <td nowrap="nowrap" style="mso-number-format:'0\.00';">{$res.amount|string_format:"%.2f"|default:"0.00"}</td>
                <td nowrap="nowrap">{$res.bank_account_target|escape|default:"&nbsp;"}</td>
                <td nowrap="nowrap">{$res.bank_account|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr>
                <td colspan="5"><span style="color: red;">{#no_items_found#|escape}</span></td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>