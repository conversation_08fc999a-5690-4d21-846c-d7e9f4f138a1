{if $reports_results}
  {assign var='design' value=$reports_results.design}

  <div style="width: 1005px; border: 1px solid #CCCCCC; text-align: center; padding: 6px 0px; font-size: 16px;">{$design.brand_name|escape}</div>

  <table class="reports_table" style="width: 1005px; margin-top: 20px;">
    <tr class="reports_title_row"><th colspan="20">{#technical_description#}</th></tr>
    <tr class="reports_title_row">
      <th>{#model#}</th>
      <th>{#season#}</th>
      <th width="93">{#date_of_creation#}</th>
      <th>{#kind_fabric#}</th>
      {foreach from=$design.colors item='color' name='colors'}
        <th>{#color#} {$smarty.foreach.colors.iteration}</th>
      {/foreach}
    </tr>
    <tr>
      <td class="hcenter">{$design.name|escape}</td>
      <td class="hcenter">{$design.season_info|escape}</td>
      <td class="hcenter">{$design.date_creation|date_format:#date_short#}</td>
      <td class="hcenter">{$design.main_fabric_name|escape}</td>
      {foreach from=$design.colors item='color'}
        <td class="hcenter">[{$color.code}] {$color.name}</td>
      {/foreach}
    </tr>
  </table>


  <table cellspacing="0" cellpadding="0" style="width: 1005px; min-height: 200px; border: 1px solid #cccccc; margin-top: 20px;">
    <tr>
      <td style="width: 50%; border: none; background-color: unset; text-align: center;">
        {if !empty($design.picture_front)}
          <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_front|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=500&amp;maxheight=800" />
        {/if}
      </td>
      <td style="width: 50%; border: none; background-color: unset; text-align: center;">
        {if !empty($design.picture_back)}
          <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_back|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=500&amp;maxheight=800" />
        {/if}
      </td>
    </tr>
  </table>

  <table class="reports_table" style="margin-top: 20px; background-color: unset; border: none; width: 1005px;">
    <tr class="reports_title_row">
      <th style="width: 60%; height: 25px; border: 1px solid #cccccc;">{#report_cuts#}</th>
    </tr>
    <tr>
      <td style="background-color: #F1F1F1; border: 1px solid #cccccc; vertical-align: top;">{$design.cut_info|nl2br}</td>
    </tr>
  </table>

  <table class="reports_table" style="margin-top: 20px; background-color: unset; border: none; width: 1005px;">
    <tr class="reports_title_row">
      <th style="width: 60%; height: 25px; border: 1px solid #cccccc;">{#technical_description_and_instructions#}</th>
      <th style="width: 40%; height: 25px; border: 1px solid #cccccc;">{#reserves_and_processing#}</th>
    </tr>
    <tr>
      <td style="background-color: #F1F1F1; border: 1px solid #cccccc; vertical-align: top;">{$design.note_info|nl2br}</td>
      <td style="background-color: #F1F1F1; border: 1px solid #cccccc; vertical-align: top;">{$design.reserves_processing|nl2br}</td>
    </tr>
  </table>

  <table class="reports_table" style="width: 1005px; margin-top: 20px;">
    <tr class="reports_title_row">
      <th style="width: 40%;" colspan="2">{#labeling_instructions#}</th>
      <th style="width: 60%;">{#packing_instructions#}</th>
    </tr>
    <tr>
      <td class="vtop" colspan="2" style="vertical-align: top;">
        {$design.labeling_instructions|nl2br}
        {if !empty($design.picture_label_instructions)}
          <br />
          <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_label_instructions|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=200&amp;maxheight=300" />
        {/if}
      </td>
      <td style="vertical-align: top;">
        {$design.packing_instructions|nl2br}
        {if !empty($design.picture_packiging)}
          <br />
          <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_packiging|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=200&amp;maxheight=300" />
        {/if}
      </td>
    </tr>
    <tr>
      <td class="vtop" style="width: 25%; vertical-align: top;">
        {$design.compound_label|nl2br}
      </td>
      <td class="vtop" style="width: 25%;  vertical-align: top;">
        {if !empty($design.picture_label_compound)}
          <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_label_compound|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=200&amp;maxheight=300" />
        {/if}
      </td>
      <td class="vtop">&nbsp;</td>
    </tr>
  </table>

  <div style="width: 1005px; border: 1px solid #CCCCCC; text-align: center; padding: 6px 0px; font-size: 16px; margin-top: 20px;">{$design.brand_name}</div>

  <table style="width: 1005px; margin-top: 20px;{if !empty($reports_results.child_skus)} margin-bottom: 20px;{/if}">
    <tr>
      <th rowspan="5" style="width: 200px;">
        <table cellpadding="0" cellspacing="0" style="width: 100%; height: 200px; border: 1px solid #CCCCCC;">
          <tr>
            <td class="hcenter vmiddle" style="border: none;">
              {if !empty($design.picture_front)}
                <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_front|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=100&amp;maxheight=200" />
              {/if}
            </td>
            <td class="hcenter vmiddle" style="border: none;">
              {if !empty($design.picture_back)}
                <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_back|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=100&amp;maxheight=200" />
              {/if}
            </td>
          </tr>
        </table>
      </th>
      <td rowspan="5" style="background-color: unset; width: 20px; border: none;">&nbsp;</td>
      <td>
        <table class="reports_table" style="width: 100%; height: 200px;">
          <tr class="reports_title_row">
            <th colspan="4">&nbsp;</th>
          </tr>
          <tr class="reports_title_row">
            <th>{#model#}</th>
            <th>{#date_of_creation#}</th>
            <th>{#kind_fabric#}</th>
            <th>{#content_fabric#}</th>
          </tr>
          <tr>
            <td class="hcenter">{$design.name|escape}</td>
            <td class="hcenter">{$design.date_creation|date_format:#date_short#}</td>
            <td class="hcenter">{$design.main_fabric_name|escape}</td>
            <td class="hcenter">{$design.content_fabric|escape}</td>
          </tr>
          <tr class="reports_title_row">
            <th>{#season#}</th>
            <th>{#stage#}</th>
            <th>{#primary_atelier_production#}</th>
            <th>{#size#}</th>
          </tr>
          <tr>
            <td class="hcenter">{$design.season_info|escape}</td>
            <td class="hcenter">{$design.stage_production|escape}</td>
            <td class="hcenter">{$design.primary_atelier_prod|escape}</td>
            <td class="hcenter">{$reports_results.sizes|escape}</td>
          </tr>
        </table>
      </td>
    </tr>
  </table>

  {*foreach from=$reports_results.child_skus item='child_sku'}
    {include file=`$theme->templatesDir`view_grouping.html
      var=$child_sku.product_group
      standalone=true}
  {/foreach*}
  {foreach from=$reports_results.child_skus item='child_sku' name='child_skus'}
    <table class="reports_table" style="margin-top: 20px;{if $smarty.foreach.child_skus.last} margin-bottom: 20px;{/if}">
      <tr class="reports_title_row">
        <th colspan="6">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$child_sku.id}" target="_blank">
            {$child_sku.code}
          </a>
        </th>
      </tr>
      <tr class="reports_title_row">
        <th style="width: 460px;">{$child_sku.product_group.labels[$child_sku.product_group_names.product_name]}</th>
        <th style="width: 109px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_code]}</th>
        <th style="width: 93px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_color_code]}</th>
        <th style="width: 133px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_color_name]}</th>
        <th style="width: 70px;">{$child_sku.product_group.labels[$child_sku.product_group_names.product_num]}</th>
        <th style="width: 70px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_measure]}</th>
      </tr>
      {foreach from=$child_sku.product_group.values item='row'}
        <tr>
          <td>{$row[$child_sku.product_group_names.product_name]|escape}</td>
          <td>{$row[$child_sku.product_group_names.material_code]|escape}</td>
          <td>{$row[$child_sku.product_group_names.material_color_code]|escape}</td>
          <td>{$row[$child_sku.product_group_names.material_color_name]|escape}</td>
          <td>{$row[$child_sku.product_group_names.product_num]|escape}</td>
          <td class="hright">{$row[$child_sku.product_group_names.material_measure]|escape}</td>
        </tr>
      {foreachelse}
        <tr>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
      {/foreach}
    </table>
  {/foreach}
{/if}
