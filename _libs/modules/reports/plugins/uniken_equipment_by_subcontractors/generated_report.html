{if $report_filters.display_results.value eq 'summary'}
  <table class="reports_table">
    <tr class="reports_title_row">
      <th style="width: 12%;">{#reports_location_name#|escape}</th>
      <th style="width: *;">{#reports_subcontractor#|escape}</th>
      <th style="width: 7%;">{#reports_contract_count#|escape}</th>
      {foreach from=$reports_additional_options.type_names item='type_name'}
        <th style="width: 7%;">{$type_name|escape}</th>
      {/foreach}
      <th style="width: 7%;">{#reports_num_equipment#|escape}</th>
      <th style="width: 7%;">{#reports_total_revenue#|escape}</th>
    </tr>
    {foreach from=$reports_results item='location' key='rk' name='ri'}
      {cycle values='t_odd1 t_odd2,t_even1 t_even2' assign='current_class'}
      {foreach from=$location item='subcontractor' key='sk' name='si'}
        <tr class="{$current_class}">
          {if $smarty.foreach.si.first}
            <td rowspan="{$location|@count|default:1}">{$rk|escape}</td>
          {/if}
          <td>{$subcontractor.name|escape}</td>
          <td class="hright">{$subcontractor.contract_count}</td>
          {foreach from=$reports_additional_options.type_names item='type_name' key='t'}
            <td class="hright">{$subcontractor.$t|default:0}</td>
          {/foreach}
          <td class="hright">{$subcontractor.equipment_count}</td>
          <td class="hright">{$subcontractor.total|string_format:"%.2f"}</td>
        </tr>
      {/foreach}
    {foreachelse}
      <tr class="t_odd1 t_odd2">
        <td class="error" colspan="{math equation='5+x' x=$reports_additional_options.type_names|@count}">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    {if is_array($reports_results) && $reports_results|@count}
      <tr class="strong">
        <td class="hright" colspan="2">{#total#|mb_upper|escape}</td>
        <td class="hright">{$reports_additional_options.totals.contracts}</td>
        {foreach from=$reports_additional_options.type_names item='type_name' key='t'}
          <td class="hright">{$reports_additional_options.totals.$t}</td>
        {/foreach}
        <td class="hright">{$reports_additional_options.totals.equipment}</td>
        <td class="hright">{$reports_additional_options.totals.total|string_format:"%.2f"}</td>
      </tr>
    {/if}
  </table>
{else}
  <table class="reports_table">
    <tr class="reports_title_row">
      <th style="width: 12%;">{#reports_contract_num_date_sign#|escape}</th>
      <th style="width: *;">{#reports_customer#|escape}</th>
      <th style="width: 10%;">{#reports_location_name#|escape}</th>
      <th style="width: 10%;">{#reports_address#|escape}</th>
      <th style="width: 10%;">{#reports_subcontractor#|escape}</th>
      <th style="width: 10%;">{#reports_production_number#|escape}</th>
      <th style="width: 10%;">{#reports_producer#|escape}</th>
      <th style="width: 10%;">{#reports_equipment_type#|escape}</th>
      <th style="width: 5%;">{#reports_stops_count#|escape}</th>
      <th style="width: 5%;">{#reports_stops_price#|escape}</th>
      <th style="width: 10%;">{#reports_monthly_charge#|escape}</th>
      {if $report_filters.status.value ne $smarty.const.CONTRACT_SUBSTATUS_SIGNED}
      <th style="width: 10%;">{#reports_date_end#|escape}</th>
      {/if}
    </tr>
    {foreach from=$reports_results item='customer' key='rk' name='ri'}
      {cycle values='t_odd1 t_odd2,t_even1 t_even2' assign='current_class'}
      {foreach from=$customer.contracts item='contract' key='ck' name='ci'}
        {foreach from=$contract.rows item='row' name='gi'}
          <tr class="{$current_class}">
            {if $smarty.foreach.gi.first}
              <td rowspan="{$contract.rows|@count|default:1}"><a href="{$smarty.server.PHP_SELF}?{$module_param}=contracts&amp;contracts=viewtopic&amp;viewtopic={$ck}" target="_blank">{$contract.num|escape}</a> / {$contract.date_sign|date_format:#date_short#}</td>
            {/if}
            {if $smarty.foreach.ci.first && $smarty.foreach.gi.first}
              <td rowspan="{$customer.rowspan|default:1}">{$customer.name|escape}</td>
            {/if}
            <td>{$row.location_name|escape}</td>
            <td>{$row.address|escape}</td>
            <td>{$row.subcontractor_name|escape}</td>
            <td>{$row.production_number|escape}</td>
            <td>{$row.producer_name|escape}</td>
            <td>{$reports_additional_options.type_names[$row.nom_type]|escape}</td>
            <td class="hright">{$row.stops_count|escape}</td>
            <td class="hright">{$row.stops_price|string_format:"%.2f"}</td>
            <td class="hright">{$row.subtotal|string_format:"%.2f"}</td>
            {if $smarty.foreach.gi.first && $report_filters.status.value ne $smarty.const.CONTRACT_SUBSTATUS_SIGNED}
              <td rowspan="{$contract.rows|@count|default:1}">{$contract.date_end|date_format:#date_short#}</td>
            {/if}
          </tr>
        {/foreach}
      {/foreach}
    {foreachelse}
      <tr class="t_odd1 t_odd2">
        <td class="error" colspan="12">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    {if is_array($reports_results) && $reports_results|@count}
      <tr>
        <th>{#reports_contract_count#|escape}</th>
        <th>{#reports_customer_count#|escape}</th>
        <th>{#reports_location_count#|escape}</th>
        <th>&nbsp;</th>
        <th>{#reports_subcontractor_count#|escape}</th>
        <th>{#reports_equipment_count#|escape}</th>
        <th colspan="4">&nbsp;</th>
        <th>{#reports_total_charge#|escape}</th>
        {if $report_filters.status.value ne $smarty.const.CONTRACT_SUBSTATUS_SIGNED}
          <th>{#reports_terminated_count#|escape}</th>
        {/if}
      </tr>
      <tr>
        <td class="hright">{$reports_additional_options.totals.contracts}</td>
        <td class="hright">{$reports_additional_options.totals.customers}</td>
        <td class="hright">{$reports_additional_options.totals.locations}</td>
        <td>&nbsp;</td>
        <td class="hright">{$reports_additional_options.totals.subcontractors}</td>
        <td class="hright">{$reports_additional_options.totals.equipment}</td>
        <td colspan="4">&nbsp;</td>
        <td class="hright">{$reports_additional_options.totals.total|string_format:"%.2f"}</td>
        {if $report_filters.status.value ne $smarty.const.CONTRACT_SUBSTATUS_SIGNED}
          <td class="hright">{$reports_additional_options.totals.terminated}</td>
        {/if}
      </tr>
    {/if}
  </table>
{/if}
<br />
