<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = $registry;

            // $filters - array containing description of all filters
            $filters = array();

            // Filter: company
            $companies = preg_split('/\s*,\s*/', CUSTOMER_COMPANY_TYPE);
            $filters['company'] = array(
                'name'         => 'company',
                'type'         => 'autocompleter',
                'label'        => $this->i18n('reports_filter_company'),
                'required'     => 1,
                'autocomplete' => array(
                    'type' => 'customers',
                    'fill_options' => array(
                        '$company => <id>',
                        '$company_autocomplete => <name>',
                        '$company_oldvalue => <name>',
//                         '$entity => ',
//                         '$entity_autocomplete => ',
//                         '$event => ',
//                         '$event_autocomplete => '
                    ),
                    'suggestions' => '<name>',
                    'combobox' => '1',
                    'combobox_mode' => 'empty',
                    'filters' => array(
                        '<type>' => 'IN (' . implode(',', $companies) . ')',
                    ),
                    'url' => $_SERVER['PHP_SELF'] . '?' . $registry['module_param'] . '=customers&customers=ajax_select',
                    'buttons' => 'clear'
                )
            );

            //DEFINE REGION FILTER
            $filter = array(
                'custom_id'     => 'region',
                'name'          => 'region',
                'type'          => 'autocompleter',
                'label'         => $this->i18n('reports_region'),
                'help'          => $this->i18n('reports_region_help'),
                'value'         => '',
                'autocomplete'  => array(
                    'type' => 'nomenclatures',
                    'combobox'         => 1,
                    'combobox_mode' => 'empty',
                    'fill_options' => array(
                        '$region => <id>',
                        '$region_autocomplete => <name>'
                    ),
                    'suggestions' => '<name>',
                    'filters' => array(
                        '<type>' => 'IN (6)'
                    ),
                    'url' => $_SERVER['PHP_SELF'] . '?' . $registry['module_param'] . '=nomenclatures&nomenclatures=ajax_select',
                    'buttons' => 'clear',
                )
            );
            $filters['region'] = $filter;

            // Filter: entity
            $filters['entity'] = array(
                'name'         => 'entity',
                'type'         => 'autocompleter',
                'label'        => $this->i18n('reports_filter_entity'),
                'autocomplete' => array(
                    'clear'         => 1,
                    'combobox'         => 1,
                    'combobox_mode' => 'empty',
                    'type'          => 'autocompleters',
                    'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'autocompleters', 'autocompleters', 'ajax_select'),
                    'plugin_search' => 'customQuery',
                    'plugin_params' => array(
                        'comp'    => '$company',
                        'sql'     => "SELECT n.id, ni.name, nti18n.name as type_name " .
                            "FROM nom AS n JOIN nom_cstm AS nc1 ON (n.type = 7 AND !n.deleted AND n.active AND nc1.model_id = n.id AND nc1.var_id = 6005 AND nc1.num = 1 AND nc1.lang = '') " .
                            "JOIN nom_cstm as nc7 ON n.id=nc7.model_id AND nc7.var_id=5601 " .
                            "JOIN customers_cstm AS cc1 ON (cc1.model_id = {$registry['currentUser']->get('employee')} AND cc1.var_id = 103 AND cc1.num = 1 AND cc1.lang = '') " .
                            "JOIN nom_cstm AS nc2  ON (nc2.var_id = 6005 AND nc2.model_id = cc1.value AND nc2.num = 1 AND nc2.lang = '') " .
                            "JOIN customers_cstm AS cc2 ON (cc2.model_id = cc1.model_id AND cc2.var_id = 109 AND cc2.num = 1 AND cc2.lang = '' AND IF(cc2.value IN (336, 334), nc1.value = nc2.value, true)) " .
                            "JOIN nom_i18n AS ni ON (ni.parent_id = n.id AND ni.lang = '{$registry['lang']}') " .
                            "LEFT JOIN nom_types_i18n nti18n ON n.type=nti18n.parent_id AND nti18n.lang='{$registry['lang']}' " .
                            "LEFT JOIN tags_models t ON n.id=t.model_id AND t.model='Nomenclature' AND t.tag_id=2 " .
                            "WHERE nc7.value=<comp> AND t.model_id is NULL AND ni.name LIKE \"%<search_string_parts>%\" ORDER BY ni.name ASC",

                    ),
                    'suggestions'   => '<name> [<type_name>]',
                    'fill_options'  => array(
                        '$entity => <id>',
                        '$entity_oldvalue => <name>',
                        '$entity_autocomplete => <name>'
                    ),
                    'buttons'  => 'clear'
                ),
            );

            // Filter: month
            $months = array(4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3);
            $options = array();
            foreach ($months as $m) {
                $options[] = array(
                    'label' => General::strftime('%B', mktime(0, 0, 0, $m, 1, General::strftime('%Y')), true),
                    'option_value' => (string)$m
                );
            }
            $filters['month'] = array(
                'name' => 'month',
                'label' => $this->i18n('reports_filter_month'),
                'type' => 'dropdown',
                'options' => $options,
                'required' => true
            );

            // Filter: year
            $filters['year'] = array(
                'name' => 'year',
                'label' => $this->i18n('reports_filter_year'),
                'type' => 'dropdown',
                'options' => $this->getFieldOptions($registry, 'fin_year'),
                'required' => true
            );

            // Custom export button
            $filters['custom_export'] = array(
                'name' => 'custom_export',
                'type' => 'custom_filter',
                'custom_template'   => PH_MODULES_DIR . "reports/plugins/{$registry['report_type']['name']}/_filter_export_button.html"
            );

            return $filters;
        }

        public function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            // Hide the standart "Generate" button
            $registry->set('hide_generate_button', true, true);

            // Set default values for filters month and year
            if (empty($filters['generated'])) {
                $filters['month']['value'] = (string)intval(General::strftime('%m', strftime('previous month')));
                $current_month = intval(General::strftime('%m'));
                if (1 <= $current_month && $current_month <= 4) {
                    $filters['year']['value'] = (string)General::strftime('%Y', strftime('previous year'));
                } else {
                    $filters['year']['value'] = (string)General::strftime('%Y');
                }
            }

            return $filters;
        }
    }
?>
