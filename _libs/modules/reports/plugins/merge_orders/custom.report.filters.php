<?php
    class Custom_Report_Filters extends Report_Filters {
        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // Prepare an array containing description of all filters
            $filters = array();

            // DEFINE FILTER: For
            $filter = array (
                'custom_id'    => 'for',
                'name'         => 'for',
                'type'         => 'radio',
                'label'        => $this->i18n('reports_filter_for'),
                'help'         => $this->i18n('reports_filter_for'),
                'required'     => true,
                'on_change'    => 'processDependentFilters(this)',
                'options'      => array(
                    array(
                        'label' => $this->i18n('reports_filter_for_payment'),
                        'option_value' => 'payment'
                    ),
                    array(
                        'label' => $this->i18n('reports_filter_for_sale'),
                        'option_value' => 'sale'
                    )
                )
            );
            $filters['for'] = $filter;

            // DEFINE FILTER: Provider
            $customers_id_rights = self::getCustomersRightsIds($registry);

            $filter = array (
                'custom_id'    => 'provider',
                'name'         => 'provider',
                'type'         => 'custom_filter',
                'actual_type'  => 'autocompleter',
                'width'        => 244,
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'label'        => $this->i18n('reports_filter_provider'),
                'help'         => $this->i18n('reports_filter_provider_help'),
                'autocomplete' => array(
                    'type'         => 'customers',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'  => '[<code>] <name> <lastname>',
                    'search'       => array('<name>'),
                    'filters'      => array('<id>' => implode(',', $customers_id_rights)),
                    'clear'        => 1)
                );
            $filters['provider'] = $filter;

            // DEFINE FILTER: Customer
            $filter = array (
                'custom_id'    => 'invoice_to',
                'name'         => 'invoice_to',
                'type'         => 'autocompleter',
                'label'        => $this->i18n('reports_filter_invoice_to'),
                'help'         => $this->i18n('reports_filter_invoice_to_help'),
                'autocomplete' => array(
                    'type'         => 'customers',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'  => '[<code>] <name> <lastname>',
                    'search'       => array('<name>'),
                    'filters'      => array('<id>' => strval(INVOICE_TO_TYPES)),
                    'clear'        => 1,
                    'buttons_hide' => 'search'));
            $filters['invoice_to'] = $filter;

            // DEFINE FILTER: Date from
            $filter = array (
                'custom_id'          => 'date_from',
                'name'               => 'date_from',
                'custom_template'    => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'additional_filter'  => 'date_to',
                'type'               => 'custom_filter',
                'required'           => true,
                'width'              => 75,
                'first_filter_label' => $this->i18n('reports_filter_date_from'),
                'label'              => $this->i18n('reports_filter_date'),
                'help'               => $this->i18n('reports_filter_date_help'));
            $filters['date_from'] = $filter;

            // DEFINE FILTER: Date to
            $filter = array (
                'custom_id' => 'date_to',
                'name'      => 'date_to',
                'type'      => 'date',
                'width'     => 74,
                'label'     => $this->i18n('reports_filter_date_to'),
                'help'      => $this->i18n('reports_filter_date_to_help'));
            $filters['date_to'] = $filter;

            // CHECK THE REPORT SUPER USERS
            $report_super_users = preg_split('#\s*,\s*#', REPORT_SUPER_USERS);
            $report_super_users = array_filter($report_super_users);
            if (!in_array($registry['currentUser']->get('id'), $report_super_users)) {
                $filters['date_to']['readonly'] = true;

                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                $filters['date_from']['disallow_date_before'] = General::strftime('%Y-%m-%d', strtotime('-' . FILTERS_OLDEST_DATE_INTERVAL));
                $filters['date_from']['disallow_date_after'] = General::strftime('%Y-%m-%d', strtotime('+1 month -1 day', strtotime(date('Y-m-01'))));
            }

            // DEFINE FILTER: Orders types
            $options_orders_types = array();
            $orders_list = preg_split('#\s*,\s*#', ORDER_TYPES);
            $orders_list = array_filter($orders_list);
            if (!empty($orders_list)) {
                $sql = 'SELECT `parent_id` as option_value, `name` as label FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' WHERE `parent_id` IN ("' . implode('","', $orders_list) . '") ORDER BY `name` ASC';
                $options_orders_types = $registry['db']->GetAll($sql);
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'orders_types',
                'name'      => 'orders_types',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_filter_orders_types'),
                'help'      => $this->i18n('reports_filter_orders_types'),
                'options'   => $options_orders_types,
            );
            $filters['orders_types'] = $filter;

            //DEFINE ORDER NUM FILTER
            $filter = array (
                'custom_id'       => 'order_num',
                'name'            => 'order_num',
                'type'            => 'custom_filter',
                'actual_type'     => 'text',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'label'           => $this->i18n('reports_order_num'),
                'help'            => $this->i18n('reports_order_num_help')
            );
            $filters['order_num'] = $filter;

            //DEFINE CUSTOMER NUM FILTER
            $filter = array (
                'custom_id'       => 'customer_num',
                'name'            => 'customer_num',
                'type'            => 'custom_filter',
                'actual_type'     => 'text',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'label'           => $this->i18n('reports_customer_num'),
                'help'            => $this->i18n('reports_customer_num_help')
            );
            $filters['customer_num'] = $filter;

            //DEFINE OFFICE FILTER
            //get offices
            require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
            $filters_offices = array('model_lang' => $registry['lang'],
                                     'sanitize' => true,
                                     'where' => array('o.active = 1'),
                                     'sort'  => array('oi18n.name'));
            $offices = Offices::search($registry, $filters_offices);
            $options_offices = array();
            foreach($offices as $office) {
                $options_offices[] = array(
                    'label'        => $office->get('name'),
                    'option_value' => $office->get('id'));
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'office',
                'name'      => 'office',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_office'),
                'help'      => $this->i18n('reports_office_help'),
                'options'   => $options_offices,
            );
            $filters['office'] = $filter;

            //DEFINE SCRIPTS
            $filter = array (
                'custom_id'       => 'scripts',
                'name'            => 'scripts',
                'type'            => 'custom_filter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_scripts.html',
                'custom_scripts'  => array(
                    array(
                        'type' => 'inline',
                        'src'  => 'processDependentFilters($(\'for_1\'))'
                    )
                )
            );
            $filters['scripts'] = $filter;

            return $filters;
        }

        /**
         * Process some filters that depend on the request
         *
         * @param array $filters - the report filters
         * @return array - the report filters after processing
         */
        function processDependentFilters(&$filters) {
            $unset_filters = array();
            if (empty($filters['date_from']['value']) && empty($filters['date_to']['value'])) {
                $filters['date_from']['value'] = General::strftime('%Y-%m-%d', strtotime('-' . FILTERS_OLDEST_DATE_INTERVAL));
                $filters['date_to']['value'] = General::strftime('%Y-%m-%d', strtotime('+1 month -1 day', strtotime(date('Y-m-01'))));
            }

            foreach ($filters as $name => $filter) {
                // Process the filter from/to date
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }

        function getCustomersRightsIds(&$registry) {
            // prepare the customers rights ids
            $sql = 'SELECT `id` FROM ' . DB_TABLE_CUSTOMERS_TYPES . ' WHERE `active`=1 AND `deleted_by`=0';
            $customers_types = $registry['db']->GetCol($sql);
            $rights = $registry['currentUser'] ? $registry['currentUser']->get('rights') : array();
            $current_user_id = $registry['currentUser']->get('id');
            $module = 'customers';
            $action = 'search';

            $current_rights_where = array();
            foreach ($customers_types as $ct) {
                $current_right = isset($rights[$module . $ct][$action]) ? $rights[$module . $ct][$action] : '';
                if ($current_user_id && $current_right) {
                    if ($current_right == 'all') {
                        $current_rights_where[] = "c.type='" . $ct . "'";
                    } elseif ($current_right == 'mine') {
                        $current_rights_where[] = "(c.added_by=$current_user_id AND c.type='" . $ct . "')";
                    } elseif ($current_right == 'group') {
                        $user_groups = $registry['currentUser']->get('groups');
                        $user_departments = $registry['currentUser']->get('departments');
                        $current_rights_where[] = "((c.added_by=$current_user_id" .
                            (count($user_groups) ? ' OR c.`group` IN (' . implode(',', $user_groups) . ')' : '') .
                            (count($user_departments) ? ' OR c.department IN (' . implode(',', $user_departments) . ')' : '') .
                            ") AND c.type='" . $ct . "')";
                    } elseif ($current_right == 'none' || $current_right == '') {
                        $current_rights_where[] = '0';
                    }
                }
            }

            $sql = 'SELECT c.`id` FROM ' . DB_TABLE_CUSTOMERS . ' AS c WHERE c.`active`=1 AND c.`deleted_by`=0 AND c.`subtype`="normal"';
            if (!empty($current_rights_where)) {
                $sql .= ' AND (' . implode(' OR ', $current_rights_where) . ')';
            }
            return $registry['db']->GetCol($sql);
        }
    }
?>
