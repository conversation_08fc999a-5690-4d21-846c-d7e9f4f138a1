/*
 * Ajax functionallity to change the options of the statuses dropdown based on the selected document type
 */
function changeDocumentStatuses(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var selected_type = element.value;

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            var result = eval('(' + t.responseText + ')');

            // clear all dropdwons except the first one
            var dropdown_fields = $$('select[id^="status_doc_"]');

            var related_dropdown = '';
            for (var i = 0; i < dropdown_fields.length; i++) {
                if (dropdown_fields[i].id.match(/^status_doc_[0-9]*$/)) {
                    // get the index
                    var dropdown_index = dropdown_fields[i].id.replace(/^status_doc_/, '');
                    if (dropdown_index == '1') {
                        related_dropdown = dropdown_fields[i];
                    } else {
                        // find the row element and delete it
                        current_element = dropdown_fields[i].parentNode;
                        while (current_element.tagName != 'TR') {
                            current_element = current_element.parentNode;
                        }

                        current_element.parentNode.removeChild(current_element);
                    }
                }
            }

            var index_option = 0;
            related_dropdown.options.length = 0;
            if (!result.length) {
                related_dropdown.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(related_dropdown, 'missing_records');
            } else {
                removeClass(related_dropdown, 'missing_records');
                for (var j = 0; j < result.length; j++) {
                    related_dropdown.options[index_option] = new Option(result[j]['label'], result[j]['option_value'], false, false);
                    related_dropdown.options[index_option].innerHTML = result[j]['label'];
                    index_option++;
                }
            }
            toggleUndefined(related_dropdown);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_load_document_statuses&report_type=' + $('report_type').value + '&document_type=' + selected_type;

    new Ajax.Request(url, opt);
}
