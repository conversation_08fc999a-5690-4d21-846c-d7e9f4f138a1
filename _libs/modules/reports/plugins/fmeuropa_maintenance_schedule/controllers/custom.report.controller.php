<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    public function customCreateModel(&$settings) {
        // Prepare some basics
        $registry = &$this->registry;

        // Get the report name
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        // Check permissions to add document of this type
        $type            = $settings['create_type'];
        $type_permission = $this->checkActionPermissions(General::singular2Plural($settings['create_model']) . $type, 'add');

        // If there are no type permissions, that means that there is an error
        $error = !$type_permission;

        // Build the method name for creating the model
        $method = 'create' . ucfirst($settings['create_model']);

        // If there's no error and the method exists
        if (!$error && method_exists($this, $method)) {
            // Include the custom report query file
            require_once PH_MODULES_DIR . 'reports/plugins/' . $report . '/custom.report.query.php';

            // Build the report factory name
            $report_factory = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report)));

            // If the method prepareModelData exists for this report factory
            if (method_exists($report_factory, 'prepareModelData')) {
                // Prepare the properties of requested model
                $record = $report_factory::prepareModelData($registry);

                // If there are any prepared properties
                if (!empty($record)) {
                    /*
                     * Creates the requested model (in this case: the document model)
                     */
                    // Prepare the properties for the new document model
                    $properties = array('type' => $settings['create_type']);

                    // Include some required files fo creating the model
                    require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                    require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';

                    // Get the model type
                    $filters = array('where'    => array('dt.id = \'' . $properties['type'] . '\''),
                                     'sanitize' => true);
                    $model_type = Documents_Types::searchOne($registry, $filters);

                    // Set the default values from the type into the new model
                    foreach ($model_type->getAll() as $key => $value) {
                        if (preg_match('#^default_(.*)#', $key, $matches)) {
                            if(empty($properties[$matches[1]])) {
                                $properties[$matches[1]] = $value;
                            }
                        }
                    }

                    // Set the direction
                    $properties['direction'] = $model_type->get('direction');

                    // Set that there are no errors
                    $error = false;

                    // Build the new document
                    $model = new Document($registry, $properties);

                    // Allow editing
                    $registry->set('edit_all', true, true);

                    // Get the additional model vars
                    $model->getVars();
                    $vars = $model->get('vars');

                    // Go through each additional model var
                    foreach ($vars as $key => $var) {
                        // If the current additional var is prepared (i.e. we have value for it)
                        if (array_key_exists($var['name'], $record['additional'])) {
                            // Set the value for the current additional var
                            $vars[$key]['value'] = $record['additional'][$var['name']];
                            // Stop using the value for the current additional var
                            unset($record['additional'][$var['name']]);
                        } else if (empty($record['additional'])) {
                            // If we have used all the values for additional vars
                            // Stop the loop
                            break;
                        }
                    }

                    // Set the additional vars into the model
                    $model->set('vars', $vars, true);

                    // Set the basic vars into the model
                    foreach ($record['basic'] as $basic_var_name => $basic_var_value) {
                        $model->set($basic_var_name, $basic_var_value, true);
                    }

                    // Load the main documents i18n file
                    $registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');

                    // Validate and add the new document
                    if ($model->save()) {
                        // If there is a model and the model has an ID
                        if ($model && $model->get('id')) {
                            // Get the new model from the database
                            $filters = array(
                                'where'                  => array(
                                    'd.id = \'' . $model->get('id') . '\'',
                                 ),
                                'model_lang'             => $model->get('model_lang'),
                                'skip_assignments'       => true,
                                'skip_permissions_check' => true);
                            $new_model = Documents::searchOne($registry, $filters);
                            // Load the additional vars of the new document
                            $get_old_vars = $registry->get('get_old_vars');
                            $registry->set('get_old_vars', true, true);
                            $new_model->getVars();

                            // Get the old model (i.e. build it)
                            $old_model = new Document($registry);
                            $old_model->set('type', $model->get('type'), true);
                            $old_model->getVars();
                            $old_model->sanitize();

                            $registry->set('get_old_vars', $get_old_vars, true);

                            // Include required files for writing history
                            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

                            // Write history for: add
                            $audit_parent = Documents_History::saveData(
                                $registry,
                                array(
                                    'model'       => $model,
                                    'action_type' => 'add',
                                    'new_model'   => $new_model,
                                    'old_model'   => $old_model));

                            // Redirect to model and display success message
                            $registry['messages']->setMessage($this->i18n('message_documents_add_success', array($new_model->getModelTypeName())), '', -1);
                            $registry['messages']->insertInSession($registry);
                            $this->redirect('documents', 'edit', array('edit' => $model->get('id')));

                        } else {
                            $error = true;
                        }
                    } else {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            } else {
                $error = true;
            }
        } else {
            $error = true;
        }

        // If there's an error
        if ($error) {
            // Redirect back to the report and display error message
            $registry['messages']->setError($this->i18n('error_reports_create_performed_activity_protocol'), '', -1);
            $registry['messages']->insertInSession($registry);
            $this->redirect($this->module, '', array('report_type' => $report), '');
        }
    }
}

?>
