<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td nowrap="nowrap"><strong>{#reports_lbl_agreement#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_client#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_type#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_agreement_yes_no#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_damage_number#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_policy_num#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_send_signal#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_claimed_amount#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_amount_inheritor#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_amount_lawyer#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_liquidation_expense#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_agreement_sum#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_case_law#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_deadline_date#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_deadline_sum#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_payment_date#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_payment_sum#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_paid_by_now#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_lbl_left_to_pay#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results item=agreement name=agr}
        {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
        {foreach from=$agreement.deadlines item=result name=ddln}
          <tr>
            {if $smarty.foreach.ddln.first}
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.full_num|escape|default:"&nbsp;"} / {$agreement.date|date_format:#date_short#|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.customer_name|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.type|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.agreement_yes_no|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.damage_number|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.policy_num|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" style="vertical-align: middle; mso-number-format: '\@';">
                {$agreement.send_signal|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.claimed_amount|string_format:"%.2f"|default:"0.00"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.amount_inheritor|string_format:"%.2f"|default:"0.00"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.amount_lawyer|string_format:"%.2f"|default:"0.00"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.liquidation_expense|string_format:"%.2f"|default:"0.00"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.agreement_sum|string_format:"%.2f"|default:"0.00"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.case_law|string_format:"%.2f"|default:"0.00"}
              </td>
            {/if}
            <td nowrap="nowrap" style="mso-number-format:'dd\.mm\.yyyy';" align="center">
              {$result.deadline_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td nowrap="nowrap" align="right" style="mso-number-format:'0\.00';">
              {$result.deadline_value|string_format:"%.2f"|default:"0.00"}
            </td>
            <td nowrap="nowrap" style="mso-number-format:'dd\.mm\.yyyy';" align="center">
              {$result.payment_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td nowrap="nowrap" align="right" style="mso-number-format:'0\.00';">
              {$result.payment_value|string_format:"%.2f"|default:"0.00"}
            </td>
            {if $smarty.foreach.ddln.first}
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.paid_by_now|string_format:"%.2f"|default:"0.00"}
              </td>
              <td nowrap="nowrap" rowspan="{$agreement.rowspan}" align="right" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$agreement.remain_to_pay|string_format:"%.2f"|default:"0.00"}
              </td>
            {/if}
          </tr>
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="19">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
      <tr>
        <td colspan="7" align="right" style="background-color: #98BCFF;">
          <strong>{#reports_total#}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_claimed|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_amount_inheritor|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_amount_lawyer|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_liquidation_expense|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_agreement|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_case_law|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="background-color: #98BCFF;">&nbsp;</td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_deadline_value|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="background-color: #98BCFF;">&nbsp;</td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_payment_value|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_paid|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
        <td align="right" style="mso-number-format:'0\.00'; background-color: #98BCFF;">
          <strong>{$reports_additional_options.total_left_to_pay|string_format:"%.2f"|default:"0.00"}</strong>
        </td>
      </tr>
    </table>
  </body>
</html>
