<?php

class Zographic_Activity_Analysis extends Reports
{
    /**
     * @var array - dummy results data
     */
    private static $data = array(
        'load' => array(
            '2018-09-24' => 20,
            '2018-09-25' => 24.40,
            '2018-09-26' => 65,
            '2018-09-27' => 45.18,
            '2018-09-28' => 19.20,
            '2018-09-29' => 0,
            '2018-09-30' => 0,
            '2018-10-01' => 17.40,
            '2018-10-02' => 24.10,
            '2018-10-03' => 19.73,
            '2018-10-04' => 22.40,
            '2018-10-05' => 16,
            '2018-10-07' => 0,
        ),
        'load_average' => 23,
        'requests' => array(
            'finished' => 3,
            'pending' => 2,
            'total' => 5,
        )
    );


    public static function buildQuery(&$registry, $filters = array())
    {
        /**
         * FINAL RESULTS
         */
        $final_results = self::$data;

        if (empty($filters['period_from']) || empty($filters['period_to'])) {
            $registry['messages']->setError($registry['translater']->translate('reports_error_required_filters'));
        } else {
            $final_results['additional_options']['charts_param'] = $filters['report_type'];
            $export_file_type = 'png';
            if ($registry->get('action') == 'export') {
                $final_results['additional_options']['placeholders'] = array();
            }

            $chart = new Chart($registry, 'column', $filters['report_type'], 1, true);

            $chart_params = array();
            $chart_params['chart'] = array(
                'width' => MONTH_CHART_WIDTH,
                'height' => MONTH_CHART_HEIGHT,
                'style' => array(
                    'marginTop' => '15px',
                    'marginBottom' => '15px'
                )
            );
            $chart_params['title'] = array(
                'text' => $registry['translater']->translate('reports_activity_load')
            );
            $chart_params['xAxis'] = array(
                'title' => array(
                    'text' => $registry['translater']->translate('reports_activity_xaxis')
                ),
                'categories' => array()
            );
            $chart_params['yAxis'] = array(
                'title' => array(
                    'text' => $registry['translater']->translate('reports_activity_yaxis')
                )
            );
            $chart_params['series'] = array(
                array(
                    'showInLegend' => false,
                    'data' => array()
                )
            );

            $begin = new DateTime($filters['period_from']);
            $end = new DateTime($filters['period_to']);

            for ($i = $begin; $i <= $end; $i->modify('+1 day')) {
                $date = $i->format("Y-m-d");
                $load = isset(self::$data['load'][$date]) ? self::$data['load'][$date] : 0;
                $chart_params['series'][0]['data'][] = $load;
                $chart_params['xAxis']['categories'][] = $i->format("m.d");
            }

            if (isset($final_results['additional_options']['placeholders'])) {
                $exported_file_monthly = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_DIR, $chart->get('image_export_name'), $export_file_type);
                if (file_exists($exported_file_monthly)) {
                    $exported_file_url = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_URL, $chart->get('image_export_name'), $export_file_type);
                    $exported_file_dir = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_DIR, $chart->get('image_export_name'), $export_file_type);
                    $chart->set('exported_file_url', $exported_file_url, true);
                    $chart->set('exported_file_dir', $exported_file_dir, true);
                    $final_results['additional_options']['placeholders']['month_chart'] = array(
                        'name' => 'month_chart',
                        'type' => 'chart',
                        'properties' => array(
                            'url' => $exported_file_url,
                            'dir' => $exported_file_dir
                        )
                    );
                }
            }
            if ($chart->prepareChart($chart_params)) {
                $final_results['additional_options']['chart'] = $chart;
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}

?>
