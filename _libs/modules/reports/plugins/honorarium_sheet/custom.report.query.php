<?php
    Class Honorarium_Sheet Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            // Prepare an array for the final results
            $final_results = array();

            // Set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                // Default model language is the interface language
                $model_lang = $registry['lang'];
            }

            // Get the database object
            $db = $registry['db'];

            // Prepare an array of fields for the request
            $request_fields = array(
                FIELD_DOC_TRANSLATION_REQUEST_ORDER_NUM,
                FIELD_DOC_TRANSLATION_REQUEST_ORDER_ID,
                FIELD_DOC_TRANSLATION_REQUEST_LANGUAGE,
                FIELD_DOC_TRANSLATION_REQUEST_LANGUAGE_ID,
                FIELD_DOC_TRANSLATION_REQUEST_COMPLEXITY,
                FIELD_DOC_TRANSLATION_REQUEST_FINAL_PRICE);

            // Check if a sheets count and price per sheet are used
            $use_sheets = false;
            if (defined('FIELD_DOC_TRANSLATION_REQUEST_ACTUAL_SHEETS') && FIELD_DOC_TRANSLATION_REQUEST_ACTUAL_SHEETS != ''
                    && defined('FIELD_DOC_TRANSLATION_REQUEST_PRICE_PER_SHEET') && FIELD_DOC_TRANSLATION_REQUEST_PRICE_PER_SHEET != '') {
                $use_sheets = true;
                $request_fields[] = FIELD_DOC_TRANSLATION_REQUEST_ACTUAL_SHEETS;
                $request_fields[] = FIELD_DOC_TRANSLATION_REQUEST_PRICE_PER_SHEET;
            }

            // Get the fields from the database
            $query = "
                SELECT `name`, `id`
                  FROM `" . DB_TABLE_FIELDS_META . "`
                  WHERE `model`      = 'Document'
                    AND `model_type` = '" . DOCUMENT_TYPE_TRANSLATION_REQUES . "'
                    AND `name`       IN (
                        '" . implode("', '", $request_fields) . "')";
            $fields_request = $db->getAssoc($query);

            // If any of the fields is missing from the database
            if (count($fields_request) != count($request_fields)) {
                // Show error
                $registry['messages']->setError($registry['translater']->translate('error_reports_missing_settings_fields'));
            } else {
                // Apply filters
                $where = array();
                if (!empty($filters['period_from'])) {
                    $where[] = "'{$filters['period_from']}' <= DATE(`d`.`added`)";
                }
                if (!empty($filters['period_to'])) {
                    $where[] = "DATE(`d`.`added`) <= '{$filters['period_to']}'";
                }
                if (!empty($filters['translator'])) {
                    $where[] = "`u`.`employee` = '{$filters['translator']}'";
                }
                if (!empty($filters['paid_honorarium']) && is_array($filters['paid_honorarium']) && count($filters['paid_honorarium']) == 1) {
                    if (in_array('paid', $filters['paid_honorarium'])) {
                        $where[] = "`fer`.`payment_status` IN ('paid', 'partial')";
                    } else if (in_array('unpaid', $filters['paid_honorarium'])) {
                        $where[] = "(`fer`.`payment_status` IS NULL OR `fer`.`payment_status` = 'unpaid')";
                    }
                }
                if (defined('REQUEST_ALLOWED_STATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', REQUEST_ALLOWED_STATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $where[] = "`d`.`status` IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                if (defined('REQUEST_ALLOWED_SUBSTATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', REQUEST_ALLOWED_SUBSTATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $where[] = "`d`.`substatus` IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                if (defined('REQUEST_DISALLOWED_STATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', REQUEST_DISALLOWED_STATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $where[] = "`d`.`status` NOT IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                if (defined('REQUEST_DISALLOWED_SUBSTATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', REQUEST_DISALLOWED_SUBSTATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $where[] = "`d`.`substatus` NOT IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                $order_filters = array();
                if (defined('ORDER_ALLOWED_STATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', ORDER_ALLOWED_STATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $order_filters[] = "`d2`.`status` IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                if (defined('ORDER_ALLOWED_SUBSTATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', ORDER_ALLOWED_SUBSTATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $order_filters[] = "`d2`.`substatus` IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                if (defined('ORDER_DISALLOWED_STATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', ORDER_DISALLOWED_STATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $order_filters[] = "`d2`.`status` NOT IN ('" . implode("', '", $statuses) . "')";
                    }
                }
                if (defined('ORDER_DISALLOWED_SUBSTATUSES')) {
                    $statuses = preg_split('/\s*,\s*/', str_replace(' ', '', ORDER_DISALLOWED_SUBSTATUSES));
                    if (count(array_filter($statuses)) > 0) {
                        $order_filters[] = "`d2`.`substatus` NOT IN ('" . implode("', '", $statuses) . "')";
                    }
                }

                // Get the results
                $query = "
                    SELECT `tmp`.*,
                        GROUP_CONCAT(`tmp_translator_user_id` SEPARATOR '|')       AS `translator_user_id`,
                        GROUP_CONCAT(`tmp_translator_customer_name` SEPARATOR '|') AS `translator_customer_name` FROM (
                      SELECT `d`.`id`                                              AS `request_id`,
                          `d`.`full_num`                                           AS `request_num`,
                          `dc1`.`value`                                            AS `request_order_num`,
                          `dc1_1`.`value`                                          AS `request_order_id`,
                          `u`.`id`                                                 AS `tmp_translator_user_id`,
                          `u`.`employee`                                           AS `translator_customer_id`,
                          TRIM(CONCAT(`ci18n1`.`name`, ' ', `ci18n1`.`lastname`))  AS `tmp_translator_customer_name`,
                          `d`.`customer`                                           AS `client_id`,
                          TRIM(CONCAT(`ci18n`.`name`, ' ', `ci18n`.`lastname`))    AS `client_name`,
                          `dc2`.`value`                                            AS `request_language`,
                          `dc2_1`.`value`                                          AS `request_language_id`,
                          `fo3`.`label`                                            AS `request_complexity`,
                          " . ($use_sheets ? "`dc4`.`value`" : "''") . "    AS `request_price_per_sheet`,
                          " . ($use_sheets ? "`dc5`.`value`" : "''") . "    AS `request_actual_sheets`,
                          `dc6`.`value`                                            AS `request_final_price`,
                          `fer`.`id`                                               AS `honorarium_sheet_id`,
                          `fer`.`num`                                              AS `honorarium_sheet_num`,
                          ''                                                       AS `honorarium_sheet_amount_paid`,
                          `dc6`.`value`                                            AS `honorarium_sheet_amount_left`
                        FROM `" . DB_TABLE_DOCUMENTS . "` AS `d`
                        LEFT JOIN `" . DB_TABLE_FINANCE_REASONS_RELATIVES . "` AS `frr`
                          ON (`frr`.`link_to`              = `d`.`id`
                            AND `frr`.`link_to_model_name` = 'Document'
                            AND `frr`.`parent_model_name`  = 'Finance_Expenses_Reason'
                            AND `frr`.`parent_id` IN (
                              SELECT `id` FROM `" . DB_TABLE_FINANCE_EXPENSES_REASONS . "`
                                WHERE `type`        = '" . FINANCE_EXPENSES_TYPE_HONORARIUM_SHEET . "'
                                  AND `active`      = '1'
                                  AND `annulled_by` = '0'))
                        LEFT JOIN `" . DB_TABLE_FINANCE_EXPENSES_REASONS . "` AS `fer`
                          ON (`fer`.`id` = `frr`.`parent_id`)
                        LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc1`
                          ON (`dc1`.`model_id` = `d`.`id`
                            AND `dc1`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_ORDER_NUM]}')
                        JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc1_1`
                          ON (`dc1_1`.`model_id` = `d`.`id`
                            AND `dc1_1`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_ORDER_ID]}'
                            AND `dc1_1`.`value`  != '')
                        JOIN `" . DB_TABLE_DOCUMENTS . "` AS `d2`
                          ON (`d2`.`id`           = `dc1_1`.`value`
                            AND `d2`.`active`     = '1'
                            AND `d2`.`deleted_by` = '0'" .
                            (!empty($order_filters) ? "
                            AND " . implode("
                            AND ", $order_filters) : "") . ")
                        LEFT JOIN `" . DB_TABLE_DOCUMENTS_ASSIGNMENTS . "` AS `da`
                          ON (`da`.`parent_id`          = `d`.`id`
                            AND `da`.`assignments_type` = '" . TRANSLATOR_USER_ASSIGNMENT_TYPE . "')
                        JOIN `" . DB_TABLE_USERS . "` AS `u`
                          ON (`u`.`id`           = `da`.`assigned_to`
                            AND `u`.`active`     = '1'
                            AND `u`.`deleted_by` = '0'
                            AND `u`.`employee`   != '')
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci18n`
                          ON (`ci18n`.`parent_id` = `d`.`customer`
                            AND `ci18n`.`lang`    = '{$model_lang}')
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci18n1`
                          ON (`ci18n1`.`parent_id` = `u`.`employee`
                            AND `ci18n1`.`lang`    = '{$model_lang}')
                        LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc2`
                          ON (`dc2`.`model_id` = `d`.`id`
                            AND `dc2`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_LANGUAGE]}')
                        LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc2_1`
                          ON (`dc2_1`.`model_id` = `d`.`id`
                            AND `dc2_1`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_LANGUAGE_ID]}')
                        LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc3`
                          ON (`dc3`.`model_id` = `d`.`id`
                            AND `dc3`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_COMPLEXITY]}')
                        LEFT JOIN `" . DB_TABLE_FIELDS_OPTIONS . "` AS `fo3`
                          ON (`fo3`.`parent_name`     = '" . FIELD_DOC_TRANSLATION_REQUEST_COMPLEXITY . "'
                            AND `fo3`.`option_value`  = `dc3`.`value`
                            AND `fo3`.`active_option` = '1'
                            AND `fo3`.`lang`          = '{$model_lang}')" .
                        ($use_sheets ? "
                        JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc4`
                          ON (`dc4`.`model_id` = `d`.`id`
                            AND `dc4`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_PRICE_PER_SHEET]}'
                            AND `dc4`.`value`  != '')" : "") .
                        ($use_sheets ? "
                        JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc5`
                          ON (`dc5`.`model_id` = `d`.`id`
                            AND `dc5`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_ACTUAL_SHEETS]}'
                            AND `dc5`.`value`  != '')" : "") . "
                        JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc6`
                          ON (`dc6`.`model_id` = `d`.`id`
                            AND `dc6`.`var_id` = '{$fields_request[FIELD_DOC_TRANSLATION_REQUEST_FINAL_PRICE]}'
                            AND `dc6`.`value`  != '')
                        WHERE `d`.`type`       = '3'
                          AND `d`.`active`     = '1'
                          AND `d`.`deleted_by` = '0'" .
                          (!empty($where) ? "
                          AND " . implode("
                          AND ", $where) : '') . "
                        ORDER BY `d`.`id` ASC, CASE WHEN `fer`.`id` IS NULL THEN 1 END ASC
                      ) AS `tmp`
                    GROUP BY `request_id`
                    ORDER BY `honorarium_sheet_id` ASC, `request_order_id` ASC, `request_id` ASC";
                $records = $db->getAssoc($query);

                // Prepare to collect the honorarium sheets data
                $hs = array();
                foreach ($records as $request_id => $record) {
                    if (!empty($record['honorarium_sheet_id'])) {
                        if (!isset($hs[$record['honorarium_sheet_id']])) {
                            $hs[$record['honorarium_sheet_id']] = array(
                                'orders'         => array(),
                                'requests_count' => 0,
                                'total_with_vat' => 0);
                        }
                        if (!isset($hs[$record['honorarium_sheet_id']]['orders'][$record['request_order_id']])) {
                            $hs[$record['honorarium_sheet_id']]['orders'][$record['request_order_id']] = array(
                                'requests_count' => 0,
                                'amount_paid'    => 0,
                                'amount_left'    => 0,
                                'amount_total'   => 0);
                        }
                        $hs[$record['honorarium_sheet_id']]['requests_count']++;
                        $hs[$record['honorarium_sheet_id']]['orders'][$record['request_order_id']]['requests_count']++;
                        $hs[$record['honorarium_sheet_id']]['orders'][$record['request_order_id']]['amount_total'] += $record['request_final_price'];
                    }
                }

                // If there are any honorarium sheets ids
                if (!empty($hs)) {
                    // Get the honorarium sheets models
                    require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
                    $filters_honorarium_sheets = array(
                        'model_lang' => $model_lang,
                        'sanitize'   => false,
                        'where'      => array('fer.id IN (\'' . implode('\', \'', array_keys($hs)) . '\')'));
                    $honorarium_sheets = Finance_Expenses_Reasons::search($registry, $filters_honorarium_sheets);

                    // If there are any honorarium sheets models
                    if (!empty($honorarium_sheets)) {
                        // Go through each honorarium sheet model
                        foreach ($honorarium_sheets as $honorarium_sheet) {
                            // Get the honorarium sheet id
                            $honorarium_sheet_id = $honorarium_sheet->get('id');

                            // Get the total amount to pay
                            $total_with_vat = $honorarium_sheet->get('total_with_vat');
                            $hs[$honorarium_sheet_id]['total_with_vat'] = $total_with_vat;

                            // If the honorarium sheet is paid
                            if ($honorarium_sheet->get('payment_status') == 'paid') {
                                // For each order of the current honorarium sheet
                                foreach ($hs[$honorarium_sheet_id]['orders'] as $hs_order_id => $hs_order) {
                                    // Set how much is paid and how much is left to be paid for the current order
                                    $hs[$honorarium_sheet_id]['orders'][$hs_order_id]['amount_paid'] = $hs_order['amount_total'];
                                    $hs[$honorarium_sheet_id]['orders'][$hs_order_id]['amount_left'] = 0;
                                }
                                // Set what amount is paid (it should be the total amount to pay)
//                                 $records[$honorarium_sheets_requests[$honorarium_sheet_id]]['honorarium_sheet_amount_paid'] = $paid_amount;
                                // Set what amount is left to pay
//                                 $records[$honorarium_sheets_requests[$honorarium_sheet_id]]['honorarium_sheet_amount_left'] = 0;
                            } else {
                                // If the total amount of the honorarium sheet is zero (0)
                                if ($total_with_vat == 0) {
                                    // Then the paid percentage is 100%
                                    $hs_paid_percentage = 100;
                                } else {
                                    // Get the paid amount for this honororium sheet
                                    $paid_amount = $honorarium_sheet->getPaidAmount();

                                    // Calculate the paid percentage
                                    $hs_paid_percentage = (100 / $total_with_vat) * $paid_amount;
                                }

                                // For each order of the current honorarium sheet
                                foreach ($hs[$honorarium_sheet_id]['orders'] as $hs_order_id => $hs_order) {
                                    // Set how much is paid and how much is left to be paid for the current order
                                    $hs[$honorarium_sheet_id]['orders'][$hs_order_id]['amount_paid'] = ($hs_order['amount_total'] / 100) * $hs_paid_percentage;
                                    $hs[$honorarium_sheet_id]['orders'][$hs_order_id]['amount_left'] = $hs_order['amount_total'] - $hs[$honorarium_sheet_id]['orders'][$hs_order_id]['amount_paid'];
                                }
//                                 // Set what amount is left to pay
//                                 $records[$honorarium_sheets_requests[$honorarium_sheet_id]]['honorarium_sheet_amount_left'] = $total_with_vat - $paid_amount;

//                                 // This is an exception, but if the total amount to pay is zero (0)
//                                 // this check is made for calcluations protection when calculating what percentage from the total amount to pay is paid
//                                 if ($total_with_vat == 0) {
//                                     // Set that the paid amount is zero (0)
//                                     $paid_amount = 0;
//                                 } else {
//                                     // Calculate what percentage from the total amount to pay is paid
//                                     $paid_amount = (100 / $total_with_vat) * $paid_amount;
//                                     if (round($paid_amount) != $paid_amount) {
//                                         $paid_amount = sprintf('%.2F', $paid_amount);
//                                     }
//                                     $paid_amount .= '%';
//                                 }
//                                 // Set the paid amount
//                                 $records[$honorarium_sheets_requests[$honorarium_sheet_id]]['honorarium_sheet_amount_paid'] = $paid_amount;
                            }
                        }
                    }
                }

                // Encode some data, to be used later when adding a honorarium sheets
                foreach ($records as $request_id => $record) {
                    $records[$request_id]['encoded_data'] = base64_encode(json_encode(array(
                        'request_id'               => $request_id,
                        'request_num'              => $record['request_num'],
                        'request_language'         => $record['request_language'],
                        'request_language_id'      => $record['request_language_id'],
                        'request_actual_sheets'    => $record['request_actual_sheets'],
                        'request_price_per_sheet'  => $record['request_price_per_sheet'],
                        'request_final_price'      => $record['request_final_price'],
                        'translator_customer_id'   => $record['translator_customer_id'],
                        'translator_customer_name' => $record['translator_customer_name'],
                        'request_order_id'         => $record['request_order_id'],
                        'request_order_num'        => $record['request_order_num'])));
                }

                // Set the final results
                $final_results = $records;

                // Set the additional data for honorarium sheets
                $final_results['additional_options']['hs'] = $hs;
            }

            // Query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                // No pagination required - return only the models
                $results = $final_results;
            }

            return $results;
        }
    }
?>
