{assign var='total_colspan' value=11}
{if $reports_additional_options.has_field_region_name}
  {assign var='total_colspan' value=$total_colspan+1}
{/if}
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="25">{#num#|escape}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="120">{#reports_th_num_date#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="300">{#reports_th_client#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;">{#reports_th_object_code#}</td>
        {if $reports_additional_options.has_field_region_name}
          <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="80">{#reports_th_region_name#}</td>
        {/if}
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="200">{#reports_th_service#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="300">{#reports_th_description_service#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="300">{#reports_th_comments#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="400" colspan="2">{#reports_th_history#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="150">{#reports_th_status#}</td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="100">{#reports_th_total_time#}</td>
      </tr>
      {counter start=0 name='item_counter' print=false}
      {foreach from=$reports_results item='result'}
        <tr>
          <td nowrap="nowrap" width="25" style="vertical-align: middle;">
            {counter name='item_counter' print=true}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 120px;">
            {strip}
              {$result.document_num|escape|default:"&nbsp;"}
              {if $result.date_request}
                /<br />
                {$result.date_request|escape|default:"&nbsp;"}
              {/if}
            {/strip}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 300px;">
            {strip}
              {$result.customer_name|escape|default:"&nbsp;"}
              {if $result.object_id}
                /<br />
                {$result.object_name|escape|default:"&nbsp;"}
              {/if}
            {/strip}
          </td>
          <td style="text-align: center; vertical-align: middle; mso-number-format:\@;">
            {$result.object_code|escape|default:"&nbsp;"}
          </td>
          {if $reports_additional_options.has_field_region_name}
            <td style="text-align: left; vertical-align: middle; max-width: 80px;">
              {$result.region_name|escape|default:"&nbsp;"}
            </td>
          {/if}
          <td style="text-align: left; vertical-align: middle; max-width: 200px;">
            {$result.service_name|escape|default:"&nbsp;"}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 300px;">
            {$result.description_service|escape|default:"&nbsp;"}
          </td>
          <td style="text-align: left; max-width: 300px;">
            {foreach from=$result.comments item='document_comment' name='document_comments'}
              <b>{$document_comment.user_name}</b>, {$document_comment.added|date_format:#date_mid#}{if $document_comment.subject} <b>{$document_comment.subject}</b>{/if}: {$document_comment.content}
              {if is_array($result.comments) && count($result.comments) gt 1 && !$smarty.foreach.document_comments.last}
                <hr>
              {/if}
            {/foreach}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 150px;">
            {foreach from=$result.history item='history' name='history'}
              {$history.status|escape|default:"&nbsp;"}
              {if !$smarty.foreach.history.last}
                <br />
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 250px;">
            {foreach from=$result.history item='history' name='history'}
              {$history.time|escape|default:"&nbsp;"}
              {if !$smarty.foreach.history.last}
                <br />
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 150px;">
            {if $result.document_substatus_name}
              {$result.document_substatus_name|escape|default:"&nbsp;"}
            {else}
              {$result.document_status_name|escape|default:"&nbsp;"}
            {/if}
          </td>
          <td style="text-align: left; vertical-align: middle; max-width: 100px;">
            {$result.history_total_duration|escape|default:"&nbsp;"}
          </td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="{$total_colspan}" style="vertical-align: middle;"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>