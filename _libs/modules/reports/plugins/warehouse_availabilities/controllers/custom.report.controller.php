<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'ajax_prepare_custom_search':
                $this->_prepareCustomSearch();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /*
     * Function to prepare the custom search link and the session content for the search
     */
    public function _prepareCustomSearch() {
        $search_data = $this->registry['request']->get('search_data');
        $search_type = $this->registry['request']->get('search_type');

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];
        $this->report = $report;

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->report_lang_file = $i18n_file;
        $this->registry['translater']->loadFile($this->report_lang_file);
        Reports::getReportSettings($this->registry, $report);

        // decode the data
        $search_data = json_decode(base64_decode($search_data));

        $redirect_url = '';

        $search_filter_index = 1;
        $search_filters = array(
            'hidden_type'      => ARTICLES_TRANSFER_PROTOCOL,
            'search_fields'    => array(),
            'compare_options'  => array(),
            'values'           => array(),
            'logical_operator' => array(),
            'date_period'      => array(),
            'sort'             => array('fwd.added'),
            'order'            => array('DESC'),
            'display'          => 10
        );

        $search_filters['search_fields'][0] = 'fwd.type';
        $search_filters['compare_options'][0] = "= '%s'";
        $search_filters['values'][0] = ARTICLES_TRANSFER_PROTOCOL;
        $search_filters['logical_operator'][0] = 'AND';
        $search_filters['date_period'][0] = '';

        foreach ($search_data as $ser_dat) {
            $search_filters['search_fields'][$search_filter_index] = 'fwd.id';
            $search_filters['compare_options'][$search_filter_index] = "= '%s'";
            $search_filters['values'][$search_filter_index] = $ser_dat;
            $search_filters['logical_operator'][$search_filter_index] = 'OR';
            $search_filters['date_period'][$search_filter_index] = '';
            $search_filter_index++;
        }

        $this->registry['session']->set('search_finance_warehouses_document', $search_filters, '', true);
        $redirect_url = sprintf('%s://%s%sindex.php?%s=finance&%s=warehouses_documents&warehouses_documents=search',
                                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'], $this->registry['controller_param']);

        echo($redirect_url);
        exit;
    }
}

?>
