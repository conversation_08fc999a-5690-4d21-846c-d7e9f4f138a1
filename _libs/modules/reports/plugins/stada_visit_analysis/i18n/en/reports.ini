# Filters
filter_employee = Representative
filter_date = Date
filter_city = City
filter_segmentation = Segmentation
filter_results_by = Results
filter_results_by_customer = Detailed
filter_results_by_segmentation = Summarized

# Table headers
th_customer_code = Code
th_customer_name = Object
th_chain_name = Chain
th_segmentation = Segmentation
th_loyalty_class = Client type
th_ims_bricks = IMS Bricks
th_region_name = Region
th_city = City
th_address = Address
th_phone = Phone
th_visits_number = Target visits
th_customer_visits_count = Number of visits
th_visit_full_num = No
th_visit_date = Date
th_employee_name = Representative
th_visit_notes = Comment
th_activity_type = Activity type
th_photo = Photo
th_position = Positioning
th_note_sr = Note SR
th_manager_comment = Comment Manager
th_total = Total