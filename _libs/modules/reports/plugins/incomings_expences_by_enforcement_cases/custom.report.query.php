<?php
    Class Incomings_Expences_By_Enforcement_Cases Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_records = array(
                'table1' => array(),
                'table2' => array()
            );

            //DEFINE CATEGORY FILTER
            $query = 'SELECT `option_value`, `label` FROM ' . DB_TABLE_FIELDS_OPTIONS . "\n" .
                     'WHERE `parent_name`="' . CATEGORY_OPTIONS_FIELD . '" AND `lang`="' . $registry['lang'] . '" ORDER BY position ASC';
            $available_categories_category = $registry['db']->GetAll($query);

            foreach ($available_categories_category as $acc) {
                $final_records['table1'][$acc['option_value']] = array(
                    'type'                  => $acc['label'],
                    'total_owed_due'        => 0,
                    'total_projects'        => 0,
                    'included_projects'     => array(),
                    'collected_sums'        => 0,
                    'collected_sums_period' => 0,
                    'expenses_period'       => 0
                );
            }
            $final_records['table1']['total'] = array(
                'type'                  => General::mb_ucfirst($registry['translater']->translate('all')),
                'total_owed_due'        => 0,
                'total_projects'        => 0,
                'collected_sums'        => 0,
                'collected_sums_period' => 0,
                'expenses_period'       => 0
            );
            $final_records['table2']['total'] = array(
                'type'                  => $registry['translater']->translate('reports_total_chsi'),
                'total_owed_due'        => 0,
                'total_projects'        => 0,
                'collected_sums'        => 0,
                'collected_sums_period' => 0,
                'expenses_period'       => 0
            );
            $no_chsi = array(
                'type'                  => '-',
                'total_owed_due'        => 0,
                'total_projects'        => 0,
                'included_projects'     => array(),
                'collected_sums'        => 0,
                'collected_sums_period' => 0,
                'expenses_period'       => 0
            );

            $available_additional_vars = array(CASE_CATEGORY, TYPE_PAYMENT, DATE_PAYMENT, CASE_TOTAL, CASE_PRICE, PAYMENT_CURRENCY, CASE_COURT_JUDGE, CUSTOMER_TYPE_COMPANY);

            //sql to take the ids of the needed additional vars
            $sql_for_project_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Project" AND fm.name IN ("' . implode('","', $available_additional_vars) . '") AND fm.model_type="' . ENFORCEMENT_CASE_TYPE . '"';
            $project_add_vars = $registry['db']->GetAll($sql_for_project_add_vars);

            $case_category_id = '';
            $case_total_id = '';
            $type_payment_id = '';
            $date_payment_id = '';
            $case_price_id = '';
            $payment_currency_id = '';
            $case_court_judge_id = '';
            $case_company_id = '';

            foreach ($project_add_vars as $proj_var) {
                if ($proj_var['name'] == CASE_CATEGORY) {
                    $case_category_id = $proj_var['id'];
                } elseif ($proj_var['name'] == TYPE_PAYMENT) {
                    $type_payment_id = $proj_var['id'];
                } elseif ($proj_var['name'] == DATE_PAYMENT) {
                    $date_payment_id = $proj_var['id'];
                } elseif ($proj_var['name'] == CASE_TOTAL) {
                    $case_total_id = $proj_var['id'];
                } elseif ($proj_var['name'] == CASE_PRICE) {
                    $case_price_id = $proj_var['id'];
                } elseif ($proj_var['name'] == PAYMENT_CURRENCY) {
                    $payment_currency_id = $proj_var['id'];
                } elseif ($proj_var['name'] == CASE_COURT_JUDGE) {
                    $case_court_judge_id = $proj_var['id'];
                } elseif ($proj_var['name'] == CUSTOMER_TYPE_COMPANY) {
                    $case_company_id = $proj_var['id'];
                }
            }

            //sql to take the full required data for the projects
            $sql_for_projects['select'] = 'SELECT p.id, p.customer, p_cstm_total.value as total, p_cstm_court_judge.value as court_judge, ' . "\n" .
                                          '  CONCAT(p_cstm_court_judge_name.name, " ", p_cstm_court_judge_name.lastname) as court_judge_name, p_cstm_case_category.value as category, ' . "\n" .
                                          '  p_cstm_case_payment.value as payment_direction, p_cstm_case_price.value as case_price, p_cstm_payment_currency.value as currency, DATE_FORMAT(p_cstm_case_payment_date.value, "%Y-%m-%d") as case_payment_date' . "\n";

            $sql_for_projects['from'] = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_total' . "\n" .
                                        '  ON (p_cstm_total.model_id=p.id AND p_cstm_total.var_id="' . $case_total_id . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_court_judge' . "\n" .
                                        '  ON (p_cstm_court_judge.model_id=p.id AND p_cstm_court_judge.var_id="' . $case_court_judge_id . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS p_cstm_court_judge_name' . "\n" .
                                        '  ON (p_cstm_court_judge.value=p_cstm_court_judge_name.parent_id AND p_cstm_court_judge_name.lang="' . $model_lang . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_category' . "\n" .
                                        '  ON (p_cstm_case_category.model_id=p.id AND p_cstm_case_category.var_id="' . $case_category_id . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_payment' . "\n" .
                                        '  ON (p_cstm_case_payment.model_id=p.id AND p_cstm_case_payment.var_id="' . $type_payment_id . '" AND p_cstm_case_payment.value IN ("' . CASE_PAYMENT_TYPE_INCOME . '", "' . CASE_PAYMENT_TYPE_EXPENSE . '"))' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_payment_date' . "\n" .
                                        '  ON (p_cstm_case_payment_date.model_id=p.id AND p_cstm_case_payment_date.var_id="' . $date_payment_id . '" AND p_cstm_case_payment_date.num=p_cstm_case_payment.num)' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_price' . "\n" .
                                        '  ON (p_cstm_case_price.model_id=p.id AND p_cstm_case_price.var_id="' . $case_price_id . '" AND p_cstm_case_price.num=p_cstm_case_payment.num)' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_payment_currency' . "\n" .
                                        '  ON (p_cstm_payment_currency.model_id=p.id AND p_cstm_payment_currency.var_id="' . $payment_currency_id . '" AND p_cstm_payment_currency.num=p_cstm_case_payment.num)' . "\n";

            // construct where
            $where = array();
            $where[] = 'p.deleted_by=0';
            $where[] = 'p.active!=0';
            $where[] = 'p.type="' . ENFORCEMENT_CASE_TYPE . '"';
            if (!empty($filters['case'])) {
                $where[] = 'p.id = "' . $filters['case'] . '"';
            }
            if (!empty($filters['case_status'])) {
                if ($filters['case_status'] == 'finished') {
                    $where[] = 'p.status="finished"';
                } else {
                    $where[] = 'p.status!="finished"';
                }
            }
            if (!empty($filters['client'])) {
                $where[] = 'p.customer = "' . $filters['client'] . '"';
            }
            if (!empty($filters['case_category'])) {
                $where[] = 'p_cstm_case_category.value = "' . $filters['case_category'] . '"';
            }
            if (!empty($filters['period_open_from'])) {
                $where[] = 'DATE_FORMAT(p.date_start, "%Y-%m-%d")>="' . $filters['period_open_from'] . '"';
            }
            if (!empty($filters['period_open_to'])) {
                $where[] = 'DATE_FORMAT(p.date_start, "%Y-%m-%d")<="' . $filters['period_open_to'] . '"';
            }
            if (!empty($filters['period_payment_to'])) {
                $where[] = 'DATE_FORMAT(p_cstm_case_payment_date.value, "%Y-%m-%d")<="' . $filters['period_payment_to'] . '"';
            }
            if (!empty($filters['company'])) {
                $sql_for_projects['from'] .= "\n" .
                                             'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_company' . "\n" .
                                             '  ON (p_cstm_case_company.model_id=p.id AND p_cstm_case_company.var_id="' . $case_company_id . '" AND p_cstm_case_company.value="' . $filters['company'] . '")' . "\n";

            }

            $sql_for_projects['where'] = 'WHERE ' . implode(' AND ', $where);
            $query_for_projects = implode("\n", $sql_for_projects);
            $projects_records = $registry['db']->GetAll($query_for_projects);

            // take the EUR to BGN conversion rate
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $eur_to_bgn_rate = Finance_Currencies::getRate($registry, 'EUR', 'BGN');

            foreach ($projects_records as $proj_rec) {
                // prepare the current sum
                $current_sum = 0;
                if ($proj_rec['currency'] == PAYMENT_CURRENCY_EUR) {
                    $current_sum = round(($proj_rec['case_price']*$eur_to_bgn_rate), 2);
                } else {
                    $current_sum = round($proj_rec['case_price'], 2);
                }

                // complete the data in the first table
                if (isset($final_records['table1'][$proj_rec['category']])) {
                    // count each distinct project
                    if (!in_array($proj_rec['id'], $final_records['table1'][$proj_rec['category']]['included_projects'])) {
                        $final_records['table1'][$proj_rec['category']]['included_projects'][] = $proj_rec['id'];
                        $final_records['table1'][$proj_rec['category']]['total_projects']++;
                        $final_records['table1'][$proj_rec['category']]['total_owed_due'] += round($proj_rec['total'], 2);
                    }

                    // if this is an income
                    if ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_INCOME) {
                        // sum all collected sums
                        $final_records['table1'][$proj_rec['category']]['collected_sums'] += $current_sum;
                    }

                    // if the case payment date is into the selected payment period
                    if (empty($filters['period_payment_from']) || !empty($filters['period_payment_from']) && $proj_rec['case_payment_date'] >= $filters['period_payment_from']) {
                        // if this is an income
                        if ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_INCOME) {
                            // sum the collected sums for this period
                            $final_records['table1'][$proj_rec['category']]['collected_sums_period'] += $current_sum;
                        } elseif ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_EXPENSE) {
                            // if this is an expense, then sum the expenses for this period
                            $final_records['table1'][$proj_rec['category']]['expenses_period'] += $current_sum;
                        }
                    }
                }

                // complete the data in the second table
                if ($proj_rec['court_judge']) {
                    if (!isset($final_records['table2'][$proj_rec['court_judge']])) {
                        $final_records['table2'][$proj_rec['court_judge']] = array(
                            'type'                  => $proj_rec['court_judge_name'],
                            'included_projects'     => array(),
                            'total_owed_due'        => 0,
                            'total_projects'        => 0,
                            'collected_sums'        => 0,
                            'collected_sums_period' => 0,
                            'expenses_period'       => 0
                        );
                    }

                    // count each distinct project
                    if (!in_array($proj_rec['id'], $final_records['table2'][$proj_rec['court_judge']]['included_projects'])) {
                        $final_records['table2'][$proj_rec['court_judge']]['included_projects'][] = $proj_rec['id'];
                        $final_records['table2'][$proj_rec['court_judge']]['total_projects']++;
                        $final_records['table2'][$proj_rec['court_judge']]['total_owed_due'] += round($proj_rec['total'], 2);
                    }

                    // if this is an income
                    if ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_INCOME) {
                        // sum all collected sums
                        $final_records['table2'][$proj_rec['court_judge']]['collected_sums'] += $current_sum;
                    }

                    // if the case payment date is into the selected payment period
                    if (empty($filters['period_payment_from']) || !empty($filters['period_payment_from']) && $proj_rec['case_payment_date'] >= $filters['period_payment_from']) {
                        // if this is an income
                        if ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_INCOME) {
                            // sum the collected sums for this period
                            $final_records['table2'][$proj_rec['court_judge']]['collected_sums_period'] += $current_sum;
                        } elseif ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_EXPENSE) {
                            // if this is an expense, then sum the expenses for this period
                            $final_records['table2'][$proj_rec['court_judge']]['expenses_period'] += $current_sum;
                        }
                    }
                } else {
                    // count each distinct project
                    if (!in_array($proj_rec['id'], $no_chsi['included_projects'])) {
                        $no_chsi['included_projects'][] = $proj_rec['id'];
                        $no_chsi['total_projects']++;
                        $no_chsi['total_owed_due'] += round($proj_rec['total'], 2);
                    }

                    // if this is an income
                    if ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_INCOME) {
                        // sum all collected sums
                        $no_chsi['collected_sums'] += $current_sum;
                    }

                    // if the case payment date is into the selected payment period
                    if (empty($filters['period_payment_from']) || !empty($filters['period_payment_from']) && $proj_rec['case_payment_date'] >= $filters['period_payment_from']) {
                        // if this is an income
                        if ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_INCOME) {
                            // sum the collected sums for this period
                            $no_chsi['collected_sums_period'] += $current_sum;
                        } elseif ($proj_rec['payment_direction'] == CASE_PAYMENT_TYPE_EXPENSE) {
                            // if this is an expense, then sum the expenses for this period
                            $no_chsi['expenses_period'] += $current_sum;
                        }
                    }
                }
            }

            $final_records['table2'][] = $no_chsi;

            $final_records['additional_options']['placeholders'] = array();
            foreach ($final_records['table1'] as $key => $category_data) {
                if ($key != 'total') {
                    unset($final_records['table1'][$key]['included_projects']);
                    $final_records['table1']['total']['total_owed_due']        += $category_data['total_owed_due'];
                    $final_records['table1']['total']['total_projects']        += $category_data['total_projects'];
                    $final_records['table1']['total']['collected_sums']        += $category_data['collected_sums'];
                    $final_records['table1']['total']['collected_sums_period'] += $category_data['collected_sums_period'];
                    $final_records['table1']['total']['expenses_period']       += $category_data['expenses_period'];
                }
                $placeholders_var_spec_name = array('total_owed_due', 'total_projects', 'collected_sums', 'collected_sums_period', 'expenses_period');
                foreach ($placeholders_var_spec_name as $spec_name) {
                    $placeholder_name = 'levins_' . ($spec_name == 'expenses_period' ? 'expenses' : 'incomes') . '_by_cases_' . $key . '_' . $spec_name;
                    $final_records['additional_options']['placeholders'][$placeholder_name] = array(
                        'name'       => $placeholder_name,
                        'type'       => 'value',
                        'properties' => array(
                            'value' => (($spec_name!='total_projects') ? sprintf('%.2f', $final_records['table1'][$key][$spec_name]) : $final_records['table1'][$key][$spec_name])
                        )
                    );
                }
            }
            foreach ($final_records['table2'] as $key => $category_data) {
                if ($key != 'total') {
                    unset($final_records['table2'][$key]['included_projects']);
                    $final_records['table2']['total']['total_owed_due']        += $category_data['total_owed_due'];
                    $final_records['table2']['total']['total_projects']        += $category_data['total_projects'];
                    $final_records['table2']['total']['collected_sums']        += $category_data['collected_sums'];
                    $final_records['table2']['total']['collected_sums_period'] += $category_data['collected_sums_period'];
                    $final_records['table2']['total']['expenses_period']       += $category_data['expenses_period'];
                }
            }

            $total_row = $final_records['table1']['total'];
            $final_records['table1'] = array('total' => $total_row) + $final_records['table1'];

            // prepare placeholders for the result tables
            $final_records['additional_options']['placeholders']['levins_incomes_by_category_cases'] = array('name' => 'levins_incomes_by_category_cases');
            $final_records['additional_options']['placeholders']['levins_incomes_by_bailiff']        = array('name' => 'levins_incomes_by_bailiff');

            // prepare placeholders for the payment period filter
            $placeholders_filters = array('period_payment_from', 'period_payment_to');
            foreach ($placeholders_filters as $placeholder_filter) {
                $final_records['additional_options']['placeholders'][$placeholder_filter] = array(
                    'name'       => $placeholder_filter,
                    'type'       => 'value',
                    'properties' => array('value' => $filters[$placeholder_filter]));
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_records, 0);
            } else {
                $results = $final_records;
            }

            return $results;
        }
    }
?>
