{if !$prepare_placeholder}
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
{/if}
{if !$prepare_placeholder || $prepare_placeholder eq 'levins_incomes_by_category_cases'}
    <table border="1">
      <tr>
        <td><div style="width: 190px;"><strong>{#reports_type_case#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_total_owed_due#|escape}</strong></div></td>
        <td><div style="width: 90px;"><strong>{#reports_count_cases#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_collected_sums_period#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_collected_sums#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_expenses_period#|escape}</strong></div></td>
      </tr>
      {foreach from=$reports_results.table1 item=result name=results key=category_data}
        <tr>
          <td width="194">
            {$result.type|escape|default:"&nbsp;"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.total_owed_due|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" width="94">
            {$result.total_projects|string_format:"%d"|default:"0"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.collected_sums_period|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.collected_sums|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.expenses_period|string_format:"%.2f"|default:"0.00"}
          </td>
        </tr>
      {/foreach}
    </table>
{/if}
{if !$prepare_placeholder}
    <br />
    <br />
{/if}
{if !$prepare_placeholder || $prepare_placeholder eq 'levins_incomes_by_bailiff'}
    <table border="1">
      <tr>
        <td><div style="width: 190px;"><strong>{#reports_first_lastname#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_total_owed_due#|escape}</strong></div></td>
        <td><div style="width: 90px;"><strong>{#reports_count_cases#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_collected_sums_period#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_collected_sums#|escape}</strong></div></td>
        <td><div style="width: 140px;"><strong>{#reports_expenses_period#|escape}</strong></div></td>
      </tr>
      {foreach from=$reports_results.table2 item=result name=results key=category_data}
        <tr>
          <td width="194">
            {$result.type|escape|default:"&nbsp;"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.total_owed_due|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" width="94">
            {$result.total_projects|string_format:"%d"|default:"0"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.collected_sums_period|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.collected_sums|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" width="144" style="mso-number-format: '0\.00';">
            {$result.expenses_period|string_format:"%.2f"|default:"0.00"}
          </td>
        </tr>
      {/foreach}
    </table>
{/if}
{if !$prepare_placeholder}
  </body>
</html>
{/if}