var grid;
Event.observe(window, 'load', function () {
    /*
    Prepare EJ2 Grid
     */
    let gridDiv = $('grid');
    if (gridDiv) {
        grid = new ej.grids.Grid({
            gridLines: 'Both',
            columns: [
                {
                    field: 'num',
                    headerText: i18n['th_num'],
                    headerTextAlign: 'center',
                    textAlign: 'Center',
                    autoFit: true,
                    template: (args) => {
                        return ((grid.pageSettings.currentPage - 1) * grid.pageSettings.pageSize + parseInt(args.index) + 1).toString();
                    },
                },
                {
                    field: 'sale_order_num',
                    headerText: i18n['th_sale_order_num'],
                    headerTextAlign: 'center',
                    width: 160,
                    template: '#templateSaleOrderNum',
                    textAlign: 'Center',
                },
                {
                    field: 'notes',
                    headerText: i18n['th_notes'],
                    headerTextAlign: 'center',
                    width: 210,
                },
                {
                    field: 'status_and_substatus',
                    headerText: i18n['th_status'],
                    headerTextAlign: 'center',
                    width: 145,
                },
                {
                    field: 'date',
                    headerText: i18n['th_date'],
                    headerTextAlign: 'center',
                    format: {
                        type: 'date',
                        format: 'dd.MM.yyyy'
                    },
                    width: 95,
                    textAlign: 'Center',
                },
                {
                    field: 'deadline',
                    headerText: i18n['th_deadline'],
                    headerTextAlign: 'center',
                    format: {
                        type: 'date',
                        format: 'dd.MM.yyyy'
                    },
                    width: 95,
                    textAlign: 'Center',
                },
                {
                    field: 'channel_name',
                    headerText: i18n['th_channel_name'],
                    headerTextAlign: 'center',
                    width: 100,
                },
                {
                    field: 'market_place',
                    headerText: i18n['th_market_place'],
                    headerTextAlign: 'center',
                    width: 100,
                },
                {
                    field: 'sku_code',
                    headerText: i18n['th_sku_code'],
                    headerTextAlign: 'center',
                    width: 170,
                },
                {
                    field: 'sku_name',
                    headerText: i18n['th_sku_name'],
                    headerTextAlign: 'center',
                    width: 200,
                    template: '#templateSkuName',
                },
                {
                    field: 'quantity',
                    headerText: i18n['th_quantity'],
                    headerTextAlign: 'center',
                    width: 100,
                    format: 'N',
                    textAlign: 'Right',
                },
                {
                    field: 'picked_quantity',
                    headerText: i18n['th_picked_quantity'],
                    headerTextAlign: 'center',
                    width: 100,
                    format: 'N',
                    textAlign: 'Right',
                },
                {
                    field: 'qc_passed',
                    headerText: i18n['th_qc_passed'],
                    headerTextAlign: 'center',
                    width: 100,
                    format: 'N',
                    textAlign: 'Right',
                },
                {
                    field: 'produced',
                    headerText: i18n['th_produced'],
                    headerTextAlign: 'center',
                    width: 110,
                    format: 'N',
                    textAlign: 'Right',
                },
                {
                    field: 'prod_type_name',
                    headerText: i18n['th_type_order_num'],
                    headerTextAlign: 'center',
                    width: 150,
                    template: '#templateTypeOrderNum',
                },
                {
                    field: 'prod_status_and_substatus',
                    headerText: i18n['th_prod_status'],
                    headerTextAlign: 'center',
                    width: 145,
                },
                {
                    field: 'atelier_name',
                    headerText: i18n['th_atelier_name'],
                    headerTextAlign: 'center',
                    width: 200,
                },
            ],
            allowTextWrap: true,
            allowSorting: true,
            allowMultiSorting: true,
            sortSettings: {
                columns: [
                    {field: 'date', direction: 'Ascending'},
                    {field: 'sku_code', direction: 'Ascending'},
                    {field: 'prod_type_name', direction: 'Ascending'},
                ]
            },
            dataBound: function () {
                ej2Helper.grid.applyRowspan(this, [
                    {
                        'criteria': 'id',
                        'spanColumns': [
                            'sale_order_num',
                            'notes',
                            'status_and_substatus',
                            'date',
                            'deadline',
                            'channel_name',
                            'market_place',
                        ],
                    },
                    {
                        'criteria': ['id', 'sku_id'],
                        'spanColumns': [
                            'sku_code',
                            'sku_name',
                            'quantity',
                            'picked_quantity',
                        ],
                    },
                ]);
            },
            allowResizing: true,
            beforeExcelExport: function (args) {
                this.getColumnByField('num').visible = false;
            },
            excelExportComplete: function (args) {
                this.getColumnByField('num').visible = true;
            },
        });

        const pageSize = parseInt(gridDiv.getAttribute('data-page-size'));
        if (pageSize) {
            grid.allowPaging = true;
            grid.pageSettings = {
                pageSize: pageSize,
            };
        }

        // Append the grid
        grid.appendTo('#grid');


        /*
        Get data
         */
        Effect.Center('loading');
        Effect.Appear('loading');
        fetch(window.location.href, {
            headers: {
                'Accept': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            grid.dataSource = ej2Helper.grid.prepareDataTypes(data, {
                date: ['date', 'deadline'],
                int: ['quantity', 'picked_quantity', 'qc_passed', 'produced'],
            });
            // TODO: Show messages from data collection (if any)

            /*
            Add export to Excel
             */
            let buttonExportGrid = $('buttonExportGrid');
            if (buttonExportGrid) {
                grid.allowExcelExport = true;
                Event.observe(buttonExportGrid, 'click', function () {
                    grid.excelExport({
                        fileName: buttonExportGrid.getAttribute('data-export-file-name')
                    });
                });
            }
        }).catch(error => {
            // TODO: on HTTP ERROR
        }).finally(() => {
            Effect.Fade('loading');
        });
    }
});
