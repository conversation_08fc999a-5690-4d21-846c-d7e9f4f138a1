<?php
    Class Chronika_Unite_Requests Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();
            $total = 0;
            $total_with_vat = 0;

            if (!empty($filters['date_from']) && !empty($filters['date_to']) && !empty($filters['provider'])) {
                // take the main currency
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $main_currency = Finance_Currencies::getMain($registry);

                $currency_multipliers = array();
                $currency_multipliers[$main_currency . '->' . $main_currency] = 1;

                /*
                 * GENERAL INFORMATION FOR ALL TYPES
                 */
                $vars_names = array(DATE_OF_PAYMENT, APPROVED_BY, PROVIDER, METHOD_PAYMENT, CURRENCY);
                $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type="' . EXPENSE_TYPE . '" AND fm.name IN ("' . implode('","', $vars_names) . '") ORDER BY fm.position';
                $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                $date_of_payment_id = '';
                $approved_by_id = '';
                $provider_id = '';
                $method_payment_id = '';

                foreach ($var_ids as $vars) {
                    if ($vars['name'] == DATE_OF_PAYMENT) {
                        $date_of_payment_id = $vars['id'];
                    } else if ($vars['name'] == APPROVED_BY) {
                        $approved_by_id = $vars['id'];
                    } else if ($vars['name'] == PROVIDER) {
                        $provider_id = $vars['id'];
                    } else if ($vars['name'] == METHOD_PAYMENT) {
                        $method_payment_id = $vars['id'];
                    } else if ($vars['name'] == CURRENCY) {
                        $currency_id = $vars['id'];
                    }
                }

                // check the gt2 rows which must not be included in the report
                $query_relatives = 'SELECT frr.rows_links, frr.parent_id, frr.link_to' . "\n" .
                                   'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                   'INNER JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                   ' ON (frr.parent_id=fer.id AND fer.type IN (' . implode(',', array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE)) . ') AND fer.active=1 AND fer.annulled_by=0)' . "\n" .
                                   'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                   ' ON (frr.link_to=d.id AND d.type="' . EXPENSE_TYPE . '" AND d.active=1 AND d.deleted_by=0)' . "\n" .
                                   'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND frr.link_to_model_name="Document"' . "\n";
                $related_expenses_reasons = $registry['db']->getAll($query_relatives);

                // process the relations to check which rows from the document were transfered to expenses reasons
                $used_document_gt2_rows = array();
                foreach ($related_expenses_reasons as $rer) {
                    $rows_links = preg_split('#\n|\r|\r\n#', $rer['rows_links']);
                    foreach ($rows_links as $row) {
                        $row_rels = preg_split('#\s*\=\>\s*#', $row);
                        if (!empty($row_rels[1])) {
                            $used_document_gt2_rows[] = $row_rels[1];
                        }
                    }
                }

                // sql to take the data from the requests
                $sql = array();
                $sql['select'] = 'SELECT d.id AS id, d.full_num, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) AS customer_name, ' . "\n" .
                                 '  d_cstm_provider.value as provider, CONCAT(prov_i18n.name, " ", prov_i18n.lastname) AS provider_name, ' . "\n" .
                                 '  DATE_FORMAT(d_cstm_payment_date.value, "%Y-%m-%d") AS payment_date, d_cstm_approved_by.value as approved_by, ' . "\n" .
                                 '  CONCAT(approved_by_name.name, " ", approved_by_name.lastname) AS approved_by_name, d_cstm_currency.value as currency, ' . "\n" .
                                 '  gt2.id as gt2_row, gt2.article_id as nomenclature, nomi18n.name as nomenclature_name, gt2.subtotal_with_vat_with_discount, ' . "\n" .
                                 '  gt2i18n.article_description as descripton, gt2.quantity, gt2.price, gt2.subtotal_with_discount' . "\n";

                //from clause
                $sql['from']  =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_provider' . "\n" .
                                 '  ON (d.id=d_cstm_provider.model_id AND d_cstm_provider.var_id="' . $provider_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS prov_i18n' . "\n" .
                                 '  ON (prov_i18n.parent_id=d_cstm_provider.value AND prov_i18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_payment_date' . "\n" .
                                 '  ON (d.id=d_cstm_payment_date.model_id AND d_cstm_payment_date.var_id="' . $date_of_payment_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_payment_method' . "\n" .
                                 '  ON (d.id=d_cstm_payment_method.model_id AND d_cstm_payment_method.var_id="' . $method_payment_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_approved_by' . "\n" .
                                 '  ON (d.id=d_cstm_approved_by.model_id AND d_cstm_approved_by.var_id="' . $approved_by_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS approved_by_name' . "\n" .
                                 '  ON (approved_by_name.parent_id=d_cstm_approved_by.value AND approved_by_name.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                 '  ON (gt2.model="Document" AND gt2.model_id=d.id)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                                 '  ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                 '  ON (nomi18n.parent_id=gt2.article_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_currency' . "\n" .
                                 '  ON (d.id=d_cstm_currency.model_id AND d_cstm_currency.var_id="' . $currency_id . '")' . "\n";

                $where = array();
                $where[] = 'd.active=1';
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . EXPENSE_TYPE . '"';
                $where[] = 'gt2.id IS NOT NULL';
                $where[] = 'd_cstm_payment_method.value="' . METHOD_PAYMENT_REFACTURED . '"';
                if (!empty($used_document_gt2_rows)) {
                    $where[] = 'gt2.id NOT IN (' . implode(',', $used_document_gt2_rows) . ')';
                }
                if (!empty($filters['customer'])) {
                    $where[] = 'd.customer="' . $filters['customer'] . '"';
                }
                if (!empty($filters['provider'])) {
                    $where[] = 'd_cstm_provider.value="' . $filters['provider'] . '"';
                }
                if (!empty($filters['from_date'])) {
                    $where[] = 'DATE_FORMAT(d.added, "%Y-%m-%d") >= "' . $filters['from_date'] . '"';
                }
                if (!empty($filters['to_date'])) {
                    $where[] = 'DATE_FORMAT(d.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '"';
                }

                $sql['where'] = 'WHERE ' . implode(' AND ', $where);

                //search basic details with current lang parameters
                $query = implode("\n", $sql);
                $records = $registry['db']->GetAll($query);

                foreach ($records as $rec) {
                    if (!isset($final_results[$rec['id']])) {
                        $final_results[$rec['id']] = array(
                            'id'                => $rec['id'],
                            'full_num'          => $rec['full_num'],
                            'payment_date'      => $rec['payment_date'],
                            'customer'          => $rec['customer'],
                            'customer_name'     => $rec['customer_name'],
                            'provider'          => $rec['provider'],
                            'provider_name'     => $rec['provider_name'],
                            'approved_by'       => $rec['approved_by'],
                            'approved_by_name'  => $rec['approved_by_name'],
                            'gt2_rows'          => array(),
                            'rowspan'           => 0
                        );
                    }

                    $currency_key = $rec['currency'] . '->' . $main_currency;
                    if (!isset($currency_multipliers[$currency_key])) {
                        $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $rec['currency'], $main_currency);
                    }
                    $current_multiplier  = $currency_multipliers[$currency_key];
                    $current_price = round(($rec['price']*$current_multiplier), 6);
                    $current_subtotal_with_discount = round(($rec['subtotal_with_discount']*$current_multiplier), 6);
                    $current_subtotal_with_vat_with_discount = round(($rec['subtotal_with_vat_with_discount']*$current_multiplier), 6);

                    $final_results[$rec['id']]['gt2_rows'][$rec['gt2_row']] = array(
                        'id'                              => $rec['gt2_row'],
                        'provider'                        => $rec['provider'],
                        'customer'                        => $rec['customer'],
                        'customer_name'                   => $rec['customer_name'],
                        'nomenclature'                    => $rec['nomenclature'],
                        'nomenclature_name'               => $rec['nomenclature_name'],
                        'descripton'                      => $rec['descripton'],
                        'quantity'                        => $rec['quantity'],
                        'currency'                        => $rec['currency'],
                        'price'                           => $current_price,
                        'subtotal_with_discount'          => $current_subtotal_with_discount,
                        'subtotal_with_vat_with_discount' => $current_subtotal_with_vat_with_discount
                    );
                    $final_results[$rec['id']]['gt2_rows'][$rec['gt2_row']]['encoded_data'] = base64_encode(json_encode($final_results[$rec['id']]['gt2_rows'][$rec['gt2_row']]));
                    $final_results[$rec['id']]['rowspan']++;
                    $total += $current_subtotal_with_discount;
                    $total_with_vat += $current_subtotal_with_vat_with_discount;
                }
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_fields'));
            }

            $final_results['additional_options']['total'] = $total;
            $final_results['additional_options']['total_with_vat'] = $total_with_vat;

            // if the report is generated then clear the session from the selected elements
            // otherwise get the data from the session
            if ($registry['request']->get('reports') == 'generate_report') {
                $registry['session']->remove('reports_chronika_unite_requests_report_table', 'selected_items');
                $final_results['additional_options']['chronika_unite_requests_items_selected_rows'] = array();
            } else {
                $final_results['additional_options']['chronika_unite_requests_items_selected_rows'] = ($registry['session']->get('reports_chronika_unite_requests_report_table', 'selected_items') ? $registry['session']->get('reports_chronika_unite_requests_report_table', 'selected_items') : array());
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>