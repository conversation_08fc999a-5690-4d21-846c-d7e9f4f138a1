/*
Freeze table headers
 */
table.freeze_table_headers {
    position: relative;
}
table.freeze_table_headers thead th,
table.freeze_table_headers td.freeze_column {
    position: sticky;
}
table.freeze_table_headers thead tr:nth-child(1) th {
    top: -1px;
}
table.freeze_table_headers thead tr:nth-child(2) th {
    top: 22px;
}
table.freeze_table_headers thead tr:nth-child(3) th {
    top: 45px;
}
table.freeze_table_headers th.header_atelier,
table.freeze_table_headers th.warehouse_name,
table.freeze_table_headers td.warehouse_name {
    left: 0px;
}
table.freeze_table_headers th.header_material,
table.freeze_table_headers th.material_code,
table.freeze_table_headers td.material_code {
    left: 141px;
}
table.freeze_table_headers th.material_name,
table.freeze_table_headers td.material_name {
    left: 212px;
}
table.freeze_table_headers th.material_type,
table.freeze_table_headers td.material_type {
    left: 434px;
}
table.freeze_table_headers th.material_color,
table.freeze_table_headers td.material_color {
    left: 505px;
}
table.freeze_table_headers td:not(.freeze_column) {
    z-index: 0;
}
table.freeze_table_headers td.freeze_column {
    z-index: 1;
}
table.freeze_table_headers th:not(.freeze_column) {
    z-index: 2;
}
table.freeze_table_headers th.freeze_column {
    z-index: 3;
}
table.freeze_table_headers {
    border-collapse: separate!important;
}
table.freeze_table_headers tbody,
table.freeze_table_headers tbody tr,
.freeze_column {
    background-color: inherit;
}

.reports_table {
    table-layout: fixed;
    /*min-width: 2360px;*/
    margin-bottom: 10px;
    border-right: none!important;
    border-bottom: none!important;
}
.reports_table thead th {
    height: 12px;
}
.reports_table tr:first-child th {
    background-color: #999999;
    color: white;
}
td.warehouse_name,
td.virtual_availability,
td.production_orders_total_needed,
td.cut_orders_total_needed {
    background-color: #DFDFDF;
}

.row_table_container {
    padding: 0px!important;
    /* This is hack: only to stimulate the sub-tables to use their 100% height */
    height: 1px;
}
.row_table_container > table {
    border-collapse: collapse;
    width: 100%;
    height: 100%;
    border: none;
}
.row_table_container > table tr:last-child > td {
    border-bottom: none;
}
.row_table_container > table td:last-child {
    border-right: none;
}

td.warehouse_name {
    vertical-align: top!important;
}
.warehouse_name_container {
    position: sticky;
    top: 99px;
}
.warehouse_name,
.other_warehouse_name {
    width: 130px;
    min-width: 130px;
    max-width: 130px;
}
.material_code {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}
.material_name {
    width: 211px;
    min-width: 211px;
    max-width: 211px;
}
.material_type {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}
.material_color {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}
.warehouse_quantity_without_reserved,
.warehouse_quantity_reserved,
.expected_quantity,
.virtual_availability {
    width: 65px;
    min-width: 65px;
    max-width: 65px;
    text-align: center;
}
.commodities_transfers {
    width: 85px;
    min-width: 85px;
    max-width: 85px;
    text-align: center;
}
.delivery_request_number_date {
    width: 125px;
    min-width: 125px;
    max-width: 125px;
    text-align: center;
}
.delivery_request_supplier,
.delivery_request_status {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}
.delivery_request_expected_delivery_quantity,
.delivery_request_total_quantity {
    width: 70px;
    min-width: 70px;
    max-width: 70px;
    text-align: center;
}
.production_orders_num {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: center;
}
.production_orders_customers,
.production_orders_stock_orders,
.production_orders_real_quantity,
.production_orders_total_needed,
.cut_orders_customers,
.cut_orders_stock_orders,
.cut_orders_total {
    width: 85px;
    min-width: 85px;
    max-width: 85px;
    text-align: center;
}
.other_warehouse_available,
.transfer_quantity,
.shortage_quantity,
.left_to_transfer,
.total_transferred_quantity {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    text-align: center;
}
td.transfer_quantity {
    text-align: left;
}
.transfer_quantity input {
    width: calc(100% - 30px)!important;
}

.warehouse_quantity_exceeded {
    color: red;
}

.negative-quantity {
    color: red;
}
