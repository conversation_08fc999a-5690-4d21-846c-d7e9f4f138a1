reports_technician = Technician
reports_client = Client
reports_office = Office
reports_towns = City
reports_service = Service
reports_date = Date
reports_object = Object

reports_document_num = Document num
reports_document_type = Visit type
reports_status = Status
reports_address = Address
reports_telephone = Phone
reports_visit_date = Visit date
reports_visit_time = Visit time
reports_executor = Executor
reports_description = Description

reports_documents_status_opened = Opened
reports_documents_status_locked = Locked
reports_documents_status_closed = Closed

reports_block = building
reports_entrance = entrance
reports_floor = floor
reports_flat = flat

weekday_0 = Sunday
weekday_1 = Monday
weekday_2 = Tuesday
weekday_3 = Wednesday
weekday_4 = Thursday
weekday_5 = Friday
weekday_6 = Saturday
reports_previous_week = Previous week
reports_next_week = Next week
reports_previous_day = Previous day
reports_next_day = Next day
reports_create = Create

reports_shedule_of = Schedule for
reports_print_schedule = Print schedule
reports_unasigned = Not distributed
reports_search = Search
reports_print_form = Print view

reports_error_missing_settings = Missing report settings!
error_reports_date_outside_period = Selected date is outside selected period!
error_reports_invalid_period = Start of period should be before its end!
