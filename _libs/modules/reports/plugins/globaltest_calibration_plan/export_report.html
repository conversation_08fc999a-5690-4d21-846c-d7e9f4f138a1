{if !$prepare_placeholder}
  <html>
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    </head>
    <body>
{/if}
  {if !$prepare_placeholder || $prepare_placeholder eq 'calibration_plan_table'}
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_inventory_num#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_tsii_name#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_calibration_date#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_action#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_organization_committed#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_testimony#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_calibration_periodicity#|escape}</td>
          <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;">{#reports_table_header_next_calibration_date#|escape}</td>
        </tr>
        {foreach from=$reports_results.tsii item=tsii key=tsii_id}
          <tr>
            <td>{$tsii.inventory_num|escape|default:"&nbsp;"}</td>
            <td>{$tsii.tsii_name|escape|default:"&nbsp;"}</td>
            <td>{$tsii.calibration_date|escape|default:"&nbsp;"}</td>
            <td>{$tsii.action|escape|default:"&nbsp;"}</td>
            <td>{$tsii.organization_committed|escape|default:"&nbsp;"}</td>
            <td>{$tsii.testimony_file_name|escape|default:"&nbsp;"}</td>
            <td>{$tsii.calibration_periodicity|escape|default:"&nbsp;"}</td>
            <td {if $tsii.next_calibration_date_color}style="color: #{$tsii.next_calibration_date_color}; font-weight: bold;"{/if}>{$tsii.next_calibration_date|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="8"><span color="red">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
  {/if}
{if !$prepare_placeholder}
    </body>
  </html>
{/if}