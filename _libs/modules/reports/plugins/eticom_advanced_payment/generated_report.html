<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_document_num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_contract_about#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_address#|escape}</div></td>
      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_advanced_payment#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}">
        <td class="t_border hright" nowrap="nowrap" width="25">
          {counter name='item_counter' print=true}
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.document_id}">{$result.full_num|numerate:$result.direction}</a>
        </td>
        <td class="t_border">
          {$result.customer|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.contract_number|default:"&nbsp;"}
        </td>
        <td class="t_border" nowrap="nowrap">
          {$result.address|escape|default:"&nbsp;"}
        </td>
        <td class="hright">
          {$result.advanced_payment|string_format:"%.2f"|default:"&nbsp;"}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="6">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="6"></td>
    </tr>
  </table>
</td>
</tr>
    <tr>
      <td class="pagemenu" bgcolor="#FFFFFF" colspan="6">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
</table>