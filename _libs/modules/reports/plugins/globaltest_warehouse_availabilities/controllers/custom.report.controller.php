<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_change_warehouse_options':
                $this->_changeWarehouseOptions();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /*
     * AJAX function to take change the warehouses depending on the selected company
     */
    public function _changeWarehouseOptions() {
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        $company_id = $this->registry['request']->get('company');

        $filters_warehouses = array('model_lang' => $this->registry['lang'],
                                    'sanitize'   => true,
                                    'sort'       => array('fwhi18n.name'),
                                    'where'      => array('fwh.deleted=0',
                                                          'fwh.company=' . $company_id,
                                                          'fwh.active=1')
        );

        $company_warehouses = Finance_Warehouses::search($this->registry, $filters_warehouses);

        $company_warehouses_options = array();
        foreach ($company_warehouses as $cmp_wh) {
            $company_warehouses_options[$cmp_wh->get('id')] = array(
                'label'         => $cmp_wh->get('name'),
                'option_value'  => $cmp_wh->get('id')
            );
        }

        print json_encode($company_warehouses_options);
        exit;
    }
}

?>
