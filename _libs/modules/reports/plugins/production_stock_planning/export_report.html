<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        <td nowrap="nowrap"><strong>{#num#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_material#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_measure#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_necessary_quantity#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_available_quantity#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_minimal_quantity#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_shortage_quantity#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_order_quantity#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_delivery_term#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results key='rk' name='ri' item='nomenclature'}
        {capture assign='row_style'}{if $nomenclature.shortage_quantity gt 0}background-color: #F4878D;{elseif $nomenclature.order_quantity gt 0}background-color: #FFFDC0;{/if}{/capture}
        <tr>
          <td style="text-align: right;{$row_style}">{$smarty.foreach.ri.iteration}</td>
          <td style="{$row_style}">
            {$nomenclature.name|escape|default:"&nbsp;"}
          </td>
          <td style="{$row_style}">
            {$nomenclature.measure|escape|default:"&nbsp;"}
          </td>
          <td style="text-align: right; mso-number-format: '0\.0000';{$row_style}">
            {$nomenclature.quantity|escape|default:"0.00"}
          </td>
          <td style="text-align: right; mso-number-format: '0\.0000';{$row_style}">
            {$nomenclature.available_quantity|escape|default:"0.00"}
          </td>
          <td style="text-align: right; mso-number-format: '0\.0000';{$row_style}">
            {$nomenclature.minimal_quantity|escape|default:"&nbsp;"}
          </td>
          <td style="text-align: right; mso-number-format: '0\.0000';{$row_style}">
            {$nomenclature.shortage_quantity|escape|default:"&nbsp;"}
          </td>
          <td style="text-align: right; mso-number-format: '0\.0000';{$row_style}">
            {$nomenclature.order_quantity|escape|default:"&nbsp;"}
          </td>
          <td style="text-align: right;{$row_style}">
            {$nomenclature.delivery_term|escape|default:"&nbsp;"}
          </td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="9"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>