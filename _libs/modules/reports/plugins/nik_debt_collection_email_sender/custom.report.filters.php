<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type of report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            /**
             * @var integer Default number of upcoming days - same as the one
             *              used for coloring in financial module
             */
            define('DEFAULT_UPCOMING_NUM_DAYS', 2);

            $companies_options = Dropdown::getCustomDropdown(array(
                $registry,
                'table' => 'DB_TABLE_FINANCE_COMPANIES',
                'table_i18n' => 'DB_TABLE_FINANCE_COMPANIES_I18N',
                'where' => 't.active = 1 AND t.id IN (' . implode(',', ($registry['currentUser']->get('finance_companies') ?: array('0'))) . ')',
                'order_by' => 'position'
            ));

            $filters['company'] = array(
                'custom_id' => 'company',
                'name'      => 'company',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_company'),
                'help'      => $this->i18n('reports_company'),
                'options'   => $companies_options,
                'required'  => 1,
                'hidden'    => count($companies_options) == 1,
            );

            $offices_options = Dropdown::getCustomDropdown(array(
                $registry,
                'table' => 'DB_TABLE_OFFICES',
                'table_i18n' => 'DB_TABLE_OFFICES_I18N',
                'where' => 't.active = 1 AND t.id IN (' . implode(',', ($registry['currentUser']->get('finance_offices') ?: array('0'))) . ')',
                //'order_by' => 'position'
            ));

            $filters['office'] = array(
                'custom_id' => 'office',
                'name'      => 'office',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_office'),
                'help'      => $this->i18n('reports_office'),
                'options'   => $offices_options,
                'required'  => 0,
                'hidden'    => count($offices_options) == 1,
            );

            $filters['currency'] = array(
                'custom_id' => 'currency',
                'name'      => 'currency',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_currency'),
                'help'      => $this->i18n('reports_currency'),
                'options'   => Dropdown::getCurrencies(array($registry))
            );

            $filters['types'] = array(
                'custom_id' => 'types',
                'name'      => 'types',
                'type'      => 'checkbox_group',
                'required'  => 1,
                'label'     => $this->i18n('reports_document_type'),
                'help'      => $this->i18n('reports_document_type'),
                'options'   => Dropdown::getCustomDropdown(array(
                    $registry,
                    'table' => 'DB_TABLE_FINANCE_DOCUMENTS_TYPES',
                    'table_i18n' => 'DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N',
                    'where' => 't.model = \'Finance_Incomes_Reason\' AND t.active = 1 AND ' .
                        (defined('FIN_DOCUMENTS_TYPES') && FIN_DOCUMENTS_TYPES ?
                            't.id IN (' . FIN_DOCUMENTS_TYPES . ')' :
                            't.id NOT IN (' . implode(', ', array(PH_FINANCE_TYPE_CORRECT_REASON, PH_FINANCE_TYPE_INTEREST_ACCOUNT)) . ')'),
                    'assoc' => true
                ))
            );

            /**
             * @var bool $use_tags Flag whether special behaviour per tag should be applied
             */
            $use_tags = defined('USE_TAGS') && USE_TAGS;
            /**
             * @var int[] $customers_tags Ids of tags to search by
             */
            $customers_tags = defined('CUSTOMERS_TAGS') ? array_filter(array_map('intval', preg_split('#\s*,\s*#', CUSTOMERS_TAGS))) : array();
            /**
             * @var int[] $customers_types Ids of types to search by
             */
            $customers_types = defined('CUSTOMERS_TYPES') ? array_filter(array_map('intval', preg_split('#\s*,\s*#', CUSTOMERS_TYPES))) : array();

            if ($use_tags) {
                $filters['tag'] = array(
                    'custom_id' => 'tag',
                    'name'      => 'tag',
                    'type'      => 'dropdown',
                    'required'  => 1,
                    'label'     => $this->i18n('reports_tag'),
                    'help'      => $this->i18n('reports_tag'),
                    'onchange'  => 'changeDueOptions(this)',
                );
            }

            self::loadDefaultFilter($registry, $filters, 'autocompleter', array('autocomplete_type' => 'customers'));
            $filters['customer']['autocomplete']['filters'] = array();
            if ($customers_types) {
                $filters['customer']['autocomplete']['filters']['<type>'] = implode(',', $customers_types);
            }

            $due_options = array(
                array(
                    'option_value' => 'upcoming',
                    'label' => $this->i18n('reports_upcoming',
                        array('num' => defined('UPCOMING_NUM_DAYS') && UPCOMING_NUM_DAYS ? UPCOMING_NUM_DAYS : DEFAULT_UPCOMING_NUM_DAYS))
                ),
                array('option_value' => 'overdue_1', 'label' => $this->i18n('reports_overdue_all')),
                array('option_value' => 'overdue_1_15', 'label' => $this->i18n('reports_overdue_from_to', array('from' => 1, 'to' => 15))),
                array('option_value' => 'overdue_16_30','label' => $this->i18n('reports_overdue_from_to', array('from' => 16, 'to' => 30))),
                array('option_value' => 'overdue_31_45', 'label' => $this->i18n('reports_overdue_from_to', array('from' => 31, 'to' => 45))),
                array('option_value' => 'overdue_46', 'label' => $this->i18n('reports_overdue_above', array('above' => 45))),
            );
            $due_options_all = array(0 => $due_options);

            $filters['due'] = array(
                'custom_id' => 'due',
                'name'      => 'due',
                'type'      => 'dropdown',
                'required'  => $use_tags,
                'label'     => $this->i18n('reports_due_date'),
                'help'      => $this->i18n('reports_due_date'),
                'first_option_label' => $this->i18n('all'),
                'options'   => $due_options,
            );

            if ($use_tags) {
                $tags_options = Dropdown::getTags(array(
                    $registry,
                    'model' => 'customers',
                    'model_types' => $customers_types,
                    'active' => '1',
                ));

                // keep only specific tags
                if ($customers_tags) {
                    foreach ($tags_options as $idx => $to) {
                        if ($idx == 'contain_optgroups') {
                            continue;
                        } elseif (is_array($to)) {
                            if (array_key_exists('option_value', $to)) {
                                if (!in_array($to['option_value'], $customers_tags)) {
                                    unset($tags_options[$idx]);
                                }
                            } else {
                                foreach ($to as $idx2 => $to2) {
                                    if (array_key_exists('option_value', $to2)) {
                                        if (!in_array($to2['option_value'], $customers_tags)) {
                                            unset($tags_options[$idx][$idx2]);
                                        }
                                    } else {
                                        // not expected, must be some error
                                        unset($tags_options[$idx][$idx2]);
                                    }
                                }
                                if (empty($tags_options[$idx])) {
                                    unset($tags_options[$idx]);
                                }
                            }
                        } else {
                            unset($tags_options[$idx]);
                        }
                    }
                }
                if (!empty($tags_options['contain_optgroups'])) {
                    unset($tags_options['contain_optgroups']);

                    foreach ($tags_options as $idx => $to) {
                        foreach ($to as $idx2 => $to2) {
                            $tag = $to2['option_value'];
                            if (defined("TAG{$tag}_DAYS") && constant("TAG{$tag}_DAYS") != '') {
                                $due_options_all[$tag] = $this->getTagDueOptions(constant("TAG{$tag}_DAYS"));
                                if (empty($due_options_all[$tag])) {
                                    unset($due_options_all[$tag]);
                                }
                            }
                        }
                    }

                    if (count($tags_options) == 1) {
                        // if there are specified tags and they are from the
                        // same tag group, displaying it is most likely not necessary
                        $filters['tag']['options'] = reset($tags_options);
                    } else {
                        $filters['tag']['optgroups'] = $tags_options;
                    }
                } else {
                    foreach ($tags_options as $idx => $to) {
                        $tag = $to['option_value'];
                        if (defined("TAG{$tag}_DAYS") && constant("TAG{$tag}_DAYS") != '') {
                            $due_options_all[$tag] = $this->getTagDueOptions(constant("TAG{$tag}_DAYS"));
                            if (empty($due_options_all[$tag])) {
                                unset($due_options_all[$tag]);
                            }
                        }
                    }

                    $filters['tag']['options'] = $tags_options;
                }

                $filters['customer']['autocomplete']['filters']['<tag>'] = '$tag';
            }

            $dirpath = dirname(realpath(__FILE__)) . '/javascript';
            if (file_exists($dirpath)) {
                $files = FilesLib::readDir($dirpath, false, 'files_only', 'js', false);
                if ($files) {
                    $dir_url = PH_MODULES_URL . 'reports/plugins/' . $registry['report_type']['name'] . '/javascript/';
                    self::loadDefaultFilter($registry, $filters, 'scripts',
                        array('custom_scripts' =>
                            array_merge(
                                array_map(function($f) use ($dir_url) {
                                    return array('type' => 'external', 'src' => $dir_url . $f);
                                }, $files),
                                array(array(
                                    'type' => 'inline',
                                    'src' => 'i18n.messages[\'confirm_send_as_mail\'] = \'' .
                                        $this->i18n('reports_confirm_send_as_mail') .
                                        '\n\n\' + i18n.messages[\'confirm_send_as_mail\'];' .
                                        ($use_tags ? "\n" .
                                        'var due_options_all = ' . json_encode($due_options_all) . ';' : '')
                                )))));
                }
            }

            $filters['display'] = array(
                'custom_id' => 'display',
                'name'      => 'display',
                'type'      => 'hidden',
                'required'  => false,
                'label'     => $this->i18n('display'),
                'help'      => $this->i18n('display'),
            );

            return $filters;
        }

        /**
         * Process some filters that depend on the request or on each other
         *
         * @param array $filters - the report filters
         * @return array - report filters after processing
         */
        function processDependentFilters(&$filters) {

            if (empty($filters['office']['value'])) {
                $filters['office']['value'] = array_map(
                    function($a) {
                        return $a['option_value'];
                    },
                    $filters['office']['options']);
            }

            if (empty($filters['types']['value'])) {
                $filters['types']['value'] = array_map(
                    function($a) {
                        return $a['option_value'];
                    },
                    $filters['types']['options']);
            }

            if (!empty($filters['tag'])) {
                if (empty($filters['tag']['value'])) {
                    if (!empty($filters['tag']['options'])) {
                        foreach ($filters['tag']['options'] as $idx => $to) {
                            $filters['tag']['value'] = $to['option_value'];
                            break;
                        }
                    } elseif (!empty($filters['tag']['optgroups'])) {
                        foreach ($filters['tag']['optgroups'] as $idx => $to) {
                            foreach ($to as $idx2 => $to2) {
                                $filters['tag']['value'] = $to2['option_value'];
                                break;
                            }
                        }
                    }
                }
                if (!empty($filters['tag']['value'])) {
                    $tag = $filters['tag']['value'];
                    if (defined("TAG{$tag}_DAYS") && constant("TAG{$tag}_DAYS") != '') {
                        $due_options = $this->getTagDueOptions(constant("TAG{$tag}_DAYS"));
                        if ($due_options) {
                            $filters['due']['options'] = $due_options;
                        }
                    }
                }
            }

            $results_per_page = defined('RESULTS_PER_PAGE') ? RESULTS_PER_PAGE : 0;
            if (empty($filters['display']['value']) && $results_per_page) {
                $filters['display']['value'] = $results_per_page;
            }

            return $filters;
        }

        /**
         * Prepares dropdown options for due days periods from a list of start days
         *
         * @param string $days - comma-separated list of start days
         * @return string[][] - prepared dropdown options
         */
        private function getTagDueOptions($days) {
            $due_options = array();
            $days = array_unique(array_filter(array_map('intval', preg_split('#\s*,\s*#', $days))));
            if ($days) {
                sort($days, SORT_ASC);
                if ($days[0] > 1) {
                    array_unshift($days, 1);
                }
                foreach ($days as $idx => $day) {
                    if (isset($days[$idx + 1])) {
                        $day_to = $days[$idx + 1] - 1;
                        $due_options[] = array(
                            'option_value' => "overdue_{$day}_{$day_to}",
                            'label' => $this->i18n('reports_overdue_from_to', array('from' => $day, 'to' => $day_to)),
                        );
                    } else {
                        $due_options[] = array(
                            'option_value' => "overdue_{$day}",
                            'label' => $this->i18n('reports_overdue_above', array('above' => $day - 1)),
                        );
                    }
                }
            }

            return $due_options;
        }
    }

?>
