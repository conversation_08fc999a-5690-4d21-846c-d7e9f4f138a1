<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  {literal}
    <style>
        td {
            vertical-align: middle;
        }
    </style>
  {/literal}
</head>
  <body>
    {if $reports_results.error}
      <span style="color: red;">{$reports_results.error}</span>
    {else}
      {assign var='customers' value=$reports_results.customers}
      {assign var='settings' value=$reports_results.settings}
      {assign var='documents' value=$reports_results.documents}
      {if is_array($customers) && count($customers) gt 1}
        {assign var='several_customers' value=true}
      {else}
        {assign var='several_customers' value=false}
      {/if}
      <table>
<!-- Table 1: Customer -->
        <tr>
          <td style="padding-bottom: 4px;"><b>{if $several_customers}{#reports_first_table_labels#}{else}{#reports_first_table_label#}{/if}</b></td>
        </tr>
        <tr>
          <td>
            <table border="1">
              <tr>
                <th>{#reports_th_first_name#}</th>
                <th>{#reports_th_first_company_name#}</th>
                <th>{#reports_th_first_registration_address#}</th>
                <th>{#reports_th_first_contacts#}</th>
                <th>{#reports_th_first_contact_persons#}</th>
                <th>{#reports_th_first_mediator_name#}</th>
                <th>{#reports_th_first_mediator_percentage#}</th>
              </tr>
              {foreach from=$customers item='customer'}
                <tr>
                  <td>[{$customer->get('code')|escape}] {$customer->get('name')|escape} {$customer->get('lastname')|escape}</td>
                  <td>{$customer->get('company_name')|escape|default:"&nbsp;"}</td>
                  <td>{$customer->get('registration_address')|escape|default:"&nbsp;"}</td>
                  <td>
                    {foreach name='z' from=$customer->contactParameters item='contact_param'}
                      {capture assign='contact_param_label'}customers_{$contact_param}{/capture}
                      {capture assign='contact_param_note_var'}{$contact_param}_note{/capture}
                      {assign var='contact_param_notes' value=$customer->get($contact_param_note_var)}
                      {if $customer->get($contact_param)}
                        {foreach name='n' from=$customer->get($contact_param) key='contact_num' item='contact'}
                          {if $use_asterisk && ($contact_param eq 'fax' || $contact_param eq 'phone' || $contact_param eq 'gsm')}
                            {include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$contact_param number=$contact label=$smarty.config.$contact_param_label error=$messages->getErrors($contact_param_error) note=$contact_param_notes.$contact_num}
                          {else}
                            <img src="{$theme->imagesUrl}{$contact_param}.png" alt="{$smarty.config.$contact_param_label}:" title="{$smarty.config.$contact_param_label|escape}" border="0" />&nbsp;{$contact}{if $contact_param_notes.$contact_num} - {$contact_param_notes.$contact_num}{/if}
                          {/if}
                          {if !$smarty.foreach.z.last || !$smarty.foreach.n.last}
                            <br />
                          {/if}
                        {/foreach}
                      {/if}
                    {/foreach}
                  </td>
                  <td>
                    {foreach from=$customer->getContactPersons() item='branch_contact_persons' name='branches'}
                      {foreach from=$branch_contact_persons item='contact_person' name='branch_contact_persons'}
                        {capture assign='salutation'}salutation_{$contact_person->get('salutation')}{/capture}
                        {assign var='salutation' value=$smarty.config.$salutation|escape}
                        {if $salutation}{$salutation} {/if}{$contact_person->get('name')} {$contact_person->get('lastname')}
                        {if !$smarty.foreach.branches.last || !$smarty.foreach.branch_contact_persons.last}
                          <br />
                        {/if}
                      {/foreach}
                    {/foreach}
                  </td>
                  <td>{$customer->getPlainVarValue('mediator_name')|escape}</td>
                  <td style="text-align: center;">{$customer->getPlainVarValue('mediator_proc')}</td>
                </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
        </tr>
<!-- Table 2: Companies (ex Trademarks) -->
        <tr>
          <td style="padding-bottom: 4px;"><b>{if $several_customers}{#reports_second_table_labels#}{else}{#reports_second_table_label#}{/if}</b></td>
        </tr>
        <tr>
          <td>
            <table border="1">
              <tr>
                <th width="20">{#num#}</th>
                <th width="200" colspan="2">{#reports_th_second_company#}</th>
                <th width="50">{#reports_th_second_active#}</th>
                <th width="200">{#reports_th_second_jurisdiction#}</th>
                <th width="100">{#reports_th_second_registration_number#}</th>
                <th width="80">{#reports_th_second_registration_date#}</th>
                <th width="100">{#reports_th_second_capital_size#}</th>
                <th width="200">{#reports_th_second_registration_agent#}</th>
                <th width="200">{#reports_th_second_registration_agent_address#}</th>
                <td colspan="5">&nbsp;</td>
              </tr>
              {foreach from=$customers item='trademark'}
                {assign var='trademark_vars' value=$trademark->getAssocVars()}
                <tr>
                  <td>{counter name='trademarks'}</td>
                  <td colspan="2">
                    [{$trademark->get('code')|escape}] {$trademark->get('name')|escape} {$trademark->get('lastname')|escape}
                  </td>
                  <td>{if $trademark_vars[$settings.field_cus_company_active_yes_no].value}{#yes#}{else}-{/if}</td>
                  <td>{$trademark_vars[$settings.field_cus_company_jurisdiction_field].value|escape|default:"&nbsp;"}</td>
                  <td>{$trademark_vars[$settings.field_cus_company_number_of_reg].value|escape|default:"&nbsp;"}</td>
                  <td style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;">
                    {$trademark_vars[$settings.field_cus_company_date_of_reg].value|escape|date_format:#date_short#|default:"&nbsp;"}
                  </td>
                  <td>{$trademark_vars[$settings.field_cus_company_capital_value].value|escape|default:"&nbsp;"}</td>
                  <td>{$trademark_vars[$settings.field_cus_company_register_agent_name].value|escape|default:"&nbsp;"}</td>
                  <td>{$trademark_vars[$settings.field_cus_company_register_agent_address].value|escape|nl2br|default:"&nbsp;"}</td>
                  <td colspan="5">&nbsp;</td>
                </tr>
                <tr>
                  <th>&nbsp;</th>
                  <th>{#num#}</th>
                  <th>{#reports_th_second_director_nominal_customer_names#}</th>
                  <th colspan="2">{#reports_th_second_director_nominal_director_names#}</th>
                  <th colspan="2">{#reports_th_second_director_director_na#}</th>
                  <th colspan="2">{#reports_th_second_director_agent_names#}</th>
                  <th>{#reports_th_second_director_names#}</th>
                  <th>{#reports_th_second_director_address#}</th>
                  <th colspan="2">{#reports_th_second_director_paid_at#}</th>
                  <th>{#reports_th_second_director_attorney_at#}</th>
                  <th>&nbsp;</th>
                </tr>
                {counter name='trademark_directors' print=false start=0}
                {foreach from=$trademark_vars[$settings.field_cus_company_director_name_id].value key='tmd_key' item='tmd_id' name='trademark_directors'}
                  <tr>
                    <td>&nbsp;</td>
                    <td>{counter name='trademark_directors'}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_director_name].value[$tmd_key]|escape|default:"&nbsp;"}</td>
                    <td colspan="2">{$trademark_vars[$settings.field_cus_company_director_agent].value[$tmd_key]|escape|default:"&nbsp;"}</td>
                    <td colspan="2">{$trademark_vars[$settings.field_cus_company_director_sec].value[$tmd_key]|escape|default:"&nbsp;"}</td>
                    <td colspan="2">{$trademark_vars[$settings.field_cus_company_dagent_sec].value[$tmd_key]|escape|default:"&nbsp;"}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_director_three].value[$tmd_key]|escape|default:"&nbsp;"}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_director_address].value[$tmd_key]|escape|nl2br|default:"&nbsp;"}</td>
                    {if $smarty.foreach.trademark_directors.first}
                      {capture assign='rowspan'}{if is_array($trademark_vars[$settings.field_cus_company_director_name_id].value)}{$trademark_vars[$settings.field_cus_company_director_name_id].value|@count}{else}0{/if}{/capture}
                      <td rowspan="{$rowspan}" colspan="2" style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;{if $trademark_vars[$settings.field_cus_company_director_paid].value lt $reports_results.red_dates_end} color: red; font-weight: bold;{/if}">
                        {$trademark_vars[$settings.field_cus_company_director_paid].value|escape|date_format:#date_short#|default:"&nbsp;"}
                      </td>
                      <td class="hcenter t_border" rowspan="{$rowspan}" style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;{if $trademark_vars[$settings.field_cus_company_attorney_date].value lt $reports_results.red_dates_end} color: red; font-weight: bold;{/if}">
                        {$trademark_vars[$settings.field_cus_company_attorney_date].value|escape|date_format:#date_short#|default:"&nbsp;"}
                      </td>
                      <td rowspan="{$rowspan}">&nbsp;</td>
                    {/if}
                  </tr>
                {foreachelse}
                  <tr>
                    <td colspan="15"><span color="red">{#no_items_found#|escape}</span></td>
                  </tr>
                {/foreach}
                <tr>
                  <th>&nbsp;</th>
                  <th>{#num#}</th>
                  <th>{#reports_th_second_shareholder_nominal_customer_names#}</th>
                  <th colspan="2">{#reports_th_second_shareholder_nominal_shareholder_names#}</th>
                  <th colspan="2">{#reports_th_second_shareholder_shareholder_names#}</th>
                  <th colspan="2">{#reports_th_second_shareholder_agent_names#}</th>
                  <th>{#reports_th_second_shareholder_names#}</th>
                  <th>{#reports_th_second_shareholder_address#}</th>
                  <th>{#reports_th_second_shareholder_shares#}</th>
                  <th>{#reports_th_second_shareholder_percentage#}</th>
                  <th>{#reports_th_second_shareholder_total_shares#}</th>
                  <th>{#reports_th_second_shareholder_paid_at#}</th>
                </tr>
                {counter name='trademark_shareholders' print=false start=0}
                {foreach from=$trademark_vars[$settings.field_cus_company_share_name_id].value key='tms_key' item='tms_id' name='trademark_shareholders'}
                  <tr>
                    <td>&nbsp;</td>
                    <td>{counter name='trademark_shareholders'}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_share_name].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    <td colspan="2">{$trademark_vars[$settings.field_cus_company_share_name_two].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    <td colspan="2">{$trademark_vars[$settings.field_cus_company_share_name_three].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    <td colspan="2">{$trademark_vars[$settings.field_cus_company_share_name_four].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_share_names].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_share_address].value[$tms_key]|escape|nl2br|default:"&nbsp;"}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_share_value].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    <td>{$trademark_vars[$settings.field_cus_company_share_procent].value[$tms_key]|escape|default:"&nbsp;"}</td>
                    {if $smarty.foreach.trademark_shareholders.first}
                      {capture assign='rowspan'}{if is_array($trademark_vars[$settings.field_cus_company_share_name_id].value)}{$trademark_vars[$settings.field_cus_company_share_name_id].value|@count}{else}0{/if}{/capture}
                      <td rowspan="{$rowspan}" style="text-align: right;">
                        {$trademark_vars[$settings.field_cus_company_share_all].value|default:"0"|number_format:2:".":""|escape}
                      </td>
                      <td class="hcenter" rowspan="{$rowspan}" style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;{if $trademark_vars[$settings.field_cus_company_share_paid].value lt $reports_results.red_dates_end} color: red; font-weight: bold;{/if}">
                        {$trademark_vars[$settings.field_cus_company_share_paid].value|escape|date_format:#date_short#|default:"&nbsp;"}
                      </td>
                    {/if}
                  </tr>
                {foreachelse}
                  <tr>
                    <td colspan="15"><span color="red">{#no_items_found#|escape}</span></td>
                  </tr>
                {/foreach}
              {foreachelse}
                <tr>
                  <td colspan="15"><span color="red">{#no_items_found#|escape}</span></td>
                </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
        </tr>
<!-- Table 3: Documents -->
        <tr>
          <td style="padding-bottom: 4px;"><b>{if $several_customers}{#reports_third_table_labels#}{else}{#reports_third_table_label#}{/if}</b></td>
        </tr>
        <tr>
          <td>
            {assign var='third_table_total_colspan' value=12}
            {if $several_customers}{assign var='third_table_total_colspan' value=$third_table_total_colspan+1}{/if}
            <table border="1">
              <tr>
                <th style="min-width: 20px;">{#num#}</th>
                <th style="min-width: 200" colspan="2">{#reports_th_third_document#}</th>
                {if $several_customers}
                  <th style="min-width: 200px;">{#reports_th_third_company#}</th>
                {/if}
                <th style="min-width: 80px;">{#reports_th_third_from_date#}</th>
                <th style="min-width: 100px;">{#reports_th_third_value#}</th>
                <th style="min-width: 150px;">{#reports_th_third_status#}</th>
                <th style="min-width: 200px;">{#reports_th_third_jurisdiction#}</th>
                <th style="min-width: 250px;">{#reports_th_third_contragent#}</th>
                <th style="min-width: 250px;">{#reports_th_third_services#}</th>
                <th style="min-width: 250px;">{#reports_th_third_subservices#}</th>
                <th style="min-width: 80px;">{#reports_th_third_deadline#}</th>
              </tr>
              {foreach from=$documents key='document_key' item='document'}
                {foreach from=$document.jurisdictions key='jurisdiction_id' item='jurisdiction' name='jurisdictions'}
                  {foreach from=$jurisdiction.services key='service_id' item='service' name='services'}
                    {foreach from=$service.subservices key='subservice_id' item='subservice' name='subservices'}
                      <tr>
                        {if $smarty.foreach.jurisdictions.first && $smarty.foreach.services.first && $smarty.foreach.subservices.first}
                          <td rowspan="{$document.rowspan}">{counter name='documents'}</td>
                          <td rowspan="{$document.rowspan}" colspan="2">{$document.name|escape|default:"&nbsp;"}</td>
                          {if $several_customers}
                            <td rowspan="{$document.rowspan}">
                              {if !empty($document.customer_name)}
                                {capture assign='regexp'}#\[{$document.customer_code}\]#{/capture}
                                {if !preg_match($regexp, $document.customer_name)}
                                  [{$document.customer_code|escape}]
                                {/if}
                                {$document.customer_name|escape}
                              {else}
                                -
                              {/if}
                            </td>
                          {/if}
                          <td rowspan="{$document.rowspan}" style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;">
                            {$document.date|escape|date_format:#date_short#|default:"-"}
                          </td>
                          <td rowspan="{$document.rowspan}" style="text-align: right;">
                            {$document.total_with_vat|default:0|number_format:2:".":""|escape} {$document.currency|escape|default:"&nbsp;"}
                          </td>
                          <td rowspan="{$document.rowspan}">
                            {if $document.model eq 'Document'}
                              {if $document.status eq 'opened'}
                                {#documents_status_opened#|escape}
                              {elseif $document.status eq 'locked'}
                                {#documents_status_locked#|escape}
                              {elseif $document.status eq 'closed' || $document.status eq 'finished'}
                                {#documents_status_closed#|escape}
                              {/if}
                              {$document.substatus_name|escape|default:"&nbsp;"}
                            {elseif $document.model eq 'Finance_Incomes_Reason' || $document.model eq 'Finance_Expenses_Reason'}
                              {capture assign='payment_status'}finance_payment_status_{$document.payment_status}{/capture}
                              {$smarty.config.$payment_status|escape}
                            {/if}
                          </td>
                        {/if}
                        {if $smarty.foreach.services.first && $smarty.foreach.subservices.first}
                          <td rowspan="{$jurisdiction.rowspan}">{$jurisdiction.name|escape|default:"-"}</td>
                        {/if}
                        <td>{$subservice.contragent_name|escape|default:"-"}</td>
                        {if $smarty.foreach.subservices.first}
                          <td rowspan="{$service.rowspan}">{$service.name|escape|default:"-"}</td>
                        {/if}
                        <td>{$subservice.name|escape|default:"-"}</td>
                        <td style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;">
                          {$subservice.deadline|escape|date_format:#date_short#|default:"-"}
                        </td>
                      </tr>
                    {/foreach}
                  {/foreach}
                {/foreach}
                {foreach from=$document.subdocuments item='subdocument'}
                  {foreach from=$subdocument.jurisdictions key='sd_jurisdiction_id' item='sd_jurisdiction' name='sd_jurisdictions'}
                    {foreach from=$sd_jurisdiction.services key='sd_service_id' item='sd_service' name='sd_services'}
                      {foreach from=$sd_service.subservices key='sd_subservice_id' item='sd_subservice' name='sd_subservices'}
                        <tr>
                          {if $smarty.foreach.sd_jurisdictions.first && $smarty.foreach.sd_services.first && $smarty.foreach.sd_subservices.first}
                            <td rowspan="{$subdocument.rowspan}">&nbsp;</td>
                            <td rowspan="{$subdocument.rowspan}" width="20">{counter name='subdocuments'}</td>
                            <td rowspan="{$subdocument.rowspan}">{$subdocument.name|escape|default:"&nbsp;"}</td>
                            {if $several_customers}
                              <td rowspan="{$subdocument.rowspan}">
                                {if !empty($subdocument.customer_name)}
                                  {capture assign='regexp'}#\[{$subdocument.customer_code}\]#{/capture}
                                  {if !preg_match($regexp, $subdocument.customer_name)}
                                    [{$subdocument.customer_code|escape}]
                                  {/if}
                                  {$subdocument.customer_name|escape}
                                {else}
                                  -
                                {/if}
                              </td>
                            {/if}
                            <td rowspan="{$subdocument.rowspan}" style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;">
                              {$subdocument.date|escape|date_format:#date_short#|default:"-"}
                            </td>
                            <td rowspan="{$subdocument.rowspan}" style="text-align: right;">
                              {$subdocument.total_with_vat|default:0|number_format:2:".":""|escape} {$subdocument.currency|escape|default:"&nbsp;"}
                            </td>
                            <td rowspan="{$subdocument.rowspan}">
                              {if $subdocument.model eq 'Document'}
                                {if $subdocument.status eq 'opened'}
                                  {#documents_status_opened#}
                                {elseif $subdocument.status eq 'locked'}
                                  {#documents_status_locked#}
                                {elseif $subdocument.status eq 'closed'}
                                  {#documents_status_closed#}
                                {/if}
                                {$subdocument.substatus_name|escape|default:"&nbsp;"}
                              {elseif $subdocument.model eq 'Finance_Incomes_Reason' || $subdocument.model eq 'Finance_Expenses_Reason'}
                                {capture assign='payment_status'}finance_payment_status_{$subdocument.payment_status}{/capture}
                                {$smarty.config.$payment_status|escape}
                              {/if}
                            </td>
                          {/if}
                          {if $smarty.foreach.sd_services.first && $smarty.foreach.sd_subservices.first}
                            <td rowspan="{$sd_jurisdiction.rowspan}">{$sd_jurisdiction.name|escape|default:"-"}</td>
                          {/if}
                          <td>{$sd_subservice.contragent_name|escape|default:"-"}</td>
                          {if $smarty.foreach.sd_subservices.first}
                            <td rowspan="{$sd_service.rowspan}">{$sd_service.name|escape|default:"-"}</td>
                          {/if}
                          <td>{$sd_subservice.name|escape|default:"-"}</td>
                          <td style="mso-number-format:'dd\.mm\.yyyy'; text-align: center;">
                            {$sd_subservice.deadline|escape|date_format:#date_short#|default:"-"}
                          </td>
                        </tr>
                      {/foreach}
                    {/foreach}
                  {/foreach}
                {/foreach}
              {foreachelse}
                <tr>
                  <td colspan="{$third_table_total_colspan}"><span color="red">{#no_items_found#|escape}</span></td>
                </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <td>
            <table border="0">
              <tr>
                <td><b>{#reports_total_orders#}:</b></td>
                <td><b>{$reports_results.orders_total_with_vat|default:0|number_format:2:".":""|escape} {$settings.currency|escape}</b></td>
              </tr>
              <tr>
                <td>
                  <b>{#reports_total_mediator_commission#}:</b>
                </td>
                <td>
                  <b>
                    {if empty($reports_results.mediator_commission_total_failed)}
                      {$reports_results.mediators_commissions_total|default:0|number_format:2:".":""|escape} {$settings.currency|escape}
                    {else}
                      <span style="color: red;">{#reports_total_mediator_commission_error#}</span>
                    {/if}
                  </b>
                </td>
              </tr>
              <tr>
                <td><b>{#reports_total_requests#}:</b></td>
                <td><b>{$reports_results.requests_total_with_vat|default:0|number_format:2:".":""|escape} {$settings.currency|escape}</b></td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    {/if}
  </body>
</html>