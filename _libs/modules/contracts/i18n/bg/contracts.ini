contract = Договор
contracts = Договори
contracts_active = Активиране
contracts_sg = договор
contracts_pl = договори
contracts_all = Всички договори
contracts_name = Относно
contracts_file_location = Прикачен файл
contracts_file_locations = Прикачени файлове
contracts_file_generated = Генериран файл
contracts_files_generated = Генерирани файлове
contracts_file_not_exist = Файлът не съществува!
contracts_type = Тип
contracts_type_section = Раздел
contracts_prefix = Префикс
contracts_relatives_children = Създател на
contracts_relatives_parent = Създаден от
contracts_relative_contract_name = Име на свързания договор
contracts_project = Проект
contracts_project_code = Код на проект
contracts_project_name = Име на проект
contracts_project_undefined = неуточнен
contracts_customer = Контрагент
contracts_customer_num = Номер на контрагент
contracts_customer_code = Код на контрагент
contracts_customer_name = Име на контрагент
contracts_customer_sender = Изпратено от
contracts_customer_recipient = Получател
contracts_company = Фирма
contracts_office = Офис
contracts_employee = Служител
contracts_group = Група
contracts_department = Отдел
contracts_package_deal = По рамков договор №
contracts_is_package_deal = Рамков договор
contracts_types = Типове договори
contracts_description = Описание
contracts_notes = Бележки
contracts_status = Статус
contracts_added_by = Добавен от
contracts_modified_by = Променен от
contracts_added = Добавен на
contracts_modified = Променен на
contracts_deleted = Изтрит на
contracts_id = Договор №
contracts_num = Договор №
contracts_num_format = %06d
contracts_custom_num = № от контрагент
contracts_type_used = Използван
contracts_view_url = Линк за преглеждане на договор
contracts_date = Дата
contracts_datetime = Дата и час
contracts_deadline = Срок
contracts_department_undefined = неуточнен
contracts_initial_notify = Уведомително писмо
contracts_referers = Свързани договори
contracts_finance_warehouses_documents_referers = Свързани складови документи
contracts_relatives_action = Операции по свързване с други договори
contracts_refid_undefined = неуточнен
contracts_assigned = Изпълнител
contracts_notassigned = Не е назначен
contracts_parents = Договори родители
contracts_children = Свързани договори
contracts_parents2 = Произлиза от
contracts_children2 = База за
contracts_vars = Данни
contracts_edit_vars = Редактиране на данни
contracts_tree = Графично представяне на свързаните договори
contracts_tree_children = Договори, създадени след този договор
contracts_tree_parents = Договори, създадени преди този договор
contracts_origin = Произход
contracts_generated_files = Файлове, генерани от този договор 
contracts_assign = Назначаване на договор
contracts_transform_type = Трансформиране към тип
contracts_source = Трансформиране от
contracts_multitransform_type = Трансформиране към тип
contracts_transform_method = Метод
contracts_transform_one2one = един към един
contracts_transform_one2many = един към много
contracts_transform_many2one = много към един
contracts_format = Формат
contracts_pattern = Шаблон
contracts_pattern_header = Хедър
contracts_pattern_footer = Футър
contracts_export = Експорт
contracts_export_title = Експортиране на договор
contracts_export_all = Експортират се всички намерени записи
contracts_export_label = Експорт към
contracts_export_help = Експортира текущия договор във файл с избраното разширение
contracts_export_xls_label = .xls
contracts_export_csv_label = .csv
contracts_export_type_label = Изходящ файл
contracts_export_type_help = типа на изходящия файл: .xls - нормален файл за EXCEL или CSV(универсален файл за данни)
contracts_multitransform = Множествено трансформиране на договори
contracts_comments = Коментари
contracts_comment = Коментар
contracts_substatus  = Състояние
contracts_setstatus  = Смени статус
contracts_status_btn = Потвърди статус
contract_transform_operations = Действие
contracts_contact_person = Лице за контакт
contracts_customers_info = Данни за контрагента
contracts_main_contact_person = Основно лице за контакт
contracts_customers_contacts = Данни за контакт
contracts_status_change_comment = Промяна на статус
contracts_remind = Напомняне
contracts_last_customers_records = Последни %s към контрагента
contracts_tags = Тагове
contracts_dates = Дати
contracts_date_sign = Дата на подписване на договора
contracts_date_start = Дата на влизане в сила на договора
contracts_date_start_formula = Дата на влизане в сила - формула
contracts_date_validity = Дата на изтичане на договора
contracts_date_validity_formula = Дата на изтичане - формула
contracts_date_end = Дата на опция за прекратяване на договора
contracts_date_start_subtype = Дата на влизане в сила на споразумението
contracts_date_sign_subtype = Дата на подписване на споразумението
contracts_date_end_subtype = Дата на край на споразумението
contracts_subtype_status_waiting = чакащо
contracts_subtype_status_started = в сила
contracts_subtype_status_executed = приложено
contracts_subtype_status_failed = отпаднало
contracts_subtype = Подвид
contracts_all = Всички
contracts_contract = Договорно споразумение
contracts_original = Първоначален договор
contracts_annex = Споразумение
contracts_invoice = Фактура
contracts_documents = Документи
contracts_model_vars = Променливи за този договор
contracts_model_vars_no_completed_standard_vars = няма попълнени променливи
contracts_model_vars_no_empty_standard_vars = няма непопълнени променливи
contracts_global_vars = Глобални променливи
contracts_model_vars_no_completed_global_vars = няма попълнени глобални променливи
contracts_model_vars_no_empty_global_vars = няма непопълнени глобални променливи
contracts_invoice_type = Фактура
contracts_amount = Сума
contracts_date_of_payment = Дата на падеж
contracts_date_of_payment_all = Падеж
contracts_payment_type = Начин на плащане
contracts_cashbox = Каса
contracts_bank_account = Банкова сметка
contracts_cash = в брой
contracts_bank = по банков път
contracts_issue_date = Дата на издаване
contracts_auto_issue = Автоматично издаване
contracts_auto_send = Автоматично изпращане
contracts_email_template = Шаблон за e-mail
contracts_pattern = Шаблон за печат
contracts_recurrent = Периодично издаване
contracts_recurrence = Издава се на всеки
contracts_periods_count = Брой периоди
contracts_invoices_start = Дата на първа фактура
contracts_periods_start = Начало на фактурирането
contracts_paid = Платено
contracts_rest = Оставащо
contracts_currency = Валута
contracts_all_contracts = Всички договори
contracts_next_issue_date = Дата на следващо издаване
contracts_no_next_issue_date = Няма следващо издаване
contracts_unfinished_contract = [неприключен]
contracts_single_period = Единичен период за фактуриране
contracts_periods_end = Край на фактурирането
contracts_single_period_rows = Начин на фактуриране<br /> на единичните периоди
contracts_one_row_one_quantity = Всичко на един ред - едно количество
contracts_one_row_all_quantities = Всичко на един ред - сборно количество
contracts_all_rows_one_quantity = Всеки период на отделен ред
contracts_first_period_invoice = Фактуриране на първи период
contracts_full_period = Фактурира се цял период
contracts_partial_period = Фактурира се остатъка от периода
contracts_partial_full_period = Фактурира се остатъка + един цял период
contracts_invoices_templates_description_vars = Променливи, които можете да ползвате в поясненията към фактурата
contracts_invoices_templates_date_from = Начална дата на периода за фактуриране
contracts_invoices_templates_date_to = Крайна дата на периода за фактуриране
contracts_related_subtypes = Договор и споразумения
contracts_current = текущ
contracts_first = първоначален
contracts_proforma_invoice = Издай проформа фактура
contracts_proforma = проформа
contracts_issue_currency = Валута за издаване
contracts_after_issue_date = след издаване
contracts_after_receive_date = след получаване
contracts_fiscal_event_date = Дата на данъчно събитие
contracts_invoices_templates_name = Наименование за шаблона
contracts_add_invoices_template = Добавяне на фактура
contracts_party = Роля
contracts_parties = Страни
contracts_party_name = Име
contracts_party_address = Адрес
contracts_party_administrative = Административен
contracts_party_financial = Финансов
contracts_party_cc = Копие до
contracts_party_email = E-mail
contracts_party_contacts = Контакти
contracts_financial_contact_person = Лице за финансов контакт
contracts_default_financial_contact_person = Лице за финансов контакт по подразбиране
contracts_action_email = Писма
contracts_handovers = Приемо-предавателни протоколи
contracts_handover = ППП
contracts_handover_incoming = ППП (приемателен)
contracts_handover_outgoing = ППП (предавателен)
contracts_name_num = [Номер] Относно
contracts_customer_name_code = [Код] Контрагент
contracts_project_name_code = [Код] Проект
contracts_model_id = За запис
contracts_assigned_to = Изпълнител
contracts_severity = Приоритет
contracts_issue = Издаване
contracts_check_invoices = Проверка фактури
contracts_company_data = Каса/Банкова сметка
contracts_system_agreement = Служебно споразумение
contracts_system_num = <i>[служебно]</i>
contracts_issued_handovers = Издадени приемо-предавателни протоколи
contracts_change_templates_observer = Промяна отговорник
contracts_invoices_templates_observer = Отговорник по издаване на фактури от договор

contracts_recurrence_astro_period = Астрономически период
contracts_recurrence_days_period = дневен период

contracts_principal = Възложител
contracts_executor = Изпълнител

contracts_origin_linked = Свързан
contracts_origin_inherited = Наследен
contracts_origin_transformed = Трансформиран
contracts_origin_cloned = Клониран
contracts_link_type = Тип на връзката
contracts_transform_from_document = Трансформиране от %s
contracts_transform_from_contract = Трансформиране от %s

contracts_advance_invoice = Авансова фактура
contracts_invoice = Нормална фактура
contracts_finish_invoice = Финална фактура

contracts_generated_files = Генерирани файлове
contracts_generated_revisions = Добави нов или презапиши
contracts_generated_add_new = Добави нов
contracts_pattern_variables = Моля, попълнете данните използвани в шаблона!
contracts_pattern_modify = Последни редакции на шаблона
contracts_generated_get_revision = Вземи данни от Версия
contracts_generated_save_revision = Замести Версия
contracts_generate_revision_title = Име на Версията
contracts_generate_revision_description = Описание на Версията
contract_generated_no_save_title = Променливи
contract_generated_no_save_text = Променливата се записва във Версията на файла
contracts_added_attachments = Добавени файлове
contracts_issued_invoices = Издадени фактури
contracts_issued_creditdebit = Издадени кредитни/дебитни известия
contracts_unissued_invoices = Отказани и чакащи издаване фактури
contracts_issue_title = Издаване на фактури
contracts_approve_title = Одобряване на фактури

contracts_generated_filename = Файл
contracts_generated_revision = Версия
contracts_generated_pattern = Шаблон
contracts_generated_description = Описание
contracts_generated_added = Добавен на
contracts_generated_added_by = Добавен от

contracts_ownership = Разпределение
contracts_ownership_unforwarded = Неразпределен
contracts_ownership_forwarded = Разпределен
contracts_ownership_assigned = Назначен
contracts_status_opened = Отворен
contracts_status_locked = Заключен
contracts_status_closed = Завършен
contracts_status_annulled = Анулиран
contracts_status_dropped = Отпаднал

contracts_has_child = Има наследници
contracts_has_parent = Има родители
contracts_has_family = Има наследници и родители

contracts_search = Търсене на договори &raquo; Филтри

contracts_my = Моите договори
contracts_my_tab = Създадени от мен
contracts_myassigned = За изпълнение
contracts_mydep = Неназначени договори в отдел

contracts_send_initial_notification = Изпратете уведомително писмо до отдела
contracts_resend_initial_notification = Изпратете <strong>отново</strong> уведомително писмо до отдела
contracts_initial_notify_sent = Уведомителното писмо е изпратено на 
contracts_initial_notify_not_sent = Все още не е изпратено уведомителното писмо

contracts_add_projects = Добавяне на нов проект
contracts_search_projects = Търсене/Избор на проект
contracts_add_customers = Добавяне на нов контрагент
contracts_search_customers = Търсене/Избор на контрагент
contracts_add_type = Добавете нов тип!
contracts_add_employee = Добавете нов служител!

contracts_add_filter = Добави нов филтър

contracts_cstm_administrative = Административен контакт на контрагента
contracts_cstm_financial = Финансов контакт на контрагента
contracts_self_administrative = Личен административен контакт
contracts_self_financial = Личен финансов контакт
contracts_cstm_adm_cc = Допълнителни административни контакти на контрагента
contracts_cstm_fin_cc = Допълнителни финансови контакти на контрагента
contracts_self_adm_cc = Допълнителни лични административни контакти

contracts_add_ = Добавяне на
contracts_view_ = Преглед
contracts_add_new = Добавяне на %s
contracts_add_annex = Добавяне на споразумение към %s
contracts_add_invoice = Добавяне на фактура
contracts_edit = Редакция на %s
contracts_annex_edit = Редакция на споразумение към %s
contracts_view = Разглеждане на %s
contracts_original_view = Разглеждане на първоначален договор към %s
contracts_annex_view = Разглеждане на споразумение към %s
contracts_translate = Превод на %s
contracts_annex_translate = Превод на споразумение към %s
contracts_history = История на %s
contracts_history_activity = История
contracts_relatives = Връзки на %s
contracts_transform = Трансформиране на %s
contracts_generate = Генериране на файл по шаблон от %s
contracts_comments = Коментари
contracts_emails = Писма
contracts_communications = Комуникации
contracts_minitasks = Мини задачи
contracts_add_legend = Добавяне на договори
contracts_history_legend2 = Ако искате да видите подробно описание на редакция, извършена по договора, кликнете на желан от Вас ред в долната таблица. Данните за избрания ред ще се заредят във втората таблица. Ако във втората таблицата не се зареди нищо, това означава, че върху това действие не се извършва одит.
contracts_attachments = Файлове към %s
contracts_annulment_annex = Прекратяване на споразумение
contracts_invoice_period_start = Начало на периода
contracts_invoice_period_finish = Край на периода
contracts_create = Създаване
contracts_minitask = Мини задача

contracts_types_transform_to = Трансформации

contracts_no_department = Договорът е неразпределен

contracts_today     = Днес
contracts_yesterday = Вчера
contracts_week      = Преди седмица
contracts_month     = Преди месец
contracts_year      = Преди година

contracts_last_modified = Редактиран за последно
contracts_expired_label = Изтеклъл
contracts_added_label = Добавен

contracts_group_by = Групиране по

contracts_reminder_email = с e-mail
contracts_reminder_toaster = през системата
contracts_reminder_both = и двата
contracts_reminder_event_name = Напомняне за договор %s

contracts_days_after_receive = дни след получаване

contracts_annul_subtype = Прекратяване
contracts_copy_from_annex = Копиране на данни от споразумение
contracts_for_contract = към Договор %s

message_contracts_add_success = Успешно добавяне на %s!
message_contracts_annex_add_success = Успешно добавено споразумение!
message_contracts_contract_edit_success = Успешна редакция на %s!
message_contracts_annex_edit_success = Успешно редактирано споразумение!
message_contracts_status_success = Успешно сменен статус!
message_contracts_translate_success = Успешен превод на %s!

message_contracts_pattern_selected1 = Избрали сте шаблон за генериране на договори.
message_contracts_pattern_selected2 = След въвеждане на данните, ще бъдете прехвърлени към генериране на файл.

message_contracts_type_add_success = Успешно добавен тип договор!
message_contracts_type_edit_success = Успешно редактиран тип договор!

message_contracts_generate_success = Успешно генериран файл!
error_contracts_generate_contract = НЕУСПЕШНО генериран файл!
error_contracts_generate_invalid_pattern = Не е избран шаблон или е избран несъществуващ шаблон

message_contracts_export_success         = Успешно експортиран файл!
error_contracts_export_contract          = НЕУСПЕШНО експортиран файл!

error_print_no_default_pattern = Не е избран шаблон за печат
error_contracts_print_contract = Файлът не може да се разпечата, заради грешка при генерирането на договор

error_contracts_change_status_closed = Договорът е със статус "Затворен". Статусът на затворени договори не може да бъде сменен!
error_contracts_uncompleted_minitasks = Записът не може да бъде приключен, докато има неприключени мини задачи към него.

error_contracts_no_counter = Няма брояч за тип "%s" и фирма "%s"!

message_initial_notify = Успешно изпратено уведомително писмо!

message_contract_vars_text = Поле от тип <strong>TEXT</strong>
message_contract_vars_dropdown = Поле от тип <strong>DROPDOWN</strong>
message_contract_vars_textarea = Поле от тип <strong>TEXTAREA</strong>
message_contract_vars_integer = Стойността на това поле трябва да е <strong>цяло положително число</strong>
message_contract_vars_float = Стойността на това поле трябва да е положително число, което може да е с плаваща запетая
message_contract_vars_date = Стойността на това поле трябва да е във формат: <strong>[гггг-мм-дд]</strong>
message_contract_vars_datetime = Стойността на тази променлива трябва да е във формат: <strong>[гггг-мм-дд чч:мм]</strong>

message_contracts_new_vals = Няма съхранени данни за допълнителните променливи
message_contracts_comments_add_success = Коментарът беше добавен успешно

message_contracts_transform_success = Успешно трансформация на %s!
message_contracts_clone_success = Успешно клониране на %s!

message_contracts_reminder_edit_success = Успешно редактиране на напомняне
message_contracts_reminder_add_success = Успешно добавяне на напомняне

message_contracts_email_success = Писмото беше изпратено успешно
message_contracts_file_deleted_success = Успешно изтрит файл!
message_contracts_formulas_success = Формулите бяха изчислени успешно!
message_contracts_parties_edit_success = Данните за страните бяха редактирани успешно!

message_contracts_no_templates = Няма добавени шаблони за фактури.
message_contracts_add_templates = Може да добавите след като преминете в режим на редакция.

error_contracts_save_contacts_failed = Грешка при запис на контактите за страните!
error_contracts_parties_edit_failed = Данните за страните не бяха редактирани!
error_finance_invoices_templates_no_payment_type = Моля, изберете каса/банкова сметка!
error_finance_invoices_templates_no_observer = Моля, изберете отговорник за издаване на фактури!
error_finance_invoices_templates_no_email_template = Моля, изберете шаблон за изпращане на e-mail!
error_finance_invoices_templates_no_recurrence = Моля, изберете период на издаване!
error_finance_invoices_templates_no_start_date = Моля, изберете дата издаване na първата фактура!
error_finance_invoices_templates_no_periods_start = Моля, изберете начало на периода!
error_finance_invoices_templates_no_periods_end = Моля, изберете край на периода!
error_finance_invoices_templates_no_issue_date = Моля, въведете валидна дата на издаване!
error_finance_invoices_templates_no_pay_after = Моля, въведете падеж!
error_contracts_file_deleted_failed = Грешка при изтриване на файл!
error_contracts_formulas_failed = Грешка при пресмятане на формулите!
error_finance_invoices_templates_no_single_period_rows = Моля, изберете начин на фактуриране!
error_finance_invoices_templates_no_single_period = Моля, изберете единичен период за фактуриране!
error_finance_invoices_templates_no_first_period_invoice = Моля, изберете опция за издаване на първа фактура!
error_finance_invoices_templates_agreement_periods = Периодът трябва да е в рамките на споразумението!
message_finance_invoices_templates_observer_edit_success = Отговорникът за издаване на фактури бе редактиран успешно.
error_finance_invoices_templates_observer_edit_failed = Грешка при редакция на отговорник за издаване на фактури!
warning_contract_no_financial_contact_person = Избрали сте лице за финансов контакт, което не е уточнено в страните по договорите! Моля, изберете лице за финансов контакт в таба "Страни" на текущ договор!

error_finance_invoices_templates_recurrence_wrong_period = Невалидна начална или крайна дата на периода!
error_finance_invoices_templates_recurrence_wrong_recurrence = Невалидна повтаряемост или единичен период!
error_finance_invoices_templates_recurrence_short_single_period = Единичният период за фактуриране е къс!
error_finance_invoices_templates_wrong_first_invoice_period = Грешен начин на фактуриране на първи период!

error_contracts_add_failed = НЕУСПЕШНО добавяне на %s!
error_contracts_annex_add_failed = НЕУСПЕШНО добавяне на споразумение!
error_contracts_contract_edit_failed = НЕУСПЕШНО редактиране на %s!
error_contracts_annex_edit_failed = НЕУСПЕШНО редактиране на споразумение!
error_contracts_status_failed = НЕУСПЕШНА смяна на статус!
error_contracts_translate_failed = НЕУСПЕШЕН превод на %s!

error_update_from_annex_unsuccessful = Грешка при копирането на данните от споразумението в %s

error_invalid_date = Невалидна дата!
error_invalid_status_change = Невалиден избор на статус!
error_invalid_substatus_change = Невалиден избор на състояние!
error_no_name = Въведете "%s" за договора!
error_invalid_email = Невалиден e-mail!
error_no_such_contract = Нямате възможност да прегледате този запис!
error_no_customer = Изберете %s!
error_no_new_customer = Моля, въведете нов контрагент или изберете от списъка с контрагенти
error_no_company = Изберете %s!
error_no_type = Изберете тип!
error_invalid_type = Избран е невалиден или неактивен тип!
error_invalid_project = Изберете %s!
error_invalid_office = Изберете %s!
error_no_custom_num = Въведете %s!
error_no_trademark = Изберете %s!
error_no_project = Изберете %s!
error_no_employee = Изберете %s!
error_no_date_sign = Въведете %s!
error_no_date_start = Въведете %s!
error_no_date_validity = Въведете %s!
error_no_date_end = Въведете %s!
error_no_description = Попълнете %s!
error_no_notes = Попълнете %s!
error_no_department = Изберете %s!

error_no_new_type = Моля, въведете нов тип или изберете от списъка с типове
error_invalid_project_customer = Избраният проект не е предназначен за избрания контрагент. Изберете друг проект или контрагент "%s"
error_no_new_project = Въведете нов проект или изберете от списъка с проекти.
error_delete_type = Избраните типове се използват и не могат да бъдат изтрити!
error_no_department = За да изпратите уведомително писмо, моля изберете отдел
error_no_department_manager = Отделът няма назначен ръководител, уведомителното писмо не може да бъде изпратено
error_type_already_used = Вече съществуват договори, използващи избрания тип. Не можете да сменяте вида му
error_invalid_status = Невалидно назначаване на статус
error_comments_add_failed = Коментарът не е добавен!

error_initial_notify = Грешка при изпращането на уведомителното писмо: 
error_view_notallowed = Нямате достатъчно права за разглеждане на договор 
error_preview_notallowed = Нямате достатъчно права за разглеждане

expired_contracts_attention = Внимание!
expired_contracts_attention_full = Договори, изискващи вниманието ми

warning_contracts_relative_child = Не може да се свърже с наследен договор
warning_contracts_date_end_subtype = Датата на край не може да бъде преди датата на начало на споразумението или по-голяма от днешната дата!

error_contracts_transform_type = Договорът не може да се трансформира в избрания тип
error_no_such_transformation = Избрана е неактивна или несъществуваща трансформация!
error_contracts_transform = НЕУСПЕШНА трансформация на %s!
error_contracts_clone = НЕУСПЕШНО клониране на %s!

error_different_types = Избраните договори са от различни типове
error_no_contracts_or_deleted = Не са избрани договори или някой от избраните е изтрит

error_contracts_send_email = Грешка при изпращане на писмо

error_contracts_reminder_failed = Грешка при запис на напомняне

error_isValidNumber = Полето [var_label] трябва да е число
error_contracts_no_valid_mails = Няма зададени валидни електронни адреси
error_contracts_invalid_mails = Следните електронни адреси са невалидни: [invalid_mails]
error_contracts_email_failed = Писмото не беше изпратено!

error_contracts_invoice_no_quantity = Няма достатъчно нефактурирани количества по %s!
error_contracts_no_advance_nomenclature = Няма номенклатура за аванс!
error_contracts_only_one_final_invoice = Можете да издавате само една финална фактура!
error_contracts_invoices_all_invoiced = Всички количества по %s са фактурирани!

error_annex_total = Сумата на споразумението е по-малка от фактурираната!
error_get_currency_rate = Не може да бъде взет курса между изходната и целевата валута!
error_no_date_start_subtype = Не сте въвели дата на начало на споразумението!
error_no_date_sign_subtype = Не сте въвели дата на подписване на споразумението!
error_date_sign_subtype_exist = Дата на подписване не може да бъде по-стара от датата на подписване на другo съществуващo споразумение към този договор
error_date_sign_subtype_exist2 = Последната подписана договореност към този договор %s е с дата на подписване %s
error_dates_package_deal = Датите на влизане в сила и на изтичане на договора не трябва да са извън периода на рамковия договор
error_dates_package_deal2 = Рамковият договор е с дата на влизане в сила %s и дата на изтичане %s

error_finance_invoices_templates_no_name = Моля, въведете име за шаблона

error_no_lastname_specified_contact = Не сте въвели фамилия за %s контакт на контрагент!
error_invalid_email_contact = Моля, въведете валиден e-mail за %s контакт на контрагент!
error_select_email_contact_cstm = Моля, изберете e-mail за %s контакт на контрагент!
error_select_email_contact_self = Моля, изберете e-mail за %s контакт на собствена фирма!
error_no_lastname_specified_contact_cstm_cc = Не сте въвели фамилия за "Копие до" на %s контакт на контрагент!
error_invalid_email_contact_cstm_cc = Моля, въведете валиден e-mail за "Копие до" на %s контакт на контрагент!
error_select_email_contact_cstm_cc = Моля, изберете e-mail за "Копие до" на %s контакт на контрагент!
error_select_email_contact_self_cc = Моля, изберете e-mail за "Копие до" на %s контакт на собствена фирма!

contracts_email_sent_success = Успешно изпратено уведомително писмо 

error_handover_no_from_specified_incoming = Моля, въведете името на човека, предал стоката за %s
error_handover_no_from_specified_outgoing = Моля, изберете или въведете името на служителя, предал стоката за %s
error_handover_no_to_specified_incoming = Моля, изберете или въведете името на служителя, приел стоката за %s
error_handover_no_to_specified_outgoing = Моля, въведете името на човека, приел стоката за %s
error_handover_no_date_specified = Моля, изберете днешна или по-късна дата на предаване за %s
error_handover_no_location_specified = Моля, въведете място за издаване на стоката за %s
error_handover_no_from_specified = Моля, изберете или въведете името на служителя, предал стоката за %s
error_handover_no_to_specified = Моля, изберете или въведете името на служителя, приел стоката за %s

error_contracts_handover_no_quantity = Няма достатъчно доставени количества по %s!
error_contracts_total_quantity_over = Общото количество за издаване/приемане е по-голямо от поместеното в %s! [%s - %s %d]
error_contracts_not_enough_quantity = Изписаното количество е по-голямо от наличното в съответния склад! [%s - %s %d]
error_contracts_protocol_invalid_data = Не е създаден ППП. Невалидни данни!
error_contracts_protocol_not_created = Не е създаден ППП. Грешка при запис!
message_contracts_no_available_quantities = Избраните стоки нямат наличност!

error_contracts_handover_setstatus = Не можете да приключите споразумението, защото от договора са издадени приемо-предавателни протоколи<br />за количества по-големи от тези, които се съдържат в предмета на споразумението.<br />За да можете да приключите споразумението, е необходимо да издадете %s протокол за следните артикули и количества:
error_contracts_handover_annulmentsubtype = Не можете да прекратите споразумението, защото от договора са издадени приемо-предавателни протоколи<br />за количества по-големи от тези, които ще се съдържат в предмета на договора след прекратяване на споразумението.<br />За да можете да прекратите споразумението, е необходимо да издадете %s протокол за следните артикули и количества:

contracts_handover_for = ППП за
contracts_handover_direction = Вид
contracts_handover_direction_incoming = Приемателен
contracts_handover_direction_outgoing = Предавателен

error_contracts_handover_failed_incoming = Допусната е грешка при приемане на стоката.
error_contracts_handover_failed_outgoing = Допусната е грешка при издаване на стоката.
message_contracts_handover_success_incoming = Стоката е успешно приета. Брой създадени ППП:
message_contracts_handover_success_outgoing = Стоката е успешно издадена. Брой създадени ППП:

contracts_addhandover_incoming = Приемане на стока
contracts_addhandover_outgoing = Издаване на стока
contracts_article = Артикул
contracts_cd_reason = Основание
contracts_quantity = Количество

contracts_log_add = %s добавя договор (статус: %s)
contracts_log_add_annex = %s добавя споразумение (статус: %s)
contracts_log_edit = %s редактира договор (статус: %s)
contracts_log_edit_annex = %s редактира споразумение (статус: %s)
contracts_log_edittopic = %s редактира предмет на договор (статус: %s)
contracts_log_edittopic_annex = %s редактира предмет на споразумение (статус: %s)
contracts_log_parties = %s редактира данни за контакт на страните по договор (статус: %s)
contracts_log_parties_annex = %s редактира данни за контакт на страните по договор (статус: %s)
contracts_log_saveformulavals = %s редактира променливите на договор
contracts_log_editclause = %s редактира условията на договор (статус: %s)
contracts_log_editclause_annex = %s редактира условията на споразумение (статус: %s)
contracts_log_translate = %s превежда договор (статус: %s)
contracts_log_translate_annex = %s превежда споразумение (статус: %s)
contracts_log_activate = %s активира договор (статус: %s)
contracts_log_deactivate = %s деактивира договор (статус: %s)
contracts_log_delete = %s изтрива договор (статус: %s)
contracts_log_restore = %s възстановява договор (статус: %s)
contracts_log_assign = %s назначава договор (статус: %s)
contracts_log_remove_assignments = %s премахва назначенията от тип %s на договор (статус: %s)
contracts_log_create = %s създава %s чрез %s
contracts_log_transform = %s трансформира договор (статус: %s) от %s
contracts_log_clone = %s клонира договор (статус: %s)
contracts_log_generate = %s генерира файл за договор, използвайки шаблон "%s"%s
contracts_log_generate_annex = %s генерира файл за споразумение, използвайки шаблон "%s"%s
contracts_log_generate_original = %s генерира файл за първоначален договор, използвайки шаблон "%s"%s
contracts_log_print = %s отпечатва договор, използвайки шаблон "%s"%s
contracts_log_print_annex = %s отпечатва споразумение, използвайки шаблон "%s"%s
contracts_log_print_original = %s отпечатва първоначален договор, използвайки шаблон "%s"%s
contracts_log_multiprint = %s отпечатва договор, използвайки шаблон "%s"%s
contracts_log_multiprint_annex = %s отпечатва дпълнително споразумение, използвайки шаблон "%s"%s
contracts_log_multiprint_original = %s отпечатва първоначален договор, използвайки шаблон "%s"%s
contracts_log_export = %s експортира файл за договор (статус: %s)
contracts_log_export_annex = %s експортира файл за споразумение (статус: %s)
contracts_log_modified_attachments = %s променя прикачен файл за договор (статус: %s)
contracts_log_modified_attachments_annex = %s променя прикачен файл за споразумение (статус: %s)
contracts_log_modified_gen = %s променя генериран файл за договор (статус: %s)
contracts_log_modified_gen_annex = %s променя генериран файл за споразумение (статус: %s)
contracts_log_add_attachments = %s добавя прикачен файл за договор (статус: %s)
contracts_log_add_attachments_annex = %s добавя прикачен файл за споразумение (статус: %s)
contracts_log_add_attachments_original = %s добавя прикачен файл за първоначален договор (статус: %s)
contracts_log_generate_delete = %s изтрива генериран файл за договор (статус: %s)
contracts_log_generate_delete_annex = %s изтрива генериран файл за споразумение (статус: %s)
contracts_log_status = %s променя статуса на договор (статус: %s)
contracts_log_status_annex = %s променя статуса на споразумение (статус: %s)
contracts_log_status_original = %s променя статуса на първоначален договор (статус: %s)
contracts_log_tag = %s променя тагове на договор (статус: %s)
contracts_log_tag_annex = %s променя тагове на споразумение (статус: %s)
contracts_log_tag_original = %s променя тагове на първоначален договор (статус: %s)
contracts_log_multitag = %s променя тагове на договор (статус: %s)
contracts_log_multitag_annex = %s променя тагове на споразумение (статус: %s)
contracts_log_multitag_original = %s променя тагове на първоначален договор (статус: %s)
contracts_log_enure_annex = Споразумение %s влиза в сила
contracts_log_end_annex = Споразумение %s бе приключено
contracts_log_annulment_annex = Споразумение %s бе прекратено
contracts_log_increased_start_date = Споразумение %s отлага началото на договора
contracts_log_decreased_start_date = Споразумение %s намалява началото на договора
contracts_log_increased_validity_date = Споразумение %s увеличава срока на договора
contracts_log_decreased_validity_date = Споразумение %s намалява срока на договора
contracts_log_modify_terms = Споразумение %s променя параметри на договора
contracts_log_signed = Подписан %s
contracts_log_addinvoice = %s добавя фактура %s към договор (статус: %s)
contracts_log_addproformainvoice = %s добавя проформа фактура %s към договор (статус: %s)
contracts_log_addexpensesreason = %s добавя разходен документ %s към договор (статус: %s)
contracts_log_addhandover = %s издава приемо-предавателен протокол %s (стойност: %s)
contracts_log_email = %s изпраща e-mail към договора
contracts_log_receive_email = %s получава e-mail към договора
contracts_log_receive_email_detailed = От %s е получен e-mail към договора, изпратен
contracts_log_add_comment = %s добавя коментар към договора
contracts_log_edit_comment = %s редактира коментар към договора
contracts_log_add_minitask = %s добавя мини задача към договор
contracts_log_add_minitask_annex = %s добавя мини задача към споразумение
contracts_log_add_minitask_original = %s добавя мини задача към първоначален договор
contracts_log_edit_minitask = %s редактира мини задача към договор
contracts_log_edit_minitask_annex = %s редактира мини задача към споразумение
contracts_log_edit_minitask_original = %s редактира мини задача към първоначален договор
contracts_log_status_minitask = %s сменя статус на мини задача към договор
contracts_log_status_minitask_annex = %s сменя статус на мини задача към споразумение
contracts_log_status_minitask_original = %s сменя статус на мини задача към първоначален договор
contracts_log_multistatus_minitask = %s сменя статус на мини задача към договор
contracts_log_multistatus_minitask_annex = %s сменя статус на мини задача към споразумение
contracts_log_multistatus_minitask_original = %s сменя статус на мини задача към първоначален договор
contracts_log_change_templates_observer = %s променя отговорник по издаване на фактури от договор

contracts_logtype_add = Добавяне
contracts_logtype_edit = Редакция
contracts_logtype_edittopic = Редакция на "Предмет"
contracts_logtype_parties = Редакция на "Страни"
contracts_logtype_editclause = Редакция на "Условия"
contracts_logtype_editfinance = Редакция на "Финанси"
contracts_logtype_saveformulavals = Редакция на променливи на договор
contracts_logtype_translate = Превод
contracts_logtype_activate = Активиране
contracts_logtype_deactivate = Деактивиране
contracts_logtype_delete = Изтриване
contracts_logtype_restore = Възстановяване
contracts_logtype_assign = Назначаване
contracts_logtype_remove_assignments = Премахване на назначенията
contracts_logtype_create = Създаване
contracts_logtype_transform = Трансформиране
contracts_logtype_clone = Клониране
contracts_logtype_generate = Генериране на файл
contracts_logtype_generate_delete = Изтриване на файл
contracts_logtype_print = Печат
contracts_logtype_multiprint = Множествен печат
contracts_logtype_modified_attachments = Променяне на файл
contracts_logtype_modified_gen = Променяне на файл
contracts_logtype_add_attachments = Добавяне на файл
contracts_logtype_status = Промяна на статус
contracts_logtype_tag = Промяна на тагове
contracts_logtype_multitag = Множествена промяна на тагове
contracts_logtype_enure_annex = Влизане в сила на споразумение
contracts_logtype_end_annex = Изтичане на споразумение
contracts_logtype_annulment_annex = Прекратяване действието на споразумение
contracts_logtype_increased_start_date = Промяна началото на договора
contracts_logtype_decreased_start_date = Промяна началото на договора
contracts_logtype_increased_validity_date = Промяна края на договора
contracts_logtype_decreased_validity_date = Промяна края на договора
contracts_logtype_modify_terms = Промяна параметри на договора
contracts_logtype_signed = Подписване
contracts_logtype_addinvoice = Издаване на фактура
contracts_logtype_addproformainvoice = Издаване на проформа фактура
contracts_logtype_addexpensesreason = Издаване на разходен документ
contracts_logtype_addhandover = Издаване на ППП
contracts_logtype_email = Изпращане на e-mail
contracts_logtype_receive_email = Получаване на e-mail
contracts_logtype_add_comment = Добавяне на коментар
contracts_logtype_edit_comment = Редакция на коментар
contracts_logtype_add_minitask = Добавяне на мини задача
contracts_logtype_edit_minitask = Редакция на мини задача
contracts_logtype_status_minitask = Смяна на статус на мини задача
contracts_logtype_multistatus_minitask = Множествена смяна на статус на мини задачи
contracts_logtype_change_templates_observer = Промяна отговорник

contracts_hotlinks = Бързи връзки
contracts_hotlinks_legend = Добавяне на нови договори

contracts_types_type_legend = Тип на договора. Уникално поле, уточняващо типа на договорите. Задължително поле
contracts_types_default_department_legend = 

contracts_types_transform_to_legend = Трансформирането на договори изисква избор на типове договори, в които зададеният договор може да се трансформира
contracts_expired_legend = Този договор е просрочен! <strong>Срокът за обработка</strong> на Договора е изтекъл на
contracts_expired_validity_legend = <strong>Срокът на валидност</strong> Договора е изтекъл на 

confirm_link_contracts = Сигурни ли сте, че искате да свържете избраните договори?\nНатиснете "OK", за да потвърдите!
alert_link_contracts = Моля, изберете договор за връзка!

contracts_department_not_forwarded = Договорът не е разпределен към отдел и не може да бъде назначен на конкретен <strong>изпълнител</strong>.
contracts_group_no_users = Няма потребители в този отдел 

var_type = Тип
var_name = Поле
var_value = Нов текст
old_value = Предишен текст
no_old_value = няма предишен текст

audit_vars = История на редакциите, извършени по договора
audit_legend = Подробни данни за промяна, извършена от %s на %s

contracts_replace_all = Замести<br />всички
contracts_replace_col = Замести колона

contracts_assign_owner = Изпълнител
contracts_assign_responsible = Отговорник
contracts_assign_observer = Наблюдаващ
contracts_assign_decision = Вземащ решения

contracts_assign_title = Назначения за
contracts_assign_change = Промяна на назначения

contracts_owner = Изпълнител
contracts_responsible = Отговорник
contracts_observer = Наблюдаващ
contracts_decision = Вземащ решения

contracts_show_full_assignments_list = Покажи пълния списък с назначения
contracts_hide_full_assignments_list = Скрий пълния списък с назначения

contracts_for_invoice = За фактура

contracts_list_invoices_total = Стойност (с ДДС)
contracts_list_invoices_paid = Платена сума
contracts_list_invoices_not_paid = За плащане

#Help SECTION for label info 

help_file_locations = Пътят на избраните файлове задължително трябва да започва с <strong>%s</strong> или с <strong>\\</strong>
help_contracts_notes = 
help_contracts_name = 
help_contracts_file_location = 
help_contracts_file_locations = 
help_contracts_file_generated = 
help_contracts_files_generated = 
help_contracts_type = 
help_contracts_prefix = 
help_contracts_project = Проект към договора. Може да се избере, като просто потърсите по име или код проекта, който Ви интересува. Достатъчно е да напишете две букви и ще излязат резултати от търсенето за тези букви.
help_contracts_customer = Контрагент към договора. Може да се избере, като просто потърсите по име или код контрагента, който Ви интересува. Достатъчно е да напишете две букви и ще излязат резултати от търсенето за тези букви.
help_contracts_customer_recipient = 
help_contracts_office = 
help_contracts_employee = 
help_contracts_group = 
help_contracts_department =
help_contracts_types = 
help_contracts_description = 
help_contracts_notes = 
help_contracts_status = Статус на договора
help_contracts_substatus = <b>Състояние: </b>
help_contracts_status_opened = Договорът е със статус <b>"ОТВОРЕН"</b> - позволена е редакция на всички параметри на договора.
help_contracts_status_closed = Договорът е със статус <b>"ЗАТВОРЕН"</b> - параметрите на договора не могат да бъдат редактирани.
help_contracts_status_locked = Договорът е със статус <b>"ЗАКЛЮЧЕН"</b> - могат да бъдат редактирани само част от параметрите на договора.
help_contracts_num = Пореден номер на договора
help_contracts_custom_num = Номер, зададен от контрагента. Обикновено се иползва за връзка към номера в регистъра от договори на контрагента.
help_contracts_type_used = 
help_contracts_view_url = 
help_contracts_date = 
help_contracts_datetime = 
help_contracts_deadline = 
help_contracts_initial_notify = 
help_contracts_relatives = 
help_contracts_parents = 
help_contracts_children = 
help_contracts_parents2 = 
help_contracts_children2 = 
help_contracts_pattern = 
help_contracts_pattern_header = 
help_contracts_pattern_footer = 
help_contracts_add_name = Име на договора, който въвеждате. В името могат да бъдат включени следните променливи, които ще бъдат заместени със съответните им стойности след добавянето:<br /><strong>[customer_name]</strong> - име на клиента<br /><strong>[office_name]</strong> - име на офиса, за който се отнася договорът<br /><strong>[project_name]</strong> - проекта, за който се отнася договорът
help_contracts_add_notes = Забележки, уточнения и др. към договора, който въвеждате. Тук могат да бъдат включени следните променливи, които ще бъдат заместени със съответните им стойности след добавянето:<br /><strong>[customer_name]</strong> - име на клиента<br /><strong>[office_name]</strong> - име на офиса, за който се отнася договорът<br /><strong>[project_name]</strong> - проекта, за който се отнася договорът
help_contracts_add_description = Описание на договора, който въвеждате. В описанието могат да бъдат включени следните променливи, които ще бъдат заместени със съответните им стойности след добавянето:<br /><strong>[customer_name]</strong> - име на клиента<br /><strong>[office_name]</strong> - име на офиса, за който се отнася договорът<br /><strong>[project_name]</strong> - проекта, за който се отнася договорът
help_contracts_add_projects = Можете да добавите нов проект, който директно ще бъде попълнен
help_contracts_add_customers = Можете да добавите нов контрагент, който директно ще бъде попълнен
help_contracts_search_projects = Потърсете нужния проект и го изберете за този договор
help_contracts_search_customers = Потърсете нужния контрагент и го изберете за този договор
help_contracts_add_type = 
help_contracts_add_employee = 
help_contracts_add_filter = 
help_contracts_check_all_caption = Маркиране / размаркиране
help_contracts_check_all_text = Маркиране / размаркиране на всички полета за заместване на колони
help_contracts_separator = Разделител на данните за csv формат

help_contracts_search_customers = Търсене/Избор на контрагент

warning_contracts_change_status_not_all = Статусът на някои %s не беше сменен, защото смяната на статуса е невалидна, имат неприключени мини задачи или нямате права за смяна на статус.
message_contracts_multistatus_success = Успешна промяна на статус на %s!
error_contracts_multistatus_failed = Неуспешна промяна на статус на %s!
error_contracts_multiprint_failed = Грешка при множествен печат на %s
error_contracts_print_invalid_pattern = Не е избран шаблон или е избран несъществуващ шаблон

message_contracts_annulment_annex_success = Успешно прекратяване на споразумение
error_contracts_annulment_annex = Грешка при прекратяване на споразумение
error_contracts_empty_cd_reason = Моля, изберете основание за издаване на всяко Кредитно/Дебитно известие!

error_contracts_date_end_subtype = %s не може да е след %s!

message_contracts_add_notices = Ще бъдат генерирани финансови документи.<br />Натиснете &quot;OK&quot;, за да потвърдите!
message_contracts_invoices_amount = Ще бъдат генерирани следните ФАКТУРИ.
message_contracts_debits_amount = Ще бъдат генерирани следните ДЕБИТНИ известия.
message_contracts_credits_amount = Ще бъдат генерирани следните КРЕДИТНИ известия.
message_contracts_email_for_sent_invoices = """Стартирана е процедура по изпращане на генерираните финансови документи на контрагентa.
След приключването ѝ ще бъдете уведомени чрез e-mail."""
message_contracts_final_template_issued = Фактура от финалния шаблон беше издадена успешно!

error_contracts_date_start_validity = Датата на влизане в сила на договора не трябва да е след датата на изтичане на договора!
error_change_date_validity_end_subtype = Не може да променяте %s с временно действащо споразумение!
error_contracts_missing_final_template = За този договор липсва финален шаблон
error_contracts_final_template_issue_failed = Грешка при издаване на фактура от финалния шаблон!
error_non_current_article_over_invoiced = Системата предвижда надфактуриране за %s според настройките в шаблоните!
error_contracts_negative_sysfit = Грешка! Системата изчисли отрицателен финален шаблон! Моля проверете авансовите шаблони за фактуриране!
error_contracts_templates_invalid_dates = Грешка! Има шаблони за фактури с некоректни дати!
error_contracts_select_all_patterns = Моля, изберете шаблони за известяване/печат за всички видове записи!
error_contracts_select_all_reasons = Моля, изберете причина за издаване на всички кредитни/дебитни известия, избрани за издаване! 

contracts_diff_options = Начин на пресмятане
contracts_diff_options_normal = КИ/ДИ за разликата
contracts_diff_options_credit_all = Анулиране на префактурираното
contracts_diff_options_credit_all_but_now = Анулиране на префактурираното - пълни периоди
contracts_agreement_no_difference = За избрания начин на пресмятане не са намерени разлики между издадените фактури и новия предмет на договора!
error_no_subtype = Записът няма подтип! Моля, свържете се с екипа по поддръжка!
error_multiple_recurrent_templates = Някой от артикулите участва в повече от един шаблон за фактуриране! Моля, свържете се с екипа по поддръжка!
contracts_templates_canceled_period = Фактурата за периода е отказана или издадена авансово!
contracts_invoices_annulled_document = Фактурата е анулирана или няма данни за нея!
error_duplicate_contract_row_key = Уникалните полета (%s) за ред %d се повтарят в редове: %s
error_invalid_company_data = Несъответствие в данните на договора и шаблоните за фактуриране! Моля, изберете наново каса/банкова сметка във всички шаблони!
