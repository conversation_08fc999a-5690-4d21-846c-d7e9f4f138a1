<div id="invoices_templates_configs">
<table id="table_config" width="650" border="0" class="t_borderless">
  <tr>
    <td>
      <fieldset>
      <legend>{#configurator_title#|escape}</legend>
        <table id="table_config_container" align="left" border="0" class="t_borderless">
          <tr>
            <td><label for="config">{help label_content=#configurator_load_save#|escape}</label></td>
            <td>
              {include file='input_combobox.html'
                       name=config
                       options=$configs
                       standalone=true
                       label=#configurator_title#
               }
            </td>
            <td>
              <img src="{$theme->imagesUrl}small/view.png" alt="{#configurator_reload#|escape}" title="{#configurator_reload#|escape}" onclick="return confirmAction('load_config', function(el) {ldelim} manageTmpConfig($('config'), 'load'); {rdelim}, this);" class="pointer" />
              <img src="{$theme->imagesUrl}small/download.png" alt="{#configurator_save#|escape}" title="{#configurator_save#|escape}" onclick="return confirmAction('save_config', function(el) {ldelim} manageTmpConfig($('config'), 'save'); {rdelim}, this);" class="pointer" />
              <img src="{$theme->imagesUrl}small/delete.png" alt="{#configurator_delete#|escape}" title="{#configurator_delete#|escape}" onclick="return confirmAction('delete_config', function(el) {ldelim} manageTmpConfig($('config'), 'delete'); {rdelim}, this);" class="pointer" />
            </td>
          </tr>
        </table>
      </fieldset>
    </td>
  </tr>
</table>
</div>
