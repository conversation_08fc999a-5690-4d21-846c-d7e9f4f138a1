<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        {include file=`$theme->templatesDir`actions_box.html}

        <form name="contracts" action="{$submitLink}" method="post" enctype="multipart/form-data" onsubmit="return calculateBeforeSubmit(this);">
        {*<input type="hidden" name="id" id="id" value="{$contract->get('id')}" />*}
        <input type="hidden" name="model_id" id="model_id" value="{$model_id}" />
        <input type="hidden" name="model_lang" id="model_lang" value="{$contract->get('model_lang')|default:$lang}" />
        {if $contract->get('transform_params')}
          <input type="hidden" name="transform_params" value="{$contract->get('transform_params')|escape}" />
        {/if}
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td class="t_footer"></td>
          </tr>
          <tr>
            <td>
              {assign var='layouts_vars' value=$contract->get('vars')}
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {foreach from=$contract->get('layouts_details') key='lkey' item='layout'}

                {capture assign='type_include_date'}include_{$lkey}{/capture}
                {if $layout.system && (!($lkey == 'system' || preg_match('#^date_(sign|start|end)_subtype$#', $lkey)) || $contract->get($type_include_date) && preg_match('#^date_(sign|start|validity|end)$#', $lkey)) || $layout.view && array_key_exists($layout.id, $layouts_vars)}
                <tr{if !$layout.view || !$layout.visible} style="display: none;"{/if}>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                    </div>
                    <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="contract_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
                      <a name="contract_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                    </div>
                  </td>
                </tr>
                {/if}

                {if $lkey eq 'type'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$contract->get('type_name')|escape|default:"&nbsp;"}
                    <input type="hidden" name="type" id="type" value="{$contract->get('type')}" />
                  </td>
                </tr>
                {elseif $lkey eq 'custom_num'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_custom_num"><label for="custom_num"{if $messages->getErrors('custom_num')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      <input type="text" class="txtbox" name="custom_num" id="custom_num" value="{$contract->get('custom_num')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" maxlength="30" />
                    {else}
                      {$contract->get('custom_num')|escape}
                      <input type="hidden" name="custom_num" id="custom_num" value="{$contract->get('custom_num')|escape}" />
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'name'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{#required#}</td>
                  <td>
                    {if $layout.edit}
                      <input type="text" class="txtbox" name="name" id="name" value="{$contract->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    {else}
                      {mb_truncate_overlib text=$contract->get('name')|escape|default:"&nbsp;"}
                      <input type="hidden" name="name" id="name" value="{$contract->get('name')|escape}" />
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'customer'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_customer"><label for="customer"{if $messages->getErrors('customer')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{#required#}</td>
                  <td>
                    {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
                    {include file=`$theme->templatesDir`input_autocompleter.html
                             name='customer'
                             autocomplete_type='customers'
                             autocomplete_var_type='basic'
                             autocomplete_buttons='add search clear'
                             value=$contract->get('customer')
                             value_code=$contract->get('customer_code')
                             value_name=$contract->get('customer_name')
                             readonly=$ac_readonly
                             width=266
                             standalone=true
                             label=$layout.name
                             help=$layout.description
                    }
                    <span id="branch_container" style="display: {if $contract->get('customer_is_company')}inline{else}none{/if};">
                      {if !$ac_readonly}
                        <span class="help" {help label_content=$contract->getBranchLabels('contracts_branch')|escape text_content=$contract->getBranchLabels('help_contracts_branch')|escape popup_only='1'}>&nbsp;</span>
                        <select name="branch" id="branch" onchange="changeContactPersonsOptions(this, 'contact_person');" class="selbox{if empty($customer_branches)} missing_records{/if}" style="width: 100px!important;" title="{$contract->getBranchLabels('contracts_branch')|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                          {if empty($customer_branches)}
                            <option value="" class="missing_records" selected="selected">[{$contract->getBranchLabels('empty_branch')|escape}]</option>
                          {else}
                            {foreach from=$customer_branches item='customer_branch'}
                              {if (!$customer_branch->isDeleted() && $customer_branch->isActivated()) || $customer_branch->get('id') === $contract->get('branch')}
                              <option value="{$customer_branch->get('id')|escape}"{if $customer_branch->get('id') === $contract->get('branch')} selected="selected"{/if}{if $customer_branch->isDeleted() || !$customer_branch->isActivated()} class="inactive_option" title="{#inactive_option#}">*&nbsp;{else}>{/if}{$customer_branch->get('name')|default:'&nbsp;'}</option>
                              {/if}
                            {/foreach}
                          {/if}
                        </select>
                      {else}
                        <span class="labelbox">{help label_content=$contract->getBranchLabels('contracts_branch')|escape}</span>
                        <span class="branch">
                        {if $contract->get('branch')}
                          <span{if !$contract->get('branch_active')} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$contract->get('branch_name')|escape}</span>
                        {/if}
                        </span>
                        <input type="hidden" name="branch" id="branch" value="{$contract->get('branch')|default:0}" />
                      {/if}
                    </span>
                    <span id="contact_person_container" style="display: {if $contract->get('customer_is_company') && ($layout.edit || $contract->get('contact_person'))}inline{else}none{/if};">
                      {if !$ac_readonly}
                        <span class="help" {help label_content=#contracts_contact_person#|escape text_content=$contract->getBranchLabels('help_contracts_contact_person')|escape popup_only='1'}>&nbsp;</span>
                        <select name="contact_person" id="contact_person" class="selbox{if empty($contact_persons)} missing_records{elseif !$contract->get('contact_person')} undefined{/if}" style="width: 100px!important;" title="{#contracts_contact_person#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                          {if empty($contact_persons)}
                            <option value="" class="missing_records" selected="selected">[{#empty_contact_person#|escape}]</option>
                          {else}
                            <option value="" class="undefined"{if !$contract->get('contact_person')} selected="selected"{/if}>[{#please_select#|escape}]</option>
                            {foreach from=$contact_persons item='contact_person'}
                              {if (!$contact_person->isDeleted() && $contact_person->isActivated()) || $contact_person->get('id') === $contract->get('contact_person')}
                              <option value="{$contact_person->get('id')|escape}"{if $contact_person->get('id') === $contract->get('contact_person')} selected="selected"{/if}{if $contact_person->isDeleted() || !$contact_person->isActivated()} class="inactive_option" title="{#inactive_option#}">*&nbsp;{else}>{/if}{$contact_person->get('name')|default:'&nbsp;'}{if $contact_person->get('lastname')} {$contact_person->get('lastname')|default:'&nbsp;'}{/if}</option>
                              {/if}
                            {/foreach}
                          {/if}
                        </select>
                      {else}
                        <span class="labelbox">{help label_content=#contracts_contact_person#|escape}</span>
                        <span class="contact_person">
                        {if $contract->get('contact_person')}
                          <span{if !$contract->get('contact_person_active')} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$contract->get('contact_person_name')|escape}</span>
                        {/if}
                        </span>
                        <input type="hidden" name="contact_person" id="contact_person" value="{$contract->get('contact_person')|default:0}" />
                      {/if}
                    </span>
                  </td>
                </tr>
                {elseif $lkey eq 'trademark'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_trademark"><label for="trademark"{if $messages->getErrors('trademark')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
                    {include file=`$theme->templatesDir`input_autocompleter.html
                             name='trademark'
                             autocomplete_type='nomenclatures'
                             autocomplete_var_type='basic'
                             autocomplete_buttons='search clear'
                             value=$contract->get('trademark')
                             value_name=$contract->get('trademark_name')
                             readonly=$ac_readonly
                             width=244
                             standalone=true
                             label=$layout.name
                             help=$layout.description
                    }
                  </td>
                </tr>
                {elseif $lkey eq 'project'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_project"><label for="project"{if $messages->getErrors('project')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
                    {include file=`$theme->templatesDir`input_autocompleter.html
                             name='project'
                             autocomplete_type='projects'
                             autocomplete_var_type='basic'
                             autocomplete_buttons='add search clear'
                             value=$contract->get('project')
                             value_code=$contract->get('project_code')
                             value_name=$contract->get('project_name')
                             readonly=$ac_readonly
                             width=266
                             standalone=true
                             label=$layout.name
                             help=$layout.description
                    }
                  </td>
                </tr>
                {elseif $lkey eq 'company'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_company"><label for="company"{if $messages->getErrors('company')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$contract->get('company_name')|escape}
                    <input type="hidden" name="company" id="company" value="{$contract->get('company')|default:0}" />
                  </td>
                </tr>
                {elseif $lkey eq 'office'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_office"><label for="office"{if $messages->getErrors('office')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{#required#}</td>
                  <td>
                    {if $layout.edit}
                      <div style="width: 200px;">
                        {include file='input_dropdown.html'
                                 name='office'
                                 custom_id='office'
                                 label=$layout.name
                                 help=$layout.description
                                 value=$contract->get('office')
                                 options=$offices
                                 required=1
                                 really_required=1
                                 width=200
                                 onchange="updateAvailableQuantities($('company').value + '_' + this.value + '_bank_0')"
                                 standalone=true
                        }
                      </div>
                    {else}
                      {$contract->get('office_name')|escape}
                      <input type="hidden" name="office" id="office" value="{$contract->get('office')|default:0}" />
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'employee'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_employee"><label for="employee"{if $messages->getErrors('employee')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      {include file=`$theme->templatesDir`input_autocompleter.html
                               name='employee'
                               autocomplete_type='customers'
                               stop_customer_details=1
                               autocomplete_var_type='basic'
                               autocomplete_buttons='search clear'
                               value=$contract->get('employee')
                               value_name=$contract->get('employee_name')
                               filters_array=$autocomplete_employee_filters
                               width=244
                               standalone=true
                               label=$layout.name
                               help=$layout.description
                      }
                    {else}
                      {$contract->get('employee_name')|escape}
                      <input type="hidden" name="employee" id="employee" value="{$contract->get('employee')|default:0}" />
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'date_sign' && $contract->get('include_date_sign')}
                <tr class="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_date_sign"><label for="date_sign"{if $messages->getErrors('date_sign')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      {include file="input_formula.html"
                        standalone=true
                        source=date
                        name='date_sign'
                        formulas=$contract->get('formulas')
                        label=$layout.name
                        help=$layout.description
                        value=$contract->get('date_sign')
                        formula_value=$contract->get('date_sign_formula')
                        width=220
                        show_calendar_icon=false}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'date_start' && $contract->get('include_date_start')}
                <tr class="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_date_start"><label for="date_start"{if $messages->getErrors('date_start')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      {include file="input_formula.html"
                        standalone=true
                        source=date
                        name='date_start'
                        formulas=$contract->get('formulas')
                        label=$layout.name
                        help=$layout.description
                        value=$contract->get('date_start')
                        formula_value=$contract->get('date_start_formula')
                        width=220
                        show_calendar_icon=false}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'date_validity' && $contract->get('include_date_validity')}
                <tr class="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_date_validity"><label for="date_validity"{if $messages->getErrors('date_validity')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      {include file="input_formula.html"
                        standalone=true
                        source=date
                        name='date_validity'
                        formulas=$contract->get('formulas')
                        label=$layout.name
                        help=$layout.description
                        value=$contract->get('date_validity')
                        formula_value=$contract->get('date_validity_formula')
                        width=220
                        show_calendar_icon=false}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'date_end' && $contract->get('include_date_end')}
                <tr class="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_date_end"><label for="date_end"{if $messages->getErrors('date_end')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      {include file="input_formula.html"
                        standalone=true
                        source=date
                        name='date_end'
                        formulas=$contract->get('formulas')
                        label=$layout.name
                        help=$layout.description
                        value=$contract->get('date_end')
                        formula_value=$contract->get('date_end_formula')
                        width=220
                        show_calendar_icon=false}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'referers'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_referers"><label for="referers"{if $messages->getErrors('referers')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {if $layout.edit}
                    <input type="hidden" name="update_relatives" id="update_relatives" value="1" />
                    <div id="referers">
                      <div id="toggleCheckboxes" style="width: 300px; display: {if is_array($contract->get('referers')) && $contract->get('referers')|@count gt 4}block{else}none{/if};">
                        <span onclick="toggleCheckboxes(this, 'referers', true)" class="pointer">{#check_all#|escape}</span> |
                        <span onclick="toggleCheckboxes(this, 'referers', false)" class="pointer">{#check_none#|escape}</span>
                      </div>
                      {if $contract->get('referers')}
                        {foreach name='i' from=$contract->get('referers') key='ref_id' item='ref'}
                          <input type="checkbox" name="referers[]" id="ref{$ref.id}" value="{$ref.id}" checked="checked" /><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$ref_id}" target="_blank">{if $ref.num}[{$ref.num}]{else}<i>{#contracts_unfinished_contract#|escape}</i>{/if}&nbsp;{$ref.name|escape}</a><br />
                        {/foreach}
                      {/if}
                    </div>
                    <button type="button" name="filterButton" class="button" onclick="var popUrl='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$module}=filter&amp;open_from={$module}&amp;customer=' + $('customer').value; pop(popUrl, 820, 580);">{#link#|escape}...</button>
                    {else}
                      {if $contract->get('referers')}
                        {foreach name='i' from=$contract->get('referers') key='ref_id' item='ref'}
                          {$smarty.foreach.i.iteration}. <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$ref_id}" target="_blank">{if $ref.num}[{$ref.num}]{else}<i>{#contracts_unfinished_contract#|escape}</i>{/if}&nbsp;{$ref.name|escape}</a><br />
                        {/foreach}
                      {/if}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'description'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                  {if $layout.edit}
                    <textarea class="areabox doubled" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$contract->get('description')|escape}</textarea>
                  {else}
                    {$contract->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                    <textarea class="areabox doubled" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="display: none;">{$contract->get('description')|escape}</textarea>
                  {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'notes'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_notes"><label for="notes"{if $messages->getErrors('notes')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                  {if $layout.edit}
                    <textarea class="areabox doubled" name="notes" id="notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$contract->get('notes')|escape}</textarea>
                  {else}
                    {$contract->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                    <textarea class="areabox doubled" name="notes" id="notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="display: none;">{$contract->get('notes')|escape}</textarea>
                  {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'department'}
                <tr id="contract_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                  <td class="labelbox"><a name="error_department"><label for="department"{if $messages->getErrors('department')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $layout.edit}
                      <select class="selbox{if !$contract->get('department')} undefined{/if}" name="department" id="department" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);" title="{$layout.name|escape}">
                        <option value="" class="undefined">[{#please_select#|escape}]</option>
                        {foreach from=$departments item='item'}
                          {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $contract->get('department')}
                          <option value="{$item->get('id')}"{if $item->get('id') eq $contract->get('department')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{if $item->isDeleted() || !$item->isActivated()}*&nbsp;{/if}{$item->get('name')|indent:$item->get('level'):"-"}</option>
                          {/if}
                        {/foreach}
                      </select>
                    {else}
                      {foreach from=$departments item='item'}
                        {if $item->get('id') eq $contract->get('department')}
                          {$item->get('name')|indent:$item->get('level'):"-"}
                          <input type="hidden" name="department" id="department" value="{$item->get('id')}" />
                        {/if}
                      {/foreach}
                    {/if}
                  </td>
                </tr>
                {elseif $layout.view && array_key_exists($layout.id, $layouts_vars)}
                <!-- Contract Additional Vars -->
                {assign var='layout_id' value=$layout.id}
                {assign var='vars' value=$layouts_vars.$layout_id}
                {if $layout.id}
                <tr id="layout_{$layout.id}_box"{if $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="nopadding" colspan="3">
                    <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                {/if}
                {foreach name='j' from=$vars item='var'}
                  {if $var.type eq 'gt2'}
                    <tr><td colspan="3">
                    {if $layout.edit}
                      {include file=`$theme->templatesDir`_gt2_edit.html table=$var}
                    {else}
                      {include file=`$theme->templatesDir`_gt2_view.html table=$var}
                    {/if}
                    </td></tr>
                  {elseif $var.type}
                    {capture assign='info'}{if $var.help}{$var.help}{else}{$var.label}{/if}{/capture}
                    {if $layout.edit}
                      {* var=$var SHOULD BE REMOVED LATER *}
                      {include file="input_`$var.type`.html"
                        var=$var
                        standalone=false
                        var_id=$var.id
                        name=$var.name
                        custom_id=$var.custom_id
                        label=$var.label
                        help=$var.help
                        back_label=$var.back_label
                        back_label_style=$var.back_label_style
                        value=$var.value
                        value_id=$var.value_id
                        options=$var.options
                        optgroups=$var.optgroups
                        option_value=$var.option_value
                        first_option_label=$var.first_option_label
                        onclick=$var.onclick
                        on_change=$var.on_change
                        sequences=$var.sequences
                        check=$var.check
                        scrollable=$var.scrollable
                        calculate=$var.calculate
                        readonly=$var.readonly
                        source=$var.formula_type|default:$var.source
                        onchange=$var.onchange
                        map_params=$var.map_params
                        width=$var.width
                        hidden=$var.hidden
                        really_required=$var.required
                        required=$var.required
                        disabled=$var.disabled
                        formulas=$contract->get('formulas')
                        indexes=$contract->get('indexes')
                        formula_value=$var.formula
                        include_index=$var.include_index
                        index_value=$var.index
                        index_date=$var.index_date
                        index_formula=$var.index_formula
                        options_align=$var.options_align
                        autocomplete=$var.autocomplete
                        js_methods=$var.js_methods
                        restrict=$var.js_filter
                        deleteid=$var.deleteid
                        show_placeholder=$var.show_placeholder
                        text_align=$var.text_align
                        custom_class=$var.custom_class
                      }
                    {else}
                      {include file="view_`$var.type`.html"}
                      {if $contract->get('transform_params')}
                      {include file="input_`$var.type`.html"
                        var=$var
                        standalone=false
                        var_id=$var.id
                        name=$var.name
                        custom_id=$var.custom_id
                        label=$var.label
                        help=$var.help
                        back_label=$var.back_label
                        back_label_style=$var.back_label_style
                        value=$var.value
                        value_id=$var.value_id
                        options=$var.options
                        optgroups=$var.optgroups
                        option_value=$var.option_value
                        first_option_label=$var.first_option_label
                        onclick=$var.onclick
                        on_change=$var.on_change
                        sequences=$var.sequences
                        check=$var.check
                        scrollable=$var.scrollable
                        calculate=$var.calculate
                        readonly=$var.readonly
                        source=$var.formula_type|default:$var.source
                        onchange=$var.onchange
                        hidden=1
                        hidden_all=1
                        really_required=$var.required
                        required=$var.required
                        width=$var.width
                        disabled=$var.disabled
                        formulas=$contract->get('formulas')
                        indexes=$contract->get('indexes')
                        formula_value=$var.formula
                        include_index=$var.include_index
                        index_value=$var.index
                        index_date=$var.index_date
                        index_formula=$var.index_formula
                        options_align=$var.options_align
                        autocomplete=$var.autocomplete
                        js_methods=$var.js_methods
                        restrict=$var.js_filter
                        deleteid=$var.deleteid
                        show_placeholder=$var.show_placeholder
                        text_align=$var.text_align
                        custom_class=$var.custom_class
                      }
                      {assign var=hidden_all value=0}
                      {/if}
                    {/if}
                  {/if}
                {/foreach}
                {if $layout.id}
                    </table>
                  </td>
                </tr>
                {/if}
                {/if}
              {/foreach}
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3" style="padding: 10px;">
                    {strip}
                      {if $contract->get('buttons')}
                        {foreach from=$contract->get('buttons') item='button'}
                          {include file=`$theme->templatesDir`input_button.html
                                  label=$button.label
                                  standalone=true
                                  name=$button.name
                                  source=$button.source
                                  disabled=$button.disabled
                                  hidden=$button.hidden
                                  width=$button.width
                                  height=$button.height}
                        {/foreach}
                      {/if}
                      <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>
                      {include file=`$theme->templatesDir`cancel_button.html}
                    {/strip}
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
        {include file=`$theme->templatesDir`help_box.html}
        {include file=`$theme->templatesDir`system_settings_box.html object=$contract exclude='is_portal'}
        {include file=`$theme->templatesDir`after_actions_box.html}
        </form>
      </div>
    </td>
  </tr>
</table>