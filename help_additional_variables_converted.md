# Описание на използване и вписване на допълнителни променливи към модели за nZoom 1.7.x

Допълнителните променливи като типове се описват в таблиците:

- **`_fields_meta`** – описва езиково независима информация за променливите
- **`_fields_i18n`** – описват се етикетите, help, описание за променливите на съответните езици и може да се добавя заден етикет
- **`_fields_options`** – изброимите елементи на променливи от тип dropdown, radio, checkbox_group
- **`layouts`** – информация за секциите (layouts) на променливи – със съответните етикет и позиция

Стойностите на допълнителните променливи се съхраняват в таблици `[име на модели]_cstm`, като `documents_cstm`, `customers_cstm` ..., с изключение на стойностите за променливи от тип:
- **gt2** - съхраняват се в `gt2_details`
- **bb** – в `bb`
- за конфигуратор тип Франкенщайн – в `configurator`

---

## I. Описание на таблиците с техните полета

### 1. Таблица `_fields_meta`:

#### 1.1 `id`
autoincrement, служи за релация с `_fields_i18n` (поле `parent_id`) и `[…]_cstm` (поле `var_id`), не се попълва, приема поредна стойност автоматично!

#### 1.2 `model`
име на модела, Например: **Customer** (за контрагенти), **Document** (за документи), **Documents_Stage** (за етапи на документи), **Tasks** (за задачи – използва се само за добавяне на бутони за справка) и т.н.

#### 1.3 `model_type`
тип на модела, число, отговаря на ID-то на типа на модела, Например: **1** за тип документи с ID 1

#### 1.4 `name`
име на променливата на латиница, уникално за модела и типа на модела, Например: **offer_description**

При трансформиране има възможност за копиране на допълнителни променливи към основни. Името на допълнителната променлива трябва да е от вида **`basic_[името на основната променлива]`**, например **`basic_customer`** се копира към основна променлива **`customer`**, **`basic_department`** се копира към основна променлива **`department`**.

За копиране на назначения при трансформиране трябва да имаме допълнителна променлива **`basic_department`** за разпределяне и променлива **`basic_assignments_owner`** за изпълнители. Възможно е да се копират и другите назначения като променливите да са **`basic_assignments_responsible`**, **`basic_assignments_observer`**, **`basic_assignments_decision`**.

Възможно е и обратното копиране – на основни към допълнителни променливи при трансформация. Името на допълнителната променлива трябва да е от вида **`source_[името на основната променлива]`**, например в **`source_customer`** се копира стойността на основната променлива **`customer`**.

Ако в конкретната инсталация се използва Астериск и полето е от тип text, променливата може да се настрои, така че да се показва от nZoom като телефонен номер, който може да бъде набран. За целта името на променливата трябва да има следния формат:
- **`asteriskcall_phone_[име на променливата]`** - за телефон
- **`asteriskcall_gsm_[име на променливата]`** - за мобилен телефон
- **`asteriskcall_fax_[име на променливата]`** - за факс

#### 1.5 `source`
в полето се записват множество различни настройки във формат: **име := стойност**. Някои от тях са специфични за даден вид променлива и са обяснени в следващите точки.

#### 1.6 `validate`
в полето се залагат функция за вторична валидация (server-side) на променливата и аргументи, както и филтри за валидация в клиентския браузър (client-side).

##### Валидация, изпълнявана на сървъра (след submit)

Например:
```
method := function
$arg1 := argument1
$arg2 := argument2
```

Могат да се ползват запазени променливи (\$lt, \$gt ...) и запазени стойности (added, modified, now ...).

В метода **compareVar** могат да се ползват за сравнение стойности от други полета в аргументите. Например, за да се валидира, че стойността на текущата променлива е по-голяма от тази в поле с име field_txt се записва **\$gt := \$field_txt** (поставя се знак \$ пред името на полето, за да е ясно, че стойността ще се определя динамично).

##### Методи за валидация:

**Важно**: всички методи за валидация изпълняват само за попълнени стойности в полето.
**Важно**: всички методи за валидация са предвидени да работят и за групови таблици (масиви). Ако само един ред (елемент) в груповата таблица (масива) не е валиден, то цялата таблица (масив) се смята за невалидна.

| Метод | Описание |
|-------|----------|
| **validEmail** | валидация на e-mail адрес |
| **isFakeEmail** | проверява дали e-mail адрес е „фалшив" (започва с fake_ или с цифри: fake01_) |
| **validUrl** | валидация на уеб адрес |
| **validDateTime** | валидация на дата и час |
| **validDate** | валидация на дата |
| **validTime** | валидация на час като част от дата (00:00 – 23:59) |
| **notEmpty** | валидация за попълнена стойност (изпълнява се служебно ако полето е задължително) |
| **notEmptyFile** | валидация за попълнена стойност на поле от тип file_upload |
| **compareVar** | валидация чрез сравнение спрямо зададена стойност или спрямо стойността на друга променлива |
| **isValidNumber** | валидация за число |
| **validPassword** | валидация за парола (дължина, специални символи и др.) |
| **validUserName** | валидация за потребителско име (позволени символи, дължина и др.) |
| **validUCN** | валидация за ЕГН (единен граждански номер) |
| **validPFN** | валидация за ЛНЧ (личен номер на чужденец) |
| **validEIK** | валидация за ЕИК (единен идентификационен код) |
| **validInDDS** | валидация на ИН по ДДС (чрез обръщение към външен източник) |
| **regexpCompare** | валидира стойността спрямо регулярен израз |
| **validateConditionalRequired** | валидация за попълнена стойност, когато друго поле отговаря на зададено условие |

##### Запазени променливи (аргументи) за метод compareVar:

| Променлива | Описание |
|------------|----------|
| **\$lt** | по-малко (less than) |
| **\$le** | по-малко или равно (less than or equal) |
| **\$gt** | по-голямо (greater than) |
| **\$ge** | по-голямо или равно (greater than or equal) |
| **\$eq** | равно (equal) |
| **\$ne** | различно от (not equal) |

Аргументи \$lt и \$gt могат да се ползват и за метод **isValidNumber**.

##### Запазени стойности за метод compareVar:

| Стойност | Описание |
|----------|----------|
| **now** | сравнява се с текуща дата и час в ISO формат (YYYY-MM-DD HH:ii:ss) |
| **added** | сравнява се със стойността на поле "Добавен на" за текущия запис |
| **modified** | сравнява се със стойността на поле "Променен на" за текущия запис |

##### Примери за валидация:

Валидация на поле за валиден и-мейл адрес:
```
method := validEmail
```

Валидация за попълнена числена стойност:
```
method := isValidNumber
```

Валидация за попълнена числена стойност, която да е по-голяма от твърдо зададена стойност:
```
method := isValidNumber
$gt := 5
```

Валидация попълнената стойност да е по-малка от стойността на друго поле и по-голяма от текущата дата и час:
```
method := compareVar
$lt := $another_field_name
$gt := now
```

Валидация спрямо регулярен израз – поне 3 съседни символа на кирилица/латиница:
```
method := regexpCompare
regexp := /[a-zа-я]{3,}/iu
```

**Желателно** е за променливата да се настрои help текст, който пояснява какви стойности се очакват.

**ВАЖНО**: За настройване на условие чрез регулярен израз се обръщайте към разработчик или към документацията в php.net.

##### Валидация за условно задължително поле:

Валидация за попълнена стойност, когато друго поле отговаря на зададено условие:

Приема 2 параметъра: **condition_field** за име на полето и **condition_value** за стойности, спрямо които се сравнява.

Проверяваното поле може да е основна или допълнителна променлива, задава се по един и същ начин (без префикс (a_/b_/\$) и без суфикс за номер на ред), например: customer, deadline, article_deliverer, any_other_name.

**а)** текущото поле е задължително, когато друго поле има попълнена каква да е стойност:
```
method := validateConditionalRequired
condition_field := other_field_name
```

**б)** текущото поле е задължително, когато друго поле няма попълнена стойност:
```
method := validateConditionalRequired
condition_field := other_field_name
condition_value :=
```

**в)** текущото поле е задължително, ако друго поле има някоя от изброените стойности:
```
method := validateConditionalRequired
condition_field := other_field_name
condition_value := 212,213
```

**г)** ако и текущото, и другото поле се намират в групова таблица, може да се сравнява поредово:
```
method := validateConditionalRequired
condition_field := other_field_name
condition_value := 212,213
per_row := 1
```

##### Валидация, изпълнявана в клиентския браузър

Във **validate** полето на променливите могат да се дефинират JavaScript филтри за валидация, която се извършва в клиентския браузър. Синтаксисът е:

```
js_filter := име_на_филтъра
```

алтернативно, когато има параметри се слагат и единични кавички:
```
js_filter := 'име_на_филтъра|параметър:<стойност параметър>'
```

Тези филтри се изпълняват при събитието **onkeypress**. Ако е зададен филтър и едновременно с това за onkeypress е подадена функция, то тя няма да се изпълни, а ще се изпълни само филтърът.

##### JavaScript филтри:

| Филтър | Описание |
|--------|----------|
| **lettersToUpperCase** | замества въведените малки букви със съответните им главни |
| **digitsToX** | замества всяка въведена цифра с X |
| **umlautsToASCII** | прави заместване на символи (ä→ae, ö→oe, ü→ue) |
| **cancelDigits** | не разрешава въвеждането на цифри |
| **insertOnlyDigits** | разрешава въвеждането само на цифри |
| **insertOnlyFloats** | разрешава въвеждането само на цифри и еднократно въвеждане на десетична точка |
| **insertOnlyNegativeFloats** | разрешава въвеждането само на цифри, десетична точка и задължително минус в началото |
| **insertOnlyReals** | разрешава въвеждането само на цифри, десетична точка и минус в началото |
| **insertOnlyPositiveIntegers** | разрешава въвеждането само на цели положителни числа (без 0) |
| **cancelEnter** | не разрешава натискането на ENTER |

За **insertOnlyFloats**, **insertOnlyNegativeFloats**, **insertOnlyReals** възможно е подаване на допълнителен параметър за брой на цифрите след десетичния знак:
```
js_filter := 'insertOnlyFloats|max_decimals:2'
```

#### 1.7 `type`
тип на променливата ('text', 'textarea', 'dropdown', 'radio', 'checkbox_group', 'date', 'datetime', 'group', 'gt2', 'config', 'button', 'table', 'bb', 'autocompleter', 'time', 'file_upload', 'formula', 'map')

##### 1.7.1 тип `text`
обикновено едноредово текстово поле за попълване

##### 1.7.2 тип `textarea`
многоредово текстово поле за попълване

За типове **text** и **textarea** може да се настрои етикетът (label) или пояснителният текст (help) на променливата да се показва като помощен текст в полето, когато то няма стойност, като в source се запише:
```
show_placeholder := label
show_placeholder := help
```

##### 1.7.3 тип `dropdown`
падащо меню, даващо възможност за еднозначен избор. Ако полето е задължително (required = 1), то няма подканващ текст [моля изберете]. Ако полето е без никакви добавени избори (опции), в него се изписва [няма данни].

##### 1.7.4 тип `radio`
тип контрола, даваща възможност за алтернативен избор – избира се винаги само една стойност

##### 1.7.5 тип `checkbox_group`
тип контрола даваща възможност за множествен избор – могат да се избират повече от една стойности

##### 1.7.6 тип `date`
тип контрола, даваща възможност за избор на дата (без час). До контролата има малка иконка "календарче", която подпомага избора. Ако полето е readonly = 1, контролата не дава възможност за "ръчно" сменяне на датата. В такъв случай датата се изчиства като се щракне два пъти (double click) в това поле.

##### 1.7.7 тип `datetime`
тип контрола, даваща възможност за избор на дата И ЧАС. До контролата има малка иконка "календарче", която подпомага избора. Ако полето е readonly = 1, контролата не дава възможност за "ръчно" сменяне на датата. В такъв случай датата се изчиства като се щракне два пъти (double click) в това поле.

##### 1.7.8 тип `group`
не е контрола и служи за променливи, които групират променливи от една и съща група (таблица). Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите. Тази променлива служи и за пренасяне на цялата групова информация в генерирания PDF анблок.

###### Запазени конфигурации
За да могат да се записват шаблони (запазени конфигурации) за групова променлива, в полето source на обединяващата променлива (от тип group) записваме:
```
configurator_group := 1
```

Над груповата таблица се появява панел за зареждане, добавяне, записване и изтриване на шаблони за конфигурации. Запазените конфигурации могат да се зареждат във всеки документ от съответния тип.

###### Скриване на +/- бутони
Добавена е възможност за скриване на бутоните за добавяне и премахване на редове при груповите променливи (таблици):
```
hide_multiple_rows_buttons := 1
```

###### Не-копиране на стойности при добавяне на ред
По подразбиране при добавяне на нов ред стойностите на променливите в по-горния ред се прехвърлят и на новия. Ако обаче искаме полетата в новодобавения ред да са празни (без стойности), може да добавим в source полето на груповата таблица:
```
dont_copy_values := 1
```

Възможно е да се зададе да не се прехвърлят данните само за някои полета:
```
dont_copy_values := participant_id, participant_name
```

##### 1.7.9 тип `gt2`
не е контрола и служи за променливи, които групират променливи от една и съща група (таблица) от 2-ри вид (ново поколение). Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите.

###### Настройки за работа и печат
За всеки тип модел има индивидуални настройки за работа и печат, които са достъпни през интерфейса.

###### Запазени конфигурации
За да могат да се записват шаблони (запазени конфигурации) за групова променлива, в полето source на обединяващата променлива (от тип gt2) записваме:
```
configurator_group := 1
```

###### Копиране стойност на поле при превод
С настройка **auto_translate_vars** могат да се укажат полета, които са езиково зависими, които настройващият инсталацията иска да манипулира като езиково независими:
```
auto_translate_vars := free_text1,article_alternative_deliverer_name,article_description
```

###### Копиране на стойности при добавяне на ред
По подразбиране при добавяне на нов ред той се добавя с празни стойности. Ако обаче искаме стойностите на променливите в по-горния ред се прехвърлят и на новия:
```
copy_values := 1
copy_values := article_id, article_name, article_measure_name, article_description
```

###### Обединяване на редове по зададен ключ
Има възможност за обединяване на редове при изрично зададени настройки:
```
merge_rows_by_unique_columns := article_id, price, article_measure_name
merge_rows_sum_columns := quantity
merge_rows_concat_columns := article_description
```

###### Сортиране на таблицата по колони след запис
Добавена е възможност да се сортира груповата таблица по зададени колони:
```
sort_by := <var1> [ASC|DESC] [algorithm], <var2> [ASC|DESC] [algorithm]
```

Примери:
```
sort_by := skills_app ASC, skills_ass DESC
sort_by := exam_results ASC strcmp, kind_training ASC
```

###### Разрешаване на отрицателни цени
```
allow_negative_price := 1
```

**ВАЖНО**: Това НЕ Е ПРЕПОРЪЧИТЕЛНО да се прави за финансови/складови документи!

##### 1.7.10 тип `config`
не е контрола и аналогично на променлива от тип group служи за променливи, които групират променливи от една и съща група (таблица). Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите.

##### 1.7.11 тип `button`
тип контрола, даваща възможност за задействане на някаква функционалност (изчисление, пренасочване, промяна в текущата форма и др.).

За бутоните могат да се задават position и layout_id, като бутонът ще се появи в съответния layout (секция), ако му е зададена такава. Ако се зададе 0 за layout_id, бутонът се показва най-отдолу на формата преди стандартните бутони за запис и отказ.

###### Последователност на изчисленията
Настройката задава последователността на изпълнение на изчисления на полетата във формата:
```
sequences := doc_total1, doc_text1
```

###### Незапускане на изчисления при запис
Възможно е изчисленията да не се пускат автоматично:
```
sequences := doc_total1, doc_text1
do_not_execute_on_save := 1
```

###### Изпълнение на javascript код при задействане на бутон
```
onclick := if confirm('Сигурни ли сте. че искате да запишете данните') this.form.submit();
```

###### Линк бутон (за пренасочване)
Настройка **href** дава възможност за директно влизане от текущия запис в посочен линк:
```
href := nomenclatures&nomenclatures=list&type=5&type_section=
href := reports&reports=generate_report&report_type=free_days_report
href := nomenclatures&nomenclatures=view&view=a_delivered_article_id
```

###### Цел на линк бутон
```
target := _self
target := _blank
target := _lightbox
lightbox_width := 800
lightbox_height := 600
lightbox_background_color := #F1F1F1
```

##### 1.7.12 тип `table`
също не е контрола и аналогично на променлива от тип group служи за променливи, които групират променливи от една и съща група (таблица), НО САМО ЗА ЕДИН РЕД. Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите.

##### 1.7.13 тип `bb`
не е контрола и служи за променливи, които групират променливи в ББ.

##### 1.7.14 тип `autocompleter`
тип контрола, даваща възможност за въвеждане на стойност чрез избор от аутокъмплийтър

Може да се настрои етикетът (label) или пояснителният текст (help) на променливата да се показва като помощен текст в полето, когато то няма стойност:
```
show_placeholder := label
show_placeholder := help
```

##### 1.7.15 тип `time`
тип контрола, даваща възможност за въвеждане на час

##### 1.7.16 тип `file_upload`
тип контрола, даваща възможност за прикачване на файлове. Важно е да се отбележи, че файлът прикачен през контрола от този тип се записва като прикачен файл към съответния модел.

Допълнителните важни настройки за поле от тип file_upload се пишат в source полето:

```
upload_max_filesize := 5M
forbidden_extensions := php, exe, ico
allowed_extensions := jpg, docx
max_width := 800
max_height := 600
view_mode := thumbnail
thumb_width := 150
thumb_height := 150
list_image_thumbnail := 1
list_image_thumbnail_width := 100
list_image_thumbnail_height := 100
```

##### 1.7.17 тип `formula`
тип контрола, даваща възможност да се избират формули за изчисляване на дадени стойности на база на някакви променливи. Формулите се записват в таблиците _formulas и _formulas_i18n, а променливите са в таблици _variables_meta, _variables_i18n, и _variables_cstm.

```
formula_type := text
formula_type := date
formula_type := index
```

**Важно**: При нужда от използване на формули, моля обърнете се за съдействие към програмистите!!!

##### 1.7.18 тип `map`
тип контрола, която активира Google карта. За това поле не се записва стойност в базата, а се използват данните от други променливи, за да изгради адрес, който да подаде на GoogleMaps.

Всички настройки са задължителни за попълване:

```
address := $a_city, $a_street $a_street_number, Bulgaria
type := button
map_type := satellite
geocoding := normal
static := 1
width := 400
height := 400
target := lightbox
```

#### 1.8 `searchable`
ако има стойност, задава по какъв начин ще може да се търси по допълнителната променлива (text, dropdown, date, datetime, autocompleter, number) в Разширено търсене

#### 1.9 `sortable`
1 - по допълнителната променлива ще може да се сортира, 0 – няма да може

#### 1.10 `required`
0 - незадължително попълване на променливата, 1 – задължително, 2 – задължително, но допуска и числото 0

#### 1.11 `hidden`
1 - скриване на променливата, 0 – показване

#### 1.12 `readonly`
1 - само за четене, 0 – за четене и запис

#### 1.13 `calculate`
0 – не се изчислява полето, 1 – изчислява се и до него се показва бутон за изчисление, 2 – изчислява се без бутон до него, 3 – показва се бутон, но полето не се изчислява автоматично

Задължително се задава readonly 1, когато calculate е по-голямо от 0.

#### 1.14 `bb`
номер на група Бойко Борисов. Стойност, равна на 1, имат всички служебни променливи за bb

#### 1.15 `grouping`
номер на група (таблично показван ред от изброими променливи в таблица), в която участва променливата като масив. Ако стойността на полето е 0, имаме обикновена променлива (не изброима).

#### 1.16 `gt2`
1 – променливата участва в групова таблица от ново поколение, 0 – не участва

#### 1.17 `configurator`
номер на група (таблично показван ред от изброими променливи в таблица), в която участва променливата като масив. Ако стойността на полето е 0, имаме обикновена променлива (не изброима).

#### 1.18 `table`
номер на таблична група за един ред.

#### 1.19 `multilang`
1 – стойностите на променливата са езиково зависими, 0 – не

#### 1.20 `multiadd`
ако е установена в 1, това означава, че тази променлива участва в колоните за множествено добавяне

#### 1.21 `multiedit`
ако е установена в 1, това означава, че тази променлива участва в колоните за множествено редактиране

#### 1.22 `auditable`
1 - да се записват разликите и промените на променливата, 0 - не

#### 1.23 `layout_id`
номер на група (идентификатор на секция), в която е разпределена променливата, съответства на layout_id от layouts

#### 1.24 `position`
позиция (номер) на показване на променливата

#### 1.25 `width`
широчина на контролата, например 200 – в пиксели или 100% - в проценти. Желателно е да се задава в пиксели.

#### 1.26 `width_print`
широчина на контролата при печат.

#### 1.27 `height`
височина на контролата, например 20 – в пиксели или 100% - в проценти.

## 2. Таблица `_fields_i18n`:

#### 2.1 `parent_id`
id на променливата (виж _fields_meta – id)

#### 2.2 `content_type`
тип на съдържанието: 'label' (етикет), 'help' (помещен текст), 'description' (описание) или 'back_label' (заден етикет).

Задължително е за всяка променлива да има поне label.

За back_label може да се зададе стил:
```
back_label_style := width: 200px; height: 14px;
```

#### 2.3 `content`
съдържание на езиковия текст (етикет, помощен текст или описание)

#### 2.4 `lang`
език

## 3. Таблица `_fields_options`:

#### 3.1 `id`
autoincrement, не се задава, получава стойност автоматично!

#### 3.2 `parent_name`
име на променливата, съответства на name от _fields_meta

#### 3.3 `label`
етикет, който се показва за опцията

#### 3.4 `option_value`
стойност на опцията

#### 3.5 `child_name`
име на зависима променлива, съответства на name от _fields_meta

#### 3.6 `extended_value`
разширена стойност за заместване в шаблони за печат

#### 3.7 `position`
позиция на опцията

#### 3.8 `option_active`
1 – опцията е активна, 0 – опцията е неактивна

#### 3.9 `lang`
език

## 4. Таблица `layouts`:

#### 4.1 `layout_id`
номер на група променливи (секция)

#### 4.2 `name`
име (етикет) на групата, езиково зависимо

#### 4.3 `place`
позиция на групата, 0 – скрита

#### 4.4 `lang`
език

---

## II. Зависимости между изчисляемо поле, бутон за изчисление и техните параметри

### А. За променлива от тип "text" ("текстово поле"), което искаме да бъде изчисляемо:

#### 1. Флагът calculate може да приема следните стойности:

- **0** - полето НЕ е изчисляемо, до текстово поле няма да се появи бутон "Изчисляване"
- **1** - полето Е изчисляемо, до текстово поле ще се появи бутон "Изчисляване". При натискане на бутон за запис това поле ще бъде автоматично изчислено.
- **2** - полето Е изчисляемо, с тази разлика, че бутона "Изчисляване" ще бъде скрит
- **3** - полето Е изчисляемо, до текстово поле ще се появи бутон "Изчисляване", но При натискане на бутон за запис това поле НЯМА бъде автоматично изчислено.

**ЗАБЕЛЕЖКА**: ако флагът calculate e 1, флагът readonly на това текстово поле трябва да бъде установен в 1, ако бутонът до полето трябва да се запуска и при натискане на бутон за запис. Ако readonly е 0, то бутонът до полето няма да се запуска при запис.

#### 2. Флаг width
ако широчината на едно изчисляемо поле е 0, то това текстово поле няма да се покаже (бутона "Изчисляване" ще се покаже, само ако calculate e 1)

### Б. Променлива от тип "button" или тип "dropdown", която служи за запускане на всички бутони от формата:

В полето source се указва в каква последователност да се натискат бутоните:

```
sequence := calculate_text1, calculate_text1
```

Бутонът може да се слага в произволна секция (layout), като това се определя от полето layout_id. Ако в полето layout_id за този бутон се сложи 0, то този бутон се разполага от лявата страна на бутона за запис на формата.

**ВНИМАНИЕ**: ако сложите флага hidden със стойност 1 за този бутон, той няма да се покаже въобще, а останалите изчисляеми бутони трябва да са видими. Скритият бутон няма да се показва, но ще присъства във формата и ще предизвиква изчисляване при избор на бутона за запис.

---

## III. Допълнителни променливи при добавянето на нов документ:

В полетата "Име", "Описание" и "Бележки" при въвеждане могат да се ползват следните променливи:

- `[document_num]` - пореден номер на документа
- `[document_sub_num]` - пореден номер на документа по тип
- `[customer_name]` - име на клиента
- `[office_name]` - име на офиса, за който се отнася документа
- `[project_name]` - проекта, за който се отнася документа

Тези променливи могат да се слагат и в "Име по подразбиране" към типове документи.

---

## Описание за използване и вписване на Конфигуратор към модели за nZoom 1.4.x

Конфигураторът е надграждане на вече съществуващата система за допълнителните променливи към модулите. Конфигураторът работи по следният начин:

- ползва се таблиците _fields_meta, _fields_i18n, _fields_options, _fields_layouts, както и всички останали допълнителни променливи.
- По подобие на групите във _fields_meta има отделно поле (configurator), което указва принадлежността на дадена променлива към конкретен конфигуратор.
- Стойноста на това поле е свързано с таблицата configurator, където се записват различни съхранени конфигурации.
- Ако тази стойност е отрицателно число, то допълнителното панелче при визуализация на конфигуратора (изтриване, запаметяване и зареждане) няма да се показва.

### Типове променливи в конфигуратора

Променливите в конфигуратора условно могат да се разделят на 2 вида: **основни** и **второстепенни**.

**Основните променливи** са идентични със стандартните допълнителни променливи и по нищо не се различават по функционалност.

**Второстепенните променливи** са допълнителни променливи по функционалност, но синтаксиса на името им е специфичен.

#### Синтаксис на второстепенните променливи:

```
[име на основна променлива]__[суфикс]
```

Например: При основна променлива: **processor**, второстепенна променлива за цената на процесора би се наричала **processor__price**.

**ЗАБЕЛЕЖКА**: Обърнете внимание на двойнатата подчертаваща линия **__**. Тя е задължителна за разпознаването на второстепенните от основните променливи.

### 1. Изграждане на конфигуратора

#### Вертикално (по редове)
Броят на редовете в таблицата за конфигуратора се определя от броя на основните променливи, които имат една и съща стойност в полето configurator в таблицата _fields_meta.

#### Хоризонтално (по колони)
Броя на колоните в конфигуратора се определя от второстепенните променливи. Суфиксите на различните променливи, обединени в конфигуратор, могат да се разглеждат като имена на колоните в крайната таблица.

### 2. Едноредова таблица тип конфигуратор

Едноредовата таблица се явява частен случай на конфигуратор. В случая когато при създаването на нов конфигуратор дефинираме САМО една основна променлива и всички други са второстепенни на тази основна, то ще се визуализира таблица с 2 реда и брой колони, толкова, колкото са и дефинираните променливи.

### 3. Конфигуратор тип Франкенщайн

Използва всички настройки за конфигуратора. Разликата е, че всички запазени конфигурации се показват таблично под зоната за редактиране на променливи.

Конфигуратора се състои от 3 зони:

1. **Панел за управление** (запазване, изтриване и зареждане) на съхранените ШАБЛОНИ за конфигурациите
2. **Конфигуратор** - таблица, в която са изредени всички променливи
3. **Съхранени конфигурации** - табличка с всички съхранени конфигурации за документа

#### Как се конфигурира Франкенщайн:

1. Прави си обикновен конфигуратор
2. В полето source (от _fields_meta) записвате:
   ```
   fields := var1, var2, var3
   ```
3. В полето source може да се добавя настройка за изчистване на формата:
   ```
   reset_form := 1
   ```

---

## Описание за използване на променлива тип Бойко Борисов (bb)

Променлива тип Бойко Борисов (bb) е обединяваща променлива, в която могат да се включват групови таблици, групови таблици от ново поколение (GT2) и конфигуратори (без конфигуратори от тип franky).

Променлива от тип bb може да се използва само в модули: **Документи** (Documents), **Контрагенти** (Customers) и **Номенклатури** (Nomenclatures).

За да бъде част от bb, една групова променлива или конфигуратор трябва да имат за стойност на bb, равна на 1, по което се обединяват (играе ролята на grouping и configurator).

### Служебни променливи към bb:

#### 1. `bb_elements`
променлива "Тип поле" от тип dropdown:
- за source задаваме: `method := getBBElements`
- за bb номера на bb (например 1)
- за position също 1

Методът извлича имената и номерата на таблиците и конфигураторите, принадлежащи към bb.

#### 2. `bb_group`
обединяваща променлива от новия тип 'bb':
- за тип се задава 'bb'
- за полето bb номера на bb (например 1)
- Въвежда се стойност за label в таблицата _fields_i18n

### Незадължителни променливи за bb:

ПО ЖЕЛАНИЕ могат да се добавят и други незадължителни променливи, които да присъстват в заглавния ред на всеки един вариант от ББ:

- `bb_name` – променлива "Име" от тип text
- `bb_variant` – променлива "Вариант" от тип text
- `bb_value` – променлива "Стойност" от тип text
- `bb_quantity` – променлива "Количество" от тип text
- `bb_all` – променлива "Общо" от тип text
- `bb_note` – променлива "Забележка" от тип textarea

### Примери за калкулации с bb променливи:

За да изчислим полето bb_all като произведение на bb_value и bb_quantity и да сумираме колоната bb_all:

```
array_function := sum
num_rows := bb_value
update_fields := bb_all
$a := request('bb_value')
$b := request('bb_quantity')
equation := $a*$b
```

За да се изчислят полетата bb_all:

```
$a := request('bb_value')
$b := request('bb_quantity')
equation := $a*$b
```

За изчисления в таблица, е необходимо добавянето на променлива с име `[името на таблицата]_calc`:

```
array_function := sum
update_fields := door_1_count
num_rows := door_1_width
$b := request('door_1_width')
equation := $b
```

### Настройване на изчисления

Полетата могат да имат изчисления, чиито параметри се дефинират в **source** колоната. Запазените думи за изчисленията са:

| Ключова дума | Обяснение |
|--------------|-----------|
| **equation** | Израз за изчисление с вече приготвените параметри |
| **array_function** | Агрегатна функция, използвана само в полета, които не са в групова таблица. Има две възможни стойности: **sum** и **avg**. **sum** - сумират се всички стойности на **equation** за редовете от групова таблица, указани в update_fields или num_rows. **avg** – взема средна аретметична стойност за всички редове |
| **update_fields** | Използва се само в полета, които не са в групова таблица. Указва списък от полета от групова таблица, които трябва да се изчислят преди да се направи калкулацията по equation |
| **num_rows** | Указва име на поле от групова таблица, което я обозначава, за да се намерят редовете в таблицата |
| **last_equation** | Израз за изчисление СЛЕД като вече е изчислена агрегатната функция |
| **format** | Низ за форматиране на крайния резултат. За 2 цифри след десетичния знак: %.2F |

#### Параметри - \$а, \$tax, \$customer

Освен запазените думи, може да има и **параметри** участващи в уравнението (equation, last_equation). Параметрите се отбелязват с \$ отпред (например: \$а, \$tax, \$customer) и могат да получават стойности от REQUEST (данни от формата) или SQL (данни от базата).

**Важно**: имената на параметри (заедно с \$) не трябва да се съдържат едно в друго, т.е. недопустимо е да имаме **\$а** (например със стойност 5) и **\$average** (например със стойност 7), защото в equation := **\$a*\$average** ще се замести: equation := **5*5verage**, което ще произведе грешка при изчислението.

**Важно**: стойността получена от изчислението трябва да е валидно число, в противен случай трябва да е в кавички.

#### Пример 1: Просто изчисление без агрегатна функция

Съдържанието на **source** за поле с име **commission**, което се намира в едноредова таблица или конфигуратор:

```
$customer := request('customer')
$total := request('total')
$commission_percentage := sql('SELECT value FROM nom_cstm WHERE var_id=123 AND model_id = $customer')
equation := ($total * $commission_percentage) / 100
format := %.2F
```

**Обяснение:**
- Параметърът \$**customer** получава стойност от формата от ид на контрагента
- Параметърът \$**total** получава стойност от формата от поле с име 'total'
- Параметърът \$**commission_percentage** получава стойност за процента на комисионната от базата от данни
- Част от параметрите участват в уравнението за определяне размера на комисионната
- Резултатът се форматира с 2 цифри след десетичния знак

#### Пример 2: Изчисление с агрегатна функция

Имаме групова таблица с колони **price**, **quantity**, **discount_percentage**, **discount** и **subtotal**:

| price | quantity | discount_percentage | discount (readonly) | subtotal (readonly) |
|-------|----------|-------------------|-------------------|-------------------|
| (въвежда се) | (въвежда се) | (въвежда се) | (изчислява се) | (изчислява се) |

Има едно свободно стоящо поле **tax_percentage**, което съдържа процента на данъка.
Има и още едно свободно стоящо поле **total**, което се използва, за да натрупа общия сбор.

Съдържанието на **source** за поле **discount**:

```
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
equation := $price*$quantity*$discount_perc/100
format := %.2F
```

**Обяснение:**
- Параметърът \$price получава стойност от формата от колоната за цената
- Параметърът \$quantity получава стойност от формата от колоната за количеството
- Параметърът \$discount_perc получава стойност от формата от колоната за процента
- Уравнението equation указва как да се изчисли стойността на отстъпката
- Резултатът се форматира с 2 цифри след десетичния знак

Съдържанието на **source** за поле **subtotal**:

```
$price := request('price')
$quantity := request('quantity')
$discount := request('discount')
equation := $price*$quantity - $discount
format := %.2F
```

**Обяснение:**
- Параметърът \$price получава стойност от формата от колоната за цената
- Параметърът \$quantity получава стойност от формата от колоната за количеството
- **ВНИМАНИЕ**: Параметърът \$discount получава стойност от вече изчислената стойност на полето discount
- Уравнението equation указва как да се изчисли поредовата сума
- Резултатът се форматира с 2 цифри след десетичния знак

Съдържанието на **source** за поле **total** (вариант 1):

```
array_function := sum
update_fields := discount, subtotal
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
$tax_perc := request('tax_percentage')
equation := $price*$quantity*(1-$discount_perc/100)
last_equation := equation*(1 + $tax_perc/100)
format := %.2F
```

**Обяснение:**
- array_function е специална ключова дума, която указва, че в полето ще се сумират стойностите от полета от групова таблица
- update_fields – указва че преди да се направят калкулациите по настоящото поле (total), трябва да се извършат калкулации за отстъпката (discount) и за поредовата сума (subtotal)
- Обърнете внимание, че при наличието на update_fields липсва num_rows
- last_equation указва как към вече сумираните поредови суми да се добави и данъкът

Съдържанието на **source** за поле **total** (вариант 2 с вече изчислен subtotal):

```
array_function := sum
update_fields := discount, subtotal
$tax_perc := request('tax_percentage')
$subtotal := request('subtotal')
equation := $subtotal
last_equation := equation*(1 + $tax_perc/100)
format := %.2F
```

#### Подсигуряване при деление:

```
$a := request('discount_percentage')
$b := request('coef')
equation := ($b!=0 ? $a/$b:0)
```

#### Пример 3: Изчисляване на работените часове

Изчисляване на работените часове между две дати (датите са с дата и час):

```
$ad := request('date')
$bd := request('activity_end')
$at := request('from_hour')
$bt := request('to_hour')
$from := SQL('select cast(concat("$ad", " ", "$at") as datetime) ')
$to := SQL('select cast(concat("$bd", " ", "$bt") as datetime) ')
$ttal := SQL('SELECT TIME_FORMAT(SEC_TO_TIME(TIMESTAMPDIFF(SECOND, "$from", "$to") - TIMESTAMPDIFF(DAY, "$ad", "$bd")*16*60*60), "%H:%i")')
equation := "$ttal"
```

Примера е от инсталация QUEISSER за променлива с ID 1821. Пресмята се колко часа е работено между две дати, като се приема, че работното време за всеки 24 ч. е 8 ч.

**Важно**: полученото трябва да е в кавички, тъй като не е валидно число, а текст.

Съдържанието на **source** за поле **discount**:

```
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
equation := $price*$quantity*$discount_perc/100
format := %.2F
```

Съдържанието на **source** за поле **subtotal**:

```
$price := request('price')
$quantity := request('quantity')
$discount := request('discount')
equation := $price*$quantity - $discount
format := %.2F
```

**ВНИМАНИЕ:** Параметърът \$**discount** получава стойност от вече изчислената стойност на полето discount.

#### Подсигуряване при деление:

```
$a := request('discount_percentage')
$b := request('coef')
equation := ($b!=0 ? $a/$b:0)
```

#### Пример 3: Изчисляване на работените часове

Изчисляване на работените часове между две дати (датите са с дата и час):

```
$ad := request('date')
$bd := request('activity_end')
$at := request('from_hour')
$bt := request('to_hour')
$from := SQL('select cast(concat("$ad", " ", "$at") as datetime) ')
$to := SQL('select cast(concat("$bd", " ", "$bt") as datetime) ')
$ttal := SQL('SELECT TIME_FORMAT(SEC_TO_TIME(TIMESTAMPDIFF(SECOND, "$from", "$to") - TIMESTAMPDIFF(DAY, "$ad", "$bd")*16*60*60), "%H:%i")')
equation := "$ttal"
```

Примера е от инсталация QUEISSER за променлива с ID 1821. Пресмята се колко часа е работено между две дати, като се приема, че работното време за всеки 24 ч. е 8 ч.

### Вземане на опции за променлива от изброим тип

Опции за променливи от изброим тип (dropdown, radio или checkbox_group) могат да се вземат по 2 начина: от стойности във `*_fields_options*` или от записи от даден (под)модул.

#### Първи начин:

Ако името (**name**) на променливата отговаря на **parent_name** на опциите, то в **source** не трябва да се задават други настройки.

Ако се налага за различни променливи да се ползват едни и същи изброими стойности, в полето **source** записваме името на променливата:

```
field := dropdown1
```

#### Втори начин:

Опциите могат да се вземат от функция (метод на клас dropdown), синтаксисът на записване е:

```
method := function
$arg1 := argument1
$arg2 := argument2
```

### Свързани (зависими) падащи списъци

Възможно е dropdown стойностите да са зависими от друг dropdown. Например:

За променлива PP_Project, полето **source** ще включва:

```
method := getDocs
deleted := 0
active := 1
type := 7
status_no := 'closed'
on_change := 'PP_Stage'
```

В **on_change** записваме променливата, която ще промени съдържанието си при промяна на PP_Project. За полето **source** на PP_Stage записваме:

```
get_id := request('PP_Project')
default_id := sql('select value from documents_cstm where model_id=$model_id and var_id=1525')
method := getDocStages
```

**get_id** взема стойността на PP_Project (ID на документ) от POST заявка и се използва при редактиране на допълнителните променливи. **default_id** взема записаната стойност от базата за PP_Project и се ползва при разглеждане на допълнителните променливи и генериране. В sql заявката **\$model_id** е запазена променлива, която е id на редактирания модел. var_id е id на променливата PP_Project.

Горният пример е за свързани дропдауни, използващи методa **getDocStages.** Възможно е да се използваt и други методи. Ето пример с **getCustomDropdown**:

В родителския дропдаун (**insurance_group**) записваме в source:

```
on_change := 'insurance_type'
```

В зависимия дропдаун (**insurance_type**) записваме в source:

```
method := getCustomDropdown
table := DB_TABLE_NOMENCLATURES
table_i18n := DB_TABLE_NOMENCLATURES_I18N
$requested_insurance_group := request('insurance_group')
$saved_insurance_group := sql('select value from contracts_cstm where model_id=$model_id and var_id IN (select id from _fields_meta where model="contract" and model_type=5 and name="insurance_group")')
where := (type = "$requested_insurance_group" OR type="$saved_insurance_group")
```

**Обяснение:** в зависимия дропдаун се вземат всички номенклатури от тип, стойността на който е взет от родителския дропдаун.

Обърнете внимание на оранжевия ред. В него се подготвя една ВРЕМЕННА променлива (**\$requested_insurance_group**), която взема стойността на родителския дропдаун при НЕГОВАТА смяна от request.

Обърнете внимание на синия ред. В него се подготвя друга ВРЕМЕННА променлива (**\$saved_insurance_group**), която взема стойността на родителския дропдаун от ПРЕДХОДНО ЗАПАЗЕНАТА МУ В БАЗАТА СТОЙНОСТ. Целите са две:
- да се заредят правилните опции на зависимия дропдаун при режим на редакция, след като вече е била съхранена стойност
- при режим на разглеждане стойността да се покаже правилно

Обърнете внимание и на зеления ред. В него е описано филтрирането (или WHERE клаузата). В случая е използван трик, при който типът номенклатура се взема или при смяна от родителския дропдаун (**\$requested_insurance_group**), или директно от предходно запазената му стойност в базата (**\$saved_insurance_group**) – никога двете стойности не са попълнени едновременно. Обърнете внимание, че за този метод могат да се използват променливи, дефинирани в по-горните редове – достатъчно е само да се сложи \$ пред тях. Обърнете внимание, също така, че липсват параметрите:

```
value := id
label := name
```

Те са направени вече да се вземат по подразбиране (виж настройката на методите за дропдауни).

**Има и втори вариант** за настройка на свързани дропдауни – опциите на дропдауните да са записани директно във **_fields_options**, а не с метод. При зависими (свързани) dropdown променливи, стойностите на които са от таблица **_fields_options**, отново записваме в родителската променлива в **source**:

```
on_change := '[името на зависимата променлива]'
```

В зависимата променлива в source записваме:

```
parent_name := [името на родителската променлива]
method := getOnChangeOptions
```

В таблицата **_fields_options** записваме опциите за родителския дропдаун по стандартния начин. В полето **parent_name** записваме името на родителския дропдаун, а в полето **child_name** записваме за всяка опция името на **ФИКТИВЕН дропдаун** (**който не трябва да се добавя**). След това добавяме редове във **_fields_options** с опциите, които трябва да се показват в зависимия (втория) дропдаун, например (виж таблицата):

| parent_name | label | option_value | child_name |
|-------------|-------|--------------|------------|
| **за родителския дропдаун insurance_group** |
| insurance_group | Здравни застраховки | 1 | health |
| insurance_group | Гражданска отговорност | 2 | liability |
| insurance_group | Имуществени застраховки | 3 | property |
| **за зависимия дропдаун insurance_type** |
| health | Колективни здравни | 56 | |
| health | Лични здравни | 57 | |
| liability | ГО автомобили | 67 | |
| liability | ГО летателни апарати | 68 | |
| liability | ГО кораби | 71 | |
| property | Застраховка апартаменти | 72 | |
| property | Застраховка ниви | 73 | |

Не може да се предава зависимост (**on_change**) повече от едно ниво (след зависимата променлива).

**Има и втори вариант** за настройка на свързани дропдауни – опциите на дропдауните да са записани директно във **_fields_options**, а не с метод. При зависими (свързани) dropdown променливи, стойностите на които са от таблица **_fields_options**, отново записваме в родителската променлива в **source**:

```
on_change := '[името на зависимата променлива]'
```

В зависимата променлива в source записваме:

```
parent_name := [името на родителската променлива]
method := getOnChangeOptions
```

В таблицата **_fields_options** записваме опциите за родителския дропдаун по стандартния начин. В полето **parent_name** записваме името на родителския дропдаун, а в полето **child_name** записваме за всяка опция името на **ФИКТИВЕН дропдаун** (**който не трябва да се добавя**). След това добавяме редове във **_fields_options** с опциите, които трябва да се показват в зависимия (втория) дропдаун, например (виж таблицата):

| parent_name | label | option_value | child_name |
|-------------|-------|--------------|------------|
| **за родителския дропдаун insurance_group** |
| insurance_group | Здравни застраховки | 1 | health |
| insurance_group | Гражданска отговорност | 2 | liability |
| insurance_group | Имуществени застраховки | 3 | property |
| **за зависимия дропдаун insurance_type** |
| health | Колективни здравни | 56 | |
| health | Лични здравни | 57 | |
| liability | ГО автомобили | 67 | |
| liability | ГО летателни апарати | 68 | |
| liability | ГО кораби | 71 | |
| property | Застраховка апартаменти | 72 | |
| property | Застраховка ниви | 73 | |

Не може да се предава зависимост (**on_change**) повече от едно ниво (след зависимата променлива).

## Конфигуратор

Конфигураторът е специален вид групова таблица, която се използва за създаване на шаблони за записи. Той позволява предварително дефиниране на набор от полета и стойности, които могат да бъдат използвани при създаване на нови записи.

### Настройки за конфигуратор

В полето **source** на груповата променлива (от тип group) може да се зададат следните настройки:

```
configurator_group := 1
```

Това позволява записването на шаблони (запазени конфигурации) за груповата променлива. Над груповата таблица се появява панел за зареждане, добавяне, записване и изтриване на шаблони за конфигурации.

### Изчисления в конфигуратор

Конфигураторът поддържа същите изчисления като груповите таблици, включително:
- Параметри с \$ префикс
- SQL заявки за получаване на данни
- Математически операции
- Условни изрази

## BB (Бизнес Блок)

BB е специален тип групова таблица, която се използва за сложни бизнес изчисления и конфигурации. Стойностите се съхраняват в таблица `bb`.

### Изчисления в BB

BB поддържа разширени изчисления, включително:
- Агрегатни функции (sum, avg)
- Сложни математически операции
- Връзки с други таблици
- Условна логика

### Настройки за BB

В полето **source** могат да се зададат различни настройки за поведението на BB таблицата.

## Групова таблица от втори вид - GT2

GT2 е разширена версия на груповата таблица с допълнителни възможности за изчисления и конфигурации.

### Скриване на първа колона в ГТ

Добавена е възможност за скриване на колоната с номерата на редовете при груповите променливи (таблици) ПРИ ПЕЧАТ:

```
hide_row_numbers := 1
```

### Скриване на +/- бутони в ГТ/ГТ2

Възможност за скриване на бутоните за добавяне и премахване на редове:

```
hide_multiple_rows_buttons := 1
```

### Бутони за множествени избор в ГТ/ГТ2

Бутони за филтриране:

```
show_select_buttons := nomenclatures
show_select_buttons := projects
show_select_buttons := customers
show_select_buttons := nomenclatures, projects
```

Бутони за обновяване:

```
show_refresh_buttons := nomenclatures
show_refresh_buttons := projects
show_refresh_buttons := customers
show_refresh_buttons := nomenclatures, projects
```

Възможни стойности: `nomenclatures`, `customers`, `documents`, `projects`, `tasks`, `contracts`, `users`, `departments`.

### Подвижни ("плаващи") бутони в ГТ/ГТ2

За показване на подвижни бутони, които се показват вдясно от ред при позициониране на курсора:

```
floating_buttons := 1
```

### Импорт на данни в ГТ/ГТ2

Импортиране на данни от Excel файл в групова таблица:

```
show_import_button := 1
```

Допълнителни настройки:

```
row_num_var := <име-на-променлива>
import_configurator_group := 1
import_plugin := <име-на-плъгин>
```

### Методи във всички модули (dropdown.class.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCustomers | Списък на контрагенти | `method := getCustomers`<br>`is_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)`<br>`type := (незадължителен параметър - id на тип контрагент, ако не е посочен се взимат контрагенти от всички типове)`<br>`id := (незадължителен параметър - id на конкретен контрагент, ако не е посочен се взимат всички контрагенти с горепосочените филтри)`<br>`tag := 3,4 (незадължителен параметър - id на таг с който са тагнати контрагентите, могат да се дават няколко тага като се взимат всички контрагенти, които имат поне единия таг – например ако имаме tag := 3,4 ще се вземат контрагенти, които са тагнати с 3, контрагенти които са тагнати с 4 и контрагенти които са тагнати с 3 и 4)` |
| 2 | getCustomerBranches | Списък на обектите за контрагент | `method := getCustomerBranches`<br>`customer := (незадължителен параметър - id на контрагент, ако не е посочен се взима id на текущия контрагент)`<br>Има възможност и да се подаде параметър, който взима ID-то на контрагент от точно определена променлива, например: `customer := $cust_id` |
| 3 | getCustomerContactPersons | Списък на лицата за контакт за определен обект на контрагент | `method := getCustomerContactPersons`<br>`customer := (незадължителен параметър - id на контрагент, ако не е посочен се взима id на текущия контрагент)`<br>`customer_branch := (незадължителен параметър - id на обект за контрагента, възможно е и да се изреждат и id-та на обекти със запетая, ако параметърът се настрои на all, се взимат лицата за контакт от ВСИЧКИ обекти към контрагента, ако не е посочен, се вземат лицата за контакт на ВСИЧКИ ОБЕКТИ)`<br>Ако контрагентът е физическо лице, тогава в dropdown-а се зарежда единствено името на контрагента.<br>Има възможност и да се подаде параметър, който взима ID-то на контрагент от точно определена променлива, например: `customer := $cust_id`<br>Има възможност и да се подаде параметър, който взима ID-то на обект от точно определена променлива, например: `customer_branch := $cust_branch_id` |
| 4 | getUsers | Списък на потребители | `method := getUsers`<br>`department := (незадължителен параметър - id на отдел)`<br>`active := 1 (дали да взима само активни потребители)`<br>`is_portal := 0 – само нормални потребители, 1 – само портални потребители`<br>`exclude_current_user := (0/1)` |
| 5 | getUnfinishedSubprojects | Списък на неприключили подпроекти | `method := getUnfinishedSubprojects` |
| 6 | getUnfinishedPhases | Списък на неприключили фази | `method := getUnfinishedPhases` |
| 7 | getProjects | Списък на проекти | `method := getProjects`<br>`type := (id на тип)`<br>`status := (статус)` |
| 8 | getEmployees | Списък на служители | `method := getEmployees`<br>`employees_with_users_only := 1` |
| 9 | getOnChangeOptions | Списък зависещ от родителски списък | `method := getOnChangeOptions` |
| 10 | getArticleCategories | Списък на категории в номенклатури | `method := getArticleCategories`<br>`category := (незадължителен параметър - id на една или повече категории, изброени със запетаи. Показват се всички категории, чийто родител е някоя от указаните категории. За да се покаже цялото дърво от категории, този параметър се пропуска или се слага на 1)`<br>`exclude_root := (незадължителен параметър – указва дали родителската категория (когато е посочена не повече от една за параметър category) да се вижда - стойност 0, или не – стойност 1, по подразбиране стойността на този параметър е 0 – вижда се)` |
| 11 | getRelatedCustomers | Списък на свързани контрагенти, за текущия контрагент | `method := getRelatedCustomers`<br>`relative_type := (тип връзка на контрагентите: major_associate – мажоритарен съдружник, minor_associate – миноритарен съдружник, shareholder – акционер, employee – служител, filial_company – дъщерно дружество, joint_venture – свързани фирми, colleague – свързани лица, associate – съдружници)` |
| 12 | getDepartments | Списък на отдели | `method := getDepartments`<br>`department := (незадължителен параметър – едно или повече id-та на отдели, разделени със запетая. Показва всички отдели, чийто родител е някой от указаните отдели. За да се покаже цялото дърво от отдели, този параметър се пропуска или се слага на 1)`<br>`exclude_root := (незадължителен параметър – указва дали родителския отдел да се вижда - стойност 0 или не – стойност 1, по подразбиране стойността на този параметър е 0 – вижда се)` |
| 13 | getGroups | Списък на групи | `method := getGroups`<br>`group := (незадължителен параметър - id на група, показва всички групи, чийто родител е указаната група. За да се покаже цялото дърво от групи, този параметър се пропуска или се слага на 1)`<br>`exclude_root := (незадължителен параметър – указва дали родителската група да се вижда - стойност 0 или не – стойност 1, по подразбиране стойността на този параметър е 0 – вижда се)` |
| 14 | getAttachments | Списък на всички прикачени файлове към текущия запис | `method := getAttachments`<br>`output := (незадължителен параметър: ако се остави празно стойностите на опциите са линкове за показване на файла без проверка на правата, ако стойността е 'link_with_permissions' стойностите на опциите са линкове за показване на файла с проверка на правата, ако стойността е 'just_id' стойностите на опциите са ID-та на файловете)`<br>Методът може да се използва за всички модели, които имат функция а прикачване на файлове и се използва в допълнителни променливи, за извеждане в dropdown на всички прикачени файлове към текущия модел. |
| 15 | getCurrencies | Списък на валути | `method := getCurrencies`<br>`format := short (незадължителен параметър – показва валутите само с кода им, опция по подразбиране)`<br>`format := verbose (незадължителен параметър – показва валутите с кода и името им например "(BGN) Български лев")`<br>**Важно**: Методът извежда всички валути, отбелязани като активни (active = 1) в таблицата fin_currencies_available |
| 16 | getAnnouncements | Списък на съобщения (само неархивираните) | `method := getAnnouncements`<br>`type := (незадължителен параметър - id на тип или типове съобщения, отделят се със запетая)`<br>`assignments_users := (незадължителен параметър – id на потребители, може да се сложи и служебната дума currentUser, която указва назначение на съобщенията към текущия потребител или отделите на текущия потребител, отделят се със запетая)`<br>`assignments_departments := (незадължителен параметър – id на отдели, към които е назначено съобщението, отделят се със запетая)` |
| 17 | getMonthsNames | Списък на месеците от годината | `method := getMonthsNames`<br>Стойностите на опциите са 1, 2, 3 ... съответно за януари, февруари, март ... |
| 18 | getWarehouses | Списък на складовете (към конкретни фирма и офис) | `method := getWarehouses`<br>`company := (незадължителен параметър –id на фирма)`<br>`office := (незадължителен параметър –id на офис)`<br>`active := (0/1)` |
| 18 | getWarehouses | Списък на складовете | `method := getWarehouses`<br>`company := (id на фирма)`<br>`office := (id на офис)`<br>`active := (0/1)` |
| 19 | getYears | Списък на години от посочена стартова до посочен брой преди/след текущата | `method := getYears`<br>`start := (стартова година, по подразбиране 1970)`<br>`offset_from_current := (отстъп спрямо текущата година – определя последната показвана година, може да е положително или отрицателно число, по подразбиране 0)`<br>`num_active := (незадължителен параметър, задава, че само последните num_active опции ще са активни)`<br>`option_prefix := (незадължителен параметър, задава префикс за стойностите (option_value))`<br>`sort := asc/desc (незадължителен параметър за сортиране на опциите, по подразбиране са във възходящ ред)` |
| 20 | getDays | Списък на дни в посочен месец и година | `method := getDays`<br>`month := (не задължителна настройка, която може да укаже за кой месец се вземат дните, работи в комбинация с настройката year, 1 - януари, ... 12 - декември)`<br>`year := (не задължителна настройка, която може да укаже за кой месец и година се вземат дните, работи в комбинация с month)`<br>Ако се пропусне указването на month и year, методът връща 1-31<br>`option_prefix := (незадължителен параметър, задава префикс за стойностите (option_value))`<br>`sort := asc/desc (незадължителен параметър за сортиране на опциите, по подразбиране са във възходящ ред)` |
| 21 | getCountries | Списък на всички държави (взема се от таблица country_list) | `method := getCountries` |

## Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в таблица **documents_statuses**. Тази таблица има следните полета:

- **id** – НЕ Е auto-increment, въвежда се ръчно и може да се дублира
- **doc_type** – id-то на типа документ, за който се отнася подстатусът
- **name** – името на подстатуса (езиково-зависимо)
- **description** – описание на подстатуса (езиково-зависимо, незадължително)
- **status** – към кой основен статус принадлежи (opened, locked, closed)
- **sequence** – поредност на състоянията
- **lang** – език за езиково-зависимите параметри

Пример за два реда от таблицата:

| id | doc_type | name | description | status | sequence | lang |
|----|----------|------|-------------|--------|----------|------|
| 2 | 1 | За одобрение | Статус "За одобрение" | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |

## Методи в контрагенти (customers.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions`<br>`is_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)`<br>`type := (незадължителен параметър - id на тип контрагент, ако не е посочен се взимат контрагенти от всички типове)`<br>`id := (незадължителен параметър - id на конкретен контрагент, ако не е посочен се взимат всички контрагенти с горепосочените филтри)`<br>`tag := 3,4 (незадължителен параметър - id на таг с който са тагнати контрагентите, могат да се дават няколко тага като се взимат всички контрагенти, които имат поне единия таг – например ако имаме tag := 3,4 ще се вземат контрагенти, които са тагнати с 3, контрагенти които са тагнати с 4 и контрагенти които са тагнати с 3 и 4)` |
| 2 | getCstmRelatives | Списък на връзки между контрагенти | `method := getCstmRelatives`<br>`is_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)`<br>`type := (незадължителен параметър - id на тип контрагент, ако не е посочен се взимат контрагенти от всички типове)`<br>`c_id := задължителен параметър - id на контрагента` |
| 3 | getIsCompany | Списък на видовете контрагенти (ЮЛ, ФЛ) | `method := getIsCompany` |
| 4 | getTypesKind | Списък на видовете контрагенти (ЮЛ, ФЛ, и двете) | `method := getTypesKind` |
| 2 | getCstmRelatives | Списък на връзки между контрагенти | `method := getCstmRelatives`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`c_id := (задължителен - id на контрагента)` |
| 3 | getIsCompany | Списък на видовете контрагенти (ЮЛ, ФЛ) | `method := getIsCompany` |
| 4 | getTypesKind | Списък на видовете контрагенти (ЮЛ, ФЛ, и двете) | `method := getTypesKind` |

## Методи в документи (documents.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions`<br>`is_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)`<br>`type := (незадължителен параметър - id на тип контрагент, ако не е посочен, се взимат контрагенти от всички типове)` |
| 2 | getFcNums | Списък на фактури за контрагент | `method := getFcNums`<br>`var_id := (задължителен параметър - id на променлива пазеща стойността на фактурата от поръчка)`<br>`type := (задължителен параметър - тип на документ (поръчка) откъдето взема var_id)` |
| 3 | getFcSums | Списък от сумите за плащане на фактури за контрагент | `method := getFcSums`<br>`sum_var_id := (задължителен параметър - id на променлива пазеща стойността на фактурата от поръчка)`<br>`currency_var_id := (незадължителен параметър - id на променлива пазеща валутата на фактурата от поръчка)`<br>`type := (задължителен параметър - тип на документ (поръчка) откъдето взема var_id)`<br>`status := (незадължителен параметър – статус на документ (поръчка))`<br>`substatus := (незадължителен параметър – подстатус на документ (поръчка))`<br>Стойността на всяка опция е сумата и валутата, а етикета е името и номера на документа |
| 4 | getDocs | Списък на документи | `method := getDocs`<br>`status_no := статус различен от`<br>`type := тип(ове) на документи, разделени с запетая` |
| 5 | getBBElements | Списък на всички елементи в ББ към документ | `method := getBBElements` |
| 6 | getStatuses | Списък на всички статуси (и подстатуси) към документ | `method := getStatuses`<br>`model_types := (незадължителен параметър – списък с id-та на типове документи, отделени със запетая)` |
| 7 | getOwnership | Списък на всички видове собственост | `method := getOwnership`<br>Опции: неразпределен, разпределен, назначен |
| 8 | getDirections | Списък на всички видове посоки | `method := getDirections`<br>Опции: входящ, изходящ, вътрешен |
| 2 | getFcNums | Списък на фактури за контрагент | `method := getFcNums`<br>`var_id := (задължителен - id на променлива)`<br>`type := (задължителен - тип документ)` |
| 3 | getFcSums | Списък от сумите за плащане на фактури | `method := getFcSums`<br>`sum_var_id := (задължителен)`<br>`currency_var_id := (незадължителен)`<br>`type := (задължителен)`<br>`status := (незадължителен)`<br>`substatus := (незадължителен)` |
| 4 | getDocs | Списък на документи | `method := getDocs`<br>`status_no := (статус различен от)`<br>`type := (тип(ове) на документи)` |
| 5 | getBBElements | Списък на всички елементи в ББ към документ | `method := getBBElements` |
| 6 | getStatuses | Списък на всички статуси (и подстатуси) | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 7 | getOwnership | Списък на видове собственост | `method := getOwnership` |
| 8 | getDirections | Списък на видове посоки | `method := getDirections` |

## Методи в проекти (projects.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на всички статуси | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 2 | getPriorities | Списък на всички приоритети за проекти | `method := getPriorities` |
| 3 | getFinishedOptions | Списък на опциите за статус приключен | `method := getFinishedOptions` |

## Методи в задачи (tasks.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на всички статуси | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 2 | getPriorities | Списък на всички приоритети | `method := getPriorities` |
| 3 | getOwnership | Списък на видове собственост | `method := getOwnership` |
| 4 | getSeverity | Списък на всички приоритети | `method := getSeverity` |

## Методи във финанси (finance.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCompanyOffices | Списък на офисите за дадена компания | `method := getCompanyOffices`<br>`company_id := (id на фирма)`<br>`active := (0/1)` |
| 2 | getWarehousesEmployees | Списък на служителите в даден склад | `method := getWarehousesEmployees`<br>`warehouse := (id на склад)`<br>`active := (0/1)` |
| 3 | getCustomDropdown | Универсален метод за dropdown | `method := getCustomDropdown`<br>`table := (име на таблица)`<br>`table_i18n := (име на i18n таблица)`<br>`where := (WHERE условие)`<br>`value := id`<br>`label := name` |
| 4 | getBankAccounts | Списък на банковите сметки | `method := getBankAccounts`<br>`company_id := (id на фирма)`<br>`office_id := (id на офис)`<br>`active := (0/1)` |
| 5 | getCashboxes | Списък на касите | `method := getCashboxes`<br>`company_id := (id на фирма)`<br>`office_id := (id на офис)`<br>`active := (0/1)` |
| 6 | getDocumentsModels | Списък на видове записи за финансови документи | `method := getDocumentsModels` |
| 7 | getFinanceDocumentsTypesModels | Списък на всички видове записи за финансови документи | `method := getFinanceDocumentsTypesModels` |
| 8 | getDocumentsModelsForSections | Списък на видове финансови документи за раздели | `method := getDocumentsModelsForSections` |
| 9 | getStatuses | Списък на статусите на финансовите документи | `method := getStatuses` |
| 10 | getFinancePaymentsTypes | Списък типовете плащания | `method := getFinancePaymentsTypes` |
| 11 | getInvoicesTemplatesTypes | Списък на шаблони за фактуриране | `method := getInvoicesTemplatesTypes` |
| 12 | getRepaymentPlansStatuses | Списък на статусите на погасителните планове | `method := getRepaymentPlansStatuses` |
| 13 | getPaymentsStatuses | Списък на статусите за плащане | `method := getPaymentsStatuses` |
| 14 | getBudgetsStatuses | Списък на статусите за бюджети | `method := getBudgetsStatuses` |
| 15 | getBudgetsMethods | Списък на видове бюджет | `method := getBudgetsMethods` |
| 16 | getTransactionExpensesOperations | Списък на видовете транзакционни операции | `method := getTransactionExpensesOperations` |
| 17 | getPaymentsTypes | Списък на типовете плащания | `method := getPaymentsTypes` |
| 18 | getBankAccountsTypes | Списък на видовете банкови сметки | `method := getBankAccountsTypes` |
| 19 | getRecurrenceTypes | Списък на опции за продължителност за периодични плащания | `method := getRecurrenceTypes` |
| 20 | getAnalysisTypesKinds | Списък на опции за анализ | `method := getAnalysisTypesKinds` |
| 21 | getDistributionStatuses | Състояния на разпределение | `method := getDistributionStatuses` |
| 22 | getDocsPaymentStatuses | Списък на платежните статуси | `method := getDocsPaymentStatuses` |
| 23 | getFinanceAnalysisItems | Списък на приходни или разходни пера | `method := getFinanceAnalysisItems`<br>`type := income/expense`<br>`item_type := income/expense` |
| 24 | getCompaniesOffices | Списък на компаниите с техните офиси | `method := getCompaniesOffices` |
| 25 | getCompaniesData | Списък на опции за Каса/Банкова сметка | `method := getCompaniesData`<br>`payment_direction := incomes/expenses`<br>`company_id := (id на фирми)`<br>`office_id := (id на офиси)`<br>`payment_type := cash/bank`<br>`active := (0/1)` |

## Методи в договори (contracts.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getSinglePeriodLengths | Списък на опции периодичност | `method := getSinglePeriodLengths` |
| 2 | getSubtypes | Списък на под-типовете договори | `method := getSubtypes` |

## Методи в събития (events.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на статусите на събития | `method := getStatuses` |
| 2 | getPriorities | Списък на приоритетите | `method := getPriorities` |
| 3 | getAllDay | Списък на опции за продължителност | `method := getAllDay` |
| 4 | getParticipantUsers | Списък на участниците в събитие | `method := getParticipantUsers` |

## Методи в инфо панели (dashlets.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getDashletsModules | Списък на модулите за инфо панели | `method := getDashletsModules` |

## Методи в съобщения (announcements.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getPriorities | Списък на приоритети на съобщенията | `method := getPriorities` |
| 2 | getReadOptions | Списък на опции за посетеност | `method := getReadOptions` |
| 3 | getArchiveOptions | Списък на опции за архив | `method := getArchiveOptions` |

## Методи в номенклатури (nomenclatures.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCDReasons | Списък на основания за издаване на кредитни/дебитни известия | `method := getCDReasons` |

## Методи в бележки (notes.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions` |

## Методи в изгледи (outlooks.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getModules | Списък на всички модули, в които се използват изгледи | `method := getModules` |
| 2 | getModelsTypes | Списък на всички типове по модули, в които се използват изгледи | `method := getModelsTypes` |
| 3 | getSections | Списък на всички секции по модули, в които се използват изгледи | `method := getSections` |
| 4 | getRolesAndUsers | Списък на всички потребители и роли за назначение към изгледи | `method := getRolesAndUsers` |

## Общ метод за опции с директен SQL (dropdown.class.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCustomDropdown | Списък на опции взети директно от базата данни | `method := getCustomDropdown`<br>`value := id` (незадължителен)<br>`label := name,lastname` (незадължителен)<br>`table := DB_TABLE_CUSTOMERS` (константа)<br>`table_i18n := DB_TABLE_CUSTOMERS_I18N` (константа)<br>`where := type = '1' AND id = '10765'` (клауза)<br>`order_by := active DESC, position ASC` (незадължителен)<br>`id_param := model_id` (незадължителен) |

**Важно**: Всички имена на таблици са дефинирани като константи. Тези константи са описани във файла с всички таблици от базата данни.

**Важно**: В getCustomDropdown параметрите могат да участват като променливи, отбелязани с \$ отпред. Например:

```
method := getCustomDropdown
table := DB_TABLE_NOMENCLATURES
table_i18n := DB_TABLE_NOMENCLATURES_I18N
$requested_nom_type := request('nom_type')
where := type = $requested_nom_type
```

### Сортиране на чекбокс групи

Опциите на чекбокс групите могат да се сортират, използвайки параметъра `reorder`:

```
reorder := reorderOptionsCheckedFirst
reorder := reorderOptionsAlphabetical
```

- `reorderOptionsCheckedFirst` - всички отметнати опции минават най-отгоре
- `reorderOptionsAlphabetical` - сортира опциите по азбучен ред

**Важно**: `reorder` работи само за чекбоксове! Не работи за дропдауни или радио бутони!

### Извикване на методи от други модули

Разработена е възможност за извикване на методи за дропдаун от други модули чрез изписване на дропдаун класа, след него `::` и името на метода.

Пример:
```
method := Finance_Dropdown::getCompaniesData
```

## Групова таблица от втори вид - GT2

### Типове документи/договори

Добавена е възможност към типовете документи и договори да се добавя групова таблица от 2-ри вид. За целта се създава новият тип документ/договор. При добавяне/редакция на типа може да се "включи" опцията за групова таблица от 2-ри вид като **!!! ЗАДЪЛЖИТЕЛНО !!!** се посочи секцията (layout), в която трябва да се покаже тя. Ако има готови потребителски (несистемни) секции, груповата таблица може да се сложи във всяка от тях, или в поле „Секция за таблицата" може да се въведе име, с което ще бъде създадена нова потребителска секция.

## Описание на настройките за nZoom 1.4.x

Настройките в nZoom се правят на няколко места:
- В главен конфигурационен файл (`/conf/.htconfig`) – тези настройки се отнасят за цялата система
- В конфигурационните файлове на всеки един модул (`/modules/<module>/conf/.htconfig`) – тези настройки са локални за конкретния модул и за модулите зависещи от този конкретен модул
- В таблица 'settings' в базата с данни – тези настройки отново се отнасят за цялата система

Тенденцията е всички настройки да се изнесат в таблица 'settings' и да има минимално настройки в конфигурационни файлове. Това обаче ще стане постепенно.

Конфигурационните файлове са разделени на секции. Всяка секция е обозначена в следния формат `[section_name]`. Всяка секция може да съдържа параметри, чиито стойности се задават непосредствено след знака за равенство в следния формат:

```
[section_name]
parameter = value
```

Например:
```
[company_info]
company = ACME
address = Address
```

**ВАЖНО**: В конфигурационните файлове НЕ БИВА да се премахват секции или параметри, а само да се сменят или премахват стойностите им.

### Главен конфигурационен файл (/conf/.htconfig)

Състои се от няколко секции:

#### 1.1. Секция [sys]
Тази секция, както подсказва и името, е системна. Секцията се състои от следните параметри:
- `system = nZoom` - име на системата
- `version = 1.4.0` - версия на системата
- `build = $Rev: 1485 $` - текуща ревизия (build) на системата
- `timezone = +2` – часова зона на системата
- `lock_records = 1` – параметър, указващ дали системата да заключва записите (стойност 0) или не (стойност 1)

#### 1.2. Секция [themes]
Тази секция съдържа настройките на темите на приложението:
- `default_theme = Default` - име на темата по подразбиране

#### 1.3. Секция [i18n]
Тази секция съдържа настройките за интернационализацията (i18n) на системата:
- `default_lang = bg` – език на интерфейса на приложението, по подразбиране той е Български
- `default_locale = bg_BG.UTF-8` - параметър, указващ локалните настройки (locale) на приложението
- `supported_langs = bg, en` – списък на поддържаните от системата езици за интерфейса
- `model_langs = bg, en, de` – списък на поддържаните от системата езици за моделите

#### 1.4. Секция [database]
Тази секция съдържа настройките за достъп до базата от данни:
- `type = mysqli` – тип на базата от данни
- `host = localhost` - адрес на сървъра, където се намира базата
- `user = <username>` – потребител за достъп до базата
- `pass = <password>` – парола за достъп до базата от данни
- `name = <database>` – име на базата

#### 1.5. Секция [modules]
Тази секция изброява модулите, които са достъпни през главното меню на системата:
- `frontend_modules = <списък от модули>` – списък от модули достъпни във основния панел
- `backend_modules = <списък от модули>` – списък от модули достъпни във панела за настройки

Списък от модули се задава във специален формат:
- `<module_name>` - само името на модула
- `<module_name>::<controller_name>` - името на модула и име на контролер
- `<module_name>|<action_name>` - името на модула и име на действието
- `<module_name>::<controller_name>|<action_name>` - пълен формат

#### 1.6. Секция [log]
Тази секция съдържа настройките за логовете на приложението:
- `level = 1` – ниво за запис на лога:
  - 0 = NONE
  - 1 = DEBUG
  - 2 = INFO
  - 3 = WARNING
  - 4 = ERROR
- `date_format = Y-m-d H:m:s` - формат на датата в лога

#### 1.7. Секция [emails]
Тази секция съдържа настройките за изпращане на известявания:
- `from_name = nZoom Notification System` – указва името на подателя на имейлите
- `from_email = <EMAIL>` - указва електронния адрес на подателя
- `manager_email = <EMAIL>` – указва електронния адрес на управителя

#### 1.8. Секция [company_info]
Тази секция съдържа данни за фирмата, ползвател на системата:
- `company = ACME` – указва името на фирмата
- `address = Address` – указва адреса на фирмата
- `phone = ***********` – указва телефона на фирмата
- `fax = ***********` – указва факса на фирмата
- `url = http://www.example.org` – указва адрес на уеб сайта
- `email = <EMAIL>` – указва електронния адрес
- `contact_person = John Smith` – указва името за контакт
- `bulstat = 1234567890` – указва БУЛСТАТ-а на фирмата
- `taxnumber = 0987654321` – указва данъчния номер

#### 1.9. Секция [mapped_drives]
Тази секция съдържа настройките мрежовите устройства:
- `y = \\***********\install\` – буквата на мрежовото устройство и мрежовия път

#### 1.10. Секция [mailer]
Тази секция съдържа настройките типа на известяване:
- `type = mail` – указва типа на системата за изпращане (mail, smtp или sendmail)

#### 1.11. Секция [smtp]
Тази секция съдържа настройките на SMTP сървъра:
- `host = 127.0.0.1` – адрес на SMTP сървъра
- `user = <username>` – потребителско име за достъп до SMTP сървъра
- `pass = <pass>` – парола за достъп до SMTP сървъра

#### 1.12. Секция [sendmail]
Тази секция съдържа настройките на sendmail програмата:
- `path = /usr/sbin/sendmail` – път до sendmail програмата в Unix/Linux

## Настройки в таблица settings

Таблицата съдържа няколко колони по подобие на конфигурационния файл: секция, параметър и стойност.

### 2.1 Секция crontab

Тази секция съдържа настройките за автоматичното известяване (имейлите, които се изпращат периодично всеки ден):

#### disable
Забранява изпълнението на всички крон задачи или на точно определени крон задачи:
- `disable = all` – забранява всички крон задачи
- `disable = main` – забранява основния кронтаб
- `disable = automations` – забранява проверката за кронтаб серийни изпълнения
- `disable = currencies_update_rates` – забранява актуализация на валутни курсове
- `disable = reminders` – забранява известяванията за напомняния за записи
- `disable = emails_campaigns` – забранява изпращането на и-мейл кампании
- `disable = notify_invoice_supervisors` – забранява известяване на отговорници по издаване на фактури
- `disable = issue_invoices` - забранява издаване на фактури от crontab
- `disable = notify_payment_status` - забранява известяване за променен статус на плащане
- `disable = archive` – забранява архивиране от crontab

Възможна е настройка за забрана на няколко вида крон задачи:
```
disable = emails_campaigns, reminders, automations
```

#### send_interval
Интервал (в часове) на колко време да се изпраща писмо. Например, ако сложим интервала да е 72, потребителят ще получава имейл на всеки 3 дни.

#### before_interval
Интервал за известяването за възможност за изтичане на срок на документ или етап на проект (в часове). Интервалът отмерва предварение в часове, оставащи до изтичането на срока.

#### base_host
Адрес на приложението, използван при автоматичните известявания.

#### tasks_send_interval
Интервал (в часове) на колко време да се изпраща известяване за задачи.

#### tasks_period_before
Интервал за известяването за възможност за изтичане на срок на задача (в часове).

#### tasks_periodicity
Максимален брой пъти, които да се изпрати известяване за изтичащ срок на задачи.

#### before_start_contract
Известяване преди влизане в сила на договори. Например `30,15,5` – известява 30 дни преди влизане в сила на договор, след това съответно 15 и 5 дни преди влизане в сила.

#### before_end_contract
Известяване преди изтичане на договори. Например `30,15,5` – известява 30 дни преди изтичане на договор.

#### archive_interval
Интервал за архивиране за записи – за всеки модел на нов ред. Задава се във формат:
```
<модел> := <основна променлива> => <интервал>
```
или само
```
<модел> := <интервал>
```

Интервалът трябва да бъде във формат: `<брой> <вид>`, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

Пример:
```
document := 1 YEAR
```
или
```
document := status_modified => 12 MONTH
```

#### archive_models
Видове записи (имена на модели в ед.ч.), разделени със запетая, за които да се извърши архивиране.

#### archive_responsible
ID на потребители, разделени със запетая, на които да се изпрати известяване за извършено архивиране от crontab.

#### archive_remove_files
Задава дали при архивиране от crontab да се премахват файловете към архивираните записи. Стойности: 1 за да се премахват, празна стойност или 0, за да не се премахват.

#### purge_interval
Интервал за унищожаване за записи – аналогично на archive_interval.

#### purge_models
Видове записи за които да се извърши унищожаване.

#### purge_responsible
ID на потребители на които да се изпрати известяване за извършено унищожаване от crontab.

#### invoices_issue_notification
Интервал преди следващо издаване на фактури, когато трябва да се известят отговорници по издаване на фактури.

#### invoices_send_lang
Език на който да се изпращат на контрагенти фактури от договори след издаване.

#### translate_invoices_issued
Езици на които да се преведат фактури от договори след издаване.

#### notify_payment_status_interval
Интервал за търсене за известяване за промяна на статус на плащане.

#### notify_payment_status_types
Типове приходни документи за известяване за промяна на статус на плащане.

### 2.2 Настройваема валидация на основни данни

#### Допълнителни задължителни полета

Отнася се за модули: Контрагенти, Документи, Проекти, Задачи, Договори. Валидацията е по типове.

**Пълен списък от полета:**

**За модул documents**: `custom_num`, `trademark`, `contract`, `project`, `office`, `employee`, `media`, `deadline`, `validity_term`, `description`, `notes`, `department`, `date`

**За модул projects**: `trademark`, `date_start`, `date_end`, `priority`, `parent_project`, `finished_part`, `description`, `notes`

**За модул tasks**: `trademark`, `project`, `planned_time`, `severity`, `progress`, `equipment`, `task_field`, `source`, `description`, `notes`, `department`

**За модул customers**:
- За физическо лице: `company_department`, `position`, `ucn`, `identity_num`, `identity_date`, `identity_by`, `identity_valid`, `address_by_personal_id`
- За юридическо лице: `company_name`, `in_dds`, `eik`, `registration_file`, `registration_volume`, `registration_number`, `registration_address`, `mol`
- За всички: `department`, `assigned`, `country`, `city`, `postal_code`, `address`, `notes`, `phone`, `fax`, `gsm`, `email`, `web`, `skype`, `othercontact`, `bank`, `iban`, `bic`

**За модул contracts**: `custom_num`, `trademark`, `project`, `employee`, `date_sign`, `date_start`, `date_validity`, `date_end`, `description`, `notes`, `department`

Допълнителните задължителни полета за определен тип записи могат да се зададат: в section се записва името на модула, в name – `validate_<id_на_тип>` и за value се изреждат със запетаи полетата.

Пример:
```
section - customers
name – validate_1
value - company_department, country
```

#### Уникални полета

**Пълен списък от полета:**

**За модул documents**: `customer`, `trademark`, `contract`, `custom_num`, `date`
**За модул projects**: `trademark`, `customer`
**За модул tasks**: `trademark`
**За модул customers**: `phone`, `fax`, `gsm`, `identity_num`, `iban`
**За модул contracts**: `custom_num`, `trademark`, `customer`
**За модул nomenclatures**: `name`

Уникални полета се зададат като името на полето се предхожда от "unique_".

Пример:
```
section - documents
name – validate_1
value - custom_num, unique_custom_num, current_year
```

### 2.3 Автоматични кодове

Настройки за автоматично генериране на кодове:

| section | name | value |
|---------|------|-------|
| projects | auto_code_leading_zeros | 4 |
| projects | auto_code_suffix | PRJ |
| users | auto_code_leading_zeros | 4 |
| users | auto_code_suffix | USR |
| documents_types | auto_code_leading_zeros | 4 |
| documents_types | auto_code_suffix | DTP |
| customers | auto_code_leading_zeros | 4 |
| customers | auto_code_suffix | CST |
| offices | auto_code_leading_zeros | 4 |
| offices | auto_code_suffix | OFF |
| contracts_types | auto_code_leading_zeros | 4 |
| contracts_types | auto_code_suffix | CRT |
| finance_companies | auto_code_leading_zeros | 4 |
| finance_companies | auto_code_suffix | COM |
| finance_documents_types | auto_code_leading_zeros | 4 |
| finance_documents_types | auto_code_suffix | FDT |

### 2.1 Секция general

#### Настройки за автоматично попълване на полета

**name**: `autocomplete_N`, където N е id на тип документ/задача/проект
**value**: списък от полета, разделени със запетая

Пример:
```
autocomplete_15 := customer,project,department
```

#### Настройки за типове назначения

**name**: `assignment_types_N`, където N е id на тип
**value**: `owner,responsible,observer,decision`

За всеки тип се настройва какви типове назначения ще се прилагат.

### 2.2 Секция tasks

#### Типове назначавания за задачи

**name**: `assignment_types_N`, където N е id на тип задача
**value**: `owner,responsible,observer,decision`

За всеки тип задача се настройва какви типове назначения ще се прилагат за нея (Изпълнител, Отговорник, Наблюдаващ, Вземащ решения).
Настройката "Типове назначения" е достъпна в интерфейса при редакция на Тип задача.

#### Полета за шаблони за типовете задачи

**name**: `configurator_1`
**value**: `name,planned_time,severity,description,department,active,group`

**name**: `configurator_2`
**value**: `name,planned_time,severity,description,department,active,group`

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета за формите

Възможни полета:
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `planned_start_date` - планирано начало
- `planned_finish_date` - планиран край
- `planned_time` - планирано време
- `severity` - приоритет
- `progress` - % на изпълнение
- `equipment` - апаратура
- `task_field` - тестово поле
- `source` - източник на задачата
- `description` - описание
- `notes` - забележка / решение
- `department` - отдел
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета, които да бъдат одитирани

Възможни полета за одит:
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `planned_start_date` - начало
- `planned_finish_date` - край
- `status` - статус
- `substatus` - състояние (подстатус)
- `planned_time` - планирано време
- `severity` - приоритет
- `progress` - % на изпълнение
- `equipment` - апаратура
- `task_field` - тестово поле
- `source` - източник на задачата
- `description` - описание
- `notes` - забележка / решение
- `department` - отдел
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ
- `tag` - промяна на тагове
- `add_attachments` - добавяне на прикачени файлове
- `del_attachment` - изтриване на прикачен файл

### 2.3 Секция documents

#### Типове назначавания за документи

**name**: `assignment_types_N`, където N е id на тип документ
**value**: `owner,responsible,observer,decision`

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета

Възможни полета:
- `custom_num` - номер от контрагент
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `office` - офис
- `employee` - служител
- `contract` - по договор №
- `media` - медия на документа
- `deadline` - срок за обработка
- `validity_term` - срок на валидност
- `date` - дата
- `description` - описание
- `notes` - бележки
- `department` - отдел
- `attachment` - файл
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

#### Настройка за полето "По договор №"

**name**: `contract_custom_label`
**value**: Използвайки ключови думи: **custom_num**, **num** и **name**

Пример:
```
[custom_num] name
```

#### Настройки за архивиране

**name**: `archive_N`, където N е id на тип документ
**value**:
```
field := <променлива>
interval := <интервал>
```

За променлива се посочва основна или допълнителна променлива от тип date/datetime, като допълнителната трябва да се запише с префикс „a__" пред името.

Интервалът трябва да бъде във формат: `<брой> <вид>`, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

#### Настройки за унищожаване

**name**: `purge_N`, където N е id на тип документ
**value**:
```
field := <променлива>
interval := <интервал>
```

### 2.4 Секция customers

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета

Възможни полета:
- `name` - име
- `code` - код
- `company_department` - отдел във фирмата (само за ФЛ)
- `position` - позиция (само за ФЛ)
- `country` - държава
- `city` - град
- `postal_code` - пощенски код
- `address` - адрес
- `notes` - бележки
- `phone` - телефон
- `fax` - факс
- `gsm` - мобилен телефон
- `email` - e-mail
- `web` - уеб сайт
- `skype` - Skype
- `othercontact` - Друг контакт
- `company_name` - пълно наименование на фирмата (само за ЮЛ)
- `in_dds` - Идентификационен номер за регистрация по ДДС (само за ЮЛ)
- `eik` - ЕИК (само за ЮЛ)
- `registration_file` - фирмено дело № (само за ЮЛ)
- `registration_volume` - том/страница/регистър (само за ЮЛ)
- `registration_number` - съд по регистрация (само за ЮЛ)
- `registration_address` - адрес по регистрация (само за ЮЛ)
- `mol` - МОЛ (само за ЮЛ)
- `ucn` - ЕГН (само за ФЛ)
- `identity_num` - лична карта № (само за ФЛ)
- `identity_date` - лична карта издадена на (само за ФЛ)
- `identity_by` - лична карта издадена от (само за ФЛ)
- `identity_valid` - валидна до (само за ФЛ)
- `address_by_personal_id` - адрес по лична карта (само за ФЛ)
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

По подразбиране са зададени: `name, lastname, code, in_dds, eik, mol, bank, iban, bic, department, assigned, tag`.

### 2.5 Секция projects

#### Настройки за избор на отговорник на фаза

**name**: `phases_choose_responsible`
**value**: 0 или 1. При 1 можем да избираме отговорник на фаза, при 0 – не можем.

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

Възможни полета:
- `name` - име
- `customer` - контрагент
- `trademark` - търговска марка
- `code` - код
- `date_start` - начало
- `date_end` - край
- `priority` - приоритет
- `parent_project` - подпроект на
- `manager` - ръководител
- `finished_part` - % изпълнение
- `budget` - планиран бюджет
- `work_period` - човекочасове
- `description` - описание
- `status` - статус
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments` - назначения
- `tag` - промяна на тагове
- `add_attachments` - добавяне на прикачени файлове
- `del_attachment` - изтриване на прикачен файл

### 2.6 Секция precision

Настройки за прецизността на закръгленията (0-6) в GT2, за разпределяне, индекси, за цени на номенклатури и др.

- `gt2_rows` – Прецизност на стойностите в редовете в GT2
- `gt2_total` – Прецизност на **Общо** в GT2
- `gt2_total_vat` – Прецизност на **Общо ДДС** в GT2
- `gt2_total_with_vat` – Прецизност на **Общо с ДДС** в GT2
- `gt2_quantity` – Прецизност за количеството в редовете в GT2
- `indexes_values` – Прецизност за резултат от формули на индекси
- `finance_analysis_percentage` – Прецизност на процентно разпределяне
- `nom_sell_price` - Прецизност на продажна цена на номенклатури
- `nom_last_delivery_price` – Прецизност на последна доставна цена
- `nom_average_weighted_delivery_price` - Прецизност на среднопретеглена доставна цена

### 2.7 Секция contracts

#### Типове назначавания за договори

**name**: `assignment_types_N`, където N е id на тип договор
**value**: `owner,responsible,observer,decision`

#### Настройка за одитираните основни променливи

**name**: `audit`
**value**: съдържа изброените полета за одит

По подразбиране са:
- `active` - готовност за публикуване
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `status` - статус
- `substatus` - състояние (подстатус)
- `company` - фирма
- `office` - офис
- `employee` - служител
- `date_sign` - дата на подписване
- `date_start` - дата на влизане в сила
- `date_validity` - дата на изтичане
- `date_end` - дата на опция за прекратяване
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ
- `tag` - промяна на тагове
- `add_attachments` - прикачени файлове

### 2.8 Активност на ред

Активност на ред е възможност ред от списък на записи да стане изцяло активен, т.е. където и да се щракне с мишката на него, да отвежда на място, което му е посочено.

В **section** се записва името на модул (`documents`, `customers`, `projects`, `nomenclatures`, `finance_budgets`, `finance_incomes_reasons`, `finance_expenses_reasons`, `finance_payments`), в **name** – текстът `row_link_action` и за **value** се въвежда името на действието.

Пример:
```
section - documents
name – row_link_action
value - edit
```

Действието трябва да съвпада с действие (action) за модула от таблица 'roles_definitions', например: `view`, `edit`, `communications`, `relatives` и т.н.

### 2.9 Секция turnovers

Настройка за валута на обороти – ако не е въведена, се взема основната валута за инсталацията.

`currency` – валута (**BGN**, **EUR** ...)

### 2.10 Свързани записи

Настройката за свързани записи позволява да се указва кои от панелите (табове) със свързани записи ще бъдат видими под формата в режим на разглеждане и редакция.

В **section** се записва името на модул (`customers`, `projects`, `nomenclatures`, `contracts`), в **name** – стойност `related_records_modules` и за **value** се изреждат имената на панелите, разделени със запетая.

Възможните стойности са:

#### За Контрагенти:
- `documents` – Документи
- `projects` – Проекти
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_repayment_plans` – Погасителни планове
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори
- `referent_documents` – Референтни документи
- `referent_projects` – Референтни проекти
- `referent_contracts` – Референтни договори
- `referent_customers` – Референтни контрагенти

#### За Проекти:
- `documents` – Документи
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори

#### За Номенклатури:
- `customers` – Контрагенти
- `documents` – Документи
- `projects` – Проекти
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_annulments` – Протоколи за анулиране
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори
- `nomenclatures` – Номенклатури

#### За Договори:
- `documents` – Документи

Пример:
```
section - customers
name – related_records_modules
value – documents, projects, referent_documents
```

### 2.11 Секция finance

#### Типове назначавания за финансови документи

**name**: `assignment_types_N`, където N е id на тип финансов документ
**value**: `owner,responsible,observer,decision`

#### Настройка за фискална валута на фактури

**name**: `fiscal_currency`
**value**: валута (**BGN**, **EUR** ...)

Ако не е въведена, се взема основната валута за инсталацията.

### 2.12 Секция users

**max_failed_attempts**: Колко най-много неуспешни опити за вход може да направи потребителя, в някакъв интервал от време, преди да се задейства защитата. В браузъра защитата е поле с captcha, през REST се връща код 429 и се блокират следващи опити за вход, докато не изтече определеното време.

**max_failed_attempts_timeout**: Какъв е интервала от време за проверка на неуспешните опити. Задава се в брой секунди (30мин = 1800сек, 60мин = 3600). За да изключите изцяло защитата задайте стойност 0.

## Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в една таблица **documents_statuses**. Тази таблица има следните полета:

- **id** – НЕ Е auto-increment, тоест въвежда се ръчно и може да се дублира. Това е направено защото нямаме отделна таблица за езиково-зависимите променливи и опциите за отделните езици се намират в тази таблица. Тоест, ако даден подстатус с id 5 сме въвели на български и искаме да го въведем и на английски, ще въведем ред в таблицата, който отново ще има id 5. Въвеждането на id е задължително!

- **doc_type** – id-то на типа документ, за който се отнася подстатусът. Примерно, ако искаме подстатусът да е за оферта, а тя е с id 15, то в това поле трябва да въведем 15.

- **name** – името на подстатуса. Полето е езиково-зависимо!

- **description** – описание на подстатуса. Полето е езиково-зависимо и е единственото от всички полета в тази таблица, чието попълване не е задължително.

- **status** – оттук се избира към кой основен статус ще принадлежи състоянието, Възможностите са opened, locked, closed

- **sequence** – поредност на състоянията. Въвеждат се номера в зависимост от това в какъв ред искаме да се извеждат подстатусите КЪМ СЪОТВЕТНИЯ ОСНОВЕН СТАТУС. Тоест, ако към "Отворен" имаме въведени три състояния – "В процес на изработка", "За одобрение" и "За редакция" – и искаме да ги виждаме в този ред в полето за sequence трябва да въведем съотеветно 1, 2 и 3 или 7, 12 и 19 (примерно). Ако състоянията са към различни основни статуси, се позволява дублиране на sequence-а. Това ще рече, че можем да имаме подстатус на opened със sequence=1 и да имаме подстатус към closed, който също да има sequence=1 – това НЕ Е проблем.

- **lang** – език за езиково-зависимите параметри. Когато искаме да въведем състояние на друг език, тук указваме какъв ще е езикът ("bg", "en", "de" или друг). Когато въвеждаме състояние на друг език, описваме на съответния език само езиково-зависимите променливи останалите трябва да останат същите.

Пример за два реда от таблицата, описващи състоянието "За одобрение" на български и на английски език:

| id | doc_type | name | description | status | sequence | lang |
|----|----------|------|-------------|--------|----------|------|
| 2 | 1 | За одобрение | Статус "За одобрение" | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |
