# Описание на използване и вписване на допълнителни променливи към модели за nZoom 1.7.x

Допълнителните променливи като типове се описват в таблиците:

- **`_fields_meta`** – описва езиково независима информация за променливите
- **`_fields_i18n`** – описват се етикетите, help, описание за променливите на съответните езици и може да се добавя заден етикет
- **`_fields_options`** – изброимите елементи на променливи от тип dropdown, radio, checkbox_group
- **`layouts`** – информация за секциите (layouts) на променливи – със съответните етикет и позиция

Стойностите на допълнителните променливи се съхраняват в таблици `[име на модели]_cstm`, като `documents_cstm`, `customers_cstm` ..., с изключение на стойностите за променливи от тип:
- **gt2** - съхраняват се в `gt2_details`
- **bb** – в `bb`
- за конфигуратор тип Франкенщайн – в `configurator`

---

## I. Описание на таблиците с техните полета

### 1. Таблица `_fields_meta`:

#### 1.1 `id`
autoincrement, служи за релация с `_fields_i18n` (поле `parent_id`) и `[…]_cstm` (поле `var_id`), не се попълва, приема поредна стойност автоматично!

#### 1.2 `model`
име на модела, Например: **Customer** (за контрагенти), **Document** (за документи), **Documents_Stage** (за етапи на документи), **Tasks** (за задачи – използва се само за добавяне на бутони за справка) и т.н.

#### 1.3 `model_type`
тип на модела, число, отговаря на ID-то на типа на модела, Например: **1** за тип документи с ID 1

#### 1.4 `name`
име на променливата на латиница, уникално за модела и типа на модела, Например: **offer_description**

При трансформиране има възможност за копиране на допълнителни променливи към основни. Името на допълнителната променлива трябва да е от вида **`basic_[името на основната променлива]`**, например **`basic_customer`** се копира към основна променлива **`customer`**, **`basic_department`** се копира към основна променлива **`department`**.

За копиране на назначения при трансформиране трябва да имаме допълнителна променлива **`basic_department`** за разпределяне и променлива **`basic_assignments_owner`** за изпълнители. Възможно е да се копират и другите назначения като променливите да са **`basic_assignments_responsible`**, **`basic_assignments_observer`**, **`basic_assignments_decision`**.

Възможно е и обратното копиране – на основни към допълнителни променливи при трансформация. Името на допълнителната променлива трябва да е от вида **`source_[името на основната променлива]`**, например в **`source_customer`** се копира стойността на основната променлива **`customer`**.

Ако в конкретната инсталация се използва Астериск и полето е от тип text, променливата може да се настрои, така че да се показва от nZoom като телефонен номер, който може да бъде набран. За целта името на променливата трябва да има следния формат:
- **`asteriskcall_phone_[име на променливата]`** - за телефон
- **`asteriskcall_gsm_[име на променливата]`** - за мобилен телефон
- **`asteriskcall_fax_[име на променливата]`** - за факс

#### 1.5 `source`
в полето се записват множество различни настройки във формат: **име := стойност**. Някои от тях са специфични за даден вид променлива и са обяснени в следващите точки.

### Настройване на изчисления

Полетата могат да имат изчисления, чиито параметри се дефинират в **source** колоната. Запазените думи за изчисленията са:

| Ключова дума | Обяснение |
|--------------|-----------|
| **equation** | Израз за изчисление с вече приготвените параметри |
| **array_function** | Агрегатна функция, използвана само в полета, които не са в групова таблица. Има две възможни стойности: **sum** и **avg**. **sum** - сумират се всички стойности на **equation** за редовете от групова таблица, указани в update_fields или num_rows. **avg** – взема средна аретметична стойност за всички редове |
| **update_fields** | Използва се само в полета, които не са в групова таблица. Указва списък от полета от групова таблица, които трябва да се изчислят преди да се направи калкулацията по equation |
| **num_rows** | Указва име на поле от групова таблица, което я обозначава, за да се намерят редовете в таблицата |
| **last_equation** | Израз за изчисление СЛЕД като вече е изчислена агрегатната функция |
| **format** | Низ за форматиране на крайния резултат. За 2 цифри след десетичния знак: %.2F |

#### Параметри - \$а, \$tax, \$customer

Освен запазените думи, може да има и **параметри** участващи в уравнението (equation, last_equation). Параметрите се отбелязват с \$ отпред (например: \$а, \$tax, \$customer) и могат да получават стойности от REQUEST (данни от формата) или SQL (данни от базата).

**Важно**: имената на параметри (заедно с \$) не трябва да се съдържат едно в друго, т.е. недопустимо е да имаме **\$а** (например със стойност 5) и **\$average** (например със стойност 7), защото в equation := **\$a*\$average** ще се замести: equation := **5*5verage**, което ще произведе грешка при изчислението.

**Важно**: стойността получена от изчислението трябва да е валидно число, в противен случай трябва да е в кавички.

#### Пример 1: Просто изчисление без агрегатна функция

Съдържанието на **source** за поле с име **commission**, което се намира в едноредова таблица или конфигуратор:

```
$customer := request('customer')
$total := request('total')
$commission_percentage := sql('SELECT value FROM nom_cstm WHERE var_id=123 AND model_id = $customer')
equation := ($total * $commission_percentage) / 100
format := %.2F
```

**Обяснение:**
- Параметърът \$**customer** получава стойност от формата от ид на контрагента
- Параметърът \$**total** получава стойност от формата от поле с име 'total'
- Параметърът \$**commission_percentage** получава стойност за процента на комисионната от базата от данни
- Част от параметрите участват в уравнението за определяне размера на комисионната
- Резултатът се форматира с 2 цифри след десетичния знак

#### Пример 2: Изчисление с агрегатна функция

Имаме групова таблица с колони **price**, **quantity**, **discount_percentage**, **discount** и **subtotal**:

| price | quantity | discount_percentage | discount (readonly) | subtotal (readonly) |
|-------|----------|-------------------|-------------------|-------------------|
| (въвежда се) | (въвежда се) | (въвежда се) | (изчислява се) | (изчислява се) |

Има едно свободно стоящо поле **tax_percentage**, което съдържа процента на данъка.
Има и още едно свободно стоящо поле **total**, което се използва, за да натрупа общия сбор.

Съдържанието на **source** за поле **discount**:

```
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
equation := $price*$quantity*$discount_perc/100
format := %.2F
```

Съдържанието на **source** за поле **subtotal**:

```
$price := request('price')
$quantity := request('quantity')
$discount := request('discount')
equation := $price*$quantity - $discount
format := %.2F
```

**ВНИМАНИЕ:** Параметърът \$**discount** получава стойност от вече изчислената стойност на полето discount.

#### Подсигуряване при деление:

```
$a := request('discount_percentage')
$b := request('coef')
equation := ($b!=0 ? $a/$b:0)
```

#### Пример 3: Изчисляване на работените часове

Изчисляване на работените часове между две дати (датите са с дата и час):

```
$ad := request('date')
$bd := request('activity_end')
$at := request('from_hour')
$bt := request('to_hour')
$from := SQL('select cast(concat("$ad", " ", "$at") as datetime) ')
$to := SQL('select cast(concat("$bd", " ", "$bt") as datetime) ')
$ttal := SQL('SELECT TIME_FORMAT(SEC_TO_TIME(TIMESTAMPDIFF(SECOND, "$from", "$to") - TIMESTAMPDIFF(DAY, "$ad", "$bd")*16*60*60), "%H:%i")')
equation := "$ttal"
```

Примера е от инсталация QUEISSER за променлива с ID 1821. Пресмята се колко часа е работено между две дати, като се приема, че работното време за всеки 24 ч. е 8 ч.

### Вземане на опции за променлива от изброим тип

Опции за променливи от изброим тип (dropdown, radio или checkbox_group) могат да се вземат по 2 начина: от стойности във `*_fields_options*` или от записи от даден (под)модул.

#### Първи начин:

Ако името (**name**) на променливата отговаря на **parent_name** на опциите, то в **source** не трябва да се задават други настройки.

Ако се налага за различни променливи да се ползват едни и същи изброими стойности, в полето **source** записваме името на променливата:

```
field := dropdown1
```

#### Втори начин:

Опциите могат да се вземат от функция (метод на клас dropdown), синтаксисът на записване е:

```
method := function
$arg1 := argument1
$arg2 := argument2
```

### Свързани (зависими) падащи списъци

Възможно е dropdown стойностите да са зависими от друг dropdown. Например:

За променлива PP_Project, полето **source** ще включва:

```
method := getDocs
deleted := 0
active := 1
type := 7
status_no := 'closed'
on_change := 'PP_Stage'
```

В **on_change** записваме променливата, която ще промени съдържанието си при промяна на PP_Project. За полето **source** на PP_Stage записваме:

```
get_id := request('PP_Project')
default_id := sql('select value from documents_cstm where model_id=$model_id and var_id=1525')
method := getDocStages
```

**get_id** взема стойността на PP_Project (ID на документ) от POST заявка и се използва при редактиране на допълнителните променливи. **default_id** взема записаната стойност от базата за PP_Project и се ползва при разглеждане на допълнителните променливи и генериране. В sql заявката **\$model_id** е запазена променлива, която е id на редактирания модел.

Горният пример е за свързани дропдауни, използващи методa **getDocStages.** Възможно е да се използваt и други методи. Ето пример с **getCustomDropdown**:

В родителския дропдаун (**insurance_group**) записваме в source:

```
on_change := 'insurance_type'
```

В зависимия дропдаун (**insurance_type**) записваме в source:

```
method := getCustomDropdown
table := DB_TABLE_NOMENCLATURES
table_i18n := DB_TABLE_NOMENCLATURES_I18N
$requested_insurance_group := request('insurance_group')
$saved_insurance_group := sql('select value from contracts_cstm where model_id=$model_id and var_id IN (select id from _fields_meta where model="contract" and model_type=5 and name="insurance_group")')
where := (type = "$requested_insurance_group" OR type="$saved_insurance_group")
```

**Обяснение:** в зависимия дропдаун се вземат всички номенклатури от тип, стойността на който е взет от родителския дропдаун.

Обърнете внимание на оранжевия ред. В него се подготвя една ВРЕМЕННА променлива (**\$requested_insurance_group**), която взема стойността на родителския дропдаун при НЕГОВАТА смяна от request.

Обърнете внимание на синия ред. В него се подготвя друга ВРЕМЕННА променлива (**\$saved_insurance_group**), която взема стойността на родителския дропдаун от ПРЕДХОДНО ЗАПАЗЕНАТА МУ В БАЗАТА СТОЙНОСТ. Целите са две:
- да се заредят правилните опции на зависимия дропдаун при режим на редакция, след като вече е била съхранена стойност
- при режим на разглеждане стойността да се покаже правилно

Обърнете внимание и на зеления ред. В него е описано филтрирането (или WHERE клаузата). В случая е използван трик, при който типът номенклатура се взема или при смяна от родителския дропдаун (**\$requested_insurance_group**), или директно от предходно запазената му стойност в базата (**\$saved_insurance_group**) – никога двете стойности не са попълнени едновременно. Обърнете внимание, че за този метод могат да се използват променливи, дефинирани в по-горните редове – достатъчно е само да се сложи \$ пред тях. Обърнете внимание, също така, че липсват параметрите:

```
value := id
label := name
```

Те са направени вече да се вземат по подразбиране (виж настройката на методите за дропдауни).

**Има и втори вариант** за настройка на свързани дропдауни – опциите на дропдауните да са записани директно във **_fields_options**, а не с метод. При зависими (свързани) dropdown променливи, стойностите на които са от таблица **_fields_options**, отново записваме в родителската променлива в **source**:

```
on_change := '[името на зависимата променлива]'
```

В зависимата променлива в source записваме:

```
parent_name := [името на родителската променлива]
method := getOnChangeOptions
```

В таблицата **_fields_options** записваме опциите за родителския дропдаун по стандартния начин. В полето **parent_name** записваме името на родителския дропдаун, а в полето **child_name** записваме за всяка опция името на **ФИКТИВЕН дропдаун** (**който не трябва да се добавя**). След това добавяме редове във **_fields_options** с опциите, които трябва да се показват в зависимия (втория) дропдаун, например (виж таблицата):

| parent_name | label | option_value | child_name |
|-------------|-------|--------------|------------|
| **за родителския дропдаун insurance_group** |
| insurance_group | Здравни застраховки | 1 | health |
| insurance_group | Гражданска отговорност | 2 | liability |
| insurance_group | Имуществени застраховки | 3 | property |
| **за зависимия дропдаун insurance_type** |
| health | Колективни здравни | 56 | |
| health | Лични здравни | 57 | |
| liability | ГО автомобили | 67 | |
| liability | ГО летателни апарати | 68 | |
| liability | ГО кораби | 71 | |
| property | Застраховка апартаменти | 72 | |
| property | Застраховка ниви | 73 | |

Не може да се предава зависимост (**on_change**) повече от едно ниво (след зависимата променлива).

### Методи във всички модули (dropdown.class.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCustomers | Списък на контрагенти | `method := getCustomers`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`id := (id на контрагент)`<br>`tag := 3,4` |
| 2 | getCustomerBranches | Списък на обектите за контрагент | `method := getCustomerBranches`<br>`customer := (id на контрагент)` |
| 3 | getCustomerContactPersons | Списък на лицата за контакт | `method := getCustomerContactPersons`<br>`customer := (id на контрагент)`<br>`customer_branch := (id на обект)` |
| 4 | getUsers | Списък на потребители | `method := getUsers`<br>`department := (id на отдел)`<br>`active := 1`<br>`is_portal := 0` |
| 5 | getUnfinishedSubprojects | Списък на неприключили подпроекти | `method := getUnfinishedSubprojects` |
| 6 | getUnfinishedPhases | Списък на неприключили фази | `method := getUnfinishedPhases` |
| 7 | getProjects | Списък на проекти | `method := getProjects`<br>`type := (id на тип)`<br>`status := (статус)` |
| 8 | getEmployees | Списък на служители | `method := getEmployees`<br>`employees_with_users_only := 1` |
| 9 | getOnChangeOptions | Списък зависещ от родителски списък | `method := getOnChangeOptions` |
| 10 | getArticleCategories | Списък на категории в номенклатури | `method := getArticleCategories`<br>`category := (id на категории)`<br>`exclude_root := (0/1)` |
| 11 | getRelatedCustomers | Списък на свързани контрагенти | `method := getRelatedCustomers`<br>`relative_type := (тип връзка)` |
| 12 | getDepartments | Списък на отдели | `method := getDepartments`<br>`department := (id на отдели)`<br>`exclude_root := (0/1)` |
| 13 | getGroups | Списък на групи | `method := getGroups`<br>`group := (id на група)`<br>`exclude_root := (0/1)` |
| 14 | getAttachments | Списък на прикачени файлове | `method := getAttachments`<br>`output := (link_with_permissions/just_id)` |
| 15 | getCurrencies | Списък на валути | `method := getCurrencies`<br>`format := short/verbose` |
| 16 | getAnnouncements | Списък на съобщения | `method := getAnnouncements`<br>`type := (id на тип)`<br>`assignments_users := (id на потребители)`<br>`assignments_departments := (id на отдели)` |
| 17 | getMonthsNames | Списък на месеците от годината | `method := getMonthsNames` |
| 18 | getWarehouses | Списък на складовете | `method := getWarehouses`<br>`company := (id на фирма)`<br>`office := (id на офис)`<br>`active := (0/1)` |
| 19 | getYears | Списък на години | `method := getYears`<br>`start := (стартова година)`<br>`offset_from_current := (отстъп)`<br>`num_active := (брой активни)`<br>`option_prefix := (префикс)`<br>`sort := asc/desc` |
| 20 | getDays | Списък на дни в месец | `method := getDays`<br>`month := (месец 1-12)`<br>`year := (година)`<br>`option_prefix := (префикс)`<br>`sort := asc/desc` |
| 21 | getCountries | Списък на всички държави | `method := getCountries` |

## Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в таблица **documents_statuses**. Тази таблица има следните полета:

- **id** – НЕ Е auto-increment, въвежда се ръчно и може да се дублира
- **doc_type** – id-то на типа документ, за който се отнася подстатусът
- **name** – името на подстатуса (езиково-зависимо)
- **description** – описание на подстатуса (езиково-зависимо, незадължително)
- **status** – към кой основен статус принадлежи (opened, locked, closed)
- **sequence** – поредност на състоянията
- **lang** – език за езиково-зависимите параметри

Пример за два реда от таблицата:

| id | doc_type | name | description | status | sequence | lang |
|----|----------|------|-------------|--------|----------|------|
| 2 | 1 | За одобрение | Статус "За одобрение" | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |

## Методи в контрагенти (customers.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`id := (id на контрагент)`<br>`tag := 3,4` |
| 2 | getCstmRelatives | Списък на връзки между контрагенти | `method := getCstmRelatives`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`c_id := (задължителен - id на контрагента)` |
| 3 | getIsCompany | Списък на видовете контрагенти (ЮЛ, ФЛ) | `method := getIsCompany` |
| 4 | getTypesKind | Списък на видовете контрагенти (ЮЛ, ФЛ, и двете) | `method := getTypesKind` |

## Методи в документи (documents.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions`<br>`is_company := (0/1)`<br>`type := (id на тип)` |
| 2 | getFcNums | Списък на фактури за контрагент | `method := getFcNums`<br>`var_id := (задължителен - id на променлива)`<br>`type := (задължителен - тип документ)` |
| 3 | getFcSums | Списък от сумите за плащане на фактури | `method := getFcSums`<br>`sum_var_id := (задължителен)`<br>`currency_var_id := (незадължителен)`<br>`type := (задължителен)`<br>`status := (незадължителен)`<br>`substatus := (незадължителен)` |
| 4 | getDocs | Списък на документи | `method := getDocs`<br>`status_no := (статус различен от)`<br>`type := (тип(ове) на документи)` |
| 5 | getBBElements | Списък на всички елементи в ББ към документ | `method := getBBElements` |
| 6 | getStatuses | Списък на всички статуси (и подстатуси) | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 7 | getOwnership | Списък на видове собственост | `method := getOwnership` |
| 8 | getDirections | Списък на видове посоки | `method := getDirections` |

## Методи в проекти (projects.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на всички статуси | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 2 | getPriorities | Списък на всички приоритети за проекти | `method := getPriorities` |
| 3 | getFinishedOptions | Списък на опциите за статус приключен | `method := getFinishedOptions` |

## Методи в задачи (tasks.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на всички статуси | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 2 | getPriorities | Списък на всички приоритети | `method := getPriorities` |
| 3 | getOwnership | Списък на видове собственост | `method := getOwnership` |
| 4 | getSeverity | Списък на всички приоритети | `method := getSeverity` |

## Методи във финанси (finance.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCompanyOffices | Списък на офисите за дадена компания | `method := getCompanyOffices`<br>`company_id := (id на фирма)`<br>`active := (0/1)` |
| 2 | getWarehousesEmployees | Списък на служителите в даден склад | `method := getWarehousesEmployees`<br>`warehouse := (id на склад)`<br>`active := (0/1)` |
| 3 | getCustomDropdown | Универсален метод за dropdown | `method := getCustomDropdown`<br>`table := (име на таблица)`<br>`table_i18n := (име на i18n таблица)`<br>`where := (WHERE условие)`<br>`value := id`<br>`label := name` |
| 4 | getBankAccounts | Списък на банковите сметки | `method := getBankAccounts`<br>`company_id := (id на фирма)`<br>`office_id := (id на офис)`<br>`active := (0/1)` |
| 5 | getCashboxes | Списък на касите | `method := getCashboxes`<br>`company_id := (id на фирма)`<br>`office_id := (id на офис)`<br>`active := (0/1)` |
| 6 | getDocumentsModels | Списък на видове записи за финансови документи | `method := getDocumentsModels` |
| 7 | getFinanceDocumentsTypesModels | Списък на всички видове записи за финансови документи | `method := getFinanceDocumentsTypesModels` |
| 8 | getDocumentsModelsForSections | Списък на видове финансови документи за раздели | `method := getDocumentsModelsForSections` |
| 9 | getStatuses | Списък на статусите на финансовите документи | `method := getStatuses` |
| 10 | getFinancePaymentsTypes | Списък типовете плащания | `method := getFinancePaymentsTypes` |
| 11 | getInvoicesTemplatesTypes | Списък на шаблони за фактуриране | `method := getInvoicesTemplatesTypes` |
| 12 | getRepaymentPlansStatuses | Списък на статусите на погасителните планове | `method := getRepaymentPlansStatuses` |
| 13 | getPaymentsStatuses | Списък на статусите за плащане | `method := getPaymentsStatuses` |
| 14 | getBudgetsStatuses | Списък на статусите за бюджети | `method := getBudgetsStatuses` |
| 15 | getBudgetsMethods | Списък на видове бюджет | `method := getBudgetsMethods` |
| 16 | getTransactionExpensesOperations | Списък на видовете транзакционни операции | `method := getTransactionExpensesOperations` |
| 17 | getPaymentsTypes | Списък на типовете плащания | `method := getPaymentsTypes` |
| 18 | getBankAccountsTypes | Списък на видовете банкови сметки | `method := getBankAccountsTypes` |
| 19 | getRecurrenceTypes | Списък на опции за продължителност за периодични плащания | `method := getRecurrenceTypes` |
| 20 | getAnalysisTypesKinds | Списък на опции за анализ | `method := getAnalysisTypesKinds` |
| 21 | getDistributionStatuses | Състояния на разпределение | `method := getDistributionStatuses` |
| 22 | getDocsPaymentStatuses | Списък на платежните статуси | `method := getDocsPaymentStatuses` |
| 23 | getFinanceAnalysisItems | Списък на приходни или разходни пера | `method := getFinanceAnalysisItems`<br>`type := income/expense`<br>`item_type := income/expense` |
| 24 | getCompaniesOffices | Списък на компаниите с техните офиси | `method := getCompaniesOffices` |
| 25 | getCompaniesData | Списък на опции за Каса/Банкова сметка | `method := getCompaniesData`<br>`payment_direction := incomes/expenses`<br>`company_id := (id на фирми)`<br>`office_id := (id на офиси)`<br>`payment_type := cash/bank`<br>`active := (0/1)` |

## Методи в договори (contracts.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getSinglePeriodLengths | Списък на опции периодичност | `method := getSinglePeriodLengths` |
| 2 | getSubtypes | Списък на под-типовете договори | `method := getSubtypes` |

## Методи в събития (events.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на статусите на събития | `method := getStatuses` |
| 2 | getPriorities | Списък на приоритетите | `method := getPriorities` |
| 3 | getAllDay | Списък на опции за продължителност | `method := getAllDay` |
| 4 | getParticipantUsers | Списък на участниците в събитие | `method := getParticipantUsers` |

## Методи в инфо панели (dashlets.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getDashletsModules | Списък на модулите за инфо панели | `method := getDashletsModules` |

## Групова таблица от втори вид - GT2

### Типове документи/договори

Добавена е възможност към типовете документи и договори да се добавя групова таблица от 2-ри вид. За целта се създава новият тип документ/договор. При добавяне/редакция на типа може да се "включи" опцията за групова таблица от 2-ри вид като **!!! ЗАДЪЛЖИТЕЛНО !!!** се посочи секцията (layout), в която трябва да се покаже тя. Ако има готови потребителски (несистемни) секции, груповата таблица може да се сложи във всяка от тях, или в поле „Секция за таблицата" може да се въведе име, с което ще бъде създадена нова потребителска секция.

## Описание на настройките за nZoom 1.4.x

Настройките в nZoom се правят на няколко места:
- В главен конфигурационен файл (`/conf/.htconfig`) – тези настройки се отнасят за цялата система
- В конфигурационните файлове на всеки един модул (`/modules/<module>/conf/.htconfig`) – тези настройки са локални за конкретния модул и за модулите зависещи от този конкретен модул
- В таблица 'settings' в базата с данни – тези настройки отново се отнасят за цялата система

Тенденцията е всички настройки да се изнесат в таблица 'settings' и да има минимално настройки в конфигурационни файлове. Това обаче ще стане постепенно.

Конфигурационните файлове са разделени на секции. Всяка секция е обозначена в следния формат `[section_name]`. Всяка секция може да съдържа параметри, чиито стойности се задават непосредствено след знака за равенство в следния формат:

```
[section_name]
parameter = value
```

Например:
```
[company_info]
company = ACME
address = Address
```

**ВАЖНО**: В конфигурационните файлове НЕ БИВА да се премахват секции или параметри, а само да се сменят или премахват стойностите им.

### Главен конфигурационен файл (/conf/.htconfig)

Състои се от няколко секции:

#### 1.1. Секция [sys]
Тази секция, както подсказва и името, е системна. Секцията се състои от следните параметри:
- `system = nZoom` - име на системата
- `version = 1.4.0` - версия на системата
- `build = $Rev: 1485 $` - текуща ревизия (build) на системата
- `timezone = +2` – часова зона на системата
- `lock_records = 1` – параметър, указващ дали системата да заключва записите (стойност 0) или не (стойност 1)

#### 1.2. Секция [themes]
Тази секция съдържа настройките на темите на приложението:
- `default_theme = Default` - име на темата по подразбиране

#### 1.3. Секция [i18n]
Тази секция съдържа настройките за интернационализацията (i18n) на системата:
- `default_lang = bg` – език на интерфейса на приложението, по подразбиране той е Български
- `default_locale = bg_BG.UTF-8` - параметър, указващ локалните настройки (locale) на приложението
- `supported_langs = bg, en` – списък на поддържаните от системата езици за интерфейса
- `model_langs = bg, en, de` – списък на поддържаните от системата езици за моделите

#### 1.4. Секция [database]
Тази секция съдържа настройките за достъп до базата от данни:
- `type = mysqli` – тип на базата от данни
- `host = localhost` - адрес на сървъра, където се намира базата
- `user = <username>` – потребител за достъп до базата
- `pass = <password>` – парола за достъп до базата от данни
- `name = <database>` – име на базата

#### 1.5. Секция [modules]
Тази секция изброява модулите, които са достъпни през главното меню на системата:
- `frontend_modules = <списък от модули>` – списък от модули достъпни във основния панел
- `backend_modules = <списък от модули>` – списък от модули достъпни във панела за настройки

Списък от модули се задава във специален формат:
- `<module_name>` - само името на модула
- `<module_name>::<controller_name>` - името на модула и име на контролер
- `<module_name>|<action_name>` - името на модула и име на действието
- `<module_name>::<controller_name>|<action_name>` - пълен формат

#### 1.6. Секция [log]
Тази секция съдържа настройките за логовете на приложението:
- `level = 1` – ниво за запис на лога:
  - 0 = NONE
  - 1 = DEBUG
  - 2 = INFO
  - 3 = WARNING
  - 4 = ERROR
- `date_format = Y-m-d H:m:s` - формат на датата в лога

#### 1.7. Секция [emails]
Тази секция съдържа настройките за изпращане на известявания:
- `from_name = nZoom Notification System` – указва името на подателя на имейлите
- `from_email = <EMAIL>` - указва електронния адрес на подателя
- `manager_email = <EMAIL>` – указва електронния адрес на управителя

#### 1.8. Секция [company_info]
Тази секция съдържа данни за фирмата, ползвател на системата:
- `company = ACME` – указва името на фирмата
- `address = Address` – указва адреса на фирмата
- `phone = ***********` – указва телефона на фирмата
- `fax = ***********` – указва факса на фирмата
- `url = http://www.example.org` – указва адрес на уеб сайта
- `email = <EMAIL>` – указва електронния адрес
- `contact_person = John Smith` – указва името за контакт
- `bulstat = 1234567890` – указва БУЛСТАТ-а на фирмата
- `taxnumber = 0987654321` – указва данъчния номер

#### 1.9. Секция [mapped_drives]
Тази секция съдържа настройките мрежовите устройства:
- `y = \\***********\install\` – буквата на мрежовото устройство и мрежовия път

#### 1.10. Секция [mailer]
Тази секция съдържа настройките типа на известяване:
- `type = mail` – указва типа на системата за изпращане (mail, smtp или sendmail)

#### 1.11. Секция [smtp]
Тази секция съдържа настройките на SMTP сървъра:
- `host = 127.0.0.1` – адрес на SMTP сървъра
- `user = <username>` – потребителско име за достъп до SMTP сървъра
- `pass = <pass>` – парола за достъп до SMTP сървъра

#### 1.12. Секция [sendmail]
Тази секция съдържа настройките на sendmail програмата:
- `path = /usr/sbin/sendmail` – път до sendmail програмата в Unix/Linux

## Настройки в таблица settings

Таблицата съдържа няколко колони по подобие на конфигурационния файл: секция, параметър и стойност.

### 2.1 Секция crontab

Тази секция съдържа настройките за автоматичното известяване (имейлите, които се изпращат периодично всеки ден):

#### disable
Забранява изпълнението на всички крон задачи или на точно определени крон задачи:
- `disable = all` – забранява всички крон задачи
- `disable = main` – забранява основния кронтаб
- `disable = automations` – забранява проверката за кронтаб серийни изпълнения
- `disable = currencies_update_rates` – забранява актуализация на валутни курсове
- `disable = reminders` – забранява известяванията за напомняния за записи
- `disable = emails_campaigns` – забранява изпращането на и-мейл кампании
- `disable = notify_invoice_supervisors` – забранява известяване на отговорници по издаване на фактури
- `disable = issue_invoices` - забранява издаване на фактури от crontab
- `disable = notify_payment_status` - забранява известяване за променен статус на плащане
- `disable = archive` – забранява архивиране от crontab

Възможна е настройка за забрана на няколко вида крон задачи:
```
disable = emails_campaigns, reminders, automations
```

#### send_interval
Интервал (в часове) на колко време да се изпраща писмо. Например, ако сложим интервала да е 72, потребителят ще получава имейл на всеки 3 дни.

#### before_interval
Интервал за известяването за възможност за изтичане на срок на документ или етап на проект (в часове). Интервалът отмерва предварение в часове, оставащи до изтичането на срока.

#### base_host
Адрес на приложението, използван при автоматичните известявания.

#### tasks_send_interval
Интервал (в часове) на колко време да се изпраща известяване за задачи.

#### tasks_period_before
Интервал за известяването за възможност за изтичане на срок на задача (в часове).

#### tasks_periodicity
Максимален брой пъти, които да се изпрати известяване за изтичащ срок на задачи.

#### before_start_contract
Известяване преди влизане в сила на договори. Например `30,15,5` – известява 30 дни преди влизане в сила на договор, след това съответно 15 и 5 дни преди влизане в сила.

#### before_end_contract
Известяване преди изтичане на договори. Например `30,15,5` – известява 30 дни преди изтичане на договор.

#### archive_interval
Интервал за архивиране за записи – за всеки модел на нов ред. Задава се във формат:
```
<модел> := <основна променлива> => <интервал>
```
или само
```
<модел> := <интервал>
```

Интервалът трябва да бъде във формат: `<брой> <вид>`, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

Пример:
```
document := 1 YEAR
```
или
```
document := status_modified => 12 MONTH
```

#### archive_models
Видове записи (имена на модели в ед.ч.), разделени със запетая, за които да се извърши архивиране.

#### archive_responsible
ID на потребители, разделени със запетая, на които да се изпрати известяване за извършено архивиране от crontab.

#### archive_remove_files
Задава дали при архивиране от crontab да се премахват файловете към архивираните записи. Стойности: 1 за да се премахват, празна стойност или 0, за да не се премахват.

#### purge_interval
Интервал за унищожаване за записи – аналогично на archive_interval.

#### purge_models
Видове записи за които да се извърши унищожаване.

#### purge_responsible
ID на потребители на които да се изпрати известяване за извършено унищожаване от crontab.

#### invoices_issue_notification
Интервал преди следващо издаване на фактури, когато трябва да се известят отговорници по издаване на фактури.

#### invoices_send_lang
Език на който да се изпращат на контрагенти фактури от договори след издаване.

#### translate_invoices_issued
Езици на които да се преведат фактури от договори след издаване.

#### notify_payment_status_interval
Интервал за търсене за известяване за промяна на статус на плащане.

#### notify_payment_status_types
Типове приходни документи за известяване за промяна на статус на плащане.

### 2.2 Настройваема валидация на основни данни

#### Допълнителни задължителни полета

Отнася се за модули: Контрагенти, Документи, Проекти, Задачи, Договори. Валидацията е по типове.

**Пълен списък от полета:**

**За модул documents**: `custom_num`, `trademark`, `contract`, `project`, `office`, `employee`, `media`, `deadline`, `validity_term`, `description`, `notes`, `department`, `date`

**За модул projects**: `trademark`, `date_start`, `date_end`, `priority`, `parent_project`, `finished_part`, `description`, `notes`

**За модул tasks**: `trademark`, `project`, `planned_time`, `severity`, `progress`, `equipment`, `task_field`, `source`, `description`, `notes`, `department`

**За модул customers**:
- За физическо лице: `company_department`, `position`, `ucn`, `identity_num`, `identity_date`, `identity_by`, `identity_valid`, `address_by_personal_id`
- За юридическо лице: `company_name`, `in_dds`, `eik`, `registration_file`, `registration_volume`, `registration_number`, `registration_address`, `mol`
- За всички: `department`, `assigned`, `country`, `city`, `postal_code`, `address`, `notes`, `phone`, `fax`, `gsm`, `email`, `web`, `skype`, `othercontact`, `bank`, `iban`, `bic`

**За модул contracts**: `custom_num`, `trademark`, `project`, `employee`, `date_sign`, `date_start`, `date_validity`, `date_end`, `description`, `notes`, `department`

Допълнителните задължителни полета за определен тип записи могат да се зададат: в section се записва името на модула, в name – `validate_<id_на_тип>` и за value се изреждат със запетаи полетата.

Пример:
```
section - customers
name – validate_1
value - company_department, country
```

#### Уникални полета

**Пълен списък от полета:**

**За модул documents**: `customer`, `trademark`, `contract`, `custom_num`, `date`
**За модул projects**: `trademark`, `customer`
**За модул tasks**: `trademark`
**За модул customers**: `phone`, `fax`, `gsm`, `identity_num`, `iban`
**За модул contracts**: `custom_num`, `trademark`, `customer`
**За модул nomenclatures**: `name`

Уникални полета се зададат като името на полето се предхожда от "unique_".

Пример:
```
section - documents
name – validate_1
value - custom_num, unique_custom_num, current_year
```

### 2.3 Автоматични кодове

Настройки за автоматично генериране на кодове:

| section | name | value |
|---------|------|-------|
| projects | auto_code_leading_zeros | 4 |
| projects | auto_code_suffix | PRJ |
| users | auto_code_leading_zeros | 4 |
| users | auto_code_suffix | USR |
| documents_types | auto_code_leading_zeros | 4 |
| documents_types | auto_code_suffix | DTP |
| customers | auto_code_leading_zeros | 4 |
| customers | auto_code_suffix | CST |
| offices | auto_code_leading_zeros | 4 |
| offices | auto_code_suffix | OFF |
| contracts_types | auto_code_leading_zeros | 4 |
| contracts_types | auto_code_suffix | CRT |
| finance_companies | auto_code_leading_zeros | 4 |
| finance_companies | auto_code_suffix | COM |
| finance_documents_types | auto_code_leading_zeros | 4 |
| finance_documents_types | auto_code_suffix | FDT |

### 2.1 Секция general

#### Настройки за автоматично попълване на полета

**name**: `autocomplete_N`, където N е id на тип документ/задача/проект
**value**: списък от полета, разделени със запетая

Пример:
```
autocomplete_15 := customer,project,department
```

#### Настройки за типове назначения

**name**: `assignment_types_N`, където N е id на тип
**value**: `owner,responsible,observer,decision`

За всеки тип се настройва какви типове назначения ще се прилагат.

### 2.2 Секция tasks

#### Типове назначавания за задачи

**name**: `assignment_types_N`, където N е id на тип задача
**value**: `owner,responsible,observer,decision`

За всеки тип задача се настройва какви типове назначения ще се прилагат за нея (Изпълнител, Отговорник, Наблюдаващ, Вземащ решения).
Настройката "Типове назначения" е достъпна в интерфейса при редакция на Тип задача.

#### Полета за шаблони за типовете задачи

**name**: `configurator_1`
**value**: `name,planned_time,severity,description,department,active,group`

**name**: `configurator_2`
**value**: `name,planned_time,severity,description,department,active,group`

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета за формите

Възможни полета:
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `planned_start_date` - планирано начало
- `planned_finish_date` - планиран край
- `planned_time` - планирано време
- `severity` - приоритет
- `progress` - % на изпълнение
- `equipment` - апаратура
- `task_field` - тестово поле
- `source` - източник на задачата
- `description` - описание
- `notes` - забележка / решение
- `department` - отдел
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета, които да бъдат одитирани

Възможни полета за одит:
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `planned_start_date` - начало
- `planned_finish_date` - край
- `status` - статус
- `substatus` - състояние (подстатус)
- `planned_time` - планирано време
- `severity` - приоритет
- `progress` - % на изпълнение
- `equipment` - апаратура
- `task_field` - тестово поле
- `source` - източник на задачата
- `description` - описание
- `notes` - забележка / решение
- `department` - отдел
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ
- `tag` - промяна на тагове
- `add_attachments` - добавяне на прикачени файлове
- `del_attachment` - изтриване на прикачен файл

### 2.3 Секция documents

#### Типове назначавания за документи

**name**: `assignment_types_N`, където N е id на тип документ
**value**: `owner,responsible,observer,decision`

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета

Възможни полета:
- `custom_num` - номер от контрагент
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `office` - офис
- `employee` - служител
- `contract` - по договор №
- `media` - медия на документа
- `deadline` - срок за обработка
- `validity_term` - срок на валидност
- `date` - дата
- `description` - описание
- `notes` - бележки
- `department` - отдел
- `attachment` - файл
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

#### Настройка за полето "По договор №"

**name**: `contract_custom_label`
**value**: Използвайки ключови думи: **custom_num**, **num** и **name**

Пример:
```
[custom_num] name
```

#### Настройки за архивиране

**name**: `archive_N`, където N е id на тип документ
**value**:
```
field := <променлива>
interval := <интервал>
```

За променлива се посочва основна или допълнителна променлива от тип date/datetime, като допълнителната трябва да се запише с префикс „a__" пред името.

Интервалът трябва да бъде във формат: `<брой> <вид>`, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

#### Настройки за унищожаване

**name**: `purge_N`, където N е id на тип документ
**value**:
```
field := <променлива>
interval := <интервал>
```

### 2.4 Секция customers

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета

Възможни полета:
- `name` - име
- `code` - код
- `company_department` - отдел във фирмата (само за ФЛ)
- `position` - позиция (само за ФЛ)
- `country` - държава
- `city` - град
- `postal_code` - пощенски код
- `address` - адрес
- `notes` - бележки
- `phone` - телефон
- `fax` - факс
- `gsm` - мобилен телефон
- `email` - e-mail
- `web` - уеб сайт
- `skype` - Skype
- `othercontact` - Друг контакт
- `company_name` - пълно наименование на фирмата (само за ЮЛ)
- `in_dds` - Идентификационен номер за регистрация по ДДС (само за ЮЛ)
- `eik` - ЕИК (само за ЮЛ)
- `registration_file` - фирмено дело № (само за ЮЛ)
- `registration_volume` - том/страница/регистър (само за ЮЛ)
- `registration_number` - съд по регистрация (само за ЮЛ)
- `registration_address` - адрес по регистрация (само за ЮЛ)
- `mol` - МОЛ (само за ЮЛ)
- `ucn` - ЕГН (само за ФЛ)
- `identity_num` - лична карта № (само за ФЛ)
- `identity_date` - лична карта издадена на (само за ФЛ)
- `identity_by` - лична карта издадена от (само за ФЛ)
- `identity_valid` - валидна до (само за ФЛ)
- `address_by_personal_id` - адрес по лична карта (само за ФЛ)
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

По подразбиране са зададени: `name, lastname, code, in_dds, eik, mol, bank, iban, bic, department, assigned, tag`.

### 2.5 Секция projects

#### Настройки за избор на отговорник на фаза

**name**: `phases_choose_responsible`
**value**: 0 или 1. При 1 можем да избираме отговорник на фаза, при 0 – не можем.

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

Възможни полета:
- `name` - име
- `customer` - контрагент
- `trademark` - търговска марка
- `code` - код
- `date_start` - начало
- `date_end` - край
- `priority` - приоритет
- `parent_project` - подпроект на
- `manager` - ръководител
- `finished_part` - % изпълнение
- `budget` - планиран бюджет
- `work_period` - човекочасове
- `description` - описание
- `status` - статус
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments` - назначения
- `tag` - промяна на тагове
- `add_attachments` - добавяне на прикачени файлове
- `del_attachment` - изтриване на прикачен файл

### 2.6 Секция precision

Настройки за прецизността на закръгленията (0-6) в GT2, за разпределяне, индекси, за цени на номенклатури и др.

- `gt2_rows` – Прецизност на стойностите в редовете в GT2
- `gt2_total` – Прецизност на **Общо** в GT2
- `gt2_total_vat` – Прецизност на **Общо ДДС** в GT2
- `gt2_total_with_vat` – Прецизност на **Общо с ДДС** в GT2
- `gt2_quantity` – Прецизност за количеството в редовете в GT2
- `indexes_values` – Прецизност за резултат от формули на индекси
- `finance_analysis_percentage` – Прецизност на процентно разпределяне
- `nom_sell_price` - Прецизност на продажна цена на номенклатури
- `nom_last_delivery_price` – Прецизност на последна доставна цена
- `nom_average_weighted_delivery_price` - Прецизност на среднопретеглена доставна цена

### 2.7 Секция contracts

#### Типове назначавания за договори

**name**: `assignment_types_N`, където N е id на тип договор
**value**: `owner,responsible,observer,decision`

#### Настройка за одитираните основни променливи

**name**: `audit`
**value**: съдържа изброените полета за одит

По подразбиране са:
- `active` - готовност за публикуване
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `status` - статус
- `substatus` - състояние (подстатус)
- `company` - фирма
- `office` - офис
- `employee` - служител
- `date_sign` - дата на подписване
- `date_start` - дата на влизане в сила
- `date_validity` - дата на изтичане
- `date_end` - дата на опция за прекратяване
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ
- `tag` - промяна на тагове
- `add_attachments` - прикачени файлове

### 2.8 Активност на ред

Активност на ред е възможност ред от списък на записи да стане изцяло активен, т.е. където и да се щракне с мишката на него, да отвежда на място, което му е посочено.

В **section** се записва името на модул (`documents`, `customers`, `projects`, `nomenclatures`, `finance_budgets`, `finance_incomes_reasons`, `finance_expenses_reasons`, `finance_payments`), в **name** – текстът `row_link_action` и за **value** се въвежда името на действието.

Пример:
```
section - documents
name – row_link_action
value - edit
```

Действието трябва да съвпада с действие (action) за модула от таблица 'roles_definitions', например: `view`, `edit`, `communications`, `relatives` и т.н.

### 2.9 Секция turnovers

Настройка за валута на обороти – ако не е въведена, се взема основната валута за инсталацията.

`currency` – валута (**BGN**, **EUR** ...)

### 2.10 Свързани записи

Настройката за свързани записи позволява да се указва кои от панелите (табове) със свързани записи ще бъдат видими под формата в режим на разглеждане и редакция.

В **section** се записва името на модул (`customers`, `projects`, `nomenclatures`, `contracts`), в **name** – стойност `related_records_modules` и за **value** се изреждат имената на панелите, разделени със запетая.

Възможните стойности са:

#### За Контрагенти:
- `documents` – Документи
- `projects` – Проекти
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_repayment_plans` – Погасителни планове
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори
- `referent_documents` – Референтни документи
- `referent_projects` – Референтни проекти
- `referent_contracts` – Референтни договори
- `referent_customers` – Референтни контрагенти

#### За Проекти:
- `documents` – Документи
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори

#### За Номенклатури:
- `customers` – Контрагенти
- `documents` – Документи
- `projects` – Проекти
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_annulments` – Протоколи за анулиране
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори
- `nomenclatures` – Номенклатури

#### За Договори:
- `documents` – Документи

Пример:
```
section - customers
name – related_records_modules
value – documents, projects, referent_documents
```

### 2.11 Секция finance

#### Типове назначавания за финансови документи

**name**: `assignment_types_N`, където N е id на тип финансов документ
**value**: `owner,responsible,observer,decision`

#### Настройка за фискална валута на фактури

**name**: `fiscal_currency`
**value**: валута (**BGN**, **EUR** ...)

Ако не е въведена, се взема основната валута за инсталацията.

### 2.12 Секция users

**max_failed_attempts**: Колко най-много неуспешни опити за вход може да направи потребителя, в някакъв интервал от време, преди да се задейства защитата. В браузъра защитата е поле с captcha, през REST се връща код 429 и се блокират следващи опити за вход, докато не изтече определеното време.

**max_failed_attempts_timeout**: Какъв е интервала от време за проверка на неуспешните опити. Задава се в брой секунди (30мин = 1800сек, 60мин = 3600). За да изключите изцяло защитата задайте стойност 0.

## Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в една таблица **documents_statuses**. Тази таблица има следните полета:

- **id** – НЕ Е auto-increment, тоест въвежда се ръчно и може да се дублира. Това е направено защото нямаме отделна таблица за езиково-зависимите променливи и опциите за отделните езици се намират в тази таблица. Тоест, ако даден подстатус с id 5 сме въвели на български и искаме да го въведем и на английски, ще въведем ред в таблицата, който отново ще има id 5. Въвеждането на id е задължително!

- **doc_type** – id-то на типа документ, за който се отнася подстатусът. Примерно, ако искаме подстатусът да е за оферта, а тя е с id 15, то в това поле трябва да въведем 15.

- **name** – името на подстатуса. Полето е езиково-зависимо!

- **description** – описание на подстатуса. Полето е езиково-зависимо и е единственото от всички полета в тази таблица, чието попълване не е задължително.

- **status** – оттук се избира към кой основен статус ще принадлежи състоянието, Възможностите са opened, locked, closed

- **sequence** – поредност на състоянията. Въвеждат се номера в зависимост от това в какъв ред искаме да се извеждат подстатусите КЪМ СЪОТВЕТНИЯ ОСНОВЕН СТАТУС. Тоест, ако към "Отворен" имаме въведени три състояния – "В процес на изработка", "За одобрение" и "За редакция" – и искаме да ги виждаме в този ред в полето за sequence трябва да въведем съотеветно 1, 2 и 3 или 7, 12 и 19 (примерно). Ако състоянията са към различни основни статуси, се позволява дублиране на sequence-а. Това ще рече, че можем да имаме подстатус на opened със sequence=1 и да имаме подстатус към closed, който също да има sequence=1 – това НЕ Е проблем.

- **lang** – език за езиково-зависимите параметри. Когато искаме да въведем състояние на друг език, тук указваме какъв ще е езикът ("bg", "en", "de" или друг). Когато въвеждаме състояние на друг език, описваме на съответния език само езиково-зависимите променливи останалите трябва да останат същите.

Пример за два реда от таблицата, описващи състоянието "За одобрение" на български и на английски език:

| id | doc_type | name | description | status | sequence | lang |
|----|----------|------|-------------|--------|----------|------|
| 2 | 1 | За одобрение | Статус "За одобрение" | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |
