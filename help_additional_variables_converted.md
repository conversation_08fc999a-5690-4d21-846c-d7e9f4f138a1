# Описание на използване и вписване на допълнителни променливи към модели за nZoom 1.7.x

Допълнителните променливи като типове се описват в таблиците:

- **`_fields_meta`** – описва езиково независима информация за променливите
- **`_fields_i18n`** – описват се етикетите, help, описание за променливите на съответните езици и може да се добавя заден етикет
- **`_fields_options`** – изброимите елементи на променливи от тип dropdown, radio, checkbox_group
- **`layouts`** – информация за секциите (layouts) на променливи – със съответните етикет и позиция

Стойностите на допълнителните променливи се съхраняват в таблици `[име на модели]_cstm`, като `documents_cstm`, `customers_cstm` ..., с изключение на стойностите за променливи от тип:
- **gt2** - съхраняват се в `gt2_details`
- **bb** – в `bb`
- за конфигуратор тип Франкенщайн – в `configurator`

---

## I. Описание на таблиците с техните полета

### 1. Таблица `_fields_meta`:

#### 1.1 `id`
autoincrement, служи за релация с `_fields_i18n` (поле `parent_id`) и `[…]_cstm` (поле `var_id`), не се попълва, приема поредна стойност автоматично!

#### 1.2 `model`
име на модела, Например: **Customer** (за контрагенти), **Document** (за документи), **Documents_Stage** (за етапи на документи), **Tasks** (за задачи – използва се само за добавяне на бутони за справка) и т.н.

#### 1.3 `model_type`
тип на модела, число, отговаря на ID-то на типа на модела, Например: **1** за тип документи с ID 1

#### 1.4 `name`
име на променливата на латиница, уникално за модела и типа на модела, Например: **offer_description**

При трансформиране има възможност за копиране на допълнителни променливи към основни. Името на допълнителната променлива трябва да е от вида **`basic_[името на основната променлива]`**, например **`basic_customer`** се копира към основна променлива **`customer`**, **`basic_department`** се копира към основна променлива **`department`**.

За копиране на назначения при трансформиране трябва да имаме допълнителна променлива **`basic_department`** за разпределяне и променлива **`basic_assignments_owner`** за изпълнители. Възможно е да се копират и другите назначения като променливите да са **`basic_assignments_responsible`**, **`basic_assignments_observer`**, **`basic_assignments_decision`**.

Възможно е и обратното копиране – на основни към допълнителни променливи при трансформация. Името на допълнителната променлива трябва да е от вида **`source_[името на основната променлива]`**, например в **`source_customer`** се копира стойността на основната променлива **`customer`**.

Ако в конкретната инсталация се използва Астериск и полето е от тип text, променливата може да се настрои, така че да се показва от nZoom като телефонен номер, който може да бъде набран. За целта името на променливата трябва да има следния формат:
- **`asteriskcall_phone_[име на променливата]`** - за телефон
- **`asteriskcall_gsm_[име на променливата]`** - за мобилен телефон
- **`asteriskcall_fax_[име на променливата]`** - за факс

#### 1.5 `source`
в полето се записват множество различни настройки във формат: **име := стойност**. Някои от тях са специфични за даден вид променлива и са обяснени в следващите точки.

### Настройване на изчисления

Полетата могат да имат изчисления, чиито параметри се дефинират в **source** колоната. Запазените думи за изчисленията са:

| Ключова дума | Обяснение |
|--------------|-----------|
| **equation** | Израз за изчисление с вече приготвените параметри |
| **array_function** | Агрегатна функция, използвана само в полета, които не са в групова таблица. Има две възможни стойности: **sum** и **avg**. **sum** - сумират се всички стойности на **equation** за редовете от групова таблица, указани в update_fields или num_rows. **avg** – взема средна аретметична стойност за всички редове |
| **update_fields** | Използва се само в полета, които не са в групова таблица. Указва списък от полета от групова таблица, които трябва да се изчислят преди да се направи калкулацията по equation |
| **num_rows** | Указва име на поле от групова таблица, което я обозначава, за да се намерят редовете в таблицата |
| **last_equation** | Израз за изчисление СЛЕД като вече е изчислена агрегатната функция |
| **format** | Низ за форматиране на крайния резултат. За 2 цифри след десетичния знак: %.2F |

#### Параметри - \$а, \$tax, \$customer

Освен запазените думи, може да има и **параметри** участващи в уравнението (equation, last_equation). Параметрите се отбелязват с \$ отпред (например: \$а, \$tax, \$customer) и могат да получават стойности от REQUEST (данни от формата) или SQL (данни от базата).

**Важно**: имената на параметри (заедно с \$) не трябва да се съдържат едно в друго, т.е. недопустимо е да имаме **\$а** (например със стойност 5) и **\$average** (например със стойност 7), защото в equation := **\$a*\$average** ще се замести: equation := **5*5verage**, което ще произведе грешка при изчислението.

**Важно**: стойността получена от изчислението трябва да е валидно число, в противен случай трябва да е в кавички.

#### Пример 1: Просто изчисление без агрегатна функция

Съдържанието на **source** за поле с име **commission**, което се намира в едноредова таблица или конфигуратор:

```
$customer := request('customer')
$total := request('total')
$commission_percentage := sql('SELECT value FROM nom_cstm WHERE var_id=123 AND model_id = $customer')
equation := ($total * $commission_percentage) / 100
format := %.2F
```

**Обяснение:**
- Параметърът \$**customer** получава стойност от формата от ид на контрагента
- Параметърът \$**total** получава стойност от формата от поле с име 'total'
- Параметърът \$**commission_percentage** получава стойност за процента на комисионната от базата от данни
- Част от параметрите участват в уравнението за определяне размера на комисионната
- Резултатът се форматира с 2 цифри след десетичния знак

#### Пример 2: Изчисление с агрегатна функция

Имаме групова таблица с колони **price**, **quantity**, **discount_percentage**, **discount** и **subtotal**:

| price | quantity | discount_percentage | discount (readonly) | subtotal (readonly) |
|-------|----------|-------------------|-------------------|-------------------|
| (въвежда се) | (въвежда се) | (въвежда се) | (изчислява се) | (изчислява се) |

Има едно свободно стоящо поле **tax_percentage**, което съдържа процента на данъка.
Има и още едно свободно стоящо поле **total**, което се използва, за да натрупа общия сбор.

Съдържанието на **source** за поле **discount**:

```
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
equation := $price*$quantity*$discount_perc/100
format := %.2F
```

Съдържанието на **source** за поле **subtotal**:

```
$price := request('price')
$quantity := request('quantity')
$discount := request('discount')
equation := $price*$quantity - $discount
format := %.2F
```

**ВНИМАНИЕ:** Параметърът \$**discount** получава стойност от вече изчислената стойност на полето discount.

#### Подсигуряване при деление:

```
$a := request('discount_percentage')
$b := request('coef')
equation := ($b!=0 ? $a/$b:0)
```

#### Пример 3: Изчисляване на работените часове

Изчисляване на работените часове между две дати (датите са с дата и час):

```
$ad := request('date')
$bd := request('activity_end')
$at := request('from_hour')
$bt := request('to_hour')
$from := SQL('select cast(concat("$ad", " ", "$at") as datetime) ')
$to := SQL('select cast(concat("$bd", " ", "$bt") as datetime) ')
$ttal := SQL('SELECT TIME_FORMAT(SEC_TO_TIME(TIMESTAMPDIFF(SECOND, "$from", "$to") - TIMESTAMPDIFF(DAY, "$ad", "$bd")*16*60*60), "%H:%i")')
equation := "$ttal"
```

Примера е от инсталация QUEISSER за променлива с ID 1821. Пресмята се колко часа е работено между две дати, като се приема, че работното време за всеки 24 ч. е 8 ч.

### Вземане на опции за променлива от изброим тип

Опции за променливи от изброим тип (dropdown, radio или checkbox_group) могат да се вземат по 2 начина: от стойности във `*_fields_options*` или от записи от даден (под)модул.

#### Първи начин:

Ако името (**name**) на променливата отговаря на **parent_name** на опциите, то в **source** не трябва да се задават други настройки.

Ако се налага за различни променливи да се ползват едни и същи изброими стойности, в полето **source** записваме името на променливата:

```
field := dropdown1
```

#### Втори начин:

Опциите могат да се вземат от функция (метод на клас dropdown), синтаксисът на записване е:

```
method := function
$arg1 := argument1
$arg2 := argument2
```

### Свързани (зависими) падащи списъци

Възможно е dropdown стойностите да са зависими от друг dropdown. Например:

За променлива PP_Project, полето **source** ще включва:

```
method := getDocs
deleted := 0
active := 1
type := 7
status_no := 'closed'
on_change := 'PP_Stage'
```

В **on_change** записваме променливата, която ще промени съдържанието си при промяна на PP_Project. За полето **source** на PP_Stage записваме:

```
get_id := request('PP_Project')
default_id := sql('select value from documents_cstm where model_id=$model_id and var_id=1525')
method := getDocStages
```

**get_id** взема стойността на PP_Project (ID на документ) от POST заявка и се използва при редактиране на допълнителните променливи. **default_id** взема записаната стойност от базата за PP_Project и се ползва при разглеждане на допълнителните променливи и генериране. В sql заявката **\$model_id** е запазена променлива, която е id на редактирания модел.

### Методи във всички модули (dropdown.class.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCustomers | Списък на контрагенти | `method := getCustomers`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`id := (id на контрагент)`<br>`tag := 3,4` |
| 2 | getCustomerBranches | Списък на обектите за контрагент | `method := getCustomerBranches`<br>`customer := (id на контрагент)` |
| 3 | getCustomerContactPersons | Списък на лицата за контакт | `method := getCustomerContactPersons`<br>`customer := (id на контрагент)`<br>`customer_branch := (id на обект)` |
| 4 | getUsers | Списък на потребители | `method := getUsers`<br>`department := (id на отдел)`<br>`active := 1`<br>`is_portal := 0` |
| 5 | getUnfinishedSubprojects | Списък на неприключили подпроекти | `method := getUnfinishedSubprojects` |
| 6 | getUnfinishedPhases | Списък на неприключили фази | `method := getUnfinishedPhases` |
| 7 | getProjects | Списък на проекти | `method := getProjects`<br>`type := (id на тип)`<br>`status := (статус)` |
| 8 | getEmployees | Списък на служители | `method := getEmployees`<br>`employees_with_users_only := 1` |
| 9 | getOnChangeOptions | Списък зависещ от родителски списък | `method := getOnChangeOptions` |
| 10 | getArticleCategories | Списък на категории в номенклатури | `method := getArticleCategories`<br>`category := (id на категории)`<br>`exclude_root := (0/1)` |
| 11 | getRelatedCustomers | Списък на свързани контрагенти | `method := getRelatedCustomers`<br>`relative_type := (тип връзка)` |
| 12 | getDepartments | Списък на отдели | `method := getDepartments`<br>`department := (id на отдели)`<br>`exclude_root := (0/1)` |
| 13 | getGroups | Списък на групи | `method := getGroups`<br>`group := (id на група)`<br>`exclude_root := (0/1)` |
| 14 | getAttachments | Списък на прикачени файлове | `method := getAttachments`<br>`output := (link_with_permissions/just_id)` |
| 15 | getCurrencies | Списък на валути | `method := getCurrencies`<br>`format := short/verbose` |
| 16 | getAnnouncements | Списък на съобщения | `method := getAnnouncements`<br>`type := (id на тип)`<br>`assignments_users := (id на потребители)`<br>`assignments_departments := (id на отдели)` |
| 17 | getMonthsNames | Списък на месеците от годината | `method := getMonthsNames` |
| 18 | getWarehouses | Списък на складовете | `method := getWarehouses`<br>`company := (id на фирма)`<br>`office := (id на офис)`<br>`active := (0/1)` |
| 19 | getYears | Списък на години | `method := getYears`<br>`start := (стартова година)`<br>`offset_from_current := (отстъп)`<br>`num_active := (брой активни)`<br>`option_prefix := (префикс)`<br>`sort := asc/desc` |
| 20 | getDays | Списък на дни в месец | `method := getDays`<br>`month := (месец 1-12)`<br>`year := (година)`<br>`option_prefix := (префикс)`<br>`sort := asc/desc` |
| 21 | getCountries | Списък на всички държави | `method := getCountries` |

## Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в таблица **documents_statuses**. Тази таблица има следните полета:

- **id** – НЕ Е auto-increment, въвежда се ръчно и може да се дублира
- **doc_type** – id-то на типа документ, за който се отнася подстатусът
- **name** – името на подстатуса (езиково-зависимо)
- **description** – описание на подстатуса (езиково-зависимо, незадължително)
- **status** – към кой основен статус принадлежи (opened, locked, closed)
- **sequence** – поредност на състоянията
- **lang** – език за езиково-зависимите параметри

Пример за два реда от таблицата:

| id | doc_type | name | description | status | sequence | lang |
|----|----------|------|-------------|--------|----------|------|
| 2 | 1 | За одобрение | Статус "За одобрение" | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |

## Методи в контрагенти (customers.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`id := (id на контрагент)`<br>`tag := 3,4` |
| 2 | getCstmRelatives | Списък на връзки между контрагенти | `method := getCstmRelatives`<br>`is_company := (0/1)`<br>`type := (id на тип)`<br>`c_id := (задължителен - id на контрагента)` |
| 3 | getIsCompany | Списък на видовете контрагенти (ЮЛ, ФЛ) | `method := getIsCompany` |
| 4 | getTypesKind | Списък на видовете контрагенти (ЮЛ, ФЛ, и двете) | `method := getTypesKind` |

## Методи в документи (documents.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCstmOptions | Списък на контрагенти | `method := getCstmOptions`<br>`is_company := (0/1)`<br>`type := (id на тип)` |
| 2 | getFcNums | Списък на фактури за контрагент | `method := getFcNums`<br>`var_id := (задължителен - id на променлива)`<br>`type := (задължителен - тип документ)` |
| 3 | getFcSums | Списък от сумите за плащане на фактури | `method := getFcSums`<br>`sum_var_id := (задължителен)`<br>`currency_var_id := (незадължителен)`<br>`type := (задължителен)`<br>`status := (незадължителен)`<br>`substatus := (незадължителен)` |
| 4 | getDocs | Списък на документи | `method := getDocs`<br>`status_no := (статус различен от)`<br>`type := (тип(ове) на документи)` |
| 5 | getBBElements | Списък на всички елементи в ББ към документ | `method := getBBElements` |
| 6 | getStatuses | Списък на всички статуси (и подстатуси) | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 7 | getOwnership | Списък на видове собственост | `method := getOwnership` |
| 8 | getDirections | Списък на видове посоки | `method := getDirections` |

## Методи в проекти (projects.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на всички статуси | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 2 | getPriorities | Списък на всички приоритети за проекти | `method := getPriorities` |
| 3 | getFinishedOptions | Списък на опциите за статус приключен | `method := getFinishedOptions` |

## Методи в задачи (tasks.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getStatuses | Списък на всички статуси | `method := getStatuses`<br>`model_types := (списък с id-та на типове)` |
| 2 | getPriorities | Списък на всички приоритети | `method := getPriorities` |
| 3 | getOwnership | Списък на видове собственост | `method := getOwnership` |
| 4 | getSeverity | Списък на всички приоритети | `method := getSeverity` |

## Методи във финанси (finance.dropdown.php)

| № | Метод | Описание | Параметри |
|---|-------|----------|-----------|
| 1 | getCompanyOffices | Списък на офисите за дадена компания | `method := getCompanyOffices`<br>`company_id := (id на фирма)`<br>`active := (0/1)` |
| 2 | getWarehousesEmployees | Списък на служителите в даден склад | `method := getWarehousesEmployees`<br>`warehouse := (id на склад)`<br>`active := (0/1)` |
| 3 | getCustomDropdown | Универсален метод за dropdown | `method := getCustomDropdown`<br>`table := (име на таблица)`<br>`table_i18n := (име на i18n таблица)`<br>`where := (WHERE условие)`<br>`value := id`<br>`label := name` |

## Настройки в таблица settings

### 2.1 Секция general

#### Настройки за автоматично попълване на полета

**name**: `autocomplete_N`, където N е id на тип документ/задача/проект
**value**: списък от полета, разделени със запетая

Пример:
```
autocomplete_15 := customer,project,department
```

#### Настройки за типове назначения

**name**: `assignment_types_N`, където N е id на тип
**value**: `owner,responsible,observer,decision`

За всеки тип се настройва какви типове назначения ще се прилагат.

### 2.2 Секция tasks

#### Полета за шаблони за типовете задачи

**name**: `configurator_1`
**value**: `name,planned_time,severity,description,department,active,group`

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета за формите

Възможни полета:
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `planned_start_date` - планирано начало
- `planned_finish_date` - планиран край
- `planned_time` - планирано време
- `severity` - приоритет
- `progress` - % на изпълнение
- `equipment` - апаратура
- `task_field` - тестово поле
- `source` - източник на задачата
- `description` - описание
- `notes` - забележка / решение
- `department` - отдел
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета, които да бъдат одитирани

Възможни полета за одит:
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `planned_start_date` - начало
- `planned_finish_date` - край
- `status` - статус
- `substatus` - състояние (подстатус)
- `planned_time` - планирано време
- `severity` - приоритет
- `progress` - % на изпълнение
- `equipment` - апаратура
- `task_field` - тестово поле
- `source` - източник на задачата
- `description` - описание
- `notes` - забележка / решение
- `department` - отдел
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ
- `tag` - промяна на тагове
- `add_attachments` - добавяне на прикачени файлове
- `del_attachment` - изтриване на прикачен файл

### 2.3 Секция documents

#### Типове назначавания за документи

**name**: `assignment_types_N`, където N е id на тип документ
**value**: `owner,responsible,observer,decision`

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета

Възможни полета:
- `custom_num` - номер от контрагент
- `customer` - контрагент
- `trademark` - търговска марка
- `project` - проект
- `office` - офис
- `employee` - служител
- `contract` - по договор №
- `media` - медия на документа
- `deadline` - срок за обработка
- `validity_term` - срок на валидност
- `date` - дата
- `description` - описание
- `notes` - бележки
- `department` - отдел
- `attachment` - файл
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

#### Настройка за полето "По договор №"

**name**: `contract_custom_label`
**value**: Използвайки ключови думи: **custom_num**, **num** и **name**

Пример:
```
[custom_num] name
```

#### Настройки за архивиране

**name**: `archive_N`, където N е id на тип документ
**value**:
```
field := <променлива>
interval := <интервал>
```

За променлива се посочва основна или допълнителна променлива от тип date/datetime, като допълнителната трябва да се запише с префикс „a__" пред името.

Интервалът трябва да бъде във формат: `<брой> <вид>`, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

#### Настройки за унищожаване

**name**: `purge_N`, където N е id на тип документ
**value**:
```
field := <променлива>
interval := <интервал>
```

### 2.4 Секция customers

#### Настройки за множествено добавяне и редактиране

**name**: `multiadd` или `multiedit`
**value**: съдържа изброените полета

Възможни полета:
- `name` - име
- `code` - код
- `company_department` - отдел във фирмата (само за ФЛ)
- `position` - позиция (само за ФЛ)
- `country` - държава
- `city` - град
- `postal_code` - пощенски код
- `address` - адрес
- `notes` - бележки
- `phone` - телефон
- `fax` - факс
- `gsm` - мобилен телефон
- `email` - e-mail
- `web` - уеб сайт
- `skype` - Skype
- `othercontact` - Друг контакт
- `company_name` - пълно наименование на фирмата (само за ЮЛ)
- `in_dds` - Идентификационен номер за регистрация по ДДС (само за ЮЛ)
- `eik` - ЕИК (само за ЮЛ)
- `registration_file` - фирмено дело № (само за ЮЛ)
- `registration_volume` - том/страница/регистър (само за ЮЛ)
- `registration_number` - съд по регистрация (само за ЮЛ)
- `registration_address` - адрес по регистрация (само за ЮЛ)
- `mol` - МОЛ (само за ЮЛ)
- `ucn` - ЕГН (само за ФЛ)
- `identity_num` - лична карта № (само за ФЛ)
- `identity_date` - лична карта издадена на (само за ФЛ)
- `identity_by` - лична карта издадена от (само за ФЛ)
- `identity_valid` - валидна до (само за ФЛ)
- `address_by_personal_id` - адрес по лична карта (само за ФЛ)
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

По подразбиране са зададени: `name, lastname, code, in_dds, eik, mol, bank, iban, bic, department, assigned, tag`.

### 2.5 Секция projects

#### Настройки за избор на отговорник на фаза

**name**: `phases_choose_responsible`
**value**: 0 или 1. При 1 можем да избираме отговорник на фаза, при 0 – не можем.

#### Настройки за одитиране

**name**: `audit`
**value**: съдържа изброените полета за одит

Възможни полета:
- `name` - име
- `customer` - контрагент
- `trademark` - търговска марка
- `code` - код
- `date_start` - начало
- `date_end` - край
- `priority` - приоритет
- `parent_project` - подпроект на
- `manager` - ръководител
- `finished_part` - % изпълнение
- `budget` - планиран бюджет
- `work_period` - човекочасове
- `description` - описание
- `status` - статус
- `active` - готовност за публикуване
- `group` - групова принадлежност
- `is_portal` - портален
- `assignments` - назначения
- `tag` - промяна на тагове
- `add_attachments` - добавяне на прикачени файлове
- `del_attachment` - изтриване на прикачен файл

### 2.6 Секция precision

Настройки за прецизността на закръгленията (0-6) в GT2, за разпределяне, индекси, за цени на номенклатури и др.

- `gt2_rows` – Прецизност на стойностите в редовете в GT2
- `gt2_total` – Прецизност на **Общо** в GT2
- `gt2_total_vat` – Прецизност на **Общо ДДС** в GT2
- `gt2_total_with_vat` – Прецизност на **Общо с ДДС** в GT2
- `gt2_quantity` – Прецизност за количеството в редовете в GT2
- `indexes_values` – Прецизност за резултат от формули на индекси
- `finance_analysis_percentage` – Прецизност на процентно разпределяне
- `nom_sell_price` - Прецизност на продажна цена на номенклатури
- `nom_last_delivery_price` – Прецизност на последна доставна цена
- `nom_average_weighted_delivery_price` - Прецизност на среднопретеглена доставна цена

### 2.7 Секция contracts

#### Типове назначавания за договори

**name**: `assignment_types_N`, където N е id на тип договор
**value**: `owner,responsible,observer,decision`

#### Настройка за одитираните основни променливи

**name**: `audit`
**value**: съдържа изброените полета за одит

По подразбиране са:
- `active` - готовност за публикуване
- `name` - относно
- `customer` - контрагент
- `trademark` - търговска марка
- `status` - статус
- `substatus` - състояние (подстатус)
- `company` - фирма
- `office` - офис
- `employee` - служител
- `date_sign` - дата на подписване
- `date_start` - дата на влизане в сила
- `date_validity` - дата на изтичане
- `date_end` - дата на опция за прекратяване
- `assignments_owner` - назначение за Изпълнител
- `assignments_responsible` - назначение за Отговорник
- `assignments_decision` - назначение за Вземащ решение
- `assignments_observer` - назначение за Наблюдаващ
- `tag` - промяна на тагове
- `add_attachments` - прикачени файлове

### 2.8 Активност на ред

Активност на ред е възможност ред от списък на записи да стане изцяло активен, т.е. където и да се щракне с мишката на него, да отвежда на място, което му е посочено.

В **section** се записва името на модул (`documents`, `customers`, `projects`, `nomenclatures`, `finance_budgets`, `finance_incomes_reasons`, `finance_expenses_reasons`, `finance_payments`), в **name** – текстът `row_link_action` и за **value** се въвежда името на действието.

Пример:
```
section - documents
name – row_link_action
value - edit
```

Действието трябва да съвпада с действие (action) за модула от таблица 'roles_definitions', например: `view`, `edit`, `communications`, `relatives` и т.н.

### 2.9 Секция turnovers

Настройка за валута на обороти – ако не е въведена, се взема основната валута за инсталацията.

`currency` – валута (**BGN**, **EUR** ...)

### 2.10 Свързани записи

Настройката за свързани записи позволява да се указва кои от панелите (табове) със свързани записи ще бъдат видими под формата в режим на разглеждане и редакция.

В **section** се записва името на модул (`customers`, `projects`, `nomenclatures`, `contracts`), в **name** – стойност `related_records_modules` и за **value** се изреждат имената на панелите, разделени със запетая.

Възможните стойности са:

#### За Контрагенти:
- `documents` – Документи
- `projects` – Проекти
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_repayment_plans` – Погасителни планове
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори
- `referent_documents` – Референтни документи
- `referent_projects` – Референтни проекти
- `referent_contracts` – Референтни договори
- `referent_customers` – Референтни контрагенти

#### За Проекти:
- `documents` – Документи
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори

#### За Номенклатури:
- `customers` – Контрагенти
- `documents` – Документи
- `projects` – Проекти
- `tasks` – Задачи
- `events` – Събития
- `finance_incomes_reasons` – Приходни документи
- `finance_expenses_reasons` – Разходни документи
- `finance_payments` – Платежни документи
- `finance_recurring_payments` – Регулярни плащания
- `finance_annulments` – Протоколи за анулиране
- `finance_warehouses_documents` – Складови докменти
- `contracts` – Договори
- `nomenclatures` – Номенклатури

#### За Договори:
- `documents` – Документи

Пример:
```
section - customers
name – related_records_modules
value – documents, projects, referent_documents
```

### 2.11 Секция finance

#### Типове назначавания за финансови документи

**name**: `assignment_types_N`, където N е id на тип финансов документ
**value**: `owner,responsible,observer,decision`

#### Настройка за фискална валута на фактури

**name**: `fiscal_currency`
**value**: валута (**BGN**, **EUR** ...)

Ако не е въведена, се взема основната валута за инсталацията.

### 2.12 Секция users

**max_failed_attempts**: Колко най-много неуспешни опити за вход може да направи потребителя, в някакъв интервал от време, преди да се задейства защитата. В браузъра защитата е поле с captcha, през REST се връща код 429 и се блокират следващи опити за вход, докато не изтече определеното време.

**max_failed_attempts_timeout**: Какъв е интервала от време за проверка на неуспешните опити. Задава се в брой секунди (30мин = 1800сек, 60мин = 3600). За да изключите изцяло защитата задайте стойност 0.

## Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в една таблица **documents_statuses**. Тази таблица има следните полета:

- **id** – НЕ Е auto-increment, тоест въвежда се ръчно и може да се дублира. Това е направено защото нямаме отделна таблица за езиково-зависимите променливи и опциите за отделните езици се намират в тази таблица. Тоест, ако даден подстатус с id 5 сме въвели на български и искаме да го въведем и на английски, ще въведем ред в таблицата, който отново ще има id 5. Въвеждането на id е задължително!

- **doc_type** – id-то на типа документ, за който се отнася подстатусът. Примерно, ако искаме подстатусът да е за оферта, а тя е с id 15, то в това поле трябва да въведем 15.

- **name** – името на подстатуса. Полето е езиково-зависимо!

- **description** – описание на подстатуса. Полето е езиково-зависимо и е единственото от всички полета в тази таблица, чието попълване не е задължително.

- **status** – оттук се избира към кой основен статус ще принадлежи състоянието, Възможностите са opened, locked, closed

- **sequence** – поредност на състоянията. Въвеждат се номера в зависимост от това в какъв ред искаме да се извеждат подстатусите КЪМ СЪОТВЕТНИЯ ОСНОВЕН СТАТУС. Тоест, ако към "Отворен" имаме въведени три състояния – "В процес на изработка", "За одобрение" и "За редакция" – и искаме да ги виждаме в този ред в полето за sequence трябва да въведем съотеветно 1, 2 и 3 или 7, 12 и 19 (примерно). Ако състоянията са към различни основни статуси, се позволява дублиране на sequence-а. Това ще рече, че можем да имаме подстатус на opened със sequence=1 и да имаме подстатус към closed, който също да има sequence=1 – това НЕ Е проблем.

- **lang** – език за езиково-зависимите параметри. Когато искаме да въведем състояние на друг език, тук указваме какъв ще е езикът ("bg", "en", "de" или друг). Когато въвеждаме състояние на друг език, описваме на съответния език само езиково-зависимите променливи останалите трябва да останат същите.

Пример за два реда от таблицата, описващи състоянието "За одобрение" на български и на английски език:

| id | doc_type | name | description | status | sequence | lang |
|----|----------|------|-------------|--------|----------|------|
| 2 | 1 | За одобрение | Статус "За одобрение" | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |
