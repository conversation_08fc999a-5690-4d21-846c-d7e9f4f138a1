<?php /* Smarty version 2.6.33, created on 2023-11-15 17:51:08
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit_email.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit_email.html', 3, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit_email.html', 23, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit_email.html', 35, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit_email.html', 41, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['gt2_audit']['new_values'] ) || ! empty ( $this->_tpl_vars['gt2_audit']['old_values'] )): ?>
  <br />
  <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong>
  <table border="1" cellpadding="5" cellspacing="5">
    <tr>
      <td style="height:20px; width: 20px; background-color: #ffffaa;">&nbsp;</td>
      <td style="background: none!important; text-align: left;"><?php echo $this->_config[0]['vars']['legend_added_row']; ?>
</td>
    </tr>
    <tr>
      <td style="height:20px; width: 20px; background-color: #ff8888;">&nbsp;</td>
      <td style="background: none!important; text-align: left;"><?php echo $this->_config[0]['vars']['legend_deleted_row']; ?>
</td>
    </tr>
    <tr>
      <td style="height:20px; width: 20px; background-color: rgb(200,255,200);">&nbsp;</td>
      <td style="background: none!important; text-align: left;"><?php echo $this->_config[0]['vars']['legend_old_value']; ?>
</td>
    </tr>
    <tr>
      <td style="height:20px; width: 20px; background-color: rgb(255,200,150);">&nbsp;</td>
      <td style="background: none!important; text-align: left;"><?php echo $this->_config[0]['vars']['legend_new_value']; ?>
</td>
    </tr>
  </table>
  <br />
  <?php ob_start(); ?><?php echo count($this->_tpl_vars['gt2_audit']['labels']); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('colspan', ob_get_contents());ob_end_clean(); ?>
  <?php $this->assign('colspan', $this->_tpl_vars['colspan']*2); ?>
  <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong>
  <table border="1" cellpadding="5" cellspacing="0">
    <tr>
      <?php $_from = $this->_tpl_vars['gt2_audit']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['label']):
        $this->_foreach['i']['iteration']++;
?>
        <th colspan="2" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
    <?php $_from = $this->_tpl_vars['gt2_audit']['new_values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['row'] => $this->_tpl_vars['values']):
        $this->_foreach['i']['iteration']++;
?>
    <?php if (! empty ( $this->_tpl_vars['values']['field_name'] ) && $this->_tpl_vars['values']['field_name'] == 'bb_delimiter'): ?>
    <tr>
      <th colspan="<?php echo $this->_tpl_vars['colspan']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</th>
    </tr>
    <?php elseif ($this->_tpl_vars['values']['article_id'] || $this->_tpl_vars['values']['price'] || $this->_tpl_vars['values']['average_weighted_delivery_price'] || $this->_tpl_vars['values']['last_delivery_price'] || $this->_tpl_vars['values']['quantity'] || $this->_tpl_vars['values']['subtotal'] || $this->_tpl_vars['values']['action'] == 'deleted'): ?>
    <tr style="<?php if ($this->_tpl_vars['values']['action'] == 'deleted' || empty ( $this->_tpl_vars['values'] )): ?>background-color: #ff8888;<?php elseif ($this->_tpl_vars['values']['action'] == 'added' || empty ( $this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']] )): ?>background-color: #ffffaa;<?php endif; ?>">
      <?php $_from = $this->_tpl_vars['gt2_audit']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var'] => $this->_tpl_vars['bla']):
        $this->_foreach['j']['iteration']++;
?>
        <?php if ($this->_tpl_vars['values']['action'] == 'deleted' || empty ( $this->_tpl_vars['values'] )): ?>
          <td colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php elseif ($this->_tpl_vars['values']['action'] == 'added' || empty ( $this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']] )): ?>
          <td colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php elseif ($this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']] == $this->_tpl_vars['values'][$this->_tpl_vars['var']]): ?>
          <td colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php else: ?>
          <td style="background-color: rgb(200,255,200);"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <td style="background-color: rgb(255,200,150);"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
    <?php endif; ?>
    <?php endforeach; else: ?>
    <tr>
      <td style="color: red;" colspan="<?php echo $this->_tpl_vars['colspan']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_changes_made'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
    <?php endif; unset($_from); ?>
  </table>
<?php endif; ?>