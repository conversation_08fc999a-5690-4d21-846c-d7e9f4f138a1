<?php /* Smarty version 2.6.33, created on 2025-05-21 16:14:32
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html', 59, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html', 73, false),array('modifier', 'strip_tags', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html', 83, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html', 83, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html', 93, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_combobox.html', 61, false),)), $this); ?>
<?php if ($this->_tpl_vars['index']): ?><?php ob_start(); ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo $this->_tpl_vars['index']; ?>
<?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php else: ?><?php echo $this->_tpl_vars['index']-1; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( $this->_tpl_vars['width'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>

<?php ob_start(); ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
px<?php elseif ($this->_tpl_vars['height']): ?><?php echo $this->_tpl_vars['height']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?>"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

<?php $this->assign('hide_first_option', '1'); ?>
<?php if (( $this->_tpl_vars['options'] && count($this->_tpl_vars['options']) || $this->_tpl_vars['optgroups'] && count($this->_tpl_vars['optgroups']) ) && ! ( $this->_tpl_vars['skip_please_select'] || ! empty ( $this->_tpl_vars['options']['skip_please_select'] ) || ! empty ( $this->_tpl_vars['optgroups']['skip_please_select'] ) )): ?>
  <?php $this->assign('hide_first_option', '0'); ?>
<?php endif; ?>

        <select 
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="selbox<?php if (! $this->_tpl_vars['undefined_strict_check'] && ! $this->_tpl_vars['value'] || $this->_tpl_vars['value'] === ''): ?> undefined<?php endif; ?><?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
      style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['width']): ?>width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['text_align']): ?> text-align: <?php echo $this->_tpl_vars['text_align']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onfocus="highlight(this);"
      onblur="unhighlight(this);"
      <?php if ($this->_tpl_vars['disabled'] || $this->_tpl_vars['readonly']): ?> disabled="disabled"<?php endif; ?>
      onchange="toggleUndefined(this); <?php if ($this->_tpl_vars['sequences']): ?>if (this.value) {<?php echo $this->_tpl_vars['sequences']; ?>
}<?php elseif ($this->_tpl_vars['on_change']): ?>change_options(<?php echo $this->_tpl_vars['on_change']; ?>
)<?php elseif ($this->_tpl_vars['onchange']): ?><?php echo $this->_tpl_vars['onchange']; ?>
<?php endif; ?>">
            <?php if (empty ( $this->_tpl_vars['options'] ) && empty ( $this->_tpl_vars['optgroups'] )): ?>
      <option value="" class="undefined"<?php if ($this->_tpl_vars['value'] === ""): ?> selected="selected"<?php endif; ?>><?php if ($this->_tpl_vars['no_select_records_label']): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['no_select_records_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['input'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
    <?php elseif (! $this->_tpl_vars['hide_first_option']): ?>
      <option value="" class="undefined"<?php if ($this->_tpl_vars['value'] === ""): ?> selected="selected"<?php endif; ?>><?php if ($this->_tpl_vars['first_option_label']): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['first_option_label'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select_or_input'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['optgroups']): ?>
      <?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optlabel'] => $this->_tpl_vars['optgroup']):
?>
        <?php if ($this->_tpl_vars['optlabel'] !== 'skip_please_select'): ?>
        <optgroup label="<?php if ($this->_tpl_vars['optgroup_label_source'] == 'config'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['optlabel']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_tpl_vars['optlabel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>">
          <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
          <?php $this->assign('label', $this->_tpl_vars['option']['label']); ?>
            <?php if (( $this->_tpl_vars['show_inactive_options'] || ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || $this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value'] )): ?>
              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['option']['class_name']): ?> class="<?php echo $this->_tpl_vars['option']['class_name']; ?>
"<?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?><?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value']): ?> selected="selected"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?>*&nbsp;<?php endif; ?><?php if ($this->_tpl_vars['option_label_source'] == 'config'): ?><?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['label']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php endif; ?></option>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </optgroup>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php else: ?>
      <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
      <?php $this->assign('label', $this->_tpl_vars['option']['label']); ?>
        <?php if ($this->_tpl_vars['idx'] !== 'skip_please_select'): ?>
        <?php if (( $this->_tpl_vars['show_inactive_options'] || ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || $this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value'] )): ?>
          <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['option']['class_name']): ?> class="<?php echo $this->_tpl_vars['option']['class_name']; ?>
"<?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?><?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value']): ?> selected="selected"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?>*&nbsp;<?php endif; ?><?php if ($this->_tpl_vars['option_label_source'] == 'config'): ?><?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['label']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php endif; ?></option>
        <?php endif; ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
    </select>
    <script type="text/javascript">
    <?php if (preg_match ( '#-#' , $this->_tpl_vars['custom_id'] ) || ! $this->_tpl_vars['custom_id'] && preg_match ( '#-#' , $this->_tpl_vars['name'] )): ?>
      new toCombo("<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>");
    <?php else: ?>
      <?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?> = new toCombo("<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>");
      <?php if ($this->_tpl_vars['check']): ?><?php echo $this->_tpl_vars['check']; ?>
;<?php endif; ?>
    <?php endif; ?>
    </script>
    <?php if ($this->_tpl_vars['readonly']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo $this->_tpl_vars['value']; ?>
" />
    <?php endif; ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>