<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/tasks/templates/_assignments_dashlet.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/tasks/templates/_assignments_dashlet.html', 12, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/tasks/templates/_assignments_dashlet.html', 18, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/tasks/templates/_assignments_dashlet.html', 18, false),)), $this); ?>

    <?php if (! $this->_tpl_vars['a_type']): ?>
      <?php $this->assign('a_type', 'assignments_owner'); ?>
    <?php endif; ?>
  
    <div<?php if (! $this->_tpl_vars['task']->get($this->_tpl_vars['a_type']) || is_array ( $this->_tpl_vars['task']->get($this->_tpl_vars['a_type']) ) && ! ( count($this->_tpl_vars['task']->get($this->_tpl_vars['a_type'])) > 3 )): ?><?php if ($this->_tpl_vars['task']->checkPermissions('assign')): ?> style="padding: 3px 0 3px 0; cursor:pointer;" onclick="changeAssignments(<?php echo $this->_tpl_vars['task']->get('id'); ?>
, 'tasks', '<?php echo $this->_tpl_vars['a_type']; ?>
')" title="<?php echo $this->_config[0]['vars']['tasks_assign_change']; ?>
"<?php endif; ?><?php endif; ?>>
      <?php $this->assign('long_text', ''); ?>
      <?php $this->assign('short_text', ''); ?>
      <?php if ($this->_tpl_vars['task']->get($this->_tpl_vars['a_type'])): ?>
        <?php $_from = $this->_tpl_vars['task']->get($this->_tpl_vars['a_type']); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['assignees'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['assignees']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignment']):
        $this->_foreach['assignees']['iteration']++;
?>
          <?php ob_start(); ?>
            <?php echo $this->_tpl_vars['long_text']; ?>
<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<br />
          <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('long_text', ob_get_contents());ob_end_clean(); ?>
          <?php if ($this->_foreach['assignees']['iteration'] <= 3): ?>
            <?php ob_start(); ?>
              <?php echo $this->_tpl_vars['short_text']; ?>
<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<br />
            <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('short_text', ob_get_contents());ob_end_clean(); ?>
          <?php endif; ?>
        <?php endforeach; else: ?>
          &nbsp;
        <?php endif; unset($_from); ?>
        <?php if (is_array ( $this->_tpl_vars['task']->get($this->_tpl_vars['a_type']) ) && count($this->_tpl_vars['task']->get($this->_tpl_vars['a_type'])) > 3): ?>
          <div id="<?php echo $this->_tpl_vars['a_type']; ?>
_<?php echo $this->_tpl_vars['dashlet_id']; ?>
_part_<?php echo $this->_foreach['i']['iteration']; ?>
">
            <div<?php if ($this->_tpl_vars['task']->checkPermissions('assign')): ?> onclick="changeAssignments(<?php echo $this->_tpl_vars['task']->get('id'); ?>
, 'tasks', '<?php echo $this->_tpl_vars['a_type']; ?>
')" style="cursor:pointer; padding: 3px 0 3px 0;" title="<?php echo $this->_config[0]['vars']['tasks_assign_change']; ?>
"<?php endif; ?>>
            <?php echo $this->_tpl_vars['short_text']; ?>

            </div>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/arrow_down.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_show_full_assignments_list'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_show_full_assignments_list'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="toggleContent('<?php echo $this->_tpl_vars['a_type']; ?>
_<?php echo $this->_tpl_vars['dashlet_id']; ?>
', <?php echo $this->_foreach['i']['iteration']; ?>
);" align="right" class="pointer" />
          </div>
          <div id="<?php echo $this->_tpl_vars['a_type']; ?>
_<?php echo $this->_tpl_vars['dashlet_id']; ?>
_full_<?php echo $this->_foreach['i']['iteration']; ?>
" style="display: none;">
            <div<?php if ($this->_tpl_vars['task']->checkPermissions('assign')): ?> onclick="changeAssignments(<?php echo $this->_tpl_vars['task']->get('id'); ?>
, 'tasks', '<?php echo $this->_tpl_vars['a_type']; ?>
')" style="cursor:pointer; padding: 3px 0 3px 0;" title="<?php echo $this->_config[0]['vars']['tasks_assign_change']; ?>
"<?php endif; ?>>
            <?php echo $this->_tpl_vars['long_text']; ?>

            </div>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/arrow_up.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_hide_full_assignments_list'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_hide_full_assignments_list'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="toggleContent('<?php echo $this->_tpl_vars['a_type']; ?>
_<?php echo $this->_tpl_vars['dashlet_id']; ?>
', <?php echo $this->_foreach['i']['iteration']; ?>
);" align="right" class="pointer" />
          </div>
        <?php else: ?>
          <?php echo $this->_tpl_vars['short_text']; ?>

        <?php endif; ?>
      <?php else: ?>
        &nbsp;
      <?php endif; ?>
    </div>