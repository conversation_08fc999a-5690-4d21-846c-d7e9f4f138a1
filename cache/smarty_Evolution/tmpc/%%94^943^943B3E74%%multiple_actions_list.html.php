<?php /* Smarty version 2.6.33, created on 2025-06-26 15:37:09
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html', 1, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html', 139, false),array('function', 'uniqid', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html', 146, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html', 21, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html', 31, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/multiple_actions_list.html', 168, false),)), $this); ?>
<?php echo smarty_function_counter(array('name' => 'stat_items_sequence1','assign' => 'stat_items_sequence'), $this);?>

<form name="<?php echo $this->_tpl_vars['module']; ?>
" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
" method="post" enctype="multipart/form-data">
  <?php if (((is_array($_tmp=@$this->_tpl_vars['exclude'])) ? $this->_run_mod_handler('default', true, $_tmp, "") : smarty_modifier_default($_tmp, "")) != 'all'): ?>
    <div class="nz-grid-multi-actions">
      <div class="nz-visible">
      <input type="hidden" name="session_param" value="<?php echo $this->_tpl_vars['session_param']; ?>
" />
      <input type="hidden" name="after_action" value="" />
      <?php ob_start(); ?><?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>, '<?php echo $this->_tpl_vars['controller']; ?>
'<?php else: ?>, ''<?php endif; ?>, <?php echo $this->_tpl_vars['stat_items_sequence']; ?>
<?php if (! empty ( $this->_tpl_vars['skip_session_ids'] )): ?>, '', 1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('additional_str', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['controller'] && ( $this->_tpl_vars['module'] != $this->_tpl_vars['controller'] )): ?><?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['module']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('module_check', ob_get_contents());ob_end_clean(); ?>
      <?php if (empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#activate#' , $this->_tpl_vars['exclude'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'activate')): ?>
        <button type="submit" name="activateButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="activate"
            ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['activate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#deactivate#' , $this->_tpl_vars['exclude'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'deactivate')): ?>
          <button type="submit" name="deactivateButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="deactivate"
            ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#delete#' , $this->_tpl_vars['exclude'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'delete')): ?>
          <button type="submit" name="deleteButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="delete"
          ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#restore#' , $this->_tpl_vars['exclude'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'restore')): ?>
          <button type="submit" name="restoreButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="restore"
          ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['restore'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (( empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#purge#' , $this->_tpl_vars['exclude'] ) ) && preg_match ( '#purge#' , $this->_tpl_vars['include'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'purge')): ?>
          <button type="submit" name="purgeButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="purge"
          ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['purge'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#multiedit#' , $this->_tpl_vars['exclude'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'multiedit')): ?>
          <button type="submit" name="multieditButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="multiedit" data-mode="submit"
          ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['multiedit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (preg_match ( '#tags#' , $this->_tpl_vars['include'] ) && ( ! empty ( $this->_tpl_vars['tags_options'] ) || ! empty ( $this->_tpl_vars['tags_optgroups'] ) ) && $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'tags_view') && $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'tags_edit')): ?>
        <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                data-popout-template="#multitags-popout"
                data-popout-position="panel: top center at: bottom center"
                data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
        ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tags'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&nbsp;<i class="material-icons">arrow_drop_down</i></button>
        <script type="text/x-template" id="multitags-popout">
          <aside class="nz-multitags-panel nz-popout-panel nz-pointer-top-center">
            <div class="nz-popout-surface nz-surface nz-elevation--z6">
              <div class="nz-popout-body">
                <div class="nz-block">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'tagsSelect','id' => 'tagsSelect','custom_class' => 'notsmall','label' => $this->_config[0]['vars']['tags'],'first_option_label' => $this->_config[0]['vars']['select'],'options' => $this->_tpl_vars['tags_options'],'optgroups' => $this->_tpl_vars['tags_optgroups'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </div>
                <div>
                  <?php ob_start(); ?>if ($('tagsSelect').value != '') { return confirmation($('tagsSelect'), '<?php echo $this->_tpl_vars['module']; ?>
', 'multitag'<?php echo $this->_tpl_vars['additional_str']; ?>
) } else {alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_tags'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
'); $('tagsSelect').focus(); return false;}<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('tags_onclick', ob_get_contents());ob_end_clean(); ?>
                  <button type="submit"
                          name="multitag"
                          data-action="multitag"
                          class="nz-button nz-form-button multitags-submit-button"
                  ><i class="material-icons">add_circle_outline</i> <?php echo $this->_config[0]['vars']['add']; ?>
</button>
                  <button type="submit"
                          name="multiremovetag"
                          data-action="multiremovetag"
                          class="nz-button nz-form-button multitags-submit-button"
                  ><i class="material-icons">remove_circle_outline</i> <?php echo $this->_config[0]['vars']['remove']; ?>
</button>
                </div>
              </div>
            </div>
          </aside>
      </script>
      <?php endif; ?>

      <?php if (preg_match ( '#multistatus#' , $this->_tpl_vars['include'] ) && ! empty ( $this->_tpl_vars['statuses'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'multistatus')): ?>
        <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                data-popout-template="#multistatus-popout"
                data-popout-position="panel: top center at: bottom center"
                data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
                ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&nbsp;<i class="material-icons">arrow_drop_down</i></button>
          <script type="text/x-template" id="multistatus-popout">
            <aside class="nz-multistatus-panel nz-popout-panel nz-pointer-top-center">
              <div class="nz-popout-surface nz-surface nz-elevation--z6">
                  <div class="nz-popout-body">
                    <div class="nz-block">
                      <table>
                        <tr>
                          <td class="required">&nbsp;</td>
                          <td class="labelbox"><select name="multistatusSelect" id="multistatusSelect" class="selbox"
                                                       onchange="toggleUndefined(this); showHideMultistatusCommentField(this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                                <option class="undefined" value="">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                                <?php $_from = $this->_tpl_vars['statuses']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['status']):
?>
                                  <option value="<?php echo $this->_tpl_vars['status']['id']; ?>
" class="<?php echo $this->_tpl_vars['status']['requires_comment']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['status']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                                <?php endforeach; endif; unset($_from); ?>
                              </select></td>
                        </tr>
                      </table>
                    </div>
                    <div class="nz-block">
                      <table id="multistatus_available_comment_table" style="display: none;">
                        <tr>
                          <td class="required" id="multistatus_required_comment" rowspan="2" style="visibility: hidden;"><?php echo $this->_config[0]['vars']['required']; ?>
<input type="hidden" name="multistatus_requires_comment" id="multistatus_requires_comment" value="0" /></td>
                          <td class="labelbox"><label for="multistatus_comment"><?php echo smarty_function_help(array('label' => 'comment'), $this);?>
</label></td>
                        </tr>
                        <tr>
                          <td>
                            <textarea class="areabox" name="multistatus_comment" id="multistatus_comment" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['comment'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"></textarea>
                            <?php if ($this->_tpl_vars['include_portal_users_option'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>
                              <br />
                              <?php ob_start(); ?>_<?php echo smarty_function_uniqid(array(), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal_suffix', ob_get_contents());ob_end_clean(); ?>
                              <input type="radio" name="is_portal" id="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! isset ( $this->_tpl_vars['default_portal_comment'] ) || $this->_tpl_vars['default_portal_comment']): ?> checked="checked"<?php endif; ?> /><label for="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                              <input type="radio" name="is_portal" id="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (isset ( $this->_tpl_vars['default_portal_comment'] ) && ! $this->_tpl_vars['default_portal_comment']): ?> checked="checked"<?php endif; ?> /><label for="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                            <?php endif; ?>
                          </td>
                        </tr>
                      </table>
                    </div>
                    <div>
                      <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_setstatus<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('button_label', ob_get_contents());ob_end_clean(); ?>
                      <button type="submit" name="multistatusButton" data-action="multistatus" class="nz-form-button nz-button-primary"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['button_label']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['setstatus']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['setstatus'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                    </div>
                  </div>
              </div>
            </aside>
          </script>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (preg_match ( '#multiprint#' , $this->_tpl_vars['include'] ) && ! empty ( $this->_tpl_vars['patterns_grouped'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'multiprint')): ?>
          <?php $this->assign('one_pattern', ''); ?>
          <?php if (count($this->_tpl_vars['patterns_grouped']) == 1): ?>
            <?php $_from = $this->_tpl_vars['patterns_grouped']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pgi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pgi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['pgk'] => $this->_tpl_vars['patterns']):
        $this->_foreach['pgi']['iteration']++;
?>
              <?php if (count($this->_tpl_vars['patterns']) == 1): ?>
                <?php $this->assign('one_pattern', $this->_tpl_vars['patterns'][0]['id']); ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php endif; ?>
          <?php if ($this->_tpl_vars['one_pattern']): ?>
            <button type="submit" name="multiprintButton" class="nz-button--multi nz-button-multi-confirm" data-action="multiprint"
            ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['print'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
            <input type="hidden" name="pattern" id="pattern" value="<?php echo $this->_tpl_vars['one_pattern']; ?>
" />
          <?php else: ?>

            <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                    data-popout-template="#multiprint-popout"
                    data-popout-position="panel: top center at: bottom center"
                    data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
            ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['print'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&nbsp;<i class="material-icons">arrow_drop_down</i></button>
            <script type="text/x-template" id="multiprint-popout">
              <aside class="nz-multiprint-panel nz-popout-panel nz-pointer-top-center">
                <div class="nz-popout-surface nz-surface nz-elevation--z6">
                  <div class="nz-popout-body">
                    <div class="nz-block">
                        <select name="pattern" id="pattern" class="undefined selbox" onchange="toggleUndefined(this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_pattern'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                          <option class="undefined" value="">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php $_from = $this->_tpl_vars['patterns_grouped']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pgi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pgi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['pgk'] => $this->_tpl_vars['patterns']):
        $this->_foreach['pgi']['iteration']++;
?>
                            <?php if ($this->_tpl_vars['pgk']): ?><optgroup label="<?php echo ((is_array($_tmp=$this->_tpl_vars['pgk'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php endif; ?>
                            <?php $_from = $this->_tpl_vars['patterns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pattern']):
?>
                              <option value="<?php echo $this->_tpl_vars['pattern']['id']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <?php endforeach; endif; unset($_from); ?>
                            <?php if ($this->_tpl_vars['pgk']): ?></optgroup><?php endif; ?>
                          <?php endforeach; endif; unset($_from); ?>
                        </select>
                    </div>
                    <div>
                      <button type="submit" name="multiprintButton" id="multiprintButton"  data-action="multiprint" data-mode="download" class="nz-form-button nz-button-primary" value="multiprint"
                              ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['print'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                    </div>
                  </div>
                </div>
              </aside>
            </script>
          <?php endif; ?>
        <?php endif; ?>
      <?php endif; ?>

      <?php if ($this->_tpl_vars['module_check'] == 'finance_expenses_reasons' && preg_match ( '#multiaddinvoice#' , $this->_tpl_vars['include'] ) && $this->_tpl_vars['expense_proformas']): ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['module_check']; ?>
<?php echo @PH_FINANCE_TYPE_EXPENSES_INVOICE; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('module_type_check', ob_get_contents());ob_end_clean(); ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_type_check'],'add')): ?>
          <button type="submit" name="multiaddinvoiceButton" id="multiaddinvoiceButton" class="nz-button--multi" value="multiaddinvoice" onclick="addMergedExpensesInvoice(this); return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_expenses_reasons_addinvoice'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if ($this->_tpl_vars['module_check'] == 'contracts' && preg_match ( '#change_templates_observer#' , $this->_tpl_vars['include'] ) && $this->_tpl_vars['currentUser']->checkRights('contracts','editfinance')): ?>
        <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                data-popout-template="#changeTemplateObserver-popout"
                data-popout-position="panel: top center at: bottom center"
                data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
        ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_change_templates_observer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&nbsp;<i class="material-icons">arrow_drop_down</i></button>

        <script type="text/x-template" id="changeTemplateObserver-popout">
          <aside class="nz-multichangeObserver-panel nz-popout-panel nz-pointer-top-center">
            <div class="nz-popout-surface nz-surface nz-elevation--z6">
              <div class="nz-popout-body">
                <div class="nz-block">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'templates_observer','standalone' => true,'options' => $this->_tpl_vars['users_options'],'label' => $this->_config[0]['vars']['contracts_invoices_templates_observer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </div>
                <div>
                  <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_change_templates_observer<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('button_label', ob_get_contents());ob_end_clean(); ?>
                  <button type="submit" name="multiChangeObserverButton" data-action="changeTemplatesObserver" class="nz-form-button nz-button-primary"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['button_label']]; ?>
</button>
                </div>
              </div>
            </div>
          </aside>
        </script>
      <?php endif; ?>

      
      <?php if ($this->_tpl_vars['locking_records'] && $this->_tpl_vars['module'] == 'users' && ( empty ( $this->_tpl_vars['exclude'] ) || ! preg_match ( '#unlock#' , $this->_tpl_vars['exclude'] ) )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'unlock')): ?>
        <button type="submit" name="unlockButton" class="nz-button--multi" onclick="return confirmation(this, '<?php echo $this->_tpl_vars['module']; ?>
', 'unlock'<?php echo $this->_tpl_vars['additional_str']; ?>
)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['unlock'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if ($this->_tpl_vars['module'] == 'layouts' && $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_type_check'],'edit')): ?>
        <?php if (preg_match ( '#multiassignpermissions#' , $this->_tpl_vars['include'] )): ?>
          <button type="submit" name="multiAssignPermissionsButton" id="multiAssignPermissionsButton" class="nz-button--multi" value="multiassignpermissions" onclick="launchMultiAssignPermissions(this); return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['layouts_multi_assign_permissions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['allow_layouts_order']): ?>
          <button type="submit" name="saveOrderButton" id="saveOrderButton" class="nz-button--multi" value="saveorder" onclick="return layoutsOrderSave() && confirmation(this, '<?php echo $this->_tpl_vars['module']; ?>
', 'saveorder'<?php echo $this->_tpl_vars['additional_str']; ?>
, 1);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['layouts_saveorder'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        <?php endif; ?>
      <?php endif; ?>

      <?php if (preg_match ( '#export#' , $this->_tpl_vars['include'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'export')): ?>
          <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                  data-popout-template="#multiexport-popout"
                  data-popout-position="panel: top center at: bottom center"
                  data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
          ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['export'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&nbsp;<i class="material-icons">arrow_drop_down</i></button>
          <script type="text/x-template" id="multiexport-popout">
            <aside class="nz-multiexport-panel nz-popout-panel nz-pointer-top-center">
              <div class="nz-popout-surface nz-surface nz-elevation--z6">
                <div class="nz-popout-body">
                  <div class="nz-block">
                    <table>
                    <?php $_from = $this->_tpl_vars['exportAction']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['exportVar']):
?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['exportVar']['type']).".html", 'smarty_include_vars' => array('name' => $this->_tpl_vars['exportVar']['name'],'id' => ((is_array($_tmp=@$this->_tpl_vars['exportVar']['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'label' => $this->_tpl_vars['exportVar']['label'],'help' => $this->_tpl_vars['exportVar']['help'],'onchange' => $this->_tpl_vars['exportVar']['onchange'],'value' => $this->_tpl_vars['exportVar']['value'],'required' => ((is_array($_tmp=@$this->_tpl_vars['exportVar']['required'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'hidden' => ((is_array($_tmp=@$this->_tpl_vars['exportVar']['hidden'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false)),'options' => ((is_array($_tmp=@$this->_tpl_vars['exportVar']['options'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endforeach; endif; unset($_from); ?>
                    </table>
                  </div>
                  <div>
                    <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_export<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('button_label', ob_get_contents());ob_end_clean(); ?>
                    <button type="submit"
                            name="multiexportButton"
                            data-action="export"
                            data-mode="download"
                            class="nz-form-button nz-button-primary"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['button_label']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['export']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['export'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>

                    <button type="button"
                            name="emptyExportButton"
                            data-action="emptyexport"
                            data-mode="download"
                            class="nz-form-button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['export_empty_file'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 (.xlsx)</button>
                  </div>
                </div>
              </div>
            </aside>
          </script>
        <?php endif; ?>
      <?php endif; ?>

            </div>
      <div class="nz-extended">
        <i class="nz-icon-button nz-extended-button">more_horiz</i>
        <div class="nz-extended-list">
        </div>
      </div>
    </div>
  <?php endif; ?>
</form>

<script>
  <?php echo '
  nz_ready().then(function() {
    // Compatibility mode
    if (document.querySelector(\'#nz-grid\')) {
      return;
    }
    document.body.addEventListener(\'click\', (e) => {
      const targetEl = e.target.closest(\'.nz-popout-panel button.nz-form-button[name^="multi"][data-action]\')
      if (!targetEl) {
        return;
      }
      const action = targetEl.getAttribute(\'data-action\');
      const controller =  env.module_name === env.controller_name ? \'\' : env.controller_name;

      if (action === \'changeTemplatesObserver\') {
        changeTemplatesObserver(document.querySelector(\'.nz-button--multi\'), document.querySelector(\'#templates_observer\'));
        e.preventDefault();
      } else if ([\'multitag\', \'multiremovetag\'].includes(action)) {
        e.preventDefault();
        const tagSelectEl = document.querySelector(\'#tagsSelect\');
        if (tagSelectEl.value === \'\') {
          return;
        }
        return confirmation(tagSelectEl, env.module_name, action, controller, 1);
      } else if (action === \'multistatus\') {
        e.preventDefault();
        confirmation(document.querySelector(\'.nz-button--multi\'), env.module_name, action, controller);
      } else {
        e.preventDefault();
        confirmation(targetEl, env.module_name, action, controller, 1);
      }
    });
    const multiActionsBarEL = document.querySelector(\'.nz-grid-multi-actions\');
    if (!multiActionsBarEL) {
      return;
    }
    multiActionsBarEL.classList.add(\'nz--active\');
    multiActionsBarEL.querySelectorAll(\'.nz-button-multi-confirm\').forEach((button) => {
      const controller =  env.module_name === env.controller_name ? \'\' : env.controller_name;
      button.addEventListener(\'click\', function(e) {
        const actionInpEl = document.querySelector(\'#multiaction-action\');
        const action = this.getAttribute(\'data-action\');
        e.preventDefault();
        confirmation(this, env.module_name, action, controller)
      });
    });
  });
  '; ?>

</script>