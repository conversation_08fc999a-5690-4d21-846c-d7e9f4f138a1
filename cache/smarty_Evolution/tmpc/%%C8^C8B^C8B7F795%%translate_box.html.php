<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/translate_box.html */ ?>
<div class="nz-translate-wrapper">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_translations_menu.html", 'smarty_include_vars' => array('translations' => $this->_tpl_vars['translations'],'make_translations' => $this->_tpl_vars['make_translations'],'lang' => $this->_tpl_vars['lang'],'model_lang' => $this->_tpl_vars['model_lang'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>

<?php echo '
<script>
  nz_ready().then(() => {
    const translate = document.querySelector(\'.nz-translate-wrapper\');
    const menu = document.querySelector(\'.nz-action_compatible\');
    const wrapper = document.createElement(\'div\');
    wrapper.classList.add(\'nz-page-menu-compatible\');
    if (menu) {
      menu.before(wrapper);
      wrapper.append(menu, translate);
    }
  });
</script>
'; ?>
