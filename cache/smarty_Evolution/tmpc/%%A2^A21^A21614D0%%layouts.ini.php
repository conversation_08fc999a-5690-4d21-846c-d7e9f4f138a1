<?php $_config_vars = array (
  'layouts' => 'Секции',
  'layouts_name' => 'Име',
  'layouts_keyname' => 'Променлива',
  'layouts_description' => 'Описание',
  'layouts_place' => 'Позиция',
  'layouts_order' => 'Последователността на секциите може да бъде променена чрез влачене на редовете от таблицата. Промените се запазват от бутона "Запиши позиции".',
  'layouts_status' => 'Статус',
  'layouts_status_active' => 'Активен',
  'layouts_status_inactive' => 'Неактивен',
  'layouts_added_by' => 'Добавен от',
  'layouts_modified_by' => 'Променен от',
  'layouts_added' => 'Добавен на',
  'layouts_modified' => 'Променен на',
  'layouts_type' => 'Вид',
  'layouts_type_system' => 'основна',
  'layouts_type_custom' => 'потребителска',
  'layouts_model' => 'За',
  'layouts_model_type' => 'За тип',
  'layouts_document_type' => 'За документ тип',
  'layouts_customer_type' => 'За контрагент тип',
  'layouts_project_type' => 'За проект тип',
  'layouts_contract_type' => 'За договор тип',
  'layouts_nomenclature_type' => 'За номенклатура тип',
  'layouts_task_type' => 'За задача тип',
  'layouts_event_type' => 'За събитие тип',
  'layouts_finance_incomes_reason_type' => 'За приходен документ тип',
  'layouts_finance_expenses_reason_type' => 'За разходен документ тип',
  'layouts_finance_annulment_type' => 'За протокол за анулиране тип',
  'layouts_finance_warehouses_document_type' => 'За складов документ тип',
  'layouts_finance_payment_type' => 'За плащане тип',
  'layouts_finance_transfer_type' => 'За трансфер',
  'layouts_user_type' => 'За Моят nZoom',
  'layouts_visible' => 'Видимост на лента със заглавието',
  'layouts_visible2' => 'Лента',
  'layouts_info_header_visibility' => 'Видимост в информационния панел',
  'layouts_info_header_visibility2' => 'Панел',
  'layouts_position' => 'Позиция',
  'layouts_assign_permissions' => 'Права по типове назначения',
  'layouts_assign_permissions_added' => 'Добавил запис',
  'layouts_assign_permissions_owner' => 'Изпълнител',
  'layouts_assign_permissions_responsible' => 'Отговорник',
  'layouts_assign_permissions_observer' => 'Наблюдаващ',
  'layouts_assign_permissions_decision' => 'Вземащ решения',
  'layouts_add' => 'Добавяне на данни за нова секция',
  'layouts_edit' => 'Редакция на данни за секция',
  'layouts_view' => 'Разглеждане на данни за секция',
  'layouts_translate' => 'Превод на данни за секция',
  'layouts_multi_assign_permissions' => 'Назначи права',
  'layouts_saveorder' => 'Запиши позиции',
  'message_layouts_add_success' => 'Успешно добавяне на секция.',
  'message_layouts_edit_success' => 'Успешно редактиране на секция.',
  'message_layouts_translate_success' => 'Успешен превод на секция.',
  'message_multiassignpermissions_success' => 'Успешно редактиране на правата на секциите',
  'message_layouts_saveorder_success' => 'Успешно редактиране на позиции на секции',
  'warning_multiassignpermissions' => 'Маркираните в секциите права НЕ са видими при масово редактиране. Ако не сте сигурни в данните, моля не правете масова редакция. При запис на данните новите права ще заменят напълно действащите досега.',
  'error_layouts_edit_failed' => 'Секцията не е редактирана:',
  'error_layouts_add_failed' => 'Секцията не е добавена:',
  'error_layouts_translate_failed' => 'Секцията не е преведена:',
  'error_no_permissions_to_edit' => 'Нямате права за редакция на секции',
  'error_layouts_saveorder_failed' => 'Неуспешно редактиране на позиции на секции.',
  'error_no_such_layout' => 'Нямате възможност да прегледате този запис!',
  'error_no_name_specified' => 'Моля, въведете име на секция!',
  'error_no_model_specified' => 'Моля, изберете запис, за който ще бъде секцията!',
  'error_no_model_type_specified' => 'Моля, изберете тип запис, за който ще бъде секцията!',
  'error_layouts_place_contracts_system' => 'Задайте позиция под %d.',
  'error_layouts_place_contracts_custom' => 'Задайте позиция между %d и %d за Предмет или между %d и %d за Условия.',
  'error_layouts_place_customers' => 'Секции с позиция до %d се разполагат в Основни данни, до %d - в Адрес/Данни за контакт, до %d - в Лични данни/Фирмени данни. Потребителска секция трябва да има позиция по-голяма от %d, за да бъде разположена след тях.',
  'model_name' => 'Име на модел',
  'model_type' => 'Тип на модел',
  'permissions_view' => 'Права за разглеждане имат',
  'permissions_edit' => 'Права за редактиране имат',
  'layouts_permissions_view' => 'Разглеждане',
  'layouts_permissions_edit' => 'Редактиране',
  'help_layouts_name' => '',
  'help_layouts_groups' => '',
  'help_layouts_description' => '',
  'help_layouts_place' => 'Ако при добавяне оставите полето празно, системата ще постави секцията на последна позиция.',
  'help_layouts_status' => '',
  'help_layouts_status_active' => '',
  'help_layouts_status_inactive' => '',
); ?>