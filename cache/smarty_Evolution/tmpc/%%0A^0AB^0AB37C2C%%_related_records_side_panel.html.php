<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:33
         compiled from _related_records_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '_related_records_side_panel.html', 8, false),array('modifier', 'escape', '_related_records_side_panel.html', 11, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<?php $this->assign('cnum', 5); ?>
<?php $this->assign('cwidth', 120); ?>
    <div class="nz-side-panel-related-records">
        <?php $_from = $this->_tpl_vars['related_records']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['rr'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['rr']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['record']):
        $this->_foreach['rr']['iteration']++;
?>
            <?php ob_start(); ?><?php if (preg_match ( '/^referent_/' , $this->_tpl_vars['record']['name'] )): ?><?php echo $this->_tpl_vars['theme']->getIconForRecord('referent_documents'); ?>
<?php else: ?><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['record']['name']); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('img_name', ob_get_contents());ob_end_clean(); ?>
        <div class="nz-side-panel-related-records__record">
            <?php if (count($this->_tpl_vars['record']['ids']) != 0): ?>
                <a href="<?php echo $this->_tpl_vars['record']['link']; ?>
"<?php if (preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['record']['link'] )): ?>onclick="toggleRelatedTabs($('tab_<?php echo $this->_tpl_vars['record']['name']; ?>
')); $('rel_type').value='<?php echo $this->_tpl_vars['record']['name']; ?>
'; return false;"<?php endif; ?>
                   title="<?php echo $this->_tpl_vars['record']['label']; ?>
">
                    <span class="material-icons nz-side-panel-related-records__record-icon" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['img_name']; ?>
</span>
                    <span class="nz-side-panel-related-records__record-value"
                          style="padding: 2px 0 2px 3px;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="strong floatl"><?php echo count($this->_tpl_vars['record']['ids']); ?>
</span>
                </a>
            <?php else: ?>
                <span class="material-icons nz-side-panel-related-records__record-icon" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['img_name']; ?>
</span>
                <span class="nz-side-panel-related-records__record-value"
                      style="padding: 2px 0 2px 3px;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">-</span>
            <?php endif; ?>
        </div>
        <?php endforeach; endif; unset($_from); ?>
    </div>
<?php endif; ?>