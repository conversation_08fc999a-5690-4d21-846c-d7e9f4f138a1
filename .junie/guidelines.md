# Nzoom Development Guidelines

## Project Overview

Nzoom is a web-based CRM system built with PHP 7.4 and MySQL 8.2. It follows a robust MVC architecture pattern and is organized in a modular structure.

## Tech Stack

- **PHP 7.4+**: Core programming language
- **MySQL 8.2**: Database
- **ADOdb**: Database abstraction layer for database operations

## Project Structure

```
/
├── _libs/                  # Core application code
│   ├── inc/                # Core libraries and MVC framework
│   │   ├── common/         # Common utilities
│   │   ├── ext/            # External libraries
│   │   └── mvc/            # MVC implementation (controllers, models, viewers)
│   ├── Nzoom/              # Namespace-based classes
│   │   ├── Export/         # Export functionality
│   │   ├── Mvc/            # MVC traits and interfaces
│   │   └── ...             # Other namespaced components
│   └── modules/            # Application modules
│       ├── customers/      # Customer management module
│       ├── documents/      # Document management module
│       └── ...             # Other functional modules
├── conf/                   # Configuration files
├── resources/              # Static resources
└── cache/                  # Cache storage
```

## Architecture

### MVC Pattern

The application follows a strict MVC (Model-View-Controller) architecture:

- **Models**: Located in `_libs/modules/*/models/`
  - Handle data logic and business rules
  - Extend the base `Model` class from `_libs/inc/mvc/model.class.php`

- **Viewers**: Located in `_libs/modules/*/viewer/`
  - Handle presentation logic
  - Responsible for rendering templates

- **Controllers**: Located in `_libs/modules/*/controllers/`
  - Handle request processing and routing
  - Extend the base `Controller` class from `_libs/inc/mvc/controller.class.php`
  - Implement various interfaces for specific functionality

### Code Organization

#### Traits and Interfaces

The application makes extensive use of PHP traits and interfaces for code reuse:

- **Traits**: Located in `_libs/Nzoom/Mvc/ControllerTrait/`
  - Provide reusable functionality across different controllers
  - Examples: `GridExportActionTrait`, `CheckAccessTrait`, `I18nTrait`

- **Interfaces**: Located in `_libs/Nzoom/Mvc/ControllerInterface/`
  - Define contracts that controllers must implement
  - Examples: `ActionsInterface`, `GlobalValsInterface`, `ManageModelInterface`

#### Module Structure

Each module follows a consistent structure:
```
module_name/
├── controllers/     # Controller classes
├── i18n/            # Internationalization files
├── javascript/      # JavaScript files
├── models/          # Model classes
├── templates/       # Template files
└── viewer/          # Viewer classes
```

## Coding Standards

### Naming Conventions

- **Classes**: PascalCase (e.g., `DocumentsController`, `CustomerModel`)
- **Methods**: camelCase (e.g., `getModel()`, `executeAfter()`)
- **Variables**: camelCase (e.g., `$documentId`, `$userRole`)
- **Constants**: UPPER_CASE (e.g., `MAX_ATTEMPTS`, `DEFAULT_TIMEOUT`)

### Code Organization

- Keep methods focused on a single responsibility
- Prefer strict return types and trowing exceptions instead of returning fallback values
- Use traits for shared functionality
- Implement interfaces to ensure contract adherence
- Follow the MVC pattern strictly

## Best Practices

1. **Follow MVC Pattern**: Keep business logic in models, presentation in viewers, and request handling in controllers
2. **Use Dependency Injection**: Utilize the registry pattern for dependencies
3. **Internationalization**: Use the i18n system for all user-facing text
4. **Error Handling**: Use the built-in error handling methods in the Model class
5. **Database Access**: Use the Model methods for database operations through ADOdb
6. **Security**: Always validate and sanitize input data
7. **Code Reuse**: Use traits for shared functionality across controllers
8. **Export Functionality**: Use the Export service for data export operations

## Common Tasks

### Adding a New Module

1. Create a new directory in `_libs/modules/`
2. Create the standard module structure (controllers, models, viewers, etc.)
3. Implement the required classes extending the base framework classes
4. Use module analyses and resources for pattern examples and reference.

### Working with Database

- Models handle database operations through the ADOdb abstraction layer
- Use the Model methods for CRUD operations

### Implementing Export Functionality

- Use the `GridExportActionTrait` for implementing export functionality in controllers
- Implement the required methods for specific export formats (CSV, XLSX, PDF)

### Debugging

- Check PHP error logs at `php_errors.log`
- Enable debugging in configuration files when needed

### Executing Scripts

- Run PHP scripts from the command line: `php script_name.php`
- Execute composer script `cd _libs && composer script_name`

## Testing

### Running Tests

- Execute tests using PHPUnit: `cd _libs && vendor/bin/phpunit _libs/tests/`
- Execute tests using composer: `cd _libs && composer test`
- Run specific test class: `cd _libs && vendor/bin/phpunit _libs/tests/Nzoom/Export/ExportServiceTest.php`
- Run tests with coverage report: `cd _libs && vendor/bin/phpunit --coverage-html ./coverage _libs/tests/`

### Writing Tests

- Write unit tests for models and services
- Use the testing framework in `_libs/tests/`
- Follow the test case patterns in existing tests
- Extend appropriate test case classes (e.g., `ExportTestCase` for export-related tests)

## Additional Resources

For more detailed documentation, refer to the comprehensive documentation outline available in the project.
