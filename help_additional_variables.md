# Описание на използване и вписване на допълнителни променливи към модели за nZoom 1.7.x

Допълнителните променливи като типове се описват в таблиците:

- **_fields_meta** – описва езиково независима информация за променливите
- **_fields_i18n** – описват се етикетите, help, описание за променливите на съответните езици и може да се добавя заден етикет
- **_fields_options** – изброимите елементи на променливи от тип dropdown, radio, checkbox_group
- **layouts** – информация за секциите (layouts) на променливи – със съответните етикет и позиция

Стойностите на допълнителните променливи се съхраняват в таблици
**[име на модели]_cstm**, като **documents_cstm**, **customers_cstm** ...,
с изключение на стойностите за променливи от тип: **gt2** - съхраняват се в **gt2_details**,
**bb** – в **bb**, за конфигуратор тип Франкенщайн – в configurator.

* * *

## I. Описание на таблиците с техните полета ##

1. Таблица **_fields_meta**
  * 1.1. **id** – autoincrement , служи за релация с _fields_i18n (поле parent_id) и […]_cstm (поле var_id), не се попълва, приема поредна стойност автоматично!
  * 1.2. **model** – име на модела, Например: Customer (за контрагенти), Document (за документи), Documents_Stage (за етапи на документи), Tasks (за задачи – използва се само за добавяне на бутони за справка) и т.н.
  * 1.3. **model_type** – тип на модела, число, отговаря на ID-то на типа на модела, Например: 1 за тип документи с ID 1
  * 1.4 **name** – име на променливата на латиница, уникално за модела и типа на модела, Например: offer_description

    При трансформиране има възможност за копиране на допълнителни променливи към основни.
    Името на допълнителната променлива трябва да е от вида
    **basic_[името на основната променлива]**, например basic_customer се копира към
    основна променлива customer, basic_department се копира към основна променлива
    department.

    За копиране на назначения при трансформиране трябва да имаме допълнителна променлива
    basic_department за разпределяне и променлива basic_assignments_owner за изпълнители.
    Възможно е да се копират и другите назначения като променливите да са
    **basic_assignments_responsible**, **basic_assignments_observer**,
    **basic_assignments_decision**.

    Възможно е и обратното копиране – на основни към допълнителни променливи при
    трансформация. Името на допълнителната променлива трябва да е от вида
    **source_[името на основната променлива]**, например в source_customer се
    копира стойността на основната променлива customer.

    Ако в конкретната инсталация се използва Астериск и полето е от тип text,
    променливата може да се настрои, така че да се показва от nZoom като телефонен
    номер, който може да бъде набран. За целта името на променливата трябва да има
    следния формат:

    - **asteriskcall_phone_[име на променливата]** - за телефон
    - **asteriskcall_gsm_[име на променливата]** - за мобилен телефон
    - **asteriskcall_fax_[име на променливата]** - за факс

  * 1.5 source – в полето се записват множество различни настройки във формат: име := стойност. Някои от тях са специфични за даден вид променлива и са обяснени в следващите точки.

Полетата могат да имат изчисления, чиито параметри се дефинират в source колоната.

Запазените думи за изчисленията са:

| Ключова дума | Обясненение |
| --- | --- |
| equation | Израз за изчисление с вече приготвените параметри (виж по-долу) |
| array_function | Агрегатна функция, използвана само в полета, които не са в групова таблица. Има две възможни стойности: sum и avg.<br>sum - сумират се всички стойности на equation за редовете от групова таблица, указани в update_fields или num_rows<br>avg – взема средна аретметична стойност за всички редове (сумата разделена на броя редове) |
| update_fields | Използва се само в полета, които не са в групова таблица. Указва списък от полета от групова таблица, които трябва да се изчислят преди да се направи калкулацията по equation.<br>Може да се използва и при липса на зададена агрегатна функция (array_function) |
| num_rows | Указва име на поле от групова таблица, което я обозначава, за да се намерят редовете в таблицата. Използва се, само ако има зададена агрегатна функция (array_function).<br>Ако е зададено update_fields, стойността на num_rows се игнорира, т.е. не е нужно (даже излишно) да се задава, ако има update_fields. |
| last_equation | Израз за изчисление СЛЕД като вече е изчислена агрегатната функция. Има за цел да дообработи резултата от сумирането. |
| format | Низ за  на крайния резултат. За 2 цифри след десетичния знак: %.2F |
| Параметри - $а, $tax, $customer | Освен запазените думи, може да има и параметри участващи в уравнението (equation, last_equation). Параметрите (математичски е правилно да се наричат операнди) се отбелязват с $ отпред (например: $а, $tax, $customer) и могат да получават стойности от REQUEST (данни от формата) или SQL (данни от базата). Освен това, параметрите могат да използват стойностите от предходно дефинираните параметри. Обсегът (контекстът) на параметрите е само в рамките на изчислението на текущото поле, т.е. параметрите не могат да се използват за изчисленията в други полета. |

Вече (след ревизия 14446):Важно: в полета от групова таблица се игнорират array_function, num_rows, update_fields, last_equation – те са запазени за полета извън груповите таблици. Достатъчно е да сложите полетата от групови таблици в update_fields на друго поле или в sequence на бутон за изчисления.Важно: в поредицата от update_fields може да се използват стойности от предходно изчислени колони от групова таблица и използвани в update_fields.Важно: ако използвате update_fields за негрупова променлива, не е нужно (даже е излишно) да упоменате и num_rows.Важно: имената на параметри (заедно с $) не трябва да се съдържат едно в друго, т.е. недопустимо е да имаме $а (например със стойност 5) и $average (например със стойност 7), защото в equation := $a*$average ще се замести: equation := 5*5verage,което ще произведе грешка при изчислението.Важно: стойността получена от изчислението трябва да е валидно число, в противен случай трябва да е в кавички (виж: Пример 3)Пример 1: Просто изчисление без агрегатна функция (array_function)Съдържанието на source за поле с име commission, което се намира в едноредова таблица или конфигуратор:$customer := request(‘customer’)$total := request(‘total’)$commission_percentage := sql(‘SELECT value FROM nom_cstm WHERE var_id=123 AND model_id = $customer’)equation := ($total * $commission_percentage) / 100format := %.2FОбяснение:Параметърът $customer получава стойност от формата от ид на контрагента.Параметърът $total получава стойност от формата от поле с име ‘total’.Параметърът $commission_percentage получава стойност за процента на комисионната от базата от данни след SQL заявка, в която участва и предходно дефинираният параметър $customer.Част от параметрите ($total и $commission_percentage) участват в уравнението (equation), за да определят размера на комисионнатаРезултатът се форматира с 2 цифри след десетичния знакПример 2: Изчисление с агрегатна функция (array_function), предизвикано изчисление на полета от групова таблица. Това е, може би, най-класическият случай на изчисленията:

Имаме групова таблица с колони price, quantity, discount_percentage, discount и subtotal:

| price | quantity | discount_percentage | discount (readonly) | subtotal (readonly) |
| --- | --- | --- | --- | --- |
|  (въвежда се) |  (въвежда се) | (въвежда се) | (изчислява се) | (изчислява се) |
|  (въвежда се) |  (въвежда се) | (въвежда се) | (изчислява се) | (изчислява се) |

Има едно свободно стоящо поле tax_percentage, което съдържа процента на данъка.

Има и още едно свободно стоящо поле total, което се използва, за да натрупа общия сбор от произведенията на цената и количеството с приспаднатата комисионна.

Съдържанието на source за поле discount:

```
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
equation := $price*$quantity*$discount_perc/100
format := %.2F
```

Обяснение:
- Параметърът \$price получава стойност от формата от колоната за цената.
- Параметърът \$quantity получава стойност от формата от колоната за количеството.
- Параметърът \$discount_perc получава стойност от формата от колоната за процента.
- Уравнението equation указва как да се изчисли стойността на отстъпката.
- Резултатът се форматира с 2 цифри след десетичния знак.

Съдържанието на source за поле subtotal:

```
$price := request('price')
$quantity := request('quantity')
$discount := request('discount')
equation := $price*$quantity - $discount
format := %.2F
```

Обяснение:
- Параметърът \$price получава стойност от формата от колоната за цената.
- Параметърът \$quantity получава стойност от формата от колоната за количеството.
- ВНИМАНИЕ: Параметърът \$discount получава стойност от вече изчислената стойност на полето discount.
- Уравнението equation указва как да се изчисли поредовата сума.
- Резултатът се форматира с 2 цифри след десетичния знак.

Съдържанието на source за поле total (вариант 1):

```
array_function := sum
update_fields := discount, subtotal
$price := request('price')
$quantity := request('quantity')
$discount_perc := request('discount_percentage')
$tax_perc := request('tax_percentage')
equation := $price*$quantity*(1-$discount_perc/100)
last_equation := equation*(1 + $tax_perc/100)
format := %.2F
```

Обяснение:
- array_function е специална ключова дума, която указва, че в полето ще се сумират стойностите от полета от групова таблица. Тук е важно да се каже, че тази ключова дума следва да се използва само за променливи, НЕУЧАСТВАЩИ в групова таблица, каквото в примера е и total.
- update_fields – указва че преди да се направят калкулациите по настоящото поле (total), трябва да се извършат калкулации за отстъпката (discount) и за поредовата сума (subtotal).
- Обърнете внимание, че при наличието на update_fields липсва num_rows.
- Параметърът \$price получава стойност от формата от колоната за цената.
- Параметърът \$quantity получава стойност от формата от колоната за количеството.
- Параметърът \$discount_perc получава стойност от формата от колоната за процента отстъпка.
- Параметърът \$tax_perc получава стойност от формата за процента данък.
- Уравнението equation указва как да се изчисли поредовата сума, която после ще бъде сумирана.
- last_equation указва как към вече сумираните поредови суми да се добави и данъкът.
- Резултатът се форматира с 2 цифри след десетичния знак.

Съдържанието на source за поле total (вариант 2 с вече изчислен subtotal):

```
array_function := sum
update_fields := discount, subtotal
$tax_perc := request('tax_percentage')
$subtotal := request('subtotal')
equation := $subtotal
last_equation := equation*(1 + $tax_perc/100)
format := %.2F
```

Обяснение:
- array_function е специална ключова дума, която указва, че в полето ще се сумира стойностите от полета от групова таблица.
- update_fields – указва че преди да се направят калкулациите по настоящото поле (total) трябва да се извършат калкулации за отстъпката (discount) и за поредовата сума (subtotal).
- Обърнете внимание, че при наличието на update_fields липсва num_rows.
- Параметърът \$tax_perc получава стойност от формата за процента данък.
- ВНИМАНИЕ: Параметърът \$subtotal получава стойност от вече изчислената стойност на полето subtotal, която е изчислена предварително благодарение на update_fields.
- Уравнението equation съдържа само параметъра \$subtotal.
- last_equation указва как към вече сумираните поредови суми да се добави и данъкът.
- Резултатът се форматира с 2 цифри след десетичния знак.

Подсигуряване при деление:
```
$a := request(‘discount_percentage’)
$b := request(‘coef’)
equation := $a/$b
```
Ако coef е със стойност 0, а полето discount_percentage e 6, при изчислението ще се замести:
```
equation := 6/0
```
Това ще даде математическа грешка (записва се в таблица logs).Вариант за подсигуряване в equation:
```
$a := request(‘discount_percentage’)
$b := request(‘coef’)
equation := ($b!=0 ? $a/$b:0)
```

Пример 3:

Изчисляване на работените часове между две дати (датите са с дата и час).
```
$ad := request('date')
$bd := request('activity_end')
$at := request('from_hour')
$bt := request('to_hour')
$from := SQL('select cast(concat("$ad", " ", "$at") as datetime)')
$to := SQL('select cast(concat("$bd", " ", "$bt") as datetime)')
$ttal := SQL('SELECT TIME_FORMAT(SEC_TO_TIME(TIMESTAMPDIFF(SECOND, "$from", "$to") - TIMESTAMPDIFF(DAY, "$ad", "$bd")*16*60*60), "%H:%i")')
equation := "$ttal"
```
Примера е от инсталация QUEISSER за променлива с ID 1821.

Пресмята се колко часа е работено между две дати, като се приема, че работното време за всеки 24 ч. е 8 ч. (не се съобразяват почивните дни, но явно в случая такаава логика е приета, но примера е интересен и може да е полезен).

Честно казано алгоритъма за пресмятане не е много точен, но зависи от начина на използване, а и както писах по-горе - самия пример е интересен. Та взима се разликата между двете дати в секунди и от нея за всеки ден (ако е за повече от ден) се премахват по 16 ч. (ако 8 ч. е работния ден, то от 24  ч. 16 са неработни) също превърнати в секунди и на получените работни секунди се прилага MySQL функцията  SEC_TO_TIME, която превръща секундите във време с формат: &lt;часове&gt;:&lt;минути&gt;:&lt;секунди&gt;. След това на полученото се прилага форматиране, за да се получат само чесове и минути, тъй като в случая не ни интересуват секундите. Т.е. при разлика между двете дати от 3 ч. и 30 мин. се получава 03:30.

Друго интересно в случая е, че полученото трябва да е в кавички, тъй като не е валидно число, а текст.

## Опции за променливи от изброим тип

Опции за променливи от изброим тип (dropdown, radio или checkbox_group) могат да се вземат по 2 начина: от стойности във _fields_options или от записи от даден (под)модул.

### Първи начин

Ако името (name) на променливата отговаря на parent_name на опциите, то в source не трябва да се задават други настройки.

Ако се налага за различни променливи да се ползват едни и същи изброими стойности, в полето source записваме името на променливата, която първа използва тези стойности, указани в _fields_options. Например имаме променлива dropdown1 с изброми стойности "да" и "не" и искаме да използваме в друга променлива dropdown2 същите изброими стойности ("да" и "не"), то в полето source за dropdown2 записваме:

```
field := parent_field_name
```

Например:
```
field := dropdown1
```

### Втори начин

Опциите могат да се вземат от фунция (метод на клас dropdown или наследен клас – възможните методи са изброени в таблицата по-долу), синтаксисът на записване е:

```
method := function
$arg1 := argument1
$arg2 := argument2 …
```

#### Зависими dropdown стойности

Възможно е dropdown стойностите да са зависими от друг dropdown. Например:

За променлива PP_Project, полето source ще включва:

```
method := getDocs
deleted := 0
active := 1
type := 7
status_no := 'closed'
on_change := 'PP_Stage'
```

В on_change записваме променливата, която ще промени съдържанието си при промяна на PP_Project - тук това е PP_Stage. За полето source на PP_Stage записваме:

```
get_id := request('PP_Project')
default_id := sql('select value from documents_cstm where model_id=$model_id and var_id=1525')
method := getDocStages
```

get_id взема стойността на PP_Project (ID на документ) от POST заявка и се използва при редактиране на допълнителните променливи. default_id взема записаната стойност от базата за PP_Project и се ползва при разглеждане на допълнителните променливи и генериране. В sql заявката $model_id е запазена променлива, която е id на редактирания модел. var_id е id на променливата PP_Project.

Горният пример е за свързани дропдауни, използващи методa getDocStages. Възможно е да се използваt и други методи.

#### Пример с getCustomDropdown

В родителския дропдаун (insurance_group) записваме в source:

```
on_change := 'insurance_type'
```

В зависимия дропдаун (insurance_type) записваме в source:

```
method := getCustomDropdown
table := DB_TABLE_NOMENCLATURES
table_i18n := DB_TABLE_NOMENCLATURES_I18N
$requested_insurance_group := request('insurance_group')
$saved_insurance_group := sql('select value from contracts_cstm where model_id=$model_id and var_id IN (select id from _fields_meta where model="contract" and model_type=5 and name="insurance_group")')
where := (type = "$requested_insurance_group" OR type="$saved_insurance_group")
```

**Обяснение:** В зависимия дропдаун се вземат всички номенклатури от тип, стойността на който е взет от родителския дропдаун.

**Важно:** В примера се използват две временни променливи:

1. **$requested_insurance_group** - взема стойността на родителския дропдаун при НЕГОВАТА смяна от request.

2. **$saved_insurance_group** - взема стойността на родителския дропдаун от ПРЕДХОДНО ЗАПАЗЕНАТА МУ В БАЗАТА СТОЙНОСТ.

Целите на използването на $saved_insurance_group са две:
- Да се заредят правилните опции на зависимия дропдаун при режим на редакция, след като вече е била съхранена стойност
- При режим на разглеждане стойността да се покаже правилно

**Важно за WHERE клаузата:** В примера е използван трик, при който типът номенклатура се взема или при смяна от родителския дропдаун (`$requested_insurance_group`), или директно от предходно запазената му стойност в базата (`$saved_insurance_group`) – никога двете стойности не са попълнени едновременно.

Обърнете внимание, че за този метод могат да се използват променливи, дефинирани в по-горните редове – достатъчно е само да се сложи $ пред тях.

Обърнете внимание, също така, че липсват параметрите:
```
value := id
label := name
```

Те са направени вече да се вземат по подразбиране (виж настройката на методите за дропдауни).

#### Втори вариант за настройка на свързани дропдауни

Има и втори вариант за настройка на свързани дропдауни – опциите на дропдауните да са записани директно във _fields_options, а не с метод. При зависими (свързани) dropdown променливи, стойностите на които са от таблица _fields_options, отново записваме в родителската променлива в source:

```
on_change := '[името на зависимата променлива]'
```

В зависимата променлива в source записваме:

```
parent_name := [името на родителската променлива]
method := getOnChangeOptions
```

В таблицата _fields_options записваме опциите за родителския дропдаун по стандартния начин. В полето parent_name записваме името на родителския дропдаун, а в полето child_name записваме за всяка опция името на ФИКТИВЕН дропдаун (който не трябва да се добавя). След това добавяме редове във _fields_options с опциите, които трябва да се показват в зависимия (втория) дропдаун, например (виж таблицата):

| parent_name | label | option_value | child_name |
| --- | --- | --- | --- |
| за родителския дропдаун insurance_group |
| insurance_group | Здравни застраховки | 1 | health |
| insurance_group | Гражданска отговорност | 2 | liability |
| insurance_group | Имуществени застраховки | 3 | property |
|  |
| за зависимия дропдаун insurance_type |
| health | Колективни здравни | 56 |  |
| health | Лични здравни | 57 |  |
| liability | ГО автомобили | 67 |  |
| liability | ГО летателни апарати | 68 |  |
| liability | ГО кораби | 71 |  |
| property | Застраховка апартаменти | 72 |  |
| property | Застраховка ниви | 73 |  |

Не може да се предава зависимост (on_change) повече от едно ниво (след зависимата променлива).

### Методи във всички модули (dropdown.class.php)
| № | Метод | Описание | Параметри |
| 1 | getCustomers | Списък на контрагенти | method := getCustomersis_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)type := (незадължителен параметър - id на тип контрагент, ако не е посочен се взимат контрагенти от всички типове)id := (незадължителен параметър - id на конкретен контрагент, ако не е посочен се взимат всички контрагенти с горепосочените филтри)tag := 3,4 (незадължителен параметър - id на таг с който са тагнати контрагентите, могат да се дават няколко тага като се взимат всички контрагенти, които имат поне единия таг – например ако имаме tag := 3,4 ще се вземат контрагенти, които са тагнати с 3, контрагенти които са тагнати с 4 и контрагенти които са тагнати с 3 и 4) |
| 2 | getCustomerBranches | Списък на обектите за контрагент | method := getCustomerBranchescustomer := (незадължителен параметър - id на контрагент, ако не е посочен се взима id на текущия контрагент).Има възможност и да се подаде параметър, който взима ID-то на контрагент от точно определена променлива, например: customer := $cust_id |
| 3 | getCustomerContactPersons | Списък на лицата за контакт за определен обект на контрагент | method := getCustomerContactPersonscustomer := (незадължителен параметър - id на контрагент, ако не е посочен се взима id на текущия контрагент)customer_branch := (незадължителен параметър - id на обект за контрагента, възможно е и да се изреждат и id-та на обекти със запетая, ако параметърът се настрои на all, се взимат лицата за контакт от ВСИЧКИ обекти към контрагента, ако не е посочен, се вземат лицата за контакт на ВСИЧКИ ОБЕКТИ)Ако контрагентът е физическо лице, тогава в dropdown-а се зарежда единствено името на контрагента.Има възможност и да се подаде параметър, който взима ID-то на контрагент от точно определена променлива, например: customer := $cust_idИма възможност и да се подаде параметър, който взима ID-то на обект от точно определена променлива, например: customer_branch := $cust_branch_id |
| 4 | getUsers | Списък на потребители | method := getUsersdepartment := (незадължителен параметър - id на отдел)active := 1 (дали да взима само активни потребители)is_portal := 0 – само нормални потребители, 1 – само портални потребители |
| 5 | getUnfinishedSubprojects | Списък на всички неприключили подпроекти за проекта, назначен на текущия запис (документ или задача) | method := getUnfinishedSubprojects |
| 6 | getUnfinishedPhases | Списък на всички неприключили фази за проекта (работи само за проекти) | method := getUnfinishedPhases |
| 7 | getProjects | Списък на проекти | method := getProjectstype := (незадължителен параметър - id на тип проект, ако не е посочен се взимат проекти от всички типове)status := (незадължителен параметър - статус на проект, ако не е посочен се взимат проекти без значение от статуса) |
| 8 | getEmployees | Списък на служители (контрагенти от тип служител) | method := getEmployeesemployees_with_users_only := 1 (незадължителен параметър, който филтрира само служителите с потребители) |
| 9 | getOnChangeOptions | Списък зависещ от родителски списък, използва се при свързани дропдауни | method := getOnChangeOptions |
| 10 | getArticleCategories | Списък на категории в номенклатури | method := getArticleCategoriescategory := (незадължителен параметър - id на една или повече категории, изброени със запетаи. Показват се всички категории, чийто родител е някоя от указаните категории. За да се покаже цялото дърво от категории, този параметър се пропуска или се слага на 1)exclude_root := (незадължителен параметър – указва дали родителската категория (когато е посочена не повече от една за параметър category) да се вижда - стойност 0, или не – стойност 1, по подразбиране стойността на този параметър е 0 – вижда се) |
| 11 | getRelatedCustomers | Списък на свързани контрагенти, за текущия контрагент | method := getRelatedCustomersrelative_type := (тип връзка на контрагентите:major_associate – мажоритарен съдружник, minor_associate – миноритарен съдружник, shareholder – акционер, employee – служител, filial_company – дъщерно дружество, joint_venture – свързани фирми, colleague – свързани лица, associate – съдружници |
| 10 | getDepartments | Списък на отдели | method := getDepartmentsdepartment := (незадължителен параметър – едно или повече id-та на отдели, разделени със запетая. Показва всички отдели, чийто родител е някой от указаните отдели. За да се покаже цялото дърво от отдели, този параметър се пропуска или се слага на 1)exclude_root := (незадължителен параметър – указва дали родителския отдел да се вижда - стойност 0 или не – стойност 1, по подразбиране стойността на този параметър е 0 – вижда се) |
| 11 | getGroups | Списък на групи | method := getGroupsgroup := (незадължителен параметър - id на група, показва всички групи, чийто родител е указаната група. За да се покаже цялото дърво от групи, този параметър се пропуска или се слага на 1)exclude_root := (незадължителен параметър – указва дали родителската група да се вижда - стойност 0 или не – стойност 1, по подразбиране стойността на този параметър е 0 – вижда се) |
| 12 | getAttachments | Списък на всички прикачени файлове към текущия запис | method := getAttachmentsoutput := (незадължителен параметър Методът може да се използва за всчики модели, които имат функция а прикачване на файлове и се използва в допълнителни променливи, за извеждане в dropdown на всички прикачени файлове към текущия модел. |
| 13 | getCurrencies | Списък на валути | method := getCurrenciesformat := short (незадължителен параметър – показва валутите само с кода им, опция по подразбиране)format := verbose (незадължителен параметър – показва валутите с кода и името им например &quot;(BGN) Български лев&quot;)Важно: Методът извежда всички валути, отбелязани като активни (active = 1) в таблицата fin_currencies_available |
| 14 | getAnnouncements | Списък на съобщения (само неархивираните) | method := getAnnouncementstype := (незадължителен параметър - id на тип или типове съобщения, отделят се със запетая)assignments_users := (незадължителен параметър – id на потребители, може да се сложи и служебната дума currentUser, която указва назначение на съобщенията към текущия потребител или отделите на текущия потребител, отделят се със запетая)assignments_departments := (незадължителен параметър – id на отдели, към които е назначено съобщението, отделят се със запетая) |
| 15 | getMonthsNames | Списък на месеците от годината. | Стойностите на опциите са 1, 2, 3 ... съответно за януари, февруари, март ... |
| 16 | getWarehouses | Списък на складовете (към конкретни фирма и офис) | method := getWarehousescompany := (незадължителен параметър –id на фирма)office := (незадължителен параметър –id на офис) active := (незадължителен параметър, ненулева стойност задава да се вземат само активни записи) |
| 17 | getYears | Списък на години от посочена стартова до посочен брой преди/след текущата. | method := getYearsstart := (стартова година, по подразбиране 1970)offset_from_current := (отстъп спрямо текущата година – определя последната показвана година, може да е положително или отрицателно число, по подразбиране 0)num_active := (незадължителен параметър, задава, че само последните num_active опции ще са активни)option_prefix := (незадължителен параметър, задава префикс за стойностите (option_value))sort := asc|desc (незадължителен параметър за сортиране на опциите, по подразбиране са във възходящ ред) |
| 18 | getDays | Списък на дни в посочен месец и година. | method := getDaysmonth := (не задължителна настройка, която може да укаже за кой месец се вземат дните, работи в комбинация с настройката year, 1 - януари, ... 12 - декември)year := (не задължителна настройка, която може да укаже за кой месец и година се вземат дните, работи в комбинация с month)Ако се пропусне указването на month и year, методът връща 1-31option_prefix := (незадължителен параметър, задава префикс за стойностите(option_value))sort := asc|desc (незадължителен параметър за сортиране на опциите, поподразбиране са във възходящ ред) |
| 19 | getCountries | Списък на всички държави (взема се от таблица country_list) | (няма) |
### Методи в контрагенти (customers.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getCstmOptions | Списък на контрагенти | method := getCstmOptionsis_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)type := (незадължителен параметър - id на тип контрагент, ако не е посочен се взимат контрагенти от всички типове)id := (незадължителен параметър - id на конкретен контрагент, ако не е посочен се взимат всички контрагенти с горепосочените филтри)tag := 3,4 (незадължителен параметър - id на таг с който са тагнати контрагентите, могат да се дават няколко тага като се взимат всички контрагенти, които имат поне единия таг – например ако имаме tag := 3,4 ще се вземат контрагенти, които са тагнати с 3, контрагенти които са тагнати с 4 и контрагенти които са тагнати с 3 и 4) |
| 2 | getCstmRelatives | Списък на връзки между контрагенти | method := getCstmRelativesis_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)type := (незадължителен параметър - id на тип контрагент, ако не е посочен се взимат контрагенти от всички типове)c_id := задължителен параметър - id на контрагента |
| 3 | getIsCompany | Списък на видовете контрагенти (ЮЛ, ФЛ) | method := getIsCompany |
| 4 | getTypesKind | Списък на видовете контрагенти (ЮЛ, ФЛ, и двете) | method := getTypesKind |
### Методи в документи (documents.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getCstmOptions | Списък на контрагенти | method := getCstmOptionsis_company := (незадължителен параметър – 1 – за юридически лица, 0 за физически лица, ако се пропусне и се взимат и физически, и юридически лица)type := (незадължителен параметър - id на тип контрагент, ако не е посочен, се взимат контрагенти от всички типове) |
| 2 | getFcNums | Списък на фактури за контрагент | method := getFcNumsvar_id := (задължителен параметър - id на променлива пазеща стойността на фактурата от поръчка)type := (задължителен параметър - тип на документ (поръчка) откъдето взема var_id) |
| 3 | getFcSums | Списък от сумите за плащане на фактури за контрагент (стойността на всяка опция е сумата и валутата, а етикета е името и номера на документа) | method := getFcSumssum_var_id := (задължителен параметър - id на променлива пазеща стойността на фактурата от поръчка)currency_var_id := (незадължителен параметър - id на променлива пазеща валутата на фактурата от поръчка)type := (задължителен параметър - тип на документ (поръчка) откъдето взема var_id)status := (незадължителен параметър – статус на документ (поръчка))substatus := (незадължителен параметър – подстатус на документ (поръчка)) |
| 4 | getDocs | Списък на документи | method := getDocsstatus_no := статус различен отtype := тип(ове) на документи, разделени с запетая |
| 5 | getBBElements | Списък на всички елементи в ББ към документ | method := getBBElements |
| 6 | getStatuses | Списък на всички статуси (и подстатуси) към документ | method := getStatusesmodel_types := (незадължителен параметър – списък с id-та на типове документи, отделени със запетая) |
| 7 | getOwnership | Списък на всички видове собственост (неразпределен, разпределен, назначен) | method := getOwnership |
| 8 | getDirections | Списък на всички видове посоки (входящ, изходящ, вътрешен) | method := getDirections |
### Методи в проекти (projects.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getStatuses | Списък на всички статуси (при подаден тип и подстатуси) | method := getStatusesmodel_types := (незадължителен параметър – списък с id-та на типове документи, отделени със запетая) |
| 2 | getPriorities | Списък на всички приоритети за проекти (нисък, нормален, висок) | method := getPriorities |
| 3 | getFinishedOptions | Списък на опциите за статус приключен (приключен, нормално приключен, пропаднал) | method := getFinishedOptions |
### Методи в задачи (tasks.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getStatuses | Списък на всички статуси (при подаден тип и подстатуси) | method := getStatusesmodel_types := (незадължителен параметър – списък с id-та на типове документи, отделени със запетая) |
| 2 | getPriorities | Списък на всички приоритети за проекти (нисък, нормален, висок) | method := getPriorities |
| 3 | getOwnership | Списък на всички видове собственост (неразпределенa, разпределенa, назначенa) | method := getOwnership |
| 4 | getSeverity | Списък на всички приоритети за проекти (много нисък, нисък, нормален, висок, много висок) | method := getSeverity |
### Методи във финанси (finance.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getCompanyOffices | Списък на офисите за дадена компания или всички офиси | method := getCompanyOfficescompany_id := (незадължителен параметър –id на фирма)active := (незадължителен параметър, ненулева стойност задава да се вземат само активни записи) |
| 2 | getWarehousesEmployees | Списък на служителите в даден склад | method := getWarehousesEmployeeswarehouse := (задължителен параметър –id на склад) active := (незадължителен параметър, ненулева стойност задава да се вземат само активни записи) |
| 3 | getBankAccounts | Списък на банковите сметки за конкретна фирма (и офис) | method := getBankAccountscompany_id := (задължителен параметър –id на фирма)office_id := (незадължителен параметър – id на офис. Ако е зададен офис, се вземат всички банкови сметки за фирмата, които са към този офис или са без офис.) active := (незадължителен параметър, ненулева стойност задава да се вземат само активни записи) |
| 4 | getCashboxes | Списък на касите за конкретна фирма и офис | method := getCashboxescompany_id := (задължителен параметър –id на фирма) office_id := (задължителен параметър – id на офис) active := (незадължителен параметър, ненулева стойност задава да се вземат само активни записи) |
| 5 | getDocumentsModels | Списък на видове записи, за които могат да се добавят нови типове финансови документи | method := getDocumentsModels |
| 6 | getFinanceDocumentsTypesModels | Списък на всички видове записи, за които има финансови документи | method := getFinanceDocumentsTypesModels |
| 7 | getDocumentsModelsForSections | Списък на видове финансови документи (за раздели) | method := getDocumentsModelsForSections |
| 8 | getStatuses | Списък на статусите на финансовите документи | method := getStatuses |
| 9 | getFinancePaymentsTypes | Списък типовете плащания (ПКО, РКО, банков превод, платежно нареждане, транзакционен разход) | method := getFinancePaymentsTypes |
| 10 | getInvoicesTemplatesTypes | Списък на шаблони за фактуриране за договори (нормална, авансова, финална) | method := getInvoicesTemplatesTypes |
| 11 | getRepaymentPlansStatuses | Списък на статусите на погасителните планове | method := getRepaymentPlansStatuses |
| 12 | getPaymentsStatuses | Списък на статусите за плащане (неприключено, приключено) | method := getPaymentsStatuses |
| 13 | getBudgetsStatuses | Списък на статусите за бюджети (в подготовка, в изпълнение, одобрен, невалиден) | method := getBudgetsStatuses |
| 14 | getBudgetsMethods | Списък на видове бюджет (bottom-up, top-down) | method := getBudgetsMethods |
| 15 | getTransactionExpensesOperations | Списък на видовете транзакционни операции (теглене, трансфер, получаване, внасяне) | method := getTransactionExpensesOperations |
| 16 | getPaymentsTypes | Списък на типовете плащания (в брой, по банков път) | method := getPaymentsTypes |
| 17 | getBankAccountsTypes | Списък на видовете банкови сметки (Банкова сметка, Дебитна карта, Кредитна карта, Кредитна линия, Овърдрафт) | method := getBankAccountsTypes |
| 18 | getRecurrenceTypes | Списък на опции за продължителност за периодични плащания (ежедневно, седмично, месечно и т.н.) | method := getRecurrenceTypes |
| 19 | getAnalysisTypesKinds | Списък на опции за анализ за: приходи, разходи, и двете | method := getAnalysisTypesKinds |
| 20 | getDistributionStatuses | Състояния на разпределение на приходни и разходни финансови документи (няма разпределение, разпределен, неразпределен). | method := getDistributionStatuses |
| 21 | getDocsPaymentStatuses | Списък на платежните статуси на финансовите документи (неплатен, частично платен, напълно платен, няма да има плащане) | method := getDocsPaymentStatuses |
| 22 | getFinanceAnalysisItems | Списък (дървовиден) на приходни или разходни пера | method := Finance_Dropdown::getFinanceAnalysisItemstype := (тип – income или expense)item_type := (алтернативен параметър за тип – income или expense) |
| 23 | getCompaniesOffices | Списък на компаниите с техните офиси. Не се ползват оптгрупи, затова компанията може да бъде избрана. | method := getCompaniesOffices |
| 24 | getCompaniesData | Списък на опции за Каса/Банкова сметка в оптгрупи по фирма | method := getCompaniesDatapayment_direction := (тип – incomes или expenses – задължителна настройка, според нея се проверяват правата до показваните опции за каси и банкови сметки)company_id := (незадължителен параметър – id-та на фирми, разделени със запетая)office_id := (незадължителен параметър – id-та на офиси, разделени със запетая)payment_type := (незадължителен параметър - cash или bank, за да се показват опции само за каси или само за банкови сметки)cashboxes := (незадължителен параметър – id-та на каси, разделени със запетая)bank_accounts := (незадължителен параметър – id-та на банкови сметки, разделени със запетая)active := (незадължителен параметър, ненулева стойност задава да се вземат само активни записи)associative – (незадължителен параметър, ненулева стойност задава опциите да имат option_value за ключ в масива) |
### Методи в договори (contracts.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getSinglePeriodLengths | Списък на опции периодичност (ден, седмица, месец, тримесечие, година) | method := getSinglePeriodLengths |
| 2 | getSubtypes | Списък на под-типовете договори (текущ, първоначален, допълнително споразумение) | method := getSubtypes |
### Методи в събития (events.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getStatuses | Списък на статусите на събития (планирано, в изпълнение, приключено и т.н.) | method := getStatuses |
| 2 | getPriorities | Списък на приоритетите (нисък, нормален, висок) | method := getPriorities |
| 3 | getAllDay | Списък на опции за продължителност (целодневно, нецелодневно, без фиксирани начало и край) | method := getAllDay |
| 4 | getParticipantUsers | Списък на участниците в събитие | method := getParticipantUsers |
### Методи в инфо панели (dashlets.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getDashletsModules | Списък на модулите за инфо панели | method := getDashletsModules |
### Методи в съобщения (announcements.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getPriorities | Списък на приоритети на съобщенията | method := getPriorities |
| 2 | getReadOptions | Списък на опции за посетеност (прочетено, непрочетено) | method := getReadOptions |
| 3 | getArchiveOptions | Списък на опции за архив (архивирано, неархивирано съобщение) | method := getArchiveOptions |
### Методи в номенклатури (nomenclatures.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getCDReasons | Списък на основания за издаване на кредитни/дебитни известия | method := getCDReasons |
### Методи в бележки (notes.dropdown.php)
| № | Метод | Описание | Параметри |
| 1 | getCstmOptions | Списък на контрагенти | method := getCstmOptions |
### Методи в изгледи (outlooks.dropdown.php)
| № | Метод | Описание | Параметри |
|  | getModules | Списък на всички модули, в които се използват изгледи | method := getModules |
|  | getModelsTypes | Списък на всички типове по модули, в които се използват изгледи | method := getModelsTypes |
|  | getSections | Списък на всички секции по модули, в които се използват изгледи | method := getSections |
|  | getRolesAndUsers | Списък на всички потребители и роли за назначение към изгледи | method := getRolesAndUsers |
### Общ метод за опции с директен SQL (dropdown.class.php)
| № | Метод | Описание | Параметри |
|  | getCustomDropdown | Списък на опции взети директно от базата данни | method := getCustomDropdownvalue := id (НЕзадължителен параметър – указва кое поле да се вземе като стойност на опцията – тази която се записва в БД). Ако параметърът се премълчи, по подразбиране се взима idlabel := name,lastname(НЕзадължителен параметър – указва кои полета да се вземат за показването на етикета на опцията, ако полетата са повече от едно се разделят със запетая). Ако параметърът се премълчи по подразбиране се взима name.table := DB_TABLE_CUSTOMERS (*константа за основната таблица)table_i18n := DB_TABLE_CUSTOMERS_I18N (*константа за езиковата таблица)where := type = &#039;1&#039; AND id = &#039;10765&#039; (**клауза за допълнително филтриране)order_by := active DESC, position ASC (НЕзадължителен параметър – указва по кои полета да се сортират опциите (виж: Bug 3424))label := name (НЕзадължителен параметър указващ в кое поле се намира етикета на опцията в дропдауна, по подразбиране има стойност name. Възмножно е да се подават повече от едно полета изредени със запетая, които се представят в етикета като сляти с интервал стойности. Например: label := code, name ще се замести с &quot;0989 Иван Иванов&quot;)id_param := model_id - (НЕзадължителен параметър указващ в кое поле се намира идентификатора, по подразбиране има стойност id)Важно: *Всички имена на таблици са дефинирани като константи. Тези константи са описани във .Важно: ** За допълнителните филтри потърсете помощ от програмистите.Важно: В getCustomDropdown параметрите могат да участват като променливи, отбелязани с $ отпред. Например:method := getCustomDropdowntable := DB_TABLE_NOMENCLATUREStable_i18n := DB_TABLE_NOMENCLATURES_I18Nrequested_nom_type := request(&#039;nom_type&#039;)where := type = $requested_nom_type |

### Важни бележки за чекбокс групи

**ВАЖНО!** Опциите на чекбокс групите могат да се сортират, използвайки параметъра reorder. Ето и примери:

```
reorder := reorderOptionsCheckedFirst  # всички отметнати опции минават най-отгоре
reorder := reorderOptionsAlphabetical  # сортира опциите по азбучен ред
```

Настройката работи при зареждане след запис и не работи при отмятане на чекбокс. reorder работи само за чекбоксове! Не работи за дропдауни или радио бутони!

### Извикване на методи за дропдаун от други модули

**ВАЖНО!** Разработена е възможност за извикване на методи за дропдаун от други модули.

Какво се има предвид: Ако се намирате в модул Документи, то няма начин директно да вземете данни от дропдаун метод, който се намира във модул Финанси (finance.dropdown.php). Може тази функционалност да бъде постигната донякъде с getCustomDropdown, но това си е излишна работа, а и не винаги може да се постигне същият ефект – примерно проверки на права до каси, БС, др.

Вече може да достигате до методи от другите модули посредством изписване на дропдаун класа, който ви е необходим, след него :: и след това името на метода.

Пример:

```
method := Finance_Dropdown::getCompaniesData
```

Подаването на параметри става по същия начин, както при нормално извикване на метод от дропдаун класа на текущия модул.

### Шаблони за групови променливи

За да могат да се записват шаблони (запазени конфигурации) за групова променлива, в полето source на обединяващата променлива (от тип group/gt2) записваме:

```
configurator_group := 1
```

Над груповата таблица се появява панел за зареждане, добавяне, записване и изтриване на шаблони за конфигурации. Запазените конфигурации могат да се зареждат във всеки документ от съответния тип.

### Скриване на номерата на редовете

Добавена е възможност за скриване на колоната с номерата на редовете при груповите променливи (таблици) ПРИ ПЕЧАТ. Това става като в полето source на груповата променлива (от тип group) запишем:

```
hide_row_numbers := 1
```

### Скриване на бутоните за добавяне и премахване на редове

Добавена е възможност за скриване на бутоните за добавяне и премахване на редове при груповите променливи (таблици). Това става като в полето source на груповата променлива (от тип group) запишем:

```
hide_multiple_rows_buttons := 1
```

### Бутони за autocompleter-и

Във връзка с новите autocompleter-и са променени опциите за показване на бутони за refresh и filter.

#### Бутони за филтриране

Бутони за филтриране се показват, ако в source полето на променливата запишем:

```
show_select_buttons := nomenclatures        # за номенклатури
show_select_buttons := projects             # за проекти
show_select_buttons := customers            # за контрагенти
show_select_buttons := nomenclatures, projects  # за номенклатури и проекти едновременно
```

#### Бутони за обновяване

Бутони за обновяване се показват, ако в source полето на променливата запишем:

```
show_refresh_buttons := nomenclatures       # за номенклатури
show_refresh_buttons := projects            # за проекти
show_refresh_buttons := customers           # за контрагенти
show_refresh_buttons := nomenclatures, projects  # за номенклатури и проекти едновременно
```

#### Възможни стойности

Възможни стойности: `nomenclatures`, `customers`, `documents`, `projects`, `tasks`, `contracts`, `users`, `departments`.

#### Настройка на търсенето

Когато се използват тези бутони, за да направят търсенето, те винаги вземат настройките на АК, който се намират в най-предната колона и попълват данните избрани в pop-up-а пак там. Като, ако има свързани полета, те ще се попълнят за всеки ред според настройките на този първи АК. Това е поведението по подразбиране.

Това обаче може да се промени ако се използва настройката:

```
select_buttons_search_var := article_deliverer_name
```

Тази настройка указва, в коя променлива ще се попълва с информацията за записите, избрани от филтъра. Съответно от тази променлива ще се използват и настройките. Задължително е полето записано тук да е АК, в противен случай бутона за филтър няма да сработи.

Пример: `select_buttons_search_var := article_deliverer_name` – ще попълва записите в полето article_deliverer_name и ще взема настройките за филтъра от там.

### Плаващи бутони

Освен бутоните горе вдясно, могат да се показват и нестатични "плаващи" бутони, които се показват вдясно от ред на таблицата при позициониране на курсора върху него. Те съответстват като вид и като поведение на основните бутони.

За показване на подвижни бутони, в source полето на променливата трябва да се зададе:

```
floating_buttons := 1
```

### Импортиране на данни от Excel

Импортиране (зареждане) на данни от Excel файл в групова таблица. За показване на бутона за импорт се задава настройка:

```
show_import_button := 1
```

#### Допълнителни незадължителни настройки

Залагат се в същата групираща променлива, като основната настройка.

```
row_num_var := <име-на-променлива-от-същата-таблица>
import_configurator_group := 1
import_plugin := selamore_products
```

##### Задаване на стойности по подразбиране

```
import_default_value_<име-на-променлива> := <стойност>
```

Например:

```
import_default_value_article_measure_name := 1
import_default_value_glazing_notes := one\ntwo\nthree
```

##### Задаване на фиксирани стойности

```
import_fixed_value_<име-на-променлива> := <стойност>
```

Например:

```
import_fixed_value_instalation_yes_no := 1
```

##### Забележки:

Горните две настройки задават първоначално състояние при зареждане на формата за импорт, но то може да бъде променено от потребителя.

Ако са налични и двете настройки за една и съща променлива, то тази за твърда стойност е с по-висок приоритет.

Стойността трябва да е такава, каквато се записва в съответната променлива в базата от данни (с особености за аутокъмплийтър): за радио бутони и падащи списъци – стойността от option_value, за аутокъмплийтър – name за съответния запис (тъй като се търси по същия начин, както с данните от файла за импорт), за дати и часове – стойност в ISO формат, за многоредов текст на textarea новите редове следва да са означени със символи „\n“, написани като текст.

### Многоколонен интерфейс

За да се постигне многоколонен интерфейс в таблици и конфигуратори, в полето source на обединяващата променлива (от тип table или config) записваме:

```
borderless := 1
```

В този случай рамките на таблицата/конфигуратора се заличават и антетката ѝ вече няма фон.

### Дефиниране на полета за принтиране/генериране като PDF

Добавена е възможност да се дефинират полетата за групова/едноредова таблица, които ще се принтират или генерират като PDF. За целта в полето source на съответната групираща променлива се записват параметри за принтиране/генериране.

Например:

```
print_columns := var1,var3,var5
```

където var1, var3 и var5 са имената на променливите към груповата променлива.

#### Настройки за колони според шаблона

Настройките за колони за принтираните/генерираните променливи могат да зависят и от шаблона. За да се дефинират полетата за съответните действия за точно определен шаблон, се използва следният синтаксис:

```
print_columns_pattern12 := var1,var3,var5
```

където суфиксът '_pattern12' указва, че се отнася за шаблон, а номерът накрая е ID-то на този шаблон от базата данни, т.е. полетата var1, var3 и var5 ще са видими при отпечатването/генерирането на документ по шаблон с id=12.

#### Изключване на колони

Съществува и подобна настройка за изключване на колони:

##### За всички шаблони за печат

```
print_exclude_columns := var1,var3,var5
```

##### За конкретни шаблони за печат:

```
print_exclude_columns_pattern12 := var1,var3,var5
```

##### Важни забележки

**ВАЖНО:** Настройките за включване и изключване на колони могат се ползват поотделно (т.е. или само за включване на колони или само за изключване), а също така и комбинирано (едновременно и двете). При комбинирано използване изключените колони **НЕ СЕ ПОКАЗВАТ**.

**ВАЖНО:** Ако не се добавят настройки за колоните, които да бъдат разпечатвани (print_columns), се отпечатват **ВСИЧКИ** колони.

### JavaScript методи

Добавена е възможност в source полето на променливите да може да се дефинират JavaScript методи, които трябва да се изпълняват при определени събития (DOM events).

Възможните събития са:
- За събития, задействани от клавиатурата: `onkeypress`, `onchange`, `onkeydown`, `onkeyup`, `onblur`, `onfocus`
- За събития, задействани от мишката: `onmouseover`, `onmouseout`, `onmousedown`, `onmouseup`, `onclick`

Синтаксисът е:

```
js_method := onkeypress => име_на_функция(параметър1, параметър2,...)
```

Чрез тези функции може и да се филтрира. Имаме много JS функции, които могат да бъдат ползвани и няма възможност да бъдат описани тук.

Нека хората, които правят настройки на допълнителни променливи, да се допитват до програмистите, ако имат нужда от някаква функция.

#### Описание на събитията:

- `onkeypress` - натиснат е и е отпуснат клавиш от клавиатурата
- `onkeydown` - натиснат е клавиш от клавиатурата
- `onkeyup` - отпуснат е клавиш от клавиатурата
- `onblur` - полето(контролата) е загубило фокуса си
- `onfocus` - полето(контролата) е на фокус
- `onchange` - съдържанието в полето(контролата) се е променило
- `onmouseover` - курсорът на мишката е върху полето
- `onmouseout` - курсорът на мишката е напуснал очертанията на полето
- `onmousedown` - натиснат е бутон на мишката
- `onmouseup` - отпуснат е бутон на мишката
- `onclick` - щракнат е бутон на мишката

#### Пример 1:

```
js_method := onkeyup => this.value = this.value.toUpperCase();
```

При задаване на тази настройка за дадено поле, въведените в него букви стават главни още в момента на въвеждане.

#### Специални събития за полета от тип date и datetime

За полетата от тип date и datetime има две специфични събития, които са строго специализирани за тези полета. Те са както следва:

- `disallow_date_before` – забранява се избирането от календара на дати преди определена дата
- `disallow_date_after` – забранява се избирането от календара на дати след определена дата

Задаването на ограничителна дата става по следните правила:
- Може да се задава конкретна дата в ISO формат: YYYY-MM-DD, без кавички (виж примерите по-долу)
- Може да се задава име на променлива, като то трябва да е предшествано от $
- Може да се задава 1 или now за текущата дата
- Ограниченията могат да се задават едновременно за before и after
- Зададената дата за disallow_date_before важи за всички дати преди указаната дата, БЕЗ ДА Я ВКЛЮЧВА
- Зададената дата за disallow_date_after важи за всички дати след указаната дата, БЕЗ ДА Я ВКЛЮЧВА

#### Примери за ограничаване на дати:

**Пример 1:** Всички дати преди 01.03.2013 няма да могат да бъдат избирани (01.03.2013 ще може да бъде избрана). Обърнете внимание: не се слагат кавички около датата.
```
js_method := disallow_date_before => 2013-03-01
```

**Пример 2:** Всички дати преди 01.06.2013 и след 01.09.2013 няма да могат да бъдат избирани (01.03.2013 и 01.09.2013 ще могат да бъдат избрани). Обърнете внимание: не се слагат кавички около датите.
```
js_method := disallow_date_before => 2013-06-01
js_method := disallow_date_after => 2013-09-01
```

**Пример 3:** Всички дати след днешната няма да могат да бъдат избирани (днешната ще може да бъде избрана)
```
js_method := disallow_date_after => now
```

**Пример 4:** Всички дати преди днешната няма да могат да бъдат избирани (днешната ще може да бъде избрана)
```
js_method := disallow_date_before => 1
```

**Пример 5:** Всички дати след съдържащата се (в момента във формата, а не записаната в базата) в променливата example_date_field няма да могат да бъдат избирани. Ако променливата не съдържа стойност, няма да има ограничение за избор на дати.
```
js_method := disallow_date_after => $example_date_field
```

В групови таблици, едноредови таблици, конфигуратори, ББ и групови таблици от ново поколение може да се добавя custom javascript код. Достатъчно е в полето от тип table, config, group, gt2 или bb да се добави настройка:

javascript := &lt;нужният javascript код&gt;Например:javascript := alert(‘Hello world!’)

За всички видове променливи в source може да се настройва параметър custom_class, който се добавя като CSS клас за съответното поле или полета (за многоредови променливи). Стойностите се изреждат, разделени с интервал. Прилага се само в режим &quot;редакция&quot;, но не и в &quot;разглеждане&quot;, на допълнителните данни.

Може да се използва за следните цели:

1. При залагане на javascript в дадена групираща променлива, полетата, които той използва, могат да се вземат чрез CSS селектори по име на клас, вместо чрез $$(&#039;input[id^=the_variable_name]&#039;);

2. За разкрасяване на интерфейса и подобряване на ползваемостта според нуждите - могат да се прилагат съществуващите CSS класове.

Ето някои примери за практическо приложение на второто (&quot;хакове&quot;):

# Ако искаме свободна променлива (text, autocompleter, dropdown, date, datetime, time) да не е широка стандартните 200px:

# 400px

custom_class := doubled

# 80px

custom_class := short

# 64px

custom_class := small

# 40px

custom_class := num

# Ако искаме свободна променлива textarea да е по-широка и по-висока:

# 400px широчина, 200px височина

custom_class := doubled higher

# Ако искаме удебелен шрифт на текста:

custom_class := strong

# Ако искаме дадено readonly поле да не е жълто, а да се слива със сивия фон:

custom_class := viewmode

Подравняване в полета от тип text, dropdown и formula може да се заложи, като в source се добави настройка text_align (възможни стойности: left, center, right):

text_align := &lt;стойност&gt;

Пример:

text_align := right

Настройката важи и за интерфейса, и за печат (с изключение на променливи в gt2, когато има създадени отделни настройки за печат).

В режим на редакция настройката задава подравняването на съдържанието в съответното поле. В режим на разглеждане и при печат настройката задава подравняването на текста в съответна клетка на съставна променлива (таблица, групова таблица, конфигуратор).

toggle := collapse – скрива (или по-точно свива) всички подтаблициtoggle := first – показва първата подтаблица, а останалите са свити

view_mode := default

view_mode := link

ВАЖНО !!!   Ако параметърът view_mode бъде пропуснат, то по подразбиране се използва стойност default.

view_mode_url – тази опция e задължителна. Например:

view_mode_url := index.php?launch=nomenclatures&amp;nomenclatures=edit&amp;edit=

Този пример позволява задаване на линк към редакция на номенклатура, чието ID се пази в стойността на dropdown или radio полето.

	1.6 validate – в полето се залагат фунция за вторична валидация (server-side) на променливата и аргументи, както и филтри за валидация в клиентския браузър (client-side).

Например:

method := function

$arg1 := argument1

$arg2 := argument2 …

Могат да се ползват запазени променливи ($lt, $gt …) и запазени стойности (added, modified, now …)

В методa compareVar могат да се ползват за сравнение стойности от други полета в аргументите. Например, за да се валидира, че стойността на текущата променлива е по-голяма от тази в поле с име field_txt се записва $gt := $field_txt (поставя се знак $ пред името на полето, за да е ясно, че стойността ще се определя динамично).

Методи:Важно: всички методи за валидация изпълняват само за попълнени стойности в полето. Важно: всички методи за валидация са предвидени да работят и за групови таблици (масиви). Ако само един ред (елемент) в груповата таблица (масива) не е валиден, то цялата таблица (масив) се смята за невалидна.

validEmail	валидация на e-mail адрес.

isFakeEmail	проверява дали e-mail адрес е „фалшив“ (започва с fake_ или с цифри: fake01_)

validUrl	валидация на уеб адрес

validDateTime	валидация на дата и час

validDate	валидация на дата

validTime	валидация на час като част от дата (00:00 – 23:59) (може да се използва за изчисляемо поле, за да не се допуска отрицателна стойност)

notEmpty 	валидация за попълнена стойност (изпълнява се служебно ако полето е задължително)

notEmptyFile	валидация за попълнена стойност на поле от тип file_upload (изпълнява се служебно, ако полето е задължително)

compareVar	валидация чрез сравнение (равно, различно, по-голямо, по-малко) спрямо зададена стойност или спрямо стойността на друга променлива

isValidNumber	валидация за число

validPassword	валидация за парола (дължина, специални символи и др.)

validUserName	валидация за потребителско име (позволени символи, дължина и др.)

validUCN	валидация за ЕГН (единен граждански номер)

validPFN	валидация за ЛНЧ (личен номер на чужденец)

validEIK	валидация за ЕИК (единен идентификационен код)

validInDDS	валидация на ИН по ДДС (идентификационен номер по закона за данък добавена стойност - валидацията се извършва чрез обръщение към външен източник)

regexpCompare	валидира стойността спрямо регулярен израз

validateConditionalRequired	валидация за попълнена стойност, когато друго поле отговаря на зададено условие

Запазени променливи (аргументи) за метод compareVar:

$lt		по-малко (less than)

$le		по-малко или равно (less than or equal)

$gt		по-голямо (greater than)

$ge		по-голямо или равно (greater than or equal)

$eq		равно (equal)

$ne		различно от (not equal)

Аргументи $lt и $gt могат да се ползват и за метод isValidNumber.

Запазени стойности за метод compareVar:

now		сравнява се с текуща дата и час в ISO формат (YYYY-MM-DD HH:ii:ss)

added		сравнява се със стойността на поле &quot;Добавен на&quot; за текущия запис

modified	сравнява се със стойността на поле &quot;Променен на&quot; за текущия запис

Примери:

Валидация на поле за валиден и-мейл адрес:

method := validEmail

Валидация за попълнена числена стойност:

method := isValidNumber

Валидация за попълнена числена стойност, която да е по-голяма от твърдо зададена стойност:

method := isValidNumber

$gt := 5

Валидация попълнената стойност да е по-малка от стойността на друго поле и по-голяма от текущата дата и час:

method := compareVar

$lt := $another_field_name

$gt := now

Валидация спрямо регулярен израз – поне 3 съседни символа на кирилица/латиница:

method := regexpCompare

regexp := /[a-zа-я]{3,}/iu

Желателно е за променливата да се настрои help текст, който пояснява какви стойности се очакват.

ВАЖНО: За настройване на условие чрез регулярен израз се обръщайте към разработчик или към .

Валидация за попълнена стойност, когато друго поле отговаря на зададено условие:

Приема 2 параметъра: condition_field за име на полето и condition_value за стойности, спрямо които се сравнява.

Проверяваното поле може да е основна или допълнителна променлива, задава се по един и същ начин (без префикс (a_/b_/$) и без суфикс за номер на ред), например: customer, deadline, article_deliverer, any_other_name.

Стойността на проверяваното поле се взема САМО от формата. Ако то е допълнителна променлива в секция, достъпна само за четене, стойността му няма да може да се вземе за проверка и ще се счита за празна. При проверка се игнорират непечатаемите символи в началото и края на проверяваното поле, т.е. ако то съдържа само интервали, ще се счита за празно.

Има няколко варианта на ползване на метода:

а) текущото поле е задължително, когато друго поле има попълнена каква да е стойност:

method := validateConditionalRequired

condition_field := other_field_name

ИЛИ

method := validateConditionalRequired

condition_field := other_field_name

condition_value := any

(параметър condition_value липсва или има служебна стойност any)

(ако проверяваното поле се намира в ГТ, то се счита за попълнено при попълнен поне един ред)

б) текущото поле е задължително, когато друго поле няма попълнена стойност:

method := validateConditionalRequired

condition_field := other_field_name

condition_value :=

ИЛИ

method := validateConditionalRequired

condition_field := other_field_name

condition_value := none

(параметър condition_value има празна стойност или има служебна стойност none)

(ако проверяваното поле се намира в ГТ, то се счита за празно, когато са празни всички редове)

в) текущото поле е задължително, ако друго поле има някоя от изброените стойности:

method := validateConditionalRequired

condition_field := other_field_name

condition_value := 212,213

(текущото поле (което може да е единично поле или колона от таблица) е задължително, когато поне в един от редовете на проверяваното поле (което също може да е единично поле или колона от групова таблица) се съдържа някоя от изброените стойности)

Може да се настрои условие някоя от стойностите ИЛИ празна стойност, като се постави запетая в края на изброяваните стойности:

condition_value := 212,213,

г ) ако и текущото, и другото поле се намират в групова таблица, може да се сравнява поредово (всеки ред е зависим от стойността на другото поле САМО на същия ред – приложимо е и за трите варианта на работа, посочени по-горе).

method := validateConditionalRequired

condition_field := other_field_name

condition_value := 212,213

per_row := 1

(параметър per_row указва да се проверява само стойността на другото поле на същия ред, а не от всички редове)

Във validate полето на променливите могат да се дефинират JavaScript филтри за валидация, която се извършва в клиентския браузър. Синтаксисът е:

js_filter := име_на_филтъраалтернативно, когато има параметри се слагат и единични кавички:js_filter := &#039;име_на_филтъра|параметър:&lt;стойност параметър&gt;&#039;

Тези филтри се изпълняват при събитието onkeypress. Ако е зададен филтър и едновременно с това за onkeypress е подадена функция, то тя няма да се изпълни, а ще се изпълни само филтърът.

Филтри:

lettersToUpperCase - замества въведените малки букви със съответните им главни

digitsToX - замества всяка въведена цифра с X

umlautsToASCII - прави следното заместване на символи:

	&quot;a&quot; става: &quot;ae&quot;

	&quot;o&quot; става: &quot;oe&quot;

	&quot;u&quot; става: &quot;ue&quot;

	&quot;A&quot; става: &quot;Ae&quot;

	&quot;O&quot; става: &quot;Oe&quot;

	&quot;U&quot; става: &quot;Ue&quot;

cancelDigits - не разрешава въвеждането на цифри

insertOnlyDigits - разрешава въвеждането само на цифри

insertOnlyFloats - разрешава въвеждането само на цифри и еднократно въвеждане на десетична точка

insertOnlyNegativeFloats - разрешава въвеждането само на цифри, еднократно въвеждане на десетична точка и изисква задължително въвеждане на минус в началото

insertOnlyReals - разрешава въвеждането само на цифри, еднократно въвеждане на десетична точка и въвеждане на минус в началото

За горните три (insertOnlyFloats, insertOnlyNegativeFloats, insertOnlyReals) възможно е подаване на допълнителен параметър за брой на цифрите след десетичния знак: js_filter := &#039;insertOnlyFloats|max_decimals:2&#039;Важно е да има единични кавички

insertOnlyPositiveIntegers - разрешава въвеждането само на цели положителни числа (без 0)

cancelEnter - не разрешава натискането на ENTER

Следват филтри, които биха работили коректно, само ако се зададат при действие onkeydown, което може да стане в поле source по следния начин:

js_method := onkeydown =&gt; return changeKey(this, event, име_на_филтъра)

Филтри:

allowOnlyClear - при натискане на BACKSPACE или DELETE премахва стойността на полето, при натиснат TAB извършва действието на клавиша, а при натиснат който и да е друг клавиш – блокира действието му

Следват филтри, които биха работили коректно, само ако се зададат при действие onkeyup, което може да стане в поле source по следния начин:

js_method := onkeyup =&gt; return changeKey(this, event, име_на_филтъра)

Филтри:

filterDate - разрешава въвеждането само на символи за датa (012345678.,:), като се комбинира с функция isAllowedDateKey, която се задава на onkeydown, за да се изпълни преди нея

Едновременно използване на филтър и метод:

Ако има зададен филтър и методът е за събитие onkeydown (или onkeypress при поле от тип formula (виж по-нагоре)), функцията на този метод няма да се изпълни.

1.7 type – тип на променливата (&#039;text&#039;, &#039;textarea&#039;, &#039;dropdown&#039;, &#039;radio&#039;, &#039;checkbox_group&#039;, &#039;date&#039;, &#039;datetime&#039;, &#039;group&#039;, &#039;gt2&#039;, &#039;config&#039;, &#039;button&#039;, &#039;table&#039;, &#039;bb&#039;, &#039;autocompleter&#039;, &#039;time&#039;, &#039;file_upload&#039;, &#039;formula&#039;, &#039;map&#039;)

1.7.1 тип text – обикновено едноредово текстово поле за попълване

1.7.2 тип textarea – многоредово текстово поле за попълване

За типове text и textarea може да се настрои етикетът (label) или пояснителният текст (help) на променливата да се показва като помощен текст в полето, когато то няма стойност, като в source се запише:

show_placeholder := label

show_placeholder := help

1.7.3 тип dropdown – падащо меню, даващо възможност за еднозначен избор. Ако полето е задължително (required = 1, виж 1.8), то няма подканващ текст [моля изберете]. Ако полето е без никакви добавени избори (опции), в него се изписва [няма данни].

1.7.4 тип radio – тип контрола, даваща възможност за алтернативен избор – избира се винаги само една стойност

1.7.5 тип checkbox_group – тип контрола даваща възможност за нито един, един или повече избора чрез отметки

По подразбиране опциите в полета от тип radio и checkbox_group се показват една под друга. За да се показват на един ред, в source се добавя настройка за хоризонтално подреждане:

options_align := horizontal

1.7.6 тип date – тип контрола, даваща възможност за избор на дата (без час). До контролата има малка иконка &quot;календарче&quot;, която подпомага избора. Ако полето е readonly = 1, контролата не дава възможност за &quot;ръчно&quot; сменяне на датата. В такъв случай датата се изчиства като се щракне два пъти (double click) в това поле.

1.7.7 тип datetime – тип контрола, даваща възможност за избор на дата И ЧАС. До контролата има малка иконка &quot;календарче&quot;, която подпомага избора. Ако полето е readonly = 1, контролата не дава възможност за &quot;ръчно&quot; сменяне на датата. В такъв случай датата се изчиства като се щракне два пъти (double click) в това поле.

1.7.8 тип group – не е контрола и служи за променливи, които групират променливи от една и съща група (таблица). Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите. Тази променлива служи и за пренасяне на цялата групова информация в генерирания PDF анблок.

За да могат да се записват шаблони (запазени конфигурации) за групова променлива, в полето source на обединяващата променлива (от тип group) записваме configurator_group := 1Над груповата таблица се появява панел за зареждане, добавяне, записване и изтриване на шаблони за конфигурации. Запазените конфигурации могат да се зареждат във всеки документ от съответния тип.

Добавена е възможност за скриване на бутоните за добавяне и премахване на редове при груповите променливи (таблици). Това става като в полето source на груповата променлива (от тип group) запишем hide_multiple_rows_buttons := 1.

По подразбиране при добавяне на нов ред стойностите на променливите в по-горния ред се прехвърлят и на новия. Ако обаче искаме полетата в новодобавения ред да са празни (без стойности), може да добавим в source полето на груповата таблица ред dont_copy_values := 1. По този начин новият ред ще бъде празен, с непопълнени полета.Възможно е да се зададе да не се прехвърлят данните само за някои полета. Това става като за стойност на настройката се запишат имената им, изброени със запетаи, например: dont_copy_values := participant_id, participant_name. Вместо &quot;1&quot; може да се зададе и стойност &quot;all&quot;.

1.7.9 тип gt2 – не е контрола и служи за променливи, които групират променливи от една и съща група (таблица) от 2-ри вид (ново поколение). Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите.

За всеки тип модел има индивидуални настройки за работа и печат, които са достъпни през интерфейса.

За да могат да се записват шаблони (запазени конфигурации) за групова променлива, в полето source на обединяващата променлива (от тип gt2) записваме:

configurator_group := 1Над груповата таблица от 2-ри вид се появява панел за зареждане, добавяне, записване и изтриване на шаблони за конфигурации. Запазените конфигурации могат да се зареждат във всеки документ от съответния тип.

Добавена е възможност за скриване на бутоните за добавяне и премахване на редове при груповите променливи (таблици). Това става като в полето source на груповата променлива (от тип gt2) запишем:

hide_multiple_rows_buttons := 1

С настройка auto_translate_vars могат да се укажат полета, които са езиково зависими, които настройващият инсталацията иска да манипулира като езиково независими. Тоест в тези полетата стойностите на основния език ще бъдат същите като стойностите на алтернативните езици, независимо, че полето е езиково зависимо.

Имената на полетата тук се изреждат със запетаи. Пример:

auto_translate_vars := free_text1,article_alternative_deliverer_name,article_description

По подразбиране при добавяне на нов ред той се добавя с празни стойности. Ако обаче искаме стойностите на променливите в по-горния ред се прехвърлят и на новия, може да добавим в source полето на групираща променлива (group_table_2) ред:

copy_values := 1Възможно е да се зададе да се прехвърлят данните само за някои полета. Това става като за стойност на настройката се запишат имената им, изброени със запетаи, например:

copy_values := article_id, article_name, article_measure_name, article_description

Вместо &quot;1&quot; може да се зададе и стойност &quot;all&quot;.

При копиране на данни за аутокъмплийтър е препоръчително да се копират или да не се  копират всички полета, попълвани от него (или поне тези, които са скрити или само за четене и не могат да бъдат обновени ръчно).

Настройките следва да бъда само имената на променливите. Не са необходими настройки за допълнителните полета, които са налични за някои типове променливи или в режим „само за четене“.

Може да се настрои копиране и в source на всяка отделна променлива, като се зададе

custom_class := copy_values

, тъй като горната настройка се преобразува до същото, но за по-добра прегледност е препоръчително настройването да се прави в групиращата променлива.

Съществуват следните ограничения:

- в складови документи, ако в последния ред за article_name е избран партиден артикул, при добавяне на следващ ред, той винаги се добавя празен, независимо от настройките за копиране.

- за складови документи от тип Ревизия и Междускладов трансфер на стоки има ограничение всеки артикул да присъства само по веднъж в таблицата, затова при добавяне на нов ред, всички полета, попълвани от аутокъмплийтъра article_name, се добавят с празни стойности, независимо от настройките за копиране.

ВАЖНО: Настройката следва да се използва с повишено внимание и само когато е наистина необходима. При определени комбинации от копирани/некопирани полета могат да бъдат записани редове с данни, в които стойностите в полетата не са съгласувани едни с други.

Има възможност за обединяване на редове само

при изрично зададени настройки за това. Например:

merge_rows_by_unique_columns := article_id, price, article_measure_name –

merge_rows_sum_columns := quantity

merge_rows_concat_columns := article_description

Таблицата ще намери всички редове, за които тези колони са еднакви и към първия

намерен ред от уникалния формиран ключ (артикул+цена+мерна единица) ще сумира

количествата и ще конкатенира описанията.

Редовете, които се явяват с повтарящ се ключ ще бъдат изтрити. Стандартните за

ГТ2 калкулации се изпълняват след обединяването на редовете.

Добавена е възможност да се сортира груповата таблица по зададени колони. Сортирането е само след запис. Не е предвидено да може да се сортира съдържанието на груповата таблица от потребителите. Синтаксис:sort_by := &lt;var1&gt; [ASC|DESC] [algorithm], &lt;var2&gt; [ASC|DESC] [algorithm]Разяснения и ограничения:- Променливите, които се задават задължително трябва да бъдат колони (имена на променливи) от груповата таблица.

- Посоката на сортиране се определя винаги само от тези две ключови думи: ASC – възходящ ред (А-Z), DESC – низходящ ред (Z-A)- Има възможност за определяне на начина на сравнение между низовете. Алгоритъмът трябва задължително да е валидна PHP функция допускаща два аргумента и връщаща стойност: -1, 0 или 1. Такива функции за сравнение са: strcmp, strcasecmp, strnatcmp. По подразбиране се използва strnatcmp, защото сравнява низове представляващи ЦЕЛИ числа и низове. Ако е нужно да се сортира по колона с дробни числа е по-добре да се ползва strcmp.- Сортирането по колони с дропдауни/радио бутони се осъществява по етикета на избраната опция на езика на интерфейса, а не по стойността!- За сортиране по многоезични променливи се прави на езика на моделаПримери:sort_by := skills_app ASC, skills_ass DESCсортира възходящо по колона skills_app и низходящо по skills_asssort_by := exam_results ASC strcmp, kind_training ASCсортира възходящо по колона exam_results използвайки функция за сравняване strcmp и възходящо по kind_training

allow_negative_price := 1

ВАЖНО: Това НЕ Е ПРЕПОРЪЧИТЕЛНО да се прави за финансови/складови документи или документи, от които ще се трансформира към финансови такива!

1.7.10 тип config – не е контрола и аналогично на променлива от тип group служи за променливи, които групират променливи от една и съща група (таблица). Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите. Тази променлива служи и за пренасяне на цялата групова информация в генерирания PDF анблок.

1.7.11 тип button – тип контрола, даваща възможност за задействане на някаква функционалност (изчисление, пренасочване, промяна в текущата форма и др.).

За бутоните могат да се задават position и layout_id, като бутонът ще се появи в съответния layout (секция), ако му е зададена такава. Ако се зададе 0 за layout_id, бутонът се показва най-отдолу на формата преди стандартните бутони за запис и отказ.

Тук са описани някои от основните настройки, които могат да бъдат попълнени в source полето на променливата.

Настройката задава последователността на изпълнение на изчисления на полетата във формата. Синтаксисът ѝ е следният:

sequences := [имената на полетата, които се изчисляват, разделени със запетая]

Пример:

sequences := doc_total1, doc_text1

За бутоните със зададен sequences не е задължително да се попълва етикет в таблица _fields_i18n. Aко е попълнен етикет, той се вижда върху бутона, а ако не е – надписът ще е &quot;Изчисляване&quot;.

Бутоните за изчисления не се показват в режим на разглеждане, а само в режим на добавяне/редакция. По подразбиране при натискане на бутона за Запис бутоните за калкулация се активират и се изчисляват.

Ако в настройката hidden на бутона се сложи 1, скритият бутон няма да се показва, но ще присъства във формата и ще предизвиква изчисляване при избор на бутона за запис.

Тук е важно да се спомене, че е възможно изчисленията да не се пускат автоматично, ако в source полето се попълни настройката do_not_execute_on_save. Тогава, за да се изчислят полетата, изрично ще се изисква да се натисне бутона за изчисление.

Пример:

sequences := doc_total1, doc_text1

do_not_execute_on_save := 1

ВАЖНО: Освен бутони, калкулации могат да запускат и дропдауни, като не е задължително да се слага стойност на calculate в дропдауна (може да е 0).

Има възможност за изпълнение на JavaScript код при натискане на бутон. Това може да се постигне като в source полето на променлива от тип бутон се попълни настройка onclick. Срещу нея може да се запише всякакъв javascript (стига да е на един ред), който ще се изпълни при натискане на бутона.onclick := &lt;код, който да се изпълни при натискане&gt;

Пример:

onclick := if confirm(&#039;Сигурни ли сте. че искате да запишете данните&#039;) this.form.submit();

Настройка href дава възможност за директно влизане от текущия запис (документ/контрагент/проект/договор/номенклатура) в посочен в настройките линк. Етикетът на самия бутон си идва от _fields_i18n. Бутоните от тип линк по подразбиране се виждат и в режим на разглеждане, и в режим на редакция. Ако са в режим на редакция и трябва да се вземат данни от текущата форма (виж описанието по-долу), данните се вземат динамично от текущо въведените във формата. Най-честата му употреба е да пренасочва потребителя към модул &quot;Справки&quot;. Форматът е следният:

href := [url]

Линкът се задава с начало винаги след &#039;launch=&#039; частта от линка в системата на nZoom. За бутон, който просто ще пренасочва към дадено място в системата, трябва да се вземе нужният линк (след &#039;launch=&#039; частта си) и да се сложи като стойност на настройката href.

Пример за пренасочване към списък на номенклатури от тип 5:

href := nomenclatures&amp;nomenclatures=list&amp;type=5&amp;type_section=

Когато бутонът ще пренасочва към справка, е задължително в параметъра href да присъства и името на справката, която искаме да бъде задействана:

Пример:

href := reports&amp;reports=generate_report&amp;report_type=free_days_report

Като част от стойността на href настройката могат да се добавят и всички параметри, които са ни нужни за извеждането на резултат от справката (или на съответното място в системата, към което пренасочваме) във формат:

&lt;име_на_параметъра&gt;=[стойност]

Стойността всъщност, е някое от основните или допълнителни полета на записа (документ/контрагент/проект), от който викаме справката. Пред името на съответната променлива се добавя &#039;b_&#039; (за основна променлива) или &#039;a_&#039; (за допълнителна променлива).

Пример за пренасочване към разглеждане на номенклатура зададена в допълнителни данни на документ

href := nomenclatures&amp;nomenclatures=view&amp;view=a_delivered_article_id

Този линк ще ни отведе в разглеждане на номенклатура с id дефинирано в допълнителната променлива delivered_article_id на текущия запис.

Ако бутонът пренасочва към справка, за името на променливата трябва да се провери какво точно очаква да получи като филтър справката. Важно е също така да се знае в какъв формат справката очаква подадените ѝ резултати. Тази информация може да се изисква от програмиста, който е разработвал съответната функционалност. Ако справката очаква резултатите като низ, то тогава оставяме &lt;име_на_параметъра&gt; както е. Ако обаче справката очаква да получи масив, то тогава след &lt;име_на_параметъра&gt; трябва да се добавят символите []. Важно е да се знае, че ако справката очаква низ, а стойността се окаже, че е масив, то той ще се подаде като стринг, в който отделните стойности са разделени със запетая (,). Ако се изпише име на доп. променлива от group таблица, то към справката ще се подадат стойностите от всички редове под формата на масив.

Пример за пренасочване към справка:

href := reports&amp;reports=generate_report&amp;report_type=hr_employee_file&amp;employee=b_customer&amp;employee_autocomplete=b_customer_name&amp;years=a_plr_leave_year

Този линк ще ни отведе в генериране на справка hr_employee_file, като срещу employee ще се попълни id на контрагента (основни данни), срещу employee_autocomplete ще се попълни името на контрагента (основни данни) на записа, в който се намира бутона, а срещу years ще се попълни стойността от полето plr_leave_year в допълнителните данни на модела.

Друга незадължителна настройка е дали линкът да се отваря в същия прозорец или в нов. Това става в отделен параметър target по следния начин:

target := [прозорец]

Възможните настройките за прозорец са:

	_self (за отваряне в същият прозорец)

	_blank (за отваряне в нов прозорец)

	_lightbox (за отваряне в lightbox)

Пример:

target := _self

Ако искаме линкът да се отваря в лайтбокс (lightbox), можем да добавим няколко допълнителни настройки като ширина (lightbox_width) и височина (lightbox_height) на lightbox-а в пиксели или фон на lightbox-а (lightbox_background_color) ако има по-специфични изисквания в тази насока. Последните три изброени настройки не са задължителни.

Пример:

target := lightbox

lightbox_width := 800

lightbox_height := 600

lightbox_background_color := #F1F1F1

Описаните настройки за lightbox-а не са задължителни, а по-скоро препоръчителни.

Настройка permissions определя за кои действия ще се показва бутонът. Използва се по следния начин:

permissions := &lt;изброяват се един или няколко режима, разделени със запетая: add,edit,view&gt;

Примери:

1) permissions := view (бутонът се вижда само в режим на разглеждане)

2) permissions := edit,view (бутонът се вижда само в режимите на редакция и разглеждане)

3) permissions := add,edit,view (това е равносилно на това настройката да е с празна стойност или просто да няма настройка permissions: бутонът се вижда във всички режими)

ВАЖНО!!! Бутоните могат да съдържат както onclick, така и sequences, настройка и href настройка. Ако има sequence и onclick настройка, редът на изпълнение се определя от реда, в който двете настройки са записани в полето source и, съответно, по-напред ще се изпълни тази, която е по-горе в настройките. При наличие на href настройка, действието за пренасочване винаги се изпълнява последно.

Към настройките на линк бутона могат да се добавят и допълнителни параметри, които да се подават към линк. Форматът за тях е следния:

&lt;име на параметър&gt; := &lt;стойност на настройка&gt;Това, което се запише в тази настройки се добавя към линка в следния вид:href := reports&amp;reports=generate_report&amp;report_type=hr_employee_file&amp;employee=b_customer&amp;employee_autocomplete=b_customer_name&amp;years=a_plr_leave_year&amp;име на параметър=стойност на настройка

В стойност на настройка може да се запише стойността на променлива от текущия модел, която да се вземе от допълнителни или от основни данни (по същия начин както е обяснено в  настройките за href по-горе). Пример:from_date := b_date

Голямото предимство тук е, че можем да въвдем PHP код срещу настройката, който да попълни релативна стойност. Пример:from_date := php(date(‘Y-m-d’))

Това ще попълни в линка параметъра from_date съсстойността на текущата дата. Съответно могат да се пишат и много по-сложни структури от код като се използват и функциите от framework-а на nZoom.

1.7.12 тип table – също не е контрола и аналогично на променлива от тип group служи за променливи, които групират променливи от една и съща група (таблица), НО САМО ЗА ЕДИН РЕД. Широчината на тази променлива дава цялата широчина на таблицата, в която се намират променливите. Тази променлива служи и за пренасяне на цялата групова информация в генерирания PDF анблок.

1.7.13 тип bb - не е контрола и служи за променливи, които групират променливи в ББ.

1.7.14 тип autocompleter - тип контрола, даваща възможност за въвеждане на стойност чрез избор от аутокъмплийтър

Може да се настрои етикетът (label) или пояснителният текст (help) на променливата да се показва като помощен текст в полето, когато то няма стойност, като в source се запише:

show_placeholder := label

show_placeholder := help

1.7.15 тип time - тип контрола, даваща възможност за въвеждане на час

1.7.16 тип file_upload - тип контрола, даваща възможност за причакване на файлове. Важно е да се отбележи, че файлът прикачен през контрола от този тип се записва като прикачен файл към съответния модел. Ако потребителят иска да подмени прикачения файл за дадена променлива, то той първо трябва да изтрие текущия, като за това си има бутон в Редактиране на допълнителни данни. Допълнителните важни настройки за поле от тип file_upload се пишат в source полето на съответната променлива:

upload_max_filesize := ххх – ограничение за размера на файла, където ххх е цяло число – задава се в байтове. За да се зададе в килобайти или мегабайти, числото трябва да бъде последвано от K/KB или M/MB, например:

upload_max_filesize := 5Мforbidden_extensions := – списък с неразрешените разширения, разделени със запетая. Ако разширението на файл, който се опитваме да качим, се намира сред тях, се извежда съобщение за грешка. Разширенията се записват без точка – само буквите на разширението. Големи и малки букви са без значение и няма нужда се пише разширението и с малки и големи букви – само едното (за пригледност с малки букви) е достатъчно. Пример:forbidden_extensions := php, exe, ico

allowed_extensions := – списък с позволените разширения, разделени със запетая. Ако разширението на файла, който се опитваме да качим, НЕ Е сред тях, се извежда съобщение за грешка.  Разширенията се записват без точка – само буквите на разширението. Големи и малки букви са без значение и няма нужда се пише разширението и с малки и големи букви – само едното (за пригледност с малки букви) е достатъчно. Пример:allowed_extensions := jpg, docx

Следващите няколко настройки важат само за изображения (картинки) и няма да се вземат предвид, ако файлът не е такъв. Ако обаче файлът е картинка, първите две настройки (max_width и max_height), са задължителни.

max_width := ххх – максимална широчина на файла, където ххх е цяло число, без суфикси от типа на &quot;px&quot; или &quot;em&quot;

max_height := ххх – максимална височина на файла, където ххх е цяло число, без суфикси от типа на &quot;px&quot; или &quot;em&quot;

Ако размерите на качвана картинка са по-големи от зададените като максимални, картинката се преоразмерява, като пропорциите й се запазват.

ВАЖНО! При задаване на max_width, трябва да се задава и max_height! Ако е нужно да се запази едната от двете стойности (търсите конкретна ширина или конкретна височина), то въвеждайте много голямо число в другата настройка.

view_mode := – как да се показва файлът в режим на разглеждане. Възможните варианти са като thumbnail (умалена картинка – важи само за image файлове) или като icon (иконка). По подразбиране файлът се визуализира като иконка. Ако обаче имаме image файл и сложим view_mode := thumbnail , то следващите две опции определят какъв да е размерът на умалената картинка:thumb_width := ххх – размер за широчина, където ххх е цяло число, без суфикси от типа на &quot;px&quot; или &quot;em&quot;thumb_height := ххх – размер за височина, където ххх е цяло число, без суфикси от типа на &quot;px&quot; или &quot;em&quot;list_image_thumbnail := – указва дали, ако файлът е картинка (image), да се показва като thumbnail в списък. ВАЖНО! Има централизирана настройка за това дали файлът ще се показва като thumbnail в списък. Намира се в конфигурации, секция Интерфейс. Текущата настройка има превес пред централизираната. Ако за дадена променлива искате файла да НЕ се показва като картинка е нужно да запишете настройката със стойност 0. Липсата на настройката, или оставянето ѝ празна, автоматично означава, че ще се ползва централизираната настройка.list_image_thumbnail_width := – указва каква ще е максималната ширина на картинката в списък. Картинката се вкарва в размерите определени от тази и следващата настройка като се запазват пропорциите ѝ.

list_image_thumbnail_height := – указва каква ще е максималната височина на картинката в списък. Картинката се вкарва в размерите определени от тази и предходната настройка като се запазват пропорциите ѝ.ВАЖНО! Има централизирани настройки за размера на thumbnail-а в списък. Намират се в конфигурации, секция Интерфейс. Текущите настройки за  list_image_thumbnail_width и list_image_thumbnail_height вземат превес пред централизираната и дават възможност за гъвкава настройка. Ако за дадена променлива от тип файл искате размерите да са различни от централно зададените можете да сложите съответната настройка. Липсата на настройката, или оставянето ѝ празна, автоматично означава, че ще се ползват централизираните настройки.

1.7.17 тип formula – тип контрола, даваща възмножност да се избират формули за изчисляване на дадени стойности на база на някакви променливи. Формулите се записват в таблиците _formulas и _formulas_i18n, а променливите са в таблици _variables_meta, _variables_i18n, и _variables_cstm. Формулите се записват във формата, който се използва в РНР за извършаване на изчисления, и чрез тях могат да бъдат пресмятани числа и дати. Все още няма интерфейс за тяхното създаване. Променливите могат да получават стойности за всеки отделен модел, за всички модели от даден тип или за всички модели, без значение от типа. За да се знае една променлива от тип formula, за каква точно формула ще се ползва (за числа, дата или индекс), в source полето на променливата се записва formula_type := text (date / index). Формулите от тип index всъщност са форули за изчисляване на числа(text), но към тях се добавят и т.нар. ИНДЕКСИ - вид фомули, които имат различни стойности за даден период от време. За индексите има изграден интерфейс, който не е труден за използване. Индексите се прилагат към стойностите на променливите за които се отнасят с една от трите операции - събиране(+), умножение(*) и заменяне(=). За индексите също могат да се ползват формули, които имам малко по-специален формат от нормалните формули.

Важно: При нужда от използване на формули, моля обърнете се за съдействие към програмистите!!!

1.7.18 тип map - тип контрола, която активира Google карта. За това поле не се записва стойност в базата, а се използват данните от други променливи (които се описват в source полето ѝ), за да изгради адрес, който да подаде на GoogleMaps и Google да се опита да го визуализира. Има няколко настройки към това поле, като ВСИЧКИ са задължителни за попълване:

address – низ, който указва на системата как да форматира подавания към GoogleMaps адрес. Реално той може да съдържа, както статичен текст, така и променливи. При заместването в низа със стойности на променливи разделите между тях се запазват точно във вида, в който са били записани в source полето. Променливите, от които искаме да се вземат данни, трябва да са с префикс a_ за допълнителни и b_ за основни и да са със символа $ пред тях. Ако искаме да включим и статичен текст, то е нужно да го отделим по някакъв начин от променливата (с интервал, със запетая и т.н.). Пример за задаване на настройката:

address := $a_city, $a_street $a_street_number, Bulgaria

Това, което ще се случи при задейстане на картата, е, че системата ще потърси допълнителни променливи city, street, street_number и ще ги попълни в посочения низ. Така, ако посочените променливи не са празни, това което ще се потърси в GoogleMaps ще бъде нещо от сорта на:

Варна, Бенковски 99, Bulgaria

Съветът, в случая, е да изградите търсения символен низ по такъв начин, че GoogleMaps да може да се ориентира. Логично е, ако сложите първо улицата, после държавата след това града и накрая номера, да не получите това, което очаквате.

Освен това, функционалността се ориентира сравнително добре за това дали се намира в BB, в групова таблица и т.н. и, ако променливата е в такава, взема съответните стойности от съответните редове.

type – тип на показване на картата. Може да бъде button и inline. При button се зарежда бутон и картата се активира с натискане върху него. inline зарежда картинка (важно!!! – този тип все още не работи), която визуализира желания адрес. Пример за задаване на настройката:

type := button

map_type – тип на картата. Може да бъде map, satellite, hybrid и terrain. Пример за задаване на настройката:

map_type := satellite

geocoding – посочва дали картата ще се оказва с координати. Може да бъде normal и reverse (важно!!! – тази настройка в момента не се използва). Пример за задаване на настройката:

geocoding := normal

static – Посочва дали картата ще бъде статична или динамична. Статична представлява картинка, която потребителя не може да контролира. Динамична съдържа контроли за движение, приближаване, отдалечаване и т.н. Стойностите могат да бъдат 0 за динамична (важно!!! – динамичните карти в момента не работят) или 1 за статична. Пример за задаване на настройката:

static := 1

width – Ширина на визуализираната карта в пиксели (без наставката &#039;px&#039;). Пример за задаване на настройката:

width := 400

height – Височина на визуализираната карта в пиксели (без наставката &#039;px&#039;). Пример за задаване на настройката:

height := 400

target – Задава къде да се визуализира картата (когато type := button). Може да бъде _blank (в нов екран), _self (в текущия прозорец) и lightbox (в lightbox форма). Пример за задаване на настройката:

target := lightbox

1.8 searchable – ако има стойност, задава по какъв начин ще може да се търси по допълнителната променлива (text, dropdown, date, datetime, autocompleter, number) в Разширено търсене, ако няма стойност, по допълнителната променлива няма да може да се търси

1.9 sortable – 1 - по допълнителната променлива ще може да се сортира, 0 – няма да може

1.10 required – 0 - незадължително попълване на променливата, 1 – задължително, 2 – задължително, но допуска и числото 0

1.11 hidden – 1 - скриване на променливата, 0 – показване

1.12 readonly – 1 - само за четене, 0 – за четене и запис

1.13 calculate – 0 – не се изчислява полето, 1 – изчислява се и до него се показва бутон за изчисление, 2 – изчислява се без бутон до него, 3 – показва се бутон, но полето не се изчислява автоматично, а само след натискане на бутона Задължително се задава readonly 1, когато calculate е по-голямо от 0. Ако readonly е 0, полето може да се редактира и изчисляването става само при натискане на бутона до полето, но не и при натискане на бутона за редакция – полето може да получи стойност, различна от изчислената.

Бутонът за изчисление по принцип има label &quot;Изчисли&quot;, но ако искаме да го променим, трябва да зададем description към това поле в таблицата _fields_i18n.

1.14 bb – номер на група Бойко Борисов. Стойност, равна на 1, имат всички служебни променливи за bb, както и променливите от групови или конфигуратори принадлежащи на bb.

1.15 grouping – номер на група (таблично показван ред от изброими променливи в таблица – ала Excel), в която участва променливата като масив. Ако стойността на полето е 0, имаме обикновена променлива (не изброима).

1.16 gt2 – 1 – променливата участва в групова таблица от ново поколение (това са набор от променливи с твърдо установени имена, които изграждат gt2 таблицата), 0 – не участва

1.17 configurator – номер на група (таблично показван ред от изброими променливи в таблица – ала Excel), в която участва променливата като масив. Ако стойността на полето е 0, имаме обикновена променлива (не изброима).

1.18 table – номер на таблична група за един ред.

1.19 multilang – 1 – стойностите на променливата са езиково зависими, 0 – не

Ако променливата е от тип group или table, при 1 могат да се заместват таблици в шаблони за различни езици.

1.20 multiadd – ако е установена в 1, това означава, че тази променлива участва в колоните за множествено добавяне

1.21 multiedit – ако е установена в 1, това означава, че тази променлива участва в колоните за множествено редактиране

1.22 auditable - 1 - да се записват разликите и промените на променливата, 0 - не

1.23 layout_id – номер на група (идентификатор на секция), в която е разпределена променливата, съответства на layout_id от layouts

1.24 position – позиция (номер) на показване на променливата, при променливи за конфигуратор има по-специално значение

1.25 width – широчина на контролата или на grouping или configurator променлива, например 200 – в пиксели или 100% - в проценти. Желателно е да се задава в пиксели.

1.26 width_print – широчина на контролата при печат.

1.27 height – височина на контролата, например 20 – в пиксели или 100% - в проценти.

* * *

_fields_i18n:

	parent_id – id на променливата (виж _fields_meta – id)



	content_type – тип на съдържанието: &#039;label&#039; (етикет), &#039;help&#039; (помещен текст – визуализира се като инфо балонче пред етикета), &#039;description&#039; (описание – има ограничена употреба) или &#039;back_label&#039; (заден етикет).

Задължително е за всяка променлива да има поне label.

Има възможност за добавяне и на back_label, който се показва зад стойността на променливата (в режим на редакция се показва като етикет зад полето на съответната променлива, а в режим на разглеждане се долепя до стойността на променливата). Има възможност за коригиране на широчината и височината на етикета посредством настройка в полето `source` на таблицата `_fields_meta`. Ето примерен вариант за използване на настройката:

  задаване на широчина на етикета:

    back_label_style := width: 200px;

  задаване на широчина и височина на етикета:

    back_label_style := width: 200px; height: 14px;

Тази настройка (back_label_style) важи само в режим на редакция. В режим на разглеждане не се прилага това форматиране, а директно се показва текстът на задния етикет.

	content – съдържание на езиковия текст (етикет, помощен текст или описание)

	lang – език

* * *

2. Таблица _fields_options:

	2.1 id – autoincrement, не се задава, получава стойност автоматично!

	2.2 parent_name – име на променливата, съответства на name от _fields_meta

	2.3 label – етикет, който се показва за опцията

	2.4 option_value – стойност на опцията

	2.5 child_name – име на зависима променлива, съответства на name от _fields_meta

	2.6 extended_value – разширена стойност за заместване в шаблони за печат

	2.7 position – позиция на опцията

	2.8 option_active – 1 – опцията е активна, 0 – опцията е неактивна (ще бъде налична, само когато е текущо избраната стойност)

	2.9 lang – език

* * *

3. Таблица layouts:

	3.1 layout_id – номер на група променливи (секция)

	3.2 name – име (етикет) на групата, езиково зависимо

	3.3 place – позиция на групата, 0 – скрита

	3.4 lang – език

* * *

II. Зависимости между изчисляемо поле, бутон за изчисление и техните параметри

А. За променлива от тип &quot;text&quot; (&quot;текстово поле&quot;), което искаме да бъде изчисляемо можем да слагаме следните настройки:

1. Флагът calculate може да приема следните стойности:

- 0 - полето НЕ е изчисляемо, до текстово поле няма да се появи бутон &quot;Изчисляване&quot;

- 1 - полето Е изчисляемо, до текстово поле ще се появи бутон &quot;Изчисляване&quot;. При натискане на бутон за запис това поле ще бъде автоматично изчислено.

- 2 - полето Е изчисляемо, с тази разлика, че бутона &quot;Изчисляване&quot; ще бъде скрит- 3 - полето Е изчисляемо, до текстово поле ще се появи бутон &quot;Изчисляване&quot;, но При натискане на бутон за запис това поле НЯМА бъде автоматично изчислено. Този ефект може да се постигне и ако полето не е обявено като readonly.

ЗАБЕЛЕЖКА: ако флагът calculate e 1, флагът readonly на това текстово поле трябва да бъде установен в 1, ако бутонът до полето трябва да се запуска и при натискане на бутон за запис. Ако readonly е 0, то бутонът до полето няма да се запуска при запис.

2. Флаг width - ако широчината на едно изчисляемо поле е 0, то това текстово поле

няма да се покаже (бутона &quot;Изчисляване&quot; ще се покаже, само ако calculate e 1)

Б. Променлива от тип &quot;button&quot; или тип “dropdown”, която служи за запускане на всички бутони от формата. В полето source се указва в каква последователност да се натискат бутоните, по следния начин:

sequence := [списък от всички имена на текстови изчисляеми полета отделени със запетая]

Пример:

sequence := calculate_text1, calculate_text1

Бутонът може да се слага в произволна секция (layout), като това се определя от полето

layout_id. Ако в полето layout_id за този бутон се сложи 0, то този бутон се разполага от лявата страна на бутона за запис на формата.

В таблицата &quot;_fields_i18n&quot; не е задължително да се слага езиков превод за тази променлива.

ВНИМАНИЕ: ако сложите флага hidden със стойност 1 за този бутон, той няма да се

покаже въобще, а останалите изчисляеми бутони трябва да са видими.

Скритият бутон няма да се показва, но ще присъства във формата и ще предизвиква изчисляване при избор на бутона за запис.

* * *

III. Допълнителни променливи при добавянето на нов документ:

В полетата &quot;Име&quot;, &quot;Описание&quot; и &quot;Бележки&quot; при въвеждане могат да се ползват следните променливи:

[document_num] - пореден номер на документа

[document_sub_num] - пореден номер на документа по тип

[customer_name] - име на клиента

[office_name] - име на офиса, за който се отнася документа

[project_name] - проекта, за който се отнася документа

Тези променливи могат да се слагат и в &quot;Име по подразбиране&quot; към типове документи.

* * *

Описание за използване и вписване на

Конфигуратор към модели

за nZoom 1.4.x

Конфигураторът е надграждане на вече съществуващата система за допълнителните променливи към модулите, подробно описана в предходната част на този документ.

	Конфигураторът работи по следният начин:

ползва се таблиците _fields_meta, _fields_i18n, _fields_options, _fields_layouts, както и всички останали допълнителни променливи.

	По подобие на групите във _fields_meta има отделно поле (configurator), което указва принадлежността на дадена променлива към конкретен конфигуратор. Стойноста на това поле е свързано с таблицата configurator, където се записват различни съхранени конфигурации. Ако тази стойност е отрицателно число, то допълнителното панелче при визуализация на конфигуратора (изтриване, запаметяване и зареждане) няма да се показва, а ако е положително число ще се покаже над таблицата за конфигуратор.

Например, след като сме направили конфигурация на компютърна система, т.е. сме определили различни стойности на полетата (параметрите на компютърната станция), може да я запаметим и в последствие да я извикаме и всички полета от конфигуратора ще приемат запаметените стойности.

Типове променливи в конфигуратора

Променливите в конфигуратора условно могат да се разделят на 2 вида: основни и второстепенни.

Основните променливи са идентични със стандартните допълнителни променливи и по нищо не се различават, що се отнася по функционалност

Второстепенните променливи са допълнителни променливи по функционалност, но синтаксиса на името им е специфичен( има се предвид синтаксиса на &#039;име на променливата&#039;, т.е. полето &#039;name&#039; от таблица &#039;_fields_meta&#039;)

Синтаксиса на второстепенните променливи е следния:

[име на основна променлива]__[суфикс]Например:

При основна променлива: processor, второстепенна променлива за цената на процесора би се наричала processor__price.

ЗАБЕЛЕЖКА: Обърнете внимание на двойнатата подчертаваща линия __. Тя е задължителна за разпознаването на второстепенните от основните променливи.

1. Изграждане на конфигуратора

Вертикално (по редове)

Броят на редовете в таблицата за конфигуратора се определя от броя на основните променливи, които имат една и съща стойност в полето configurator в таблицата _fields_meta. Освен това, данните на основните променливи, като име и стойност, се използват за попълване на първите 2 колони от таблицата, където реално се визуализират тези променливи.

Броя на колоните в конфигуратора се определя от второстепенните променливи, което всъщност е:

2. Хоризонталната (по колони) структура

На конфигуратора. Тук специфичното е, че суфиксите на различните променливи, обединени в конфигуратор, могат да се разглеждат като имена на колоните в крайната таблица. Това означава следното: ако имаме 3 основни променливи и 6 второстепенни (виж таблица1.1), следвайки препоръките за съставяне на имената

Таблица 2.1

| Основни променливи | Второстепенни променливи |
| --- | --- |
| config_item1 | config_item1__price |
|  | config_item1__quantity |
| config_item2 | config_item2__quantity |
|  | config_item2__customer |
| config_item3 | config_item3__ customer |
|  | config_item3__price |

може да различим следните уникални суфикси за всички обединени в конфигуратор променливи:

__price

__quantity

__customer

Броят на уникалните суфикси посочва броя на колоните в таблицата + 2 колони за основната променлива. В зависимост дали съществува второстепенна променлива със съответния суфикс, се определя дали клетка в таблицата ще има поле или не.

Позицията на всяка една променлива в конфигуратора се определя от полето &#039;position&#039;. Позицията на основните променливи определя поредността по редове, а позицията на воторостепенните променливи ОТ ПЪРВИЯ РЕД определят поредността по колони. Следвайте правилото за задаване на позиции отгоре-надолу и отляво-надясно. Например (с червено е отбелязана позицията - position):

| config_item11 | config_item1__price2 | config_item1__quantity3 | 4 |
| --- | --- | --- | --- |
| config_item25 | 6 | config_item2__quantity7 | config_item2__customer8 |
| config_item39 | config_item3__price10 | 11 | config_item3__ customer12 |

Полето &#039;content&#039; на първата второстепенна променлива от групата променливи с еднакъв суфикс, подредени по &#039;position&#039;, определя и заглавието на колоната.

Използвайки данните от таблица 1.1, сега ще визуализираме крайната таблица на конфигуратора, която ще има 3 реда (за всяка от основните променливи) и 5 колони (2 колони за основните променливи + 3 колони за всяка второстепенна променлива с уникален суфикс)



| [име на полето] | [поле на осн. променлива] | [__price] [втор. променлива] | [__quantity] | [__ customer] |
| --- | --- | --- | --- | --- |
| config_item1[name] | config_item1[field] | config_item1__price | config_item1__quantity |  |
| config_item2[name] | config_item2[field] |  | config_item2__quantity | config_item2__customer |
| config_item3[name] | config_item3[field] | config_item3__price |  | config_item3__ customer |

3. Едноредова таблица тип конфигуратор

Едноредовата таблица се явява частен случай на конфигуратор. В случая когато при създаването на нов конфигуратор дефинираме САМО една основна променлива и всички други са второстепенни на тази основна, то ще се визуализира таблица с 2 реда и брой колони, толкова, колкото са и дефинираните променливи.

Първият ред от таблицата е за заглавието на колоните. Това заглавие за всяка колона е всъщност label-а на променливата за съответният език.

Вторият ред е полето, дефинирано от променливата.

Разположението на колоните се определя, както досега, от стойността на полето &#039;position&#039;

4. Конфигуратор тип Франкенщайн

Използва всички настройки за конфигуратора. Разликата е, че всички запазени конфигурации се показват таблично под зоната за редактиране на променливи, която наричаме конфигуратор.

Конфигуратора се състои от 2, а &quot;Франкенщайн&quot; се състои от 3 зони:

- Панел за управление (запазване, изтриване и зареждане) на съхранените ШАБЛОНИ за конфигурациите - това е малко панелче с дропдаун оградено в рамка. Шаблоните се използват за всички документи от използвания тип, не са специфични за отделните документи. Панелът може да се премахва (да се скрива) ако номера на конфигуратора е отрицателно число (полето configurator във _fields_meta e отрицателно число).

- Конфигуратор - таблица, в която са изредени всички променливи, където се радактират стойностите в конфигурациите. Тази зона не може да се скрива.

- Съхранени конфигурации - табличка с всички съхранени конфигурации за документа. Тук е специфичната за Франкенщайн информация. Всеки ред от табличката е съхранена конфигурация за документа. Всеки ред може да се редактира като се щракне на иконката за редакция - тогава променливите се зареждат в зоната на конфигуратора (зона 2). При редакция на конфигурация има възможност за съхраняването и - появява се бутон с текст &quot;Редактиране&quot;. Копирането на ред от Франкенщайн става като изберете конфигурация, щракате на моливчето (има го за всеки един ред от зона 3), променливите се зареждат в зона 2 и после само щракате на добавяне - получавате готов копиран ред, копирана конфигурация. По същия начин могат да се използват и шаблоните за конфигурации.

Как се конфигурира Франкенщайн:

1. Прави си обикновен конфигуратор

2. В полето source (от _fields_meta) записвате:

fields := var1, var2, var3

Това е списъка с променливите, които се явяват колони в зона 3 (Съхранени

конфигурации). Можете да сложите колкото променливи искате. Колкото са променливите, толкова колони ще имате в зона 3.

3. В полето source (от _fields_meta) може да се добавя настройка за изчистване на формата за конфигуратор след добавяне на ред във Франкенщайн:

reset_form := 1

При режим &quot;Генериране&quot; (записване като PDF):

1. В шаблона за генериране на PDF се записва общата променлива на

конфигуратора и тя се замества с цялата таблица от съхранени конфигурации (от зона

3)

2. Конфигураторът и Франкенщайн могат да използват отделните съставни променливи

за заместване в шаблоните за генериране.

3. За да се ползват колоните от Франкенщайн за изчисления в друга допълнителна променлива ползваме request(&#039;[име на колоната]_[номер на конфигуратора]&#039;)

Например за сумиране на колона:

array_function := sumnum_rows := config_item5_1$a := request(&#039;config_item5_1&#039;)equation := $a

4. Варианти за разпечатване на поле от тип Франкенщайн

Има три варианта за разпечатване на:

- Франкенщайн като списък от конфигуратори (така както изглежда нормален конфигуратор) Настройка в шаблона:[a_config|display:configs]- Франкенщайн като списък от таблици (вариантите на конфигуратора, отпечатани като редове на една таблица, със специфицираните колони виж т. 5) Настройка в шаблона:[a_config] – не се добавя никакъв модификатор за променливата

- Франкенщайн като списък от конфигуратори и таблици (комбинация от горните два варианта)Настройка в шаблона:[a_config|display:both]



Описание за използване на

променлива тип Бойко Борисов (bb)

Променлива тип Бойко Борисов (bb) е обединяваща променлива, в която могат да се включват групови таблици, групови таблици от ново поколение (GT2) и конфигуратори (без конфигуратори от тип franky). Променлива от тип bb може да се използва само в модули: Документи (Documents), Контрагенти (Customers - добавено с Bug 2910) и Номенклатури (Nomenclatures добавено в Bug 4707). За да бъде част от bb, една групова променлива или конфигуратор трябва да имат за стойност на bb, равна на 1, по което се обединяват (играе ролята на grouping и configurator). Например, ако имаме една групова таблица и един конфигуратор, записваме стойност 1 за полето bb за всички променливи на таблицата и конфигуратора и те вече не се появяват като таблица и конфигуратор в модела.

Има само две служебни променливи към bb:

bb_elements – променлива &quot;Тип поле&quot; от тип dropdown, за source задаваме:

 method := getBBElements, за bb номера на bb (например 1), за position също 1. Методът извлича имената и номерата на таблиците и конфигураторите, принадлежащи към bb.

bb_group - другата служебна променлива е обединяваща променлива от новия тип &#039;bb&#039;, например bb_group за тип се задава &#039;bb&#039;, за полето bb номера на bb (например 1). Въвежда се стойност за label в таблицата _fields_i18n. Променливата bb представлява таблица, всеки ред от която е набор от служебните променливи и променлива от тип групова таблица или конфигуратор, в зависимост от стойността на bb_elements. Интерфейсът позволява добавяне, редактиране и изтриване на редове от тази таблица.

	ПО ЖЕЛАНИЕ могат да се добавят и други незадължителни променливи, които да присъстват в заглавния ред на всеки един вариант от ББ. Няма ограничение в това как да се наричат променливите, от какъв тип да са и в каква последователност да са подредени. Ето и примерни променливи:

bb_name – променлива &quot;Име&quot; от тип text

bb_variant – променлива &quot; Вариант&quot; от тип text

bb_value – променлива &quot; Стойност&quot; от тип text

bb_quantity – променлива &quot; Количество&quot; от тип text

bb_all – променлива &quot; Общо&quot; от тип text

bb_note – променлива &quot; Забележка&quot; от тип textarea.

За position се задават стойности, за да са в тази последователност променливите. Въвеждат се съотвентите стойности за label в таблицата _fields_i18n.

Всяка една от bb_ променливите може да бъде скрита или направена като readonly. За да стане това, трябва колоните hidden и readonly да се установят в 1. Освен това, може да се задава широчина на всички bb_ променливи, включително и на основната, която е bb_group. За целта се използва колоната width.

Ето няколко примера за калкулации с незадължителните променливи.

За да изчислим полето bb_all като произведение на bb_value и bb_quantity и да сумираме колоната bb_all, може да добавим обикновена променлива, в която да запишем резултата, за source записваме:

array_function := sumnum_rows := bb_valueupdate_fields := bb_all$a := request(&#039;bb_value&#039;)$b := request(&#039;bb_quantity&#039;)equation := $a*$b

	За да се изчислят полетата bb_all, за source записваме:

$a := request(&#039;bb_value&#039;)$b := request(&#039;bb_quantity&#039;)equation := $a*$b

За изчисления вътре в конфигуратор се ползва стандартната процедура като в обикновен конфигуратор. За да има изчисления в таблица, е необходимо добавянето на прменлива с име [името на таблицата]_calc, за която се задава име на модел, различен от Document, например BBDocument, model_type трябва да е същият и в source записваме формула за изчисление в таблица, например:

array_function := sumupdate_fields := door_1_countnum_rows := door_1_width$b := request(&#039;door_1_width&#039;)equation := $b

Получената стойност се записва променливата в bb_value на съответния ред.

Възможно е изчисляване на bb_value от конфигуратор по същия начин. Добавя се променлива с име [името на конфигуратора]_calc, за която се задава име на модел, различен от Document, например BBDocument, model_type трябва да е същият и в source записваме формула за изчисление от конфигуратор, например:

$b := request(&#039;config2_item5&#039;)equation := $b



Групова таблица от втори вид - GT2

Типове документи/договори

Добавена е възможност към типовете документи и договори да се добавя групова таблица от 2-ри вид. За целта се създава новият тип документ/договор. При добавяне/редакция на типа може да се &quot;включи&quot; опцията за групова таблица от 2-ри вид като !!! ЗАДЪЛЖИТЕЛНО !!! се посочи секцията (layout), в която трябва да се покаже тя. Ако има готови потребителски (несистемни) секции, груповата таблица може да се сложи във всяка от тях, или в поле „Секция за таблицата“ може да се въведе име, с което ще бъде създадена нова потребителска секция.

Описание на настройките за nZoom 1.4.x

Настройките в nZoom се правят на няколко места:

Тенденцията е всички настройки да се изнесат в таблица &#039;settings&#039; и да има минимално настройки в конфигурационни файлове. Това обаче ще стане постепенно.

Конфигурационните файлове са разделени на секции. Всяка секция е обозначена в следния формат [section_name]. Всяка секция може да съдържа параметри, чиито стойности се задават непосредствено след знака за равенство в следния формат:

[section_name]

parameter = value

Например:

[company_info]

company = ACME

address = Address

ВАЖНО: В конфигурационните файлове НЕ БИВА да се премахват секции или параметри, а само да се сменят или премахват стойностите им.

Ето и пояснение на основните настройки в системата:

1.1. Секция [sys]. Тази секция, както подсказва и името, е системна. Секцията се състои от следните параметри:

1.2. Секция [themes]. Тази секция, съдържа настройките на темите на приложението. Секцията се състои само от един параметър:

1.3. Секция [i18n]. Тази секция, съдържа настройките за интернационализацията (i18n) na системата. Секцията се състои от следните параметри:

1.4. Секция [database]. Тази секция, съдържа настройките за достъп до базата от данни. Секцията се състои от следните параметри:

1.5. Секция [modules]. Тази секция изброява модулите, които са достъпни през главното меню на системата. Секцията се състои от следните параметри:

Списък от модули, разделени със запетая се задава във специален формат:

1.6. Секция [log]. Тази секция, съдържа настройките за логовете на приложението. Секцията се състои от следните параметри:

1.7. Секция [emails]. Тази секция, съдържа настройките за изпращане на известявание. Секцията се състои от следните параметри:

1.8. Секция [company_info]. Тази секция, съдържа данни за фирмата, ползвател на системата. Секцията се състои от следните параметри:

1.9. Секция [mapped_drives]. Тази секция, съдържа настройките мрежовите устройства. Секцията се състои от следните параметри:

1.10. Секция [mailer]. Тази секция, съдържа настройките типа на известяване. Секцията се състои само от един параметър:

1.11. Секция [smtp]. Тази секция, съдържа настройките на SMTP сървъра, ако типът на системата за известяване е зададен като smtp в секцията [mailer] (1.10) . Секцията се състои от следните параметри:

1.12. Секция [sendmail]. Тази секция, съдържа настройките на sendmail програмата, ако типтъ на системата за известяване е зададен като sendmail в секцията [mailer] (1.10). Секцията се състои само от един параметър:

Таблицата съдържа няколко колони по подобие на конфигурационния файл: секция, параметър и стойност

2.1. Секция crontab. Тази секция съдържа настройките за автоматичното известяване (имейлите, които се изпращат периодично всеки ден):

Променливата трябва да бъде от тип date/datetime и да бъде налична в основната таблица за модула. Ако не бъде посочена (вторият вариант), по подразбиране се използва „added“.Интервалът трябва да бъде във формат: &lt;брой&gt; &lt;вид&gt;, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.Например:document := 1 YEARилиdocument := status_modified =&gt; 12 MONTHНастройката важи за всички типове от модула, за които няма записани настройки по тип (виж „Настройки за архивиране“ към секция documents). Ако е необходимо да се архивират само определени типове, то общата настройка трябва да се изключи (редът за модула да се закоменира с „#“ или да се премахне от поле value на настройката).

Променливата трябва да бъде от тип date/datetime и да бъде налична в основната таблица за модула. Ако не бъде посочена (вторият вариант), по подразбиране се използва „added“.Интервалът трябва да бъде във формат: &lt;брой&gt; &lt;вид&gt;, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.Например:document := 1 YEARилиdocument := status_modified =&gt; 12 MONTHНастройката важи за всички типове от модула, за които няма записани настройки по тип (виж „Настройки за унищожаване“ към секция documents). Ако е необходимо да се унищожат само определени типове, то общата настройка трябва да се изключи (редът за модула да се закоменира с „#“ или да се премахне от поле value на настройката).



2.2 Настройваема валидация на основни данни

Допълнителни задължителни полета

Отнася се за модули: Контрагенти, Документи, Проекти, Задачи, Договори. Валидацията е по типове.

Създадени са настройки за всички незадължителни полета, които могат да се зададат като задължителни в съответния модул - в section се записва името на модула, в name – additional_validate_fields и за value се изреждат със запетаи полетата, които могат да бъдат зададени като задължителни.

Тези настройки не бива да се променят, освен ако се налага да се добави/премахне допълнително задължително поле за модула.

Пълен списък от полета:

За модул documents:

custom_num, trademark, contract, project, office, employee, media, deadline, validity_term, description, notes, department, date

За модул projects:

trademark, date_start, date_end, priority, parent_project, finished_part, description, notes

За модул tasks:

trademark, project, planned_time, severity, progress, equipment, task_field, source, description, notes, department

За модул customers:

company_department, position, ucn, identity_num, identity_date, identity_by, identity_valid, address_by_personal_id (важат за контрагент физическо лице)

company_name, in_dds, eik, registration_file, registration_volume, registration_number, registration_address, mol (важат за контрагент юридическо лице)

department, assigned, country, city, postal_code, address, notes, phone, fax, gsm, email, web, skype, othercontact, bank, iban, bic (важат за контрагент физическо или юридическо лице)За модул contracts:

custom_num, trademark, project, employee, date_sign, date_start, date_validity, date_end, description, notes, department

Допълнителните задължителни полета за определен тип записи могат да се зададат през системата (при редакция/добавяне на тип) или директно в базата от данни: в section се записва името на модула, в name – validate_&lt;id_на_тип&gt; и за value се изреждат със запетаи полетата, които са задължителни.

Пример:

section - customers

name – validate_1

value - company_department, country

За контрагенти от тип с id = 1 са зададени да са задължителни полетата company_department и country.

За модулите със секции (за момента всички възможни модули):

- В случай, че потребителят няма право за редактиране на дадена секция на текущия модел, валидацията на това поле няма да се изпълни.

За модул Контрагенти:

- При редакция/добавяне на тип: За всяко допълнително задължително поле е указано дали се ползва за физическо/юридическо лице или и в двата случая.

- При валидация на контрагент: Вземат се под внимание само тези от избраните опции, които важат за неговия вид (физическо/юридическо лице).

- Поле с данни за контакт (phone, fax, gsm, email, web, skype, othercontact), зададено като задължително, изисква попълването на поне един контакт от съответния вид.

Уникални полета

Отнася се за модули: Контрагенти, Документи, Проекти, Задачи, Договори, Номенклатури. Валидацията е по типове.

Създадени са настройки за всички полета, които могат да се зададат като уникални в съответния модул - в section се записва името на модула, в name – validate_unique_fields и за value се изреждат със запетаи полетата, които могат да бъдат зададени като уникални.

Тези настройки не бива да се променят, освен ако се налага да се добави/премахне уникално поле за модула.

Пълен списък от полета:

За модул documents:

customer, trademark, contract, custom_num, date

За модул projects:

trademark, customer

За модул tasks:

trademark

За модул customers:

phone, fax, gsm, identity_num, ibanЗа модул contracts:

custom_num, trademark, customer За модул nomenclatures:

name

Уникални полета за определен тип записи могат да се зададат през системата (при редакция/добавяне на тип) или директно в базата от данни: в section се записва името на модула, в name – validate_&lt;id_на_тип&gt; и за value се изреждат със запетаи полетата, които са уникални, като името на полето трябва да бъде предхождано от &quot;unique_&quot;.

Реално, уникални и допълнителни задължителни полета се записват в една и съща настройка за типа.

Може да се зададе да се проверява за уникалност спрямо записи, добавени само през текущата година. Това става като към списъка от полета се добави &quot;current_year&quot;.

Пример:

section - documents

name – validate_1

value - custom_num, unique_custom_num, current_year

За документи от тип с id = 1 е настроено поле &quot;№ от контрагент&quot; да бъде задължително за попълване и уникално сред документите от този тип, добавени през текущата година.

Особености:

За уникалност се проверяват само полета с попълнена стойност.

За модул Контрагенти се търси само в данните на Контрагенти (но не на Обекти и Лица за контакт).

Полетата за контакти в Контрагенти се валидират спрямо всяко от трите полета, но се настройват потделно. Това означава, че при настроено като уникално само поле &quot;Мобилен телефон&quot;, стойностите за него ще се валидират спрямо данните в трите полета &quot;Мобилен телефон&quot;, &quot;Телефон&quot; и &quot;Факс&quot;, но въвежданите стойности за &quot;Телефон&quot; и &quot;Факс&quot; няма да се валидират за уникалност и за тях една и съща стойност ще може да се въвежда без ограничение.

За модул Договори не се търси в оригинални договори.

За Споразумения не се изпълнява валидация за уникалност на Контрагент и Търговска марка, тъй като те се копират от Договор.

За езиково-зависими полета (за момента такова е само Име в Номенклатури) валидация се прави спрямо данни само на езика на записа.

2.3 Автоматични кодове – настройки:

в section се записва за кой (под-)модул е настройката,

в name – auto_code_leading_zeros за брой водещи нули, auto_code_suffix за допълнение на кода,

във value съответната стойност:

section				name				value

projects			auto_code_leading_zeros	4

projects			auto_code_suffix 		PRJ

users				auto_code_leading_zeros 	4

users				auto_code_suffix 		USR

documents_types		auto_code_leading_zeros 	4

documents_types		auto_code_suffix 		DTP

customers			auto_code_leading_zeros 	4

customers			auto_code_suffix 		CST

offices				auto_code_leading_zeros 	4

offices				auto_code_suffix 		OFFcontracts_types		auto_code_leading_zeros 	4

contracts_types		auto_code_suffix		CRT

finance_companies 		auto_code_leading_zeros 	4

finance_companies		auto_code_suffix		COM

finance_documents_types	auto_code_leading_zeros 	4

finance_documents_types	auto_code_suffix		FDT

finance_analysis_items	auto_code_leading_zeros 	4

finance_analysis_items	auto_code_suffix		FAI

finance_warehouses		auto_code_leading_zeros 	4

finance_warehouses		auto_code_suffix		W

finance_cashboxes		auto_code_leading_zeros 	6

finance_cashboxes		auto_code_suffix		PC

finance_bank_accounts	auto_code_leading_zeros 	6

finance_bank_accounts	auto_code_suffix		BA

2.4 Секция tasks.

Типове назначавания за задачи в дадената инсталация:

name: assignment_types_N, където N е id на тип задача

value: owner,responsible,observer,decision

За всеки тип задача се настройва какви типове назначения ще се прилагат за нея (Изпълнител, Отговорник, Наблюдаващ, Вземащ решения).

Настройката &quot;Типове назначения&quot; е достъпна в интерфейса при редакция на Тип задача.

Полета за шаблони за типовете задачи:

name: configurator_1

value: name,planned_time,severity,description,department,active,group

name: configurator_2

value: name,planned_time,severity,description,department,active,group

Настройки за множествено добавяне и редактиране:

name – multiadd или multiedit – за настройка на полетата по подразбиране, които да се включват във формите при множествено редактиране и множествено добавяне. Ако искаме да настроим полетата, които да се показват за конкретен тип, тогава използваме:

multiadd_&lt;id_на_типа_задачата&gt; - за множествено добавяне multiedit_&lt;id_на_типа_задачата&gt; - за множествено редактиране

value – съдържа изброените полета, които могат да се включат във формите за редактиране+ или за добавяне+. Пълният списък на полетата ще видите по-долу като за да влязат те в съответната форма е нужно в полето value от таблицата settings се записват имената им и се разделят с &#039;, &#039; (запетая и интервал).

name				относно

customer			контрагент

trademark			търговска марка

project				проект

planned_start_date		планирано начало

planned_finish_date		планиран край

planned_time			планирано време

severity			приоритет

progress			% на изпълнение

equipment			апаратура

task_field			тестово поле

source				източник на задачата

description			описание

notes				забележка / решение

department			отдел

active				готовност за публикуване

group				групова принадлежност

is_portal			портален

assignments_owner	назначение за Изпълнител (само при Добавяне+, възможност за избор само на един потребител, изисква се Отдел)

assignments_responsible	назначение за Отговорник (само при Добавяне+, възможност за избор само на един потребител)

аssignments_decision	назначение за Вземащ решение (само при Добавяне+, възможност за избор само на един потребител)

assignments_observer	назначение за Наблюдаващ (само при Добавяне+, възможност за избор само на един потребител)

Когато броячът на добавения/редактирания тип документ изисква определени полета, които не са записани в таблицата settings, те се добавят автоматично. Ако, примерно, към брояча се добавя код на проекта, то във формата за добавяне+/редактиране+ ще се появи и autocompleter за проекти, дори и такъв да не е изрично упоменат в таблицата settings.

Редът на показване на колоните е същият както и реда, в който те са изброени в полето value от таблицата settings.

Настройки за одитиране:

name – audit за настройка за одит

value – съдържа изброените полета, които да бъдат одитирани. Пълният списък на полетата ще видите по-долу, като за да влязат те в одита, е нужно в полето value от таблицата settings да се записват имената им и да се разделят с &#039;, &#039; (запетая и интервал).

name				относно

customer			контрагент

trademark			търговска марка

project				проект

planned_start_date		начало

planned_finish_date		край

status				статус

substatus			състояние (подстатус)

planned_time			планирано време

severity			приоритет

progress			% на изпълнение

equipment			апаратура

task_field			тестово поле

source				източник на задачата

description			описание

notes				забележка / решение

department			отдел

active				готовност за публикуване

group				групова принадлежност

is_portal			портален

assignments_owner	назначение за Изпълнител

assignments_responsible	назначение за Отговорник

аssignments_decision	назначение за Вземащ решение

assignments_observer	назначение за Наблюдаващ

tag				промяна на тагове

add_attachments		добавяне на прикачени файлове

del_attachment		изтриване на прикачен файл

За да се включи допълнителна променлива в одита, е необходимо нейното поле &#039;audit&#039; в таблицата _fields_meta да има стойност 1. Допълнителните променливи НЕ СЕ записват във value полето на таблицата settings.

2.5 Секция documents.

2.5.1. Типове назначавания за документи в дадената инсталация:

name: assignment_types_N, където N е id на тип документ

value: owner,responsible,observer,decision

За всеки тип документ се настройва какви типове назначения ще се прилагат за него (Изпълнител, Отговорник, Наблюдаващ, Вземащ решения).

Настройката &quot;Типове назначения&quot; е достъпна в интерфейса при редакция на Тип документ.

2.5.2. Настройки за множествено добавяне и редактиране:

name –multiadd или multiedit – за настройка на полетата по подразбиране, които да се включват във формите при множествено редактиране и множествено добавяне. Ако искаме да настроим полетата, които да се показват за конкретен тип тогава използваме:

multiadd_&lt;id_на_типа_документ&gt; - за множествено добавяне multiedit_&lt;id_на_типа_документ&gt; - за множествено редактиране

value – съдържа изброените полета, които могат да се включат във формите за редактиране+ или за добавяне+. Пълният списък на полетата ще видите по-долу, като за да влязат те в съответната форма, е нужно да се запишат в полето value от таблицата settings се записват имената им и се разделят с &#039;, &#039; (запетая и интервал).

name				относно

custom_num			номер от контрагент

customer			контрагент

trademark			търговска марка

project				проект

office				офис

employee			служител

contract			по договор №

media				медия на документа

deadline			срок за обработка

validity_term			срок на валидност

date				дата

description			описание

notes				бележки

department			отдел

attachment			файл (за прикачане на файл към документа)

active				готовност за публикуване

group				групова принадлежност

is_portal			портален

Когато броячът на добавения/редактирания тип документ изисква определени полета, които не са записани в таблицата settings, те се добавят автоматично. Ако, примерно, към брояча се добавя код на проекта, то във формата за добавяне+/редактиране+ ще се появи и autocompleter за проекти, дори и такъв да не е изрично упоменат в таблицата settings.

Редът на показване на колоните е същият, както и реда, в който те са изброени в полето value от таблицата settings, а допълнителните променливи се добавят най-отзад след избраните основни. Ако искаме дадена допълнителна променлива да се включи във формата за добавяне+, правим полето й multiadd в таблицата _fields_meta 1, а ако искаме да се включва във формата за редактиране+ - полето multiedit равно на 1.

2.5.3. Настройки за одитиране:

name – audit за настройка за одит

value – съдържа изброените полета, които да бъдат одитирани. Пълният списък на полетата ще видите по-долу като за да влязат те в одита е нужно в полето value от таблицата settings да се записват имената им да се разделят с &#039;, &#039; (запетая и интервал).

name				относно

custom_num			номер от контрагент

customer			контрагент

trademark			търговска марка

project				проект

office				офис

employee			служител

media				медия на документа

deadline			срок за обработка

validity_term			срок на валидност

description			описание

notes				бележки

status				статус

substatus			състояние (подстатус)

department			отдел

active				готовност за публикуване

group				групова принадлежност

is_portal			портален

assignments_owner	назначение за Изпълнител

assignments_responsible	назначение за Отговорник

аssignments_decision	назначение за Вземащ решение

assignments_observer	назначение за Наблюдаващ

tag				промяна на тагове

add_attachments		добавяне на прикачени файлове

del_attachment		изтриване на прикачен файл

За да се включи допълнителна променлива в одита, е необходимо нейното поле &#039;audit&#039; в таблицата _fields_meta да има стойност 1. Допълнителните променливи НЕ СЕ записват във value полето на таблицата settings.

Настройка за полето &quot;По договор №&quot;:

Чрез тази настройка може да се персонализира (за конкретната инсталация) информацията, която се показва в полето.

name – contract_custom_label

value – Като се използват следните ключови думи: custom_num, num и name, може да се определи как да се визуализира информацията.

Пример:

[custom_num] name

(за опциите (или стойността (при разглеждане)) на полето ще се изпишат въведеният от контрагента номер (custom_num) и &quot;Относно&quot; на договора).

2.5.4. Настройки за архивиране

name: archive_N, където N е id на тип документ

value: field := &lt;променлива&gt;

interval := &lt;интервал&gt;

За всеки тип документ може да се настройва на какъв интервал да се изпълнява архивиране от crontab.

За променлива се посочва основна или допълнителна променлива от тип date/datetime, която е налична за типа, като допълнителната трябва да се запише с префикс „a__“ (малко латинско „a“ и две подчертаващи тирета) пред името.

Интервалът трябва да бъде във формат: &lt;брой&gt; &lt;вид&gt;, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

Настройката за архивиране е достъпна в интерфейса при добавяне/редакция на Тип документ.

2.5.5. Настройки за унищожаване

name: purge_N, където N е id на тип документ

value: field := &lt;променлива&gt;

interval := &lt;интервал&gt;

За всеки тип документ може да се настройва на какъв интервал да се изпълнява унищожаване от crontab.

За променлива се посочва основна или допълнителна променлива от тип date/datetime, която е налична за типа, като допълнителната трябва да се запише с префикс „a__“ (малко латинско „a“ и две подчертаващи тирета) пред името.

Интервалът трябва да бъде във формат: &lt;брой&gt; &lt;вид&gt;, където първото е цяло положително число, а второто е текст DAY/WEEK/MONTH/YEAR.

Настройката за унищожаване е достъпна в интерфейса при добавяне/редакция на Тип документ.

2.6 Секция customers.

Настройки за множествено добавяне и редактиране:

name – multiadd или multiedit – за настройка на полетата по подразбиране, които да се включват във формите при множествено редактиране и множествено добавяне. Ако искаме да настроим полетата, които да се показват за конкретен тип тогава използваме:

multiadd_&lt;id_на_типа_контрагент&gt; - за множествено добавяне multiedit_&lt;id_на_типа_контрагент&gt; - за множествено редактиране

value – съдържа изброените полета, които могат да се включат във формите за редактиране+ или за добавяне+. Пълният списък на полетата ще видите по-долу, като за да влязат те в съответната форма е нужно в полето value от таблицата settings се записват имената им и се разделят с &#039;, &#039; (запетая и интервал).

name				име (автоматично се добавя поле фамилия, ако контрагентът е ФЛ)

code				код

company_department		отдел във фирмата (само за ФЛ)

position			позиция (само за ФЛ)

country			държава

city				град

postal_code			пощенски код

address			адрес

notes				бележки

phone				телефон

fax				факс

gsm				мобилен телефон

email				e-mail

web				уеб сайт

skype				Skype

othercontact			Друг контакт

company_name		пълно наименование на фирмата (само за ЮЛ)

in_dds				Идентификационен номер за регистрация по ДДС (само за ЮЛ)

eik 				ЕИК (само за ЮЛ)

registration_file		фирмено дело № (само за ЮЛ)

registration_volume		том/страница/регистър (само за ЮЛ)

registration_number		съд по регистрация (само за ЮЛ)

registration_address		адрес по регистрация (само за ЮЛ)

mol				МОЛ (само за ЮЛ)

ucn				ЕГН (само за ФЛ)

identity_num			лична карта № (само за ФЛ)

identity_date			лична карта издадена на (само за ФЛ)

identity_by			лична карта издадена от (само за ФЛ)

identity_valid			валидна до (само за ФЛ)

address_by_personal_id	адрес по лична карта (само за ФЛ)

active				готовност за публикуване

group				групова принадлежност

is_portal			портален

Редът на показване на колоните е същият, както и реда, в който те са изброени в полето value от таблицата settings, а допълнителните променливи се добавят най-отзад след избраните основни. Ако искаме дадена допълнителна променлива да се включи във формата за Добавяне+, правим полето й multiadd в таблицата _fields_meta 1, а ако искаме да се включва във формата за Редактиране+ - полето multiedit равно на 1.

Малко допълнително уточнение за въвеждането на контактите: във формата за добавяне+/редактиране+ може да се включи всеки един от типовете контакт (телефон, факс, мобилен телефон, e-mail, web страница, skype и друг контакт). Когато въвеждаме повече от един контакт за съответния тип (примерно два телефонни номера), ги отделяме със запетая (пример: 052456522, 023554643), а ако искаме да въвеждаме коментар (бележка) към контакта просто към данните за контакта добавяме &#039;|&#039;(отвесна черта) и желания коментар. Пример за телефони:

052456522|служебен, 052343312|домашен

Настройки за одитиране:

name – audit за настройка за одит

value – съдържа изброените полета, които да бъдат одитирани. За да влязат полета в одита, е нужно в полето value от таблицата settings да се записват имената им и да се разделят с &#039;, &#039; (запетая и интервал).

По подразбиране са зададени: name, lastname, code, in_dds, eik, mol, bank, iban, bic, department, assigned, tag.

За да се включи допълнителна променлива в одита, е необходимо нейното поле &#039;audit&#039; в таблицата _fields_meta да има стойност 1. Допълнителните променливи НЕ СЕ записват във value полето на таблицата settings.

2.7 Секция projects.

Настройки за избор на отговорник на фаза:

name – phases_choose_responsible

value – 0 или 1. При 1 можем да избираме отговорник на фаза за съответната инсталация, а при 0 – не можем. По подразбиране тази стойност е 1.

Настройки за одитиране:

name – audit за настройка за одит

value – съдържа изброените полета, които да бъдат одитирани. Пълният списък на полетата ще видите по-долу, като за да влязат те в одита, е нужно в полето value от таблицата settings да се записват имената им и да се разделят с &#039;, &#039; (запетая и интервал).

name				име

customer			контрагент

trademark			търговска марка

code				код

date_start			начало

date_end			край

priority			приоритет

parent_project			подпроект на

manager			ръководител

finished_part			% изпълнение

budget				планиран бюджет

work_period			човекочасове

description			описание

status				статус

active				готовност за публикуване

group				групова принадлежност

is_portal			портален

assignments			назначения (по потребители или отдели)

tag				промяна на тагове

add_attachments		добавяне на прикачени файлове

del_attachment		изтриване на прикачен файл

За да се включи допълнителна променлива в одита, е необходимо нейното поле &#039;audit&#039; в таблицата _fields_meta да има стойност 1. Допълнителните променливи НЕ СЕ записват във value полето на таблицата settings.

2.8 Секция precision

Настройки за прецизността на закръгленията (0-6) в GT2, за разпределяне, индекси, за цени на номенклатури и др.

gt2_rows – Прецизност на стойностите в редовете в GT2; Прецизност на разпределяне и бюджети

gt2_total – Прецизност на Общо в GT2

gt2_total_vat – Прецизност на Общо ДДС в GT2

gt2_total_with_vat – Прецизност на Общо с ДДС в GT2

gt2_quantity – Прецизност за количеството в редовете в GT2 (изключение от gt2_rows)

indexes_values – Прецизност за резултат от формули на индекси

finance_analysis_percentage – Прецизност на процентно разпределяне по подразбиране на главни центрове, пера и номенклатури

nom_sell_price - Прецизност на продажна цена на номенклатури

nom_last_delivery_price – Прецизност на последна доставна цена на номенклатури

nom_average_weighted_delivery_price - Прецизност на среднопретеглена доставна цена на номенклатури

2.9. Секция contracts

Типове назначавания за договори в дадената инсталация:

name: assignment_types_N, където N е id на тип договор

value: owner,responsible,observer,decision

За всеки тип договор се настройва какви типове назначения ще се прилагат за него (Изпълнител, Отговорник, Наблюдаващ, Вземащ решения).

Настройката &quot;Типове назначения&quot; е достъпна в интерфейса при редакция на Тип договор.

(Назначения се прилагат само за договори с подвид &quot;Договорно споразумение&quot;.)

Настройка за одитираните основни променливи

name – audit за настройка за одит

value – съдържа изброените полета, които да бъдат одитирани. За да влязат полета в одита, е нужно в полето value от таблицата settings да се записват имената им и да се разделят с &#039;, &#039; (запетая и интервал).

По подразбиране са:

active				готовност за публикуване

name				относно

customer			контрагент

trademark			търговска марка

status				статус

substatus			състояние (подстатус)

company			фирма

office				офис

employee			служител

date_sign			дата на подписване

date_start			дата на влизане в сила

date_validity			дата на изтичане

date_end			дата на опция за прекратяване

assignments_owner	назначение за Изпълнител

assignments_responsible	назначение за Отговорник

аssignments_decision	назначение за Вземащ решение

assignments_observer	назначение за Наблюдаващ

tag				промяна на тагове

add_attachments		прикачени файлове

За да се включи допълнителна променлива в одита, е необходимо нейното поле &#039;audit&#039; в таблицата _fields_meta да има стойност 1. Допълнителните променливи НЕ СЕ записват във value полето на таблицата settings.

2.10. Активност на ред - отнася се за модули: Контрагенти, Документи, Проекти, Номенклатури, Бюджети, Плащания, Разходи, Приходи.

Активност на ред е възможност ред от списък на записи да стане изцяло активен, т.е. където и да се щракне с мишката на него, да отвежда на място, което му е посочено. Това действие важи за колоните, които съдържат единствено текст (а не важи за колоните с линкове (Контрагент, Проект), за Статус, за Тагове, за назначения и др.).

Настройката е обща за всички потребители на инсталацията.

Активност на ред се прилага в основни списък и търсене, изгледи, инфо панели и списъците със свързани записи.

В section се записва името на модул (documents, customers, projects, nomenclatures, finance_budgets, finance_incomes_reasons, finance_expenses_reasons, finance_payments), в name – текстът &#039;row_link_action&#039; и за value се въвежда името на действието, което да се активира при щракване с мишката върху реда.

Например:

section - documents

name – row_link_action

value - edit

Действието трябва да съвпада с действие (action) за модула (module, controller) от таблица &#039;roles_definitions&#039;, например: view (разглеждане), edit (редакция), communications (комуникации: коментари, писма, мини задачи), relatives (връзки) и т.н..

За Бюджети е възможно да се зададат и действия enter и control, които нямат права в &#039;roles_definitions&#039;.

Ако потребителят няма права за зададеното действие с даден запис, действието

по подразбиране е: view; при &quot;активация&quot; потребителят ще бъде пренасочен към това действие за съответния модел. Ако потребителят няма и правото по подразбиране, редът за модела няма да бъде &quot;активен&quot;.

Ако моделът няма превод на текущия език на интерфейса, &quot;активацията&quot; ще води до действие translate (ако потребителят има право за него) или редът за модела няма да бъде &quot;активен&quot;.

Изтритите модели не могат да бъдат &quot;активирани&quot;.

Активност на ред може да бъде спряна за всеки модул, когато за value се зададе празна стойност.

Има възможност за настройване на &quot;активност на ред&quot; по тип, което е с по-висок приоритет от общата настройка за модула. Могат да се добавят настройки по тип по аналогичен начин (като нови записи в таблица `settings`):

За section се записва същото, както в общата настройка, например: &#039;documents&#039;, &#039;finance_payments&#039; или &#039;finance_incomes_reasons&#039;.

За name се записва &#039;row_link_action_&lt;id–на-типа&gt;&#039;, например: &#039;row_link_action_104&#039; или &#039;row_link_action_BP&#039;.

За value има три варианта за настройка:

Например, това са настройките за активиране на lightbox към плъгин от списъци за документ тип Тикет в инсталацията на БГСервиз:

href := launch=dashlets&amp;dashlets=custom_action&amp;plugin=bgs_tickets&amp;custom_plugin_action=load&amp;force=1&amp;id=b_id&amp;model_lang=b_model_lang

target := lightbox

lightbox_height := auto

lightbox_background_color := #f1f1f1

module := documents

Тук важната особеност е, че има настройки за module (и controller, при необходимост), за да се запусне подготовката на адреса към тях, а не към текущия модул (за да може функционалността да се изпълнява и от инфо панел в начална страница).

В този случай функционалността се задейства при щракване навсякъде в реда, където няма заложена друга функционалност, както и при щракване на бутона за редакция.

Само този вариант е приложим и за модули, в които не е налична обща функционалност за активност на ред, например модул Събития (events).

2.11. Секция turnovers

Настройка за валута на обороти – ако не е въведена, се взема основната валута за инсталацията.

currency – валута (BGN, EUR …)

2.12. Свързани записи – отнася се за модули Контрагенти, Проекти, Номенклатури, Договори

Настройката за свързани записи позволява да се указва кои от панелите (табове) със свързани записи ще бъдат видими под формата в режим на разглеждане и редакция. В страничния малък панел &quot;Свързани записи&quot; се показва броят записи само за видимите панели със свързани записи. Показването и на двата панела зависи от това дали е настроено да се показва свързан панел &quot;Свързани записи&quot; за текущото действие и за текущия тип записи.

В section се записва името на модул (customers, projects, nomenclatures, contracts), в name – стойност &#039;related_records_modules&#039; и за value се изреждат имената на панелите (отчасти съвпадат с имената на съответните модули), разделени със запетая.

Последователността от панелите се определя от реда, в който са записани, като опциите за финансови и за референтни записи се показват в общо подменюта.

Всеки потребител ще вижда само тези панели (табове), до които има право за &quot;Достъп до модула&quot; (&#039;_access_&#039;) в ролята си.

За показване на референтни записи се проверява и дали в &quot;Референтни типове записи&quot; за текущия тип конрагент е отметнат поне един тип записи от съответния модул.

Възможните стойности са:

За Контрагенти:

documents – Документи

projects – Проекти

tasks – Задачи

events – Събития

finance_incomes_reasons – Приходни документи

finance_expenses_reasons – Разходни документи

finance_payments – Платежни документи

finance_recurring_payments – Регулярни плащания

finance_repayment_plans – Погасителни планове

finance_warehouses_documents – Складови докменти

contracts – Договори

referent_documents – Референтни документи *

referent_projects – Референтни проекти *

referent_contracts – Референтни договори *

referent_customers – Референтни контрагенти *

* Референтни записи са такива, които съдържат текущия контрагент като допълнителни (а не основни) данни.

За Проекти:

documents – Документи

tasks – Задачи

events – Събития

finance_incomes_reasons – Приходни документи

finance_expenses_reasons – Разходни документи

finance_payments – Платежни документи

finance_recurring_payments – Регулярни плащания

finance_warehouses_documents – Складови докменти

contracts – Договори

За Номенклатури:

customers – Контрагенти

documents – Документи

projects – Проекти

tasks – Задачи *

events – Събития *

finance_incomes_reasons – Приходни документи

finance_expenses_reasons – Разходни документи

finance_payments – Платежни документи *

finance_recurring_payments – Регулярни плащания

finance_annulments – Протоколи за анулиране

finance_warehouses_documents – Складови докменти

contracts – Договори

nomenclatures –  Номенклатури

* Отбелязаните видове записи присъстват само за номенклатури от системен тип „Търговска марка“, тъй като нямат допълнителни данни и не биха могли да съдържат друг тип номенклатура.

За Договори:

documents – Документи

Например:

section - customers

name – related_records_modules

value – documents, projects, referent_documents

2.13. Секция finance

Типове назначавания за финансови документи в дадената инсталация:

name: assignment_types_N, където N е id на тип финансов документ

value: owner,responsible,observer,decision

За всеки тип финансов документ се настройва какви типове назначения ще се прилагат за него (Изпълнител, Отговорник, Наблюдаващ, Вземащ решения).

Настройката &quot;Типове назначения&quot; е достъпна в интерфейса при редакция на Тип финансов документ.

(Назначения са налични за приходни, разхдни и складови документи и не са налични за протоколи за анулирание и шаблони за фактури.)

Настройка за фискална валута на фактури – ако не е въведена, се взема основната валута за инсталацията.

Колоните за Данъчна основа (фискална) и ДДС (фискален) се добавят от изгледи за списък и търсене на фактури.

fiscal_currency – валута (BGN, EUR …)

2.14. Секция users

max_failed_attempts: Колко най-много неуспешни опити за вход може да направи потребителя, в някакъв интервал от време, преди да се задейства защитата. В браузъра защитата е поле с captcha, през REST се връща код 429  и се блокират следващи опити за вход, докато не изтече определеното време.

max_failed_attempts_timeout: Какъв е интервала от време за проверка на неуспешните опити. Задава се в брой секунди (30мин = 1800сек, 60мин = 3600). За да изключите изцяло защитата задайте стойност 0.

Състояния (Подстатуси)

За да се въведат подстатуси, е нужно да се попълнят данни в една таблица documents_statuses. Тази таблица има следните полета:

- id – НЕ Е auto-increment, тоест въвежда се ръчно и може да се дублира. Това е направено защото нямаме отделна таблица за езиково-зависимите променливи и опциите за отделните езици се намират в тази таблица. Тоест, ако даден подстатус с id 5 сме въвели на български и искаме да го въведем и на английски, ще въведем ред в таблицата, който отново ще има id 5. Въвеждането на id е задължително!

- doc_type – id-то на типа документ, за който се отнася подстатусът. Примерно, ако искаме подстатусът да е за оферта, а тя е с id 15, то в това поле трябва да въведем 15.

- name – името на подстатуса. Полето е езиково-зависимо!

- description – описание на подстатуса. Полето е езиково-зависимо и е единственото от всички полета в тази таблица, чието попълване не е задължително.

- status – оттук се избира към кой основен статус ще принадлежи състоянието, Възможностите са opened, locked, closed

- sequence – поредност на състоянията. Въвеждат се номера в зависимост от това в какъв ред искаме да се извеждат подстатусите КЪМ СЪОТВЕТНИЯ ОСНОВЕН СТАТУС. Тоест, ако към &quot;Отворен&quot; имаме въведени три състояния – &quot;В процес на изработка&quot;, &quot;За одобрение&quot; и &quot;За редакция&quot; – и искаме да ги виждаме в този ред в полето за sequence трябва да въведем съотеветно 1, 2 и 3 или 7, 12 и 19 (примерно). Ако състоянията са към различни основни статуси, се позволява дублиране на sequence-а. Това ще рече, че можем да имаме подстатус на opened със sequence=1 и да имаме подстатус към closed, който също да има sequence=1 – това НЕ Е проблем.

- lang – език за езиково-зависимите параметри. Когато искаме да въведем състояние на друг език, тук указваме какъв ще е езикът (&quot;bg&quot;, &quot;en&quot;, &quot;de&quot; или друг). Когато въвеждаме състояние на друг език, описваме на съответния език само езиково-зависимите променливи останалите трябва да останат същите. Пример за два реда от таблицата, описващи състоянието &quot;За одобрение&quot; на български и на английски език:

| id | doc_type | name | description | status | sequence | lang |
| --- | --- | --- | --- | --- | --- | --- |
| 2 | 1 | За одобрение | Статус &quot;За одобрение&quot; | opened | 2 | bg |
| 2 | 1 | Approving | Approving status | opened | 2 | en |
